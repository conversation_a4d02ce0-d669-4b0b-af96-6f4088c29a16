2025-08-05 20:33:38,506 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,291 - models - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,322 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,698 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,709 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,719 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,735 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:39,750 - auth - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,274 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,319 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,347 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,356 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,369 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,369 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 21606e0f)
2025-08-05 20:33:42,379 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,410 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,420 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,430 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,441 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,516 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,525 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,533 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,542 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,553 - game_server - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:42,554 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 20:33:42,554 - msgManager - INFO - Monster handlers registered
2025-08-05 20:33:42,554 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 20:33:42,556 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 20:33:42,565 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 20:33:42,565 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 20:33:42,566 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9693c4c0)
2025-08-05 20:33:42,615 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 20:33:42,661 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 20:33:42,662 - game_server - INFO - 模板引擎初始化成功
2025-08-05 20:33:42,665 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:33:42,667 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 5688)
2025-08-05 20:33:42,668 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 20:33:42,851 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:33:42,853 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 20:33:43,383 - ConnectionManager - INFO - 后台任务已启动 (Worker 5688)
2025-08-05 20:33:43,383 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 5688)
2025-08-05 20:33:43,393 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:43,396 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:43,396 - game_server - INFO - 游戏配置加载完成 (Worker: 5688)
2025-08-05 20:33:43,397 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:33:47,990 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:33:47,992 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=0, 总计=2
2025-08-05 20:33:47,992 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 5688): 2/2 (100.0%)
2025-08-05 20:33:47,992 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:33:48,035 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 9693c4c0)
2025-08-05 20:33:48,035 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9693c4c0)
2025-08-05 20:33:48,042 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:48,043 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 20:33:48,044 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 20:33:48,051 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:48,052 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 20:33:48,054 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:48,054 - ConnectionManager - INFO - Worker 5688 开始消费广播消息，消费者标签: ctag1.7888e554cfd8433b9d073effd31ded47
2025-08-05 20:33:48,099 - ConnectionManager - INFO - Worker 5688 开始消费个人消息，消费者标签: ctag1.76fb88ca6e564a0e803b374ac1d9dd35
2025-08-05 20:33:48,200 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 5688)
2025-08-05 20:33:48,268 - distributed_lock - INFO - Worker 5688 获取锁超时: scheduler_initialization
2025-08-05 20:33:48,268 - game_server_scheduler_integration - INFO - Worker 5688 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 20:33:48,269 - game_server - INFO - 统一调度器初始化成功 (Worker: 5688)
2025-08-05 20:33:48,275 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 5688)
2025-08-05 20:33:48,276 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 20:33:48,276 - game_server - INFO - 日志清理管理器已启动 (Worker: 5688)
2025-08-05 20:33:48,277 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:33:48,277 - game_server - INFO - Monster cooldown manager initialized (Worker: 5688)
2025-08-05 20:33:48,632 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 20:33:48,719 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:33:48,721 - game_server - INFO - 公会系统初始化成功 (Worker: 5688)
2025-08-05 20:33:48,722 - game_server - INFO - 邮件系统初始化成功 (Worker: 5688)
2025-08-05 20:33:48,724 - game_server - INFO - 商店系统初始化成功 (Worker: 5688)
2025-08-05 20:33:48,725 - game_server - INFO - 初始化完成 (Worker: 5688)
2025-08-05 20:34:00,076 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:34:01,838 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:34:02,539 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:34:05,049 - shop_api - INFO - [ShopAPI] 获取商店详情: shop_d346cca3b749
2025-08-05 20:34:13,399 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:34:30,286 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:34:43,401 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:34:47,995 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:34:47,995 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:34:49,794 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:34:49,817 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:34:49,819 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:35:00,498 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:35:13,414 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:35:30,712 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:35:43,419 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:35:48,006 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:35:48,007 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:35:48,921 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:35:48,922 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:36:00,919 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:36:13,433 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:38:41,494 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:38:41,495 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:38:41,496 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:38:41,497 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost?heartbeat=30" at 0x1891d71c050> was stuck. No frames were received in 93 seconds.
2025-08-05 20:38:41,499 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost?heartbeat=30 closed. Reconnecting after 5 seconds.
2025-08-05 20:38:42,107 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:38:42,107 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:38:48,920 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:38:48,923 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:39:00,618 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:39:11,496 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:39:30,823 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:39:41,504 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:39:41,505 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:39:41,506 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:39:48,988 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:39:48,988 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:40:05,961 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:40:11,510 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:40:30,418 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:40:41,510 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:40:41,511 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:40:41,526 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:40:48,919 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:40:48,920 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:41:00,592 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:41:11,532 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:41:30,795 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:41:41,517 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:41:41,518 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:41:41,548 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:41:48,918 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:41:48,918 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:42:00,000 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:42:11,556 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:42:30,176 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:42:41,529 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:42:41,530 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:42:41,560 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:42:48,937 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:42:48,937 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:43:00,360 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:43:11,570 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:43:17,459 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:43:30,537 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:43:41,544 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:43:41,544 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:43:41,575 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:43:48,893 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:43:48,893 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:39,372 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:48:39,373 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:48:39,373 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:48:39,374 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost?heartbeat=30" at 0x1891c975400> was stuck. No frames were received in 93 seconds.
2025-08-05 20:48:39,375 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost" at 0x1891d9dad50> was stuck. No frames were received in 183 seconds.
2025-08-05 20:48:39,376 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost?heartbeat=30 closed. Reconnecting after 5 seconds.
2025-08-05 20:48:39,376 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost closed. Reconnecting after 5 seconds.
2025-08-05 20:48:39,378 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:48:40,008 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:48:40,009 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:48,914 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:48:48,915 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:58,589 - equipment_service_distributed - ERROR - Error handling cache message: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 20:48:58,698 - ConnectionManager - ERROR - Redis获取消息出错: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 20:48:58,699 - ConnectionManager - WARNING - Redis连接错误，将重新订阅
2025-08-05 20:48:58,828 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 5688)
2025-08-05 20:49:00,506 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:49:09,381 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:49:30,671 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:49:39,386 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:49:42,207 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:49:42,208 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:49:49,056 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:49:49,060 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:50:06,029 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:50:12,219 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:50:30,309 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:50:42,222 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:50:42,222 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:50:42,223 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:50:48,944 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:50:48,947 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:51:00,497 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:51:12,236 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:51:30,694 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:51:42,236 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:51:42,237 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:51:42,241 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:51:48,930 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:51:48,930 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:52:00,891 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:52:12,247 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:52:30,090 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:52:42,244 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:52:42,245 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:52:42,259 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:52:48,943 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:52:48,943 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:53:00,330 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:53:12,273 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:53:30,557 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:53:42,247 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:53:42,247 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:53:42,277 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:53:48,922 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:53:48,922 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:54:00,799 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:54:12,282 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:54:30,973 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:54:42,253 - ConnectionManager - INFO - Redis连接池状态 (Worker 5688): 使用中=2, 可用=1, 总计=3
2025-08-05 20:54:42,254 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5688): 连接中
2025-08-05 20:54:42,283 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:54:48,902 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:54:48,902 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:55:00,145 - ConnectionManager - INFO - 连接状态 (Worker 5688): 活跃连接数=0, 用户数=0
2025-08-05 20:55:04,466 - game_server - INFO - 关闭服务器... (Worker: 5688)
2025-08-05 20:55:04,467 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 20:55:09,483 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 20:55:09,484 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:55:09,663 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:55:10,086 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:55:10,087 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 20:55:10,087 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:10,088 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:10,088 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 20:55:10,089 - game_server - INFO - 日志清理管理器已停止
2025-08-05 20:55:10,089 - game_server - INFO - 统一调度器已关闭
2025-08-05 20:55:10,090 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 5688)
2025-08-05 20:55:10,133 - game_server - INFO - 服务器资源已清理 (Worker: 5688)
