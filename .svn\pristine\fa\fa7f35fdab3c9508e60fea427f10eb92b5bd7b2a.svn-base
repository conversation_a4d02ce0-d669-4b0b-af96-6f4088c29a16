"""
玩家服务 - 商店系统依赖
"""

import logging
from typing import Dict, Optional
from mongodb_manager import MongoDBManager

logger = logging.getLogger(__name__)


class PlayerService:
    """玩家服务 - 提供玩家基础信息"""
    
    def __init__(self):
        self.players_collection = "players"
        self.guilds_collection = "guilds"
        self.guild_members_collection = "guild_members"
    
    async def _get_db(self):
        """获取数据库连接"""
        db_manager = await MongoDBManager.get_instance()
        return await db_manager.get_db()
    
    async def get_player_level(self, player_id: str) -> int:
        """获取玩家等级"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            player = await collection.find_one({"username": player_id})
            if player:
                return player.get("level", 1)
            
            # 如果玩家不存在，返回默认等级
            logger.warning(f"玩家 {player_id} 不存在，返回默认等级 1")
            return 1
            
        except Exception as e:
            logger.error(f"获取玩家等级失败: {str(e)}")
            return 1
    
    async def get_vip_level(self, player_id: str) -> int:
        """获取玩家VIP等级"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            player = await collection.find_one({"username": player_id})
            if player:
                return player.get("vip_level", 0)
            
            # 如果玩家不存在，返回默认VIP等级
            logger.warning(f"玩家 {player_id} 不存在，返回默认VIP等级 0")
            return 0
            
        except Exception as e:
            logger.error(f"获取玩家VIP等级失败: {str(e)}")
            return 0
    
    async def get_guild_level(self, player_id: str) -> int:
        """获取玩家公会等级"""
        try:
            # 先获取玩家的公会ID
            guild_id = await self.get_player_guild_id(player_id)
            if not guild_id:
                return 0
            
            # 获取公会信息
            db = await self._get_db()
            guilds_collection = db[self.guilds_collection]
            
            guild = await guilds_collection.find_one({"guild_id": guild_id})
            if guild:
                return guild.get("level", 1)
            
            return 0
            
        except Exception as e:
            logger.error(f"获取玩家公会等级失败: {str(e)}")
            return 0
    
    async def get_player_guild_id(self, player_id: str) -> Optional[str]:
        """获取玩家的公会ID"""
        try:
            db = await self._get_db()
            collection = db[self.guild_members_collection]
            
            member = await collection.find_one({"player_id": player_id})
            if member:
                return member.get("guild_id")
            
            return None
            
        except Exception as e:
            logger.error(f"获取玩家公会ID失败: {str(e)}")
            return None
    
    async def get_player_info(self, player_id: str) -> Dict:
        """获取玩家基础信息"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            player = await collection.find_one({"username": player_id})
            if player:
                # 获取公会信息
                guild_id = await self.get_player_guild_id(player_id)
                guild_level = await self.get_guild_level(player_id) if guild_id else 0
                
                return {
                    "player_id": player_id,
                    "username": player.get("username", player_id),
                    "nickname": player.get("nickname", ""),
                    "level": player.get("level", 1),
                    "vip_level": player.get("vip_level", 0),
                    "exp": player.get("exp", 0),
                    "guild_id": guild_id,
                    "guild_level": guild_level,
                    "created_at": player.get("created_at"),
                    "last_login": player.get("last_login"),
                    "online_status": player.get("online_status", False)
                }
            
            # 如果玩家不存在，返回默认信息
            logger.warning(f"玩家 {player_id} 不存在，返回默认信息")
            return {
                "player_id": player_id,
                "username": player_id,
                "nickname": "",
                "level": 1,
                "vip_level": 0,
                "exp": 0,
                "guild_id": None,
                "guild_level": 0,
                "created_at": None,
                "last_login": None,
                "online_status": False
            }
            
        except Exception as e:
            logger.error(f"获取玩家信息失败: {str(e)}")
            return {
                "player_id": player_id,
                "username": player_id,
                "level": 1,
                "vip_level": 0,
                "guild_level": 0
            }
    
    async def update_player_level(self, player_id: str, level: int) -> bool:
        """更新玩家等级"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            result = await collection.update_one(
                {"username": player_id},
                {"$set": {"level": level}},
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"更新玩家等级失败: {str(e)}")
            return False
    
    async def update_vip_level(self, player_id: str, vip_level: int) -> bool:
        """更新玩家VIP等级"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            result = await collection.update_one(
                {"username": player_id},
                {"$set": {"vip_level": vip_level}},
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"更新玩家VIP等级失败: {str(e)}")
            return False
    
    async def add_exp(self, player_id: str, exp: int) -> bool:
        """增加玩家经验"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            result = await collection.update_one(
                {"username": player_id},
                {"$inc": {"exp": exp}},
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"增加玩家经验失败: {str(e)}")
            return False
    
    async def check_player_exists(self, player_id: str) -> bool:
        """检查玩家是否存在"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            player = await collection.find_one({"username": player_id})
            return player is not None
            
        except Exception as e:
            logger.error(f"检查玩家是否存在失败: {str(e)}")
            return False
    
    async def get_player_achievements(self, player_id: str) -> list:
        """获取玩家成就列表"""
        try:
            # 这里可以实现成就系统
            # 暂时返回空列表
            return []
            
        except Exception as e:
            logger.error(f"获取玩家成就失败: {str(e)}")
            return []
    
    async def check_player_achievement(self, player_id: str, achievement_id: str) -> bool:
        """检查玩家是否拥有特定成就"""
        try:
            achievements = await self.get_player_achievements(player_id)
            return achievement_id in achievements
            
        except Exception as e:
            logger.error(f"检查玩家成就失败: {str(e)}")
            return False
    
    async def get_player_statistics(self, player_id: str) -> Dict:
        """获取玩家统计信息"""
        try:
            # 这里可以实现各种统计信息
            # 例如：登录天数、购买次数、战斗次数等
            return {
                "total_login_days": 0,
                "total_purchases": 0,
                "total_battles": 0,
                "total_online_time": 0
            }
            
        except Exception as e:
            logger.error(f"获取玩家统计信息失败: {str(e)}")
            return {}
    
    async def is_player_online(self, player_id: str) -> bool:
        """检查玩家是否在线"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            player = await collection.find_one({"username": player_id})
            if player:
                return player.get("online_status", False)
            
            return False
            
        except Exception as e:
            logger.error(f"检查玩家在线状态失败: {str(e)}")
            return False
    
    async def set_player_online_status(self, player_id: str, online: bool) -> bool:
        """设置玩家在线状态"""
        try:
            db = await self._get_db()
            collection = db[self.players_collection]
            
            result = await collection.update_one(
                {"username": player_id},
                {"$set": {"online_status": online}},
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"设置玩家在线状态失败: {str(e)}")
            return False
