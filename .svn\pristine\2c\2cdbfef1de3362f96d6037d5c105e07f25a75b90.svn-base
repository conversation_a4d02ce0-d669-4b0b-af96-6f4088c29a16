# -*- coding: utf-8 -*-
"""
公会缓存管理器
处理公会相关的Redis缓存操作和分布式锁
"""

import json
import logging
import time
from typing import Optional, List, Dict, Any
from datetime import datetime
from redis_manager import RedisManager
from distributed_lock import DistributedLock
from guild_models import Guild, GuildMember, GuildApplication

logger = logging.getLogger(__name__)


# 自定义JSON编码器，处理特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理特殊类型"""
    def default(self, obj):
        try:
            from bson import ObjectId
        except ImportError:
            ObjectId = None

        if ObjectId and isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, bytes):
            return obj.decode('utf-8')
        return super().default(obj)


class GuildCacheManager:
    """公会缓存管理器"""

    _instance = None
    _lock = None

    # 缓存键前缀
    GUILD_PREFIX = "guild:info:"
    MEMBER_LIST_PREFIX = "guild:members:"
    MEMBER_INFO_PREFIX = "guild:member:"
    PLAYER_GUILD_PREFIX = "player:guild:"
    GUILD_APPLICATIONS_PREFIX = "guild:applications:"
    GUILD_ONLINE_PREFIX = "guild:online:"

    # 缓存过期时间（秒）
    GUILD_INFO_TTL = 3600        # 公会信息缓存1小时
    MEMBER_LIST_TTL = 1800       # 成员列表缓存30分钟
    MEMBER_INFO_TTL = 1800       # 成员信息缓存30分钟
    PLAYER_GUILD_TTL = 3600      # 玩家公会信息缓存1小时
    APPLICATIONS_TTL = 600       # 申请列表缓存10分钟
    ONLINE_TTL = 300             # 在线状态缓存5分钟

    def __init__(self):
        self.redis_client = None

    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            if cls._lock is None:
                import asyncio
                cls._lock = asyncio.Lock()

            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    logger.info("GuildCacheManager实例已创建")

        return cls._instance
    
    async def _get_redis(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            redis_manager = await RedisManager.get_instance()
            self.redis_client = await redis_manager.get_redis()
        return self.redis_client

    # ==================== 公会信息缓存 ====================
    
    async def cache_guild_info(self, guild: Guild) -> bool:
        """缓存公会信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_PREFIX}{guild.guild_id}"
            # 使用自定义编码器而不是default=str
            value = json.dumps(guild.to_dict(), ensure_ascii=False, cls=CustomJSONEncoder)

            await redis.setex(key, self.GUILD_INFO_TTL, value)
            logger.debug(f"公会信息已缓存: {guild.guild_id}")
            return True

        except Exception as e:
            logger.error(f"缓存公会信息时发生错误: {str(e)}")
            return False

    async def get_cached_guild_info(self, guild_id: str) -> Optional[Guild]:
        """获取缓存的公会信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_PREFIX}{guild_id}"
            value = await redis.get(key)
            
            if value:
                guild_data = json.loads(value)
                return Guild.from_dict(guild_data)
            return None
            
        except Exception as e:
            logger.error(f"获取缓存公会信息时发生错误: {str(e)}")
            return None

    async def get_guild_info_by_player(self, player_id: str) -> Optional[Guild]:
        """获取玩家所在的公会信息"""
        try:
            guild_id = await self.get_cached_player_guild(player_id)
            if guild_id:
                return await self.get_cached_guild_info(guild_id)
            return None            
        except Exception as e:
            logger.error(f"获取玩家公会信息时发生错误: {str(e)}")
            return None

    async def invalidate_guild_info(self, guild_id: str) -> bool:
        """使公会信息缓存失效"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_PREFIX}{guild_id}"
            await redis.delete(key)
            
            # 发布缓存失效通知
            await self._publish_cache_invalidation("guild_info", guild_id)
            logger.debug(f"公会信息缓存已失效: {guild_id}")
            return True
            
        except Exception as e:
            logger.error(f"使公会信息缓存失效时发生错误: {str(e)}")
            return False

    # ==================== 成员信息缓存 ====================
    
    async def cache_member_list(self, guild_id: str, members: List[GuildMember]) -> bool:
        """缓存公会成员列表"""
        try:
            redis = await self._get_redis()
            key = f"{self.MEMBER_LIST_PREFIX}{guild_id}"

            # 转换为字典列表
            members_data = [member.to_dict() for member in members]
            # 使用自定义编码器而不是default=str
            value = json.dumps(members_data, ensure_ascii=False, cls=CustomJSONEncoder)

            await redis.setex(key, self.MEMBER_LIST_TTL, value)
            logger.debug(f"成员列表已缓存: {guild_id}")
            return True

        except Exception as e:
            logger.error(f"缓存成员列表时发生错误: {str(e)}")
            return False

    async def get_cached_member_list(self, guild_id: str) -> Optional[List[GuildMember]]:
        """获取缓存的成员列表"""
        try:
            redis = await self._get_redis()
            key = f"{self.MEMBER_LIST_PREFIX}{guild_id}"
            value = await redis.get(key)
            
            if value:
                members_data = json.loads(value)
                return [GuildMember.from_dict(data) for data in members_data]
            return None
            
        except Exception as e:
            logger.error(f"获取缓存成员列表时发生错误: {str(e)}")
            return None

    async def cache_member_info(self, member: GuildMember) -> bool:
        """缓存成员信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.MEMBER_INFO_PREFIX}{member.guild_id}:{member.player_id}"
            # 使用自定义编码器而不是default=str
            value = json.dumps(member.to_dict(), ensure_ascii=False, cls=CustomJSONEncoder)

            await redis.setex(key, self.MEMBER_INFO_TTL, value)
            return True

        except Exception as e:
            logger.error(f"缓存成员信息时发生错误: {str(e)}")
            return False

    async def get_cached_member_info(self, guild_id: str, player_id: str) -> Optional[GuildMember]:
        """获取缓存的成员信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.MEMBER_INFO_PREFIX}{guild_id}:{player_id}"
            value = await redis.get(key)
            
            if value:
                member_data = json.loads(value)
                return GuildMember.from_dict(member_data)
            return None
            
        except Exception as e:
            logger.error(f"获取缓存成员信息时发生错误: {str(e)}")
            return None

    async def invalidate_member_cache(self, guild_id: str, player_id: str = None) -> bool:
        """使成员缓存失效"""
        try:
            redis = await self._get_redis()
            
            # 删除成员列表缓存
            member_list_key = f"{self.MEMBER_LIST_PREFIX}{guild_id}"
            await redis.delete(member_list_key)
            
            # 如果指定了玩家ID，删除特定成员信息缓存
            if player_id:
                member_info_key = f"{self.MEMBER_INFO_PREFIX}{guild_id}:{player_id}"
                await redis.delete(member_info_key)
                
                # 删除玩家公会信息缓存
                player_guild_key = f"{self.PLAYER_GUILD_PREFIX}{player_id}"
                await redis.delete(player_guild_key)
            
            # 发布缓存失效通知
            await self._publish_cache_invalidation("member_cache", guild_id, player_id)
            logger.debug(f"成员缓存已失效: {guild_id} - {player_id}")
            return True
            
        except Exception as e:
            logger.error(f"使成员缓存失效时发生错误: {str(e)}")
            return False

    # ==================== 玩家公会信息缓存 ====================
    
    async def cache_player_guild(self, player_id: str, guild_id: str) -> bool:
        """缓存玩家所在公会信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.PLAYER_GUILD_PREFIX}{player_id}"
            
            await redis.setex(key, self.PLAYER_GUILD_TTL, guild_id)
            return True
            
        except Exception as e:
            logger.error(f"缓存玩家公会信息时发生错误: {str(e)}")
            return False

    async def get_cached_player_guild(self, player_id: str) -> Optional[str]:
        """获取缓存的玩家公会信息"""
        try:
            redis = await self._get_redis()
            key = f"{self.PLAYER_GUILD_PREFIX}{player_id}"
            guild_id = await redis.get(key)
            
            return guild_id if guild_id else None
            
        except Exception as e:
            logger.error(f"获取缓存玩家公会信息时发生错误: {str(e)}")
            return None

    # ==================== 在线状态缓存 ====================
    
    async def set_member_online(self, guild_id: str, player_id: str) -> bool:
        """设置成员在线状态"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_ONLINE_PREFIX}{guild_id}"
            
            await redis.sadd(key, player_id)
            await redis.expire(key, self.ONLINE_TTL)
            return True
            
        except Exception as e:
            logger.error(f"设置成员在线状态时发生错误: {str(e)}")
            return False

    async def set_member_offline(self, guild_id: str, player_id: str) -> bool:
        """设置成员离线状态"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_ONLINE_PREFIX}{guild_id}"
            
            await redis.srem(key, player_id)
            return True
            
        except Exception as e:
            logger.error(f"设置成员离线状态时发生错误: {str(e)}")
            return False

    async def get_online_members(self, guild_id: str) -> List[str]:
        """获取在线成员列表"""
        try:
            redis = await self._get_redis()
            key = f"{self.GUILD_ONLINE_PREFIX}{guild_id}"
            
            members = await redis.smembers(key)
            return list(members) if members else []
            
        except Exception as e:
            logger.error(f"获取在线成员列表时发生错误: {str(e)}")
            return []

    # ==================== 分布式锁 ====================
    
    async def acquire_guild_lock(self, guild_id: str, operation: str, ttl: int = 30) -> DistributedLock:
        """获取公会操作锁"""
        lock_key = f"lock:guild:{operation}:{guild_id}"
        return DistributedLock(lock_key, ttl)

    async def acquire_member_lock(self, player_id: str, operation: str, ttl: int = 30) -> DistributedLock:
        """获取成员操作锁"""
        lock_key = f"lock:member:{operation}:{player_id}"
        return DistributedLock(lock_key, ttl)

    # ==================== 缓存失效通知 ====================
    
    async def _publish_cache_invalidation(self, cache_type: str, guild_id: str, player_id: str = None):
        """发布缓存失效通知"""
        try:
            redis = await self._get_redis()
            
            message = {
                "type": cache_type,
                "guild_id": guild_id,
                "timestamp": int(time.time())
            }
            
            if player_id:
                message["player_id"] = player_id
            
            channel = "guild_cache_invalidation"
            await redis.publish(channel, json.dumps(message))
            
        except Exception as e:
            logger.error(f"发布缓存失效通知时发生错误: {str(e)}")

    async def subscribe_cache_invalidation(self, callback):
        """订阅缓存失效通知"""
        try:
            redis = await self._get_redis()
            pubsub = redis.pubsub()
            await pubsub.subscribe("guild_cache_invalidation")
            
            async for message in pubsub.listen():
                if message["type"] == "message":
                    try:
                        data = json.loads(message["data"])
                        await callback(data)
                    except Exception as e:
                        logger.error(f"处理缓存失效通知时发生错误: {str(e)}")
                        
        except Exception as e:
            logger.error(f"订阅缓存失效通知时发生错误: {str(e)}")

    # ==================== 批量操作 ====================
    
    async def invalidate_all_guild_cache(self, guild_id: str) -> bool:
        """使公会所有相关缓存失效"""
        try:
            redis = await self._get_redis()
            
            # 构建所有相关的缓存键
            keys_to_delete = [
                f"{self.GUILD_PREFIX}{guild_id}",
                f"{self.MEMBER_LIST_PREFIX}{guild_id}",
                f"{self.GUILD_APPLICATIONS_PREFIX}{guild_id}",
                f"{self.GUILD_ONLINE_PREFIX}{guild_id}"
            ]
            
            # 删除所有相关缓存
            if keys_to_delete:
                await redis.delete(*keys_to_delete)
            
            # 发布缓存失效通知
            await self._publish_cache_invalidation("all_guild_cache", guild_id)
            logger.debug(f"公会所有缓存已失效: {guild_id}")
            return True
            
        except Exception as e:
            logger.error(f"使公会所有缓存失效时发生错误: {str(e)}")
            return False
