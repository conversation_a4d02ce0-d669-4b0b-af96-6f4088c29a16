"""
游戏服务器调度器集成模块
提供游戏服务器与统一调度器的集成接口
"""

import asyncio
from typing import Optional
from logger_config import setup_logger
from service_locator import ServiceLocator
from unified_scheduler_manager import UnifiedSchedulerManager, SchedulerMode
from scheduler_tasks_unified import create_integrated_scheduler
from scheduler_health_checks import task_metrics

logger = setup_logger(__name__)

class GameServerSchedulerIntegration:
    """游戏服务器调度器集成"""
    
    def __init__(self):
        self.scheduler: Optional[UnifiedSchedulerManager] = None
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化集成调度器"""
        if self.is_initialized:
            logger.warning("调度器已经初始化")
            return True

        try:
            import os
            worker_id = os.getpid()

            # 使用Redis分布式锁确保只有一个Worker初始化调度器
            from distributed_lock import DistributedLock

            scheduler_init_lock = DistributedLock("scheduler_initialization", ttl=60)

            try:
                if await scheduler_init_lock.acquire(blocking=False):
                    logger.info(f"Worker {worker_id} 获得调度器初始化权限")

                    # 创建集成调度器
                    self.scheduler = await create_integrated_scheduler()

                    # 注册已存在的服务（如果有的话）
                    await self._register_existing_services()

                    # 启动调度器
                    if await self.scheduler.start():
                        self.is_initialized = True
                        logger.info(f"Worker {worker_id} 调度器初始化成功")
                        return True
                    else:
                        logger.error(f"Worker {worker_id} 调度器启动失败")
                        return False
                else:
                    logger.info(f"Worker {worker_id} 未获得调度器初始化权限，跳过调度器初始化")
                    return True  # 其他Worker不初始化调度器，但返回成功

            finally:
                if scheduler_init_lock._acquired:
                    await scheduler_init_lock.release()

        except Exception as e:
            logger.error(f"初始化游戏服务器集成调度器失败: {str(e)}")
            return False
    
    async def _register_existing_services(self):
        """注册已存在的服务"""
        # 检查并注册已经在ServiceLocator中的服务
        existing_services = [
            ("conn_manager", "连接管理器"),
            ("monster_cooldown_manager", "怪物冷却管理器"),
            ("redis_manager", "Redis管理器"),
            ("mongodb_manager", "MongoDB管理器")
        ]
        
        for service_key, service_name in existing_services:
            service = ServiceLocator.get(service_key)
            if service:
                logger.info(f"发现已存在的服务: {service_name}")
                # 服务已经在ServiceLocator中，调度器会自动使用
            else:
                logger.debug(f"服务 {service_name} 尚未注册，将由调度器初始化")
    
    async def shutdown(self):
        """关闭调度器"""
        if self.scheduler and self.is_initialized:
            logger.info("关闭游戏服务器集成调度器...")
            await self.scheduler.stop()
            self.is_initialized = False
            logger.info("游戏服务器集成调度器已关闭")
    
    def get_task_metrics(self) -> dict:
        """获取任务执行指标"""
        return task_metrics.get_all_stats()
    
    async def get_health_status(self) -> dict:
        """获取服务健康状态"""
        if self.scheduler:
            return await self.scheduler.check_service_health()
        return {}
    
    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self.is_initialized and self.scheduler and self.scheduler.is_running

# 全局集成实例
_game_server_scheduler: Optional[GameServerSchedulerIntegration] = None

async def get_game_server_scheduler() -> GameServerSchedulerIntegration:
    """获取游戏服务器调度器集成实例"""
    global _game_server_scheduler
    if _game_server_scheduler is None:
        _game_server_scheduler = GameServerSchedulerIntegration()
    return _game_server_scheduler

async def initialize_game_server_scheduler() -> bool:
    """初始化游戏服务器调度器"""
    scheduler_integration = await get_game_server_scheduler()
    return await scheduler_integration.initialize()

async def shutdown_game_server_scheduler():
    """关闭游戏服务器调度器"""
    global _game_server_scheduler
    if _game_server_scheduler:
        await _game_server_scheduler.shutdown()
        _game_server_scheduler = None

# 兼容性接口 - 保持与现有代码的兼容性
async def register_tasksQueue():
    """兼容性接口：注册任务队列处理器"""
    logger.info("使用统一调度器，跳过传统任务队列注册")
    # 统一调度器会直接调用任务方法，不需要队列处理器

# 兼容性任务处理器 - 如果需要保持队列模式
async def do_reset_daily(params):
    """兼容性接口：每日重置任务"""
    from scheduler_tasks_unified import UnifiedSchedulerTasks
    scheduler_integration = await get_game_server_scheduler()
    if scheduler_integration.scheduler:
        tasks = UnifiedSchedulerTasks(scheduler_integration.scheduler)
        await tasks.daily_reset_task()

async def do_push_online_count(params):
    """兼容性接口：推送在线人数任务"""
    from scheduler_tasks_unified import UnifiedSchedulerTasks
    scheduler_integration = await get_game_server_scheduler()
    if scheduler_integration.scheduler:
        tasks = UnifiedSchedulerTasks(scheduler_integration.scheduler)
        await tasks.push_online_count_task()

async def monster_cooldown_persist_cooldowns(params):
    """兼容性接口：怪物冷却持久化任务"""
    from scheduler_tasks_unified import UnifiedSchedulerTasks
    scheduler_integration = await get_game_server_scheduler()
    if scheduler_integration.scheduler:
        tasks = UnifiedSchedulerTasks(scheduler_integration.scheduler)
        await tasks.monster_cooldown_persist_task()

async def monster_cooldown_notify_expired_cooldowns(params):
    """兼容性接口：怪物冷却通知任务"""
    from scheduler_tasks_unified import UnifiedSchedulerTasks
    scheduler_integration = await get_game_server_scheduler()
    if scheduler_integration.scheduler:
        tasks = UnifiedSchedulerTasks(scheduler_integration.scheduler)
        await tasks.monster_cooldown_notify_task()
