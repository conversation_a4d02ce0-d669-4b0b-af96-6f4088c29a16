<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理 - 商店管理系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-container">
            <h1 class="header-title">商店管理系统</h1>
            <nav class="header-nav">
                <a href="index.html" class="nav-link">商店管理</a>
                <a href="items.html" class="nav-link active">商品管理</a>
            </nav>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="index.html">商店管理</a>
            <span class="breadcrumb-separator">></span>
            <span id="currentShopName">商品管理</span>
        </div>

        <!-- 操作工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h2>商品配置列表</h2>
                <span class="item-count">总计: <span id="itemCount">0</span> 个商品</span>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-primary" onclick="openItemForm()">
                    <i class="icon-plus"></i>
                    新增商品
                </button>
                <button class="btn btn-secondary" onclick="refreshItemList()">
                    <i class="icon-refresh"></i>
                    刷新
                </button>
                <button class="btn btn-secondary" onclick="goBackToShops()">
                    返回商店列表
                </button>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-bar">
            <div class="filter-group">
                <label for="statusFilter">状态:</label>
                <select id="statusFilter" onchange="filterItems()">
                    <option value="">全部</option>
                    <option value="true">激活</option>
                    <option value="false">禁用</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="searchInput">搜索商品:</label>
                <input type="text" id="searchInput" placeholder="搜索商品模板ID或配置ID..." onkeyup="searchItems()">
            </div>
            <div class="filter-group">
                <label for="sortBy">排序:</label>
                <select id="sortBy" onchange="sortItems()">
                    <option value="sort_order">按排序权重</option>
                    <option value="item_template_id">按模板ID</option>
                    <option value="created_at">按创建时间</option>
                </select>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="item-list-container">
            <div id="itemList" class="item-grid">
                <!-- 商品卡片将通过JavaScript动态生成 -->
            </div>
            
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state">
                <div class="spinner"></div>
                <p>加载中...</p>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-icon">📦</div>
                <h3>暂无商品配置</h3>
                <p>点击"新增商品"按钮为此商店添加第一个商品</p>
                <button class="btn btn-primary" onclick="openItemForm()">新增商品</button>
            </div>
        </div>
    </main>

    <!-- 商品表单模态框 -->
    <div id="itemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增商品</h3>
                <button class="modal-close" onclick="closeItemForm()" title="关闭弹窗 (按ESC键也可关闭)">&times;</button>
            </div>
            <div class="modal-body">
                <form id="itemForm" onsubmit="saveItem(event)">
                    <!-- 基础信息 -->
                    <div class="form-section">
                        <h4 class="section-title">基础信息</h4>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="itemTemplateId">道具模板ID *</label>
                                <input type="text" id="itemTemplateId" name="item_template_id" required 
                                       placeholder="例: 10001" pattern="[0-9]+" 
                                       title="只能包含数字">
                            </div>
                            <div class="form-group">
                                <label for="itemQuantity">道具数量 *</label>
                                <input type="number" id="itemQuantity" name="item_quantity" required 
                                       min="1" max="999999" value="1">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="itemQuality">道具品质</label>
                                <select id="itemQuality" name="item_quality">
                                    <option value="">默认品质</option>
                                    <option value="1">白色</option>
                                    <option value="2">绿色</option>
                                    <option value="3">蓝色</option>
                                    <option value="4">紫色</option>
                                    <option value="5">橙色</option>
                                    <option value="6">红色</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="slotId">商品槽位ID</label>
                                <input type="number" id="slotId" name="slot_id" 
                                       min="1" max="100" placeholder="可选">
                            </div>
                        </div>
                    </div>

                    <!-- 价格配置 -->
                    <div class="form-section">
                        <h4 class="section-title">价格配置</h4>
                        <div class="form-group">
                            <label for="priceConfig">价格配置 (JSON) *</label>
                            <textarea id="priceConfig" name="price_config" required rows="4"
                                      placeholder='{"currency": "gold", "amount": 100}'></textarea>
                            <small class="form-help">示例: {"currency": "gold", "amount": 100} 或 {"currency": "diamond", "amount": 10}</small>
                        </div>
                    </div>

                    <!-- 限购配置 -->
                    <div class="form-section">
                        <h4 class="section-title">限购配置</h4>
                        <div class="form-group">
                            <label for="purchaseLimit">限购配置 (JSON)</label>
                            <textarea id="purchaseLimit" name="purchase_limit" rows="3"
                                      placeholder='{"type": "daily", "count": 5}'></textarea>
                            <small class="form-help">示例: {"type": "daily", "count": 5} 表示每日限购5次</small>
                        </div>
                    </div>

                    <!-- 可用性配置 -->
                    <div class="form-section">
                        <h4 class="section-title">可用性配置</h4>
                        <div class="form-group">
                            <label for="availability">可用性配置 (JSON) *</label>
                            <textarea id="availability" name="availability" required rows="3"
                                      placeholder='{"start_time": null, "end_time": null, "conditions": {}}'></textarea>
                            <small class="form-help">配置商品的可用时间和条件</small>
                        </div>
                    </div>

                    <!-- 刷新配置 -->
                    <div class="form-section">
                        <h4 class="section-title">刷新配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="refreshWeight">刷新权重</label>
                                <input type="number" id="refreshWeight" name="refresh_weight" 
                                       min="0" max="10000" value="100">
                            </div>
                            <div class="form-group">
                                <label for="refreshProbability">出现概率</label>
                                <input type="number" id="refreshProbability" name="refresh_probability" 
                                       min="0" max="1" step="0.01" value="1.0">
                            </div>
                        </div>
                    </div>

                    <!-- 显示配置 -->
                    <div class="form-section">
                        <h4 class="section-title">显示配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="sortOrder">排序权重</label>
                                <input type="number" id="sortOrder" name="sort_order" 
                                       min="0" max="9999" value="0">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="isActive" name="is_active" checked>
                                    <span class="checkmark"></span>
                                    激活商品
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="displayConfig">显示配置 (JSON)</label>
                            <textarea id="displayConfig" name="display_config" rows="3"
                                      placeholder='{"show_discount": true, "highlight": false}'></textarea>
                            <small class="form-help">配置商品在商店中的显示方式</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="modal-hint">💡 提示：按ESC键可快速关闭弹窗</div>
                <div class="modal-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeItemForm()">取消</button>
                    <button type="submit" form="itemForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" onclick="closeDeleteModal()" title="关闭弹窗 (按ESC键也可关闭)">&times;</button>
            </div>
            <div class="modal-body">
                <p>确定要删除商品配置 "<span id="deleteItemName"></span>" 吗？</p>
                <p class="warning-text">此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <div class="modal-hint">💡 提示：按ESC键可快速关闭弹窗</div>
                <div class="modal-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/item-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
