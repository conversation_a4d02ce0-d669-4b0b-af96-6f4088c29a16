"""
商店系统使用示例
"""

import asyncio
import logging
from datetime import datetime
from shop_service import ShopService
from shop_models import LimitType

logger = logging.getLogger(__name__)


class ShopSystemExample:
    """商店系统使用示例"""
    
    def __init__(self):
        self.shop_service = ShopService()
        
        # 模拟外部服务
        self.setup_mock_services()
    
    def setup_mock_services(self):
        """设置模拟的外部服务"""
        
        class MockCurrencyService:
            """模拟货币服务"""
            
            def __init__(self):
                # 模拟玩家货币数据
                self.player_currencies = {
                    "player123": {
                        "gold": 10000,
                        "diamond": 500,
                        "arena_coin": 200,
                        "guild_coin": 300
                    }
                }
            
            async def check_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
                """检查货币是否足够"""
                player_currency = self.player_currencies.get(player_id, {})
                current_amount = player_currency.get(currency_type, 0)
                return current_amount >= amount
            
            async def deduct_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
                """扣除货币"""
                if await self.check_currency(player_id, currency_type, amount):
                    self.player_currencies[player_id][currency_type] -= amount
                    logger.info(f"扣除货币: {player_id} -{amount} {currency_type}")
                    return True
                return False
            
            async def add_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
                """增加货币（回滚用）"""
                if player_id not in self.player_currencies:
                    self.player_currencies[player_id] = {}
                
                if currency_type not in self.player_currencies[player_id]:
                    self.player_currencies[player_id][currency_type] = 0
                
                self.player_currencies[player_id][currency_type] += amount
                logger.info(f"增加货币: {player_id} +{amount} {currency_type}")
                return True
        
        class MockItemService:
            """模拟道具服务"""
            
            def __init__(self):
                self.item_counter = 1000
                self.player_items = {}
            
            async def create_item_instance(self, template_id: str, quality: int = None) -> str:
                """创建道具实例"""
                instance_id = f"item_instance_{self.item_counter}"
                self.item_counter += 1
                logger.info(f"创建道具实例: {instance_id} (模板: {template_id})")
                return instance_id
            
            async def delete_item_instance(self, instance_id: str) -> bool:
                """删除道具实例（回滚用）"""
                logger.info(f"删除道具实例: {instance_id}")
                return True
            
            async def add_items_to_player(self, player_id: str, item_instances: list) -> bool:
                """发放道具给玩家"""
                if player_id not in self.player_items:
                    self.player_items[player_id] = []
                
                self.player_items[player_id].extend(item_instances)
                logger.info(f"发放道具给玩家: {player_id} 获得 {len(item_instances)} 个道具")
                return True
            
            async def remove_items_from_player(self, player_id: str, item_instances: list) -> bool:
                """从玩家移除道具（回滚用）"""
                if player_id in self.player_items:
                    for item in item_instances:
                        if item in self.player_items[player_id]:
                            self.player_items[player_id].remove(item)
                logger.info(f"从玩家移除道具: {player_id} 移除 {len(item_instances)} 个道具")
                return True
        
        class MockPlayerService:
            """模拟玩家服务"""
            
            def __init__(self):
                self.player_data = {
                    "player123": {
                        "level": 25,
                        "vip_level": 3,
                        "guild_level": 2
                    }
                }
            
            async def get_player_level(self, player_id: str) -> int:
                """获取玩家等级"""
                return self.player_data.get(player_id, {}).get("level", 1)
            
            async def get_vip_level(self, player_id: str) -> int:
                """获取VIP等级"""
                return self.player_data.get(player_id, {}).get("vip_level", 0)
            
            async def get_guild_level(self, player_id: str) -> int:
                """获取公会等级"""
                return self.player_data.get(player_id, {}).get("guild_level", 0)
        
        # 设置外部服务
        currency_service = MockCurrencyService()
        item_service = MockItemService()
        player_service = MockPlayerService()
        
        self.shop_service.set_external_services(currency_service, item_service, player_service)
        
        # 保存引用以便查看数据
        self.currency_service = currency_service
        self.item_service = item_service
        self.player_service = player_service
    
    async def run_examples(self):
        """运行示例"""
        try:
            print("=" * 50)
            print("商店系统使用示例")
            print("=" * 50)
            
            # 1. 获取商店列表
            await self.example_get_shops()
            
            # 2. 获取商店商品
            await self.example_get_shop_items()
            
            # 3. 获取商品详情
            await self.example_get_item_detail()
            
            # 4. 预览购买
            await self.example_preview_purchase()
            
            # 5. 购买商品
            await self.example_purchase_item()
            
            # 6. 查看购买历史
            await self.example_get_purchase_history()
            
            # 7. 查看限购状态
            await self.example_get_limits()
            
            # 8. 重置限购
            await self.example_reset_limits()
            
            # 9. 查看玩家数据
            await self.example_show_player_data()
            
        except Exception as e:
            logger.error(f"运行示例时发生错误: {str(e)}")
    
    async def example_get_shops(self):
        """示例：获取商店列表"""
        print("\n1. 获取商店列表")
        print("-" * 30)
        
        shops = await self.shop_service.get_available_shops("player123")
        
        for shop in shops:
            print(f"商店: {shop.shop_name} ({shop.shop_type})")
            print(f"  ID: {shop.shop_id}")
            print(f"  描述: {shop.description}")
            print(f"  访问条件: {shop.access_conditions}")
            print()
    
    async def example_get_shop_items(self):
        """示例：获取商店商品"""
        print("\n2. 获取商店商品")
        print("-" * 30)
        
        # 获取第一个商店的商品
        shops = await self.shop_service.get_available_shops("player123")
        if shops:
            shop = shops[0]
            items = await self.shop_service.get_shop_items(shop.shop_id, "player123")
            
            print(f"商店 {shop.shop_name} 的商品:")
            for item in items:
                print(f"  商品: {item.item_template_id}")
                print(f"    配置ID: {item.config_id}")
                print(f"    数量: {item.item_quantity}")
                print(f"    价格配置: {item.price_config}")
                print(f"    限购: {item.purchase_limit}")
                print()
    
    async def example_get_item_detail(self):
        """示例：获取商品详情"""
        print("\n3. 获取商品详情")
        print("-" * 30)
        
        # 获取第一个商品的详情
        shops = await self.shop_service.get_available_shops("player123")
        if shops:
            items = await self.shop_service.get_shop_items(shops[0].shop_id, "player123")
            if items:
                item = items[0]
                detail = await self.shop_service.get_item_detail(item.config_id, "player123")
                
                if detail:
                    print(f"商品详情: {item.item_template_id}")
                    print(f"  原价: {detail['price_info'].original_price}")
                    print(f"  现价: {detail['price_info'].final_price}")
                    print(f"  货币类型: {detail['price_info'].currency_type}")
                    print(f"  折扣金额: {detail['price_info'].discount_amount}")
                    print(f"  可购买: {detail['can_purchase']}")
                    print(f"  限购剩余: {detail['limit_status'].remaining}")
                    print()
    
    async def example_preview_purchase(self):
        """示例：预览购买"""
        print("\n4. 预览购买")
        print("-" * 30)
        
        # 预览购买第一个商品
        shops = await self.shop_service.get_available_shops("player123")
        if shops:
            items = await self.shop_service.get_shop_items(shops[0].shop_id, "player123")
            if items:
                item = items[0]
                preview = await self.shop_service.preview_purchase("player123", item.config_id, 2)
                
                print(f"预览购买: {item.item_template_id} x2")
                print(f"  结果: {preview}")
                print()
    
    async def example_purchase_item(self):
        """示例：购买商品"""
        print("\n5. 购买商品")
        print("-" * 30)
        
        # 购买第一个商品
        shops = await self.shop_service.get_available_shops("player123")
        if shops:
            items = await self.shop_service.get_shop_items(shops[0].shop_id, "player123")
            if items:
                item = items[0]
                
                print(f"购买前货币: {self.currency_service.player_currencies['player123']}")
                
                result = await self.shop_service.purchase_item(
                    "player123", 
                    item.config_id, 
                    1,
                    {"source": "example"}
                )
                
                print(f"购买结果: {result.success}")
                if result.success:
                    print(f"  购买ID: {result.purchase_id}")
                    print(f"  获得道具: {result.items}")
                    print(f"  购买信息: {result.data}")
                else:
                    print(f"  错误: {result.error}")
                
                print(f"购买后货币: {self.currency_service.player_currencies['player123']}")
                print()
    
    async def example_get_purchase_history(self):
        """示例：获取购买历史"""
        print("\n6. 获取购买历史")
        print("-" * 30)
        
        history = await self.shop_service.get_purchase_history("player123", 10, 0)
        
        print(f"购买历史 (共 {len(history)} 条):")
        for purchase in history:
            print(f"  {purchase['purchase_time']}: {purchase['item_template_id']} x{purchase['quantity']}")
            print(f"    花费: {purchase['final_price']} {purchase['currency_type']}")
            print()
    
    async def example_get_limits(self):
        """示例：获取限购状态"""
        print("\n7. 获取限购状态")
        print("-" * 30)
        
        limits = await self.shop_service.get_player_limits("player123")
        
        print("限购状态:")
        for limit_type, status in limits.items():
            if isinstance(status, dict) and status.get("counters"):
                print(f"  {limit_type}: {len(status['counters'])} 个商品有限购")
                for item_id, count in status["counters"].items():
                    print(f"    {item_id}: 已购买 {count}")
            print()
    
    async def example_reset_limits(self):
        """示例：重置限购"""
        print("\n8. 重置限购")
        print("-" * 30)
        
        # 重置每日限购
        success = await self.shop_service.reset_limits(LimitType.DAILY)
        print(f"重置每日限购: {'成功' if success else '失败'}")
        
        # 再次查看限购状态
        limits = await self.shop_service.get_player_limits("player123")
        print(f"重置后限购状态: {limits.get('daily', {}).get('total_items', 0)} 个商品")
        print()
    
    async def example_show_player_data(self):
        """示例：显示玩家数据"""
        print("\n9. 玩家数据总览")
        print("-" * 30)
        
        print("货币:")
        for currency, amount in self.currency_service.player_currencies["player123"].items():
            print(f"  {currency}: {amount}")
        
        print("\n道具:")
        items = self.item_service.player_items.get("player123", [])
        print(f"  总计: {len(items)} 个道具")
        for item in items:
            print(f"    {item}")
        
        print("\n玩家信息:")
        player_data = self.player_service.player_data["player123"]
        for key, value in player_data.items():
            print(f"  {key}: {value}")


async def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    example = ShopSystemExample()
    await example.run_examples()


if __name__ == "__main__":
    asyncio.run(main())
