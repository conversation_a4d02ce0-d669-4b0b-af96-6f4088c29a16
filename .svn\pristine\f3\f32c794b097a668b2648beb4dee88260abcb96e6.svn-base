import os
import logging
import traceback
import glob
import time
from logging.handlers import TimedRotatingFileHandler, RotatingFileHandler
import colorlog

def _cleanup_old_log_files(log_dir: str, current_pid: int, max_age_days: int = 7):
    """
    清理旧的日志文件

    Args:
        log_dir: 日志目录
        current_pid: 当前进程ID
        max_age_days: 最大保留天数
    """
    try:
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600

        # 查找所有日志文件
        log_pattern = os.path.join(log_dir, "*.log*")
        log_files = glob.glob(log_pattern)

        cleaned_count = 0
        for log_file in log_files:
            try:
                # 跳过当前进程的日志文件
                if f"pid-{current_pid}" in log_file:
                    continue

                # 检查文件年龄
                file_age = current_time - os.path.getmtime(log_file)
                if file_age > max_age_seconds:
                    os.remove(log_file)
                    cleaned_count += 1

            except Exception as e:
                print(f"清理日志文件失败: {log_file}, 错误: {str(e)}")

        if cleaned_count > 0:
            print(f"清理了 {cleaned_count} 个旧日志文件")

    except Exception as e:
        print(f"日志文件清理过程失败: {str(e)}")

def setup_logger(module_name=None):
    """
    设置日志系统，支持按进程ID分离日志文件
    
    Args:
        module_name: 模块名称，如果不提供则使用调用者的模块名
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 延迟导入config以避免循环导入
    from config import config
    
    # 获取日志配置
    log_config = config.get_log_config()

    # 确保日志目录存在
    log_dir = os.path.dirname(log_config["file"])
    os.makedirs(log_dir, exist_ok=True)

    # 获取进程ID并在日志文件名中包含进程ID
    pid = os.getpid()
    base_log_file = log_config["file"]
    log_file_name, log_file_ext = os.path.splitext(base_log_file)
    process_log_file = f"{log_file_name}.pid-{pid}{log_file_ext}"
    log_file_path = os.path.abspath(process_log_file)
    print(f"进程 {pid} 日志文件将写入: {log_file_path}")

    try:
        # 清除任何现有的根记录器处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        
        # 创建根记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_config["level"]))
        
        # 添加文件处理器 - 使用大小轮转而不是时间轮转
        file_handler = RotatingFileHandler(
            log_file_path,
            maxBytes=10*1024*1024,  # 10MB per file
            backupCount=log_config.get("backup_count", 5),
            encoding="utf-8"
        )
        file_handler.setFormatter(logging.Formatter(log_config["format"]))
        root_logger.addHandler(file_handler)

        # 清理旧的日志文件
        _cleanup_old_log_files(log_dir, pid)
        
        # 添加控制台处理器
        handler = colorlog.StreamHandler()
        handler.setFormatter(colorlog.ColoredFormatter(
            '%(log_color)s%(levelname)s:%(name)s:%(message)s',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        ))
        root_logger.addHandler(handler)       

        # 获取应用记录器
        logger = logging.getLogger(module_name)
        logger.info(f"日志系统初始化成功 (进程 ID: {pid})")
        
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {str(e)}")
        # 降级为仅控制台日志
        logging.basicConfig(
            level=logging.INFO,  # 默认使用INFO级别
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        logger = logging.getLogger(module_name)
        logger.error(f"日志文件配置失败，降级为仅控制台输出: {str(e)}")
        return logger 