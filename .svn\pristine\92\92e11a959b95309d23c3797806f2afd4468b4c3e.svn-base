
(hj) C:\Users\<USER>\Desktop>if defined _OLD_VIRTUAL_PYTHONPATH (set "PYTHONPATH=" )  else (set "_OLD_VIRTUAL_PYTHONPATH=" )

(hj) D:\python_evns\game_server>python main.py
进程 89196 日志文件将写入: D:\python_evns\game_server\logs\server.pid-89196.log
2025-07-20 14:30:24,378 - __main__ - INFO - 日志系统初始化成功 (进程 ID: 89196)
2025-07-20 14:30:24,378 - __main__ - INFO - 启动游戏服务器: host=0.0.0.0, port=8000, workers=4, debug=True
[32mINFO[0m:     Uvicorn running on [1mhttp://0.0.0.0:8000[0m (Press CTRL+C to quit)
[32mINFO[0m:     Started parent process [[36m[1m89196[0m]
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:24,768 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:24,777 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:24,802 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 85340)
2025-07-20 14:30:24,803 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:26,040 - game_database - INFO - 日志系统初始化成功 (进程 ID: 85348)
2025-07-20 14:30:26,041 - game_database - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:26,044 - game_database - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:26,050 - auth - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:26,051 - auth - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:26,053 - auth - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:26,063 - game_database - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:26,071 - auth - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:28,441 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:28,442 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:28,446 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,451 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 87448)
2025-07-20 14:30:28,451 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: c45fefd4)
2025-07-20 14:30:28,452 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,453 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 87448)
2025-07-20 14:30:28,454 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,457 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,461 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 85348)
2025-07-20 14:30:28,461 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9fd4c55b)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,463 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,464 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,468 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,470 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,472 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 85340)
2025-07-20 14:30:28,472 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,475 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,478 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 90324)
2025-07-20 14:30:28,478 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: d72bdf5f)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,480 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 90324)
2025-07-20 14:30:28,480 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 85340)
2025-07-20 14:30:28,483 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 769e802e)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,484 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:28,498 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 87448)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,500 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 87448)
2025-07-20 14:30:28,500 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,501 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 87448)
2025-07-20 14:30:28,502 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,503 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 85348)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,507 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,508 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,509 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 90324)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,514 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,515 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,517 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 85340)
进程 85348 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85348.log
2025-07-20 14:30:28,569 - game_server - INFO - 日志系统初始化成功 (进程 ID: 85348)
2025-07-20 14:30:28,570 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-07-20 14:30:28,570 - msgManager - INFO - Monster handlers registered
2025-07-20 14:30:28,570 - general_service_distributed - ERROR - Redis客户端未初始化，武将系统可能无法正常工作
进程 87448 日志文件将写入: D:\python_evns\game_server\logs\server.pid-87448.log
2025-07-20 14:30:28,571 - msgManager - INFO - 武将相关消息处理器注册完成
2025-07-20 14:30:28,572 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: e86b8009)
2025-07-20 14:30:28,572 - game_server - INFO - 日志系统初始化成功 (进程 ID: 87448)
2025-07-20 14:30:28,573 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-07-20 14:30:28,573 - msgManager - INFO - Monster handlers registered
2025-07-20 14:30:28,573 - general_service_distributed - ERROR - Redis客户端未初始化，武将系统可能无法正常工作
进程 90324 日志文件将写入: D:\python_evns\game_server\logs\server.pid-90324.log
2025-07-20 14:30:28,574 - msgManager - INFO - 武将相关消息处理器注册完成
2025-07-20 14:30:28,575 - game_server - INFO - 日志系统初始化成功 (进程 ID: 90324)
2025-07-20 14:30:28,575 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 542d1ab0)
[32mINFO[0m:     Started server process [[36m85348[0m]
[32mINFO[0m:     Waiting for application startup.
2025-07-20 14:30:28,576 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-07-20 14:30:28,577 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 85348)
2025-07-20 14:30:28,577 - msgManager - INFO - Monster handlers registered
2025-07-20 14:30:28,577 - game_database - INFO - Worker 85348: 开始初始化数据库连接 (DatabaseManager id: 1977247746432)
2025-07-20 14:30:28,578 - general_service_distributed - ERROR - Redis客户端未初始化，武将系统可能无法正常工作
2025-07-20 14:30:28,578 - game_database - INFO - Worker 85348: 正在连接到Redis: *************:6379, 连接池大小: 120
2025-07-20 14:30:28,578 - msgManager - INFO - 武将相关消息处理器注册完成
2025-07-20 14:30:28,579 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 214c779f)
[32mINFO[0m:     Started server process [[36m87448[0m]
[32mINFO[0m:     Waiting for application startup.
2025-07-20 14:30:28,580 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 87448)
2025-07-20 14:30:28,580 - game_database - INFO - Worker 87448: 开始初始化数据库连接 (DatabaseManager id: 2778995575168)
2025-07-20 14:30:28,581 - game_database - INFO - Worker 87448: 正在连接到Redis: *************:6379, 连接池大小: 120
[32mINFO[0m:     Started server process [[36m90324[0m]
[32mINFO[0m:     Waiting for application startup.
2025-07-20 14:30:28,583 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 90324)
2025-07-20 14:30:28,583 - game_database - INFO - Worker 90324: 开始初始化数据库连接 (DatabaseManager id: 2256684157312)
进程 85340 日志文件将写入: D:\python_evns\game_server\logs\server.pid-85340.log
2025-07-20 14:30:28,583 - game_database - INFO - Worker 90324: 正在连接到Redis: *************:6379, 连接池大小: 120
2025-07-20 14:30:28,584 - game_server - INFO - 日志系统初始化成功 (进程 ID: 85340)
2025-07-20 14:30:28,585 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-07-20 14:30:28,585 - msgManager - INFO - Monster handlers registered
2025-07-20 14:30:28,585 - general_service_distributed - ERROR - Redis客户端未初始化，武将系统可能无法正常工作
2025-07-20 14:30:28,586 - msgManager - INFO - 武将相关消息处理器注册完成
2025-07-20 14:30:28,586 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9a76a1ba)
[32mINFO[0m:     Started server process [[36m85340[0m]
[32mINFO[0m:     Waiting for application startup.
2025-07-20 14:30:28,594 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 85340)
2025-07-20 14:30:28,594 - game_database - INFO - Worker 85340: 开始初始化数据库连接 (DatabaseManager id: 2393928403328)
2025-07-20 14:30:28,595 - game_database - INFO - Worker 85340: 正在连接到Redis: *************:6379, 连接池大小: 120
2025-07-20 14:30:28,739 - game_database - INFO - Worker 85348: Redis连接成功: *************:6379
2025-07-20 14:30:28,739 - game_database - INFO - Worker 85348: 缓存管理器初始化成功
2025-07-20 14:30:28,740 - game_database - INFO - Worker 85348: 正在连接到MongoDB: *************:27017
2025-07-20 14:30:28,743 - game_database - INFO - Worker 87448: Redis连接成功: *************:6379
2025-07-20 14:30:28,744 - game_database - INFO - Worker 87448: 缓存管理器初始化成功
2025-07-20 14:30:28,744 - game_database - INFO - Worker 87448: 正在连接到MongoDB: *************:27017
2025-07-20 14:30:28,764 - game_database - INFO - Worker 85340: Redis连接成功: *************:6379
2025-07-20 14:30:28,764 - game_database - INFO - Worker 85340: 缓存管理器初始化成功
2025-07-20 14:30:28,765 - game_database - INFO - Worker 85340: 正在连接到MongoDB: *************:27017
2025-07-20 14:30:28,767 - game_database - INFO - Worker 90324: Redis连接成功: *************:6379
2025-07-20 14:30:28,767 - game_database - INFO - Worker 90324: 缓存管理器初始化成功
2025-07-20 14:30:28,767 - game_database - INFO - Worker 90324: 正在连接到MongoDB: *************:27017
2025-07-20 14:30:29,025 - game_database - INFO - Worker 85348: MongoDB连接成功: *************:27017
2025-07-20 14:30:29,045 - game_database - INFO - Worker 87448: MongoDB连接成功: *************:27017
2025-07-20 14:30:29,073 - game_database - INFO - Worker 90324: MongoDB连接成功: *************:27017
2025-07-20 14:30:29,093 - game_database - INFO - Worker 85340: MongoDB连接成功: *************:27017
2025-07-20 14:30:29,199 - game_database - INFO - Worker 85348: 数据库初始化完成，所有连接正常
2025-07-20 14:30:29,199 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-07-20 14:30:29,227 - game_database - INFO - Worker 87448: 数据库初始化完成，所有连接正常
2025-07-20 14:30:29,227 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-07-20 14:30:29,256 - game_database - INFO - Worker 90324: 数据库初始化完成，所有连接正常
2025-07-20 14:30:29,256 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-07-20 14:30:29,274 - game_database - INFO - Worker 85340: 数据库初始化完成，所有连接正常
2025-07-20 14:30:29,274 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-07-20 14:30:29,383 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-07-20 14:30:29,410 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-07-20 14:30:29,434 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-07-20 14:30:29,452 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-07-20 14:30:29,908 - ConnectionManager - INFO - 后台任务已启动 (Worker 85348)
2025-07-20 14:30:29,908 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 85348)
2025-07-20 14:30:29,916 - ConnectionManager - INFO - 后台任务已启动 (Worker 87448)
2025-07-20 14:30:29,916 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 87448)
2025-07-20 14:30:29,919 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:29,919 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:29,921 - game_server - INFO - 游戏配置加载完成 (Worker: 85348)
2025-07-20 14:30:29,922 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:30:29,927 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:29,927 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:29,928 - game_server - INFO - 游戏配置加载完成 (Worker: 87448)
2025-07-20 14:30:29,928 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:30:29,965 - ConnectionManager - INFO - 后台任务已启动 (Worker 90324)
2025-07-20 14:30:29,966 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 90324)
2025-07-20 14:30:29,967 - ConnectionManager - INFO - 后台任务已启动 (Worker 85340)
2025-07-20 14:30:29,967 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 85340)
2025-07-20 14:30:29,978 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:29,978 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:29,979 - game_server - INFO - 游戏配置加载完成 (Worker: 90324)
2025-07-20 14:30:29,979 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:30:29,982 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:29,983 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:29,983 - game_server - INFO - 游戏配置加载完成 (Worker: 85340)
2025-07-20 14:30:29,984 - ConnectionManager - INFO - 连接状态 (Worker 85340): 活跃连接数=0, 用户数=0
2025-07-20 14:30:31,959 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-07-20 14:30:31,960 - ConnectionManager - INFO - Redis连接池状态 (Worker 90324): 使用中=2, 可用=0, 总计=2
2025-07-20 14:30:31,960 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 90324): 2/2 (100.0%)
2025-07-20 14:30:31,961 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 90324): 连接中
2025-07-20 14:30:31,962 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 214c779f)
2025-07-20 14:30:31,962 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 214c779f)
2025-07-20 14:30:31,964 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-07-20 14:30:31,965 - game_server - INFO - Monster cooldown manager initialized (Worker: 90324)
2025-07-20 14:30:31,966 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-07-20 14:30:31,975 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:31,975 - config - INFO - 检测到游戏配置文件变更: monsters
2025-07-20 14:30:31,978 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:32,011 - ConnectionManager - INFO - Worker 90324 开始消费广播消息，消费者标签: ctag1.14e20fbd0cbe4476b118034c46523999
2025-07-20 14:30:32,012 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-07-20 14:30:32,012 - game_server - INFO - 初始化完成 (Worker: 90324)
[32mINFO[0m:     Application startup complete.
2025-07-20 14:30:32,055 - ConnectionManager - INFO - Worker 90324 开始消费个人消息，消费者标签: ctag1.98ad9d529673499e9a6661fc4e0f7c5e
2025-07-20 14:30:32,091 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 90324)
2025-07-20 14:30:32,108 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-07-20 14:30:32,109 - ConnectionManager - INFO - Redis连接池状态 (Worker 85348): 使用中=2, 可用=0, 总计=2
2025-07-20 14:30:32,109 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 85348): 2/2 (100.0%)
2025-07-20 14:30:32,110 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 85348): 连接中
2025-07-20 14:30:32,111 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: e86b8009)
2025-07-20 14:30:32,111 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: e86b8009)
2025-07-20 14:30:32,112 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-07-20 14:30:32,112 - game_server - INFO - Monster cooldown manager initialized (Worker: 85348)
2025-07-20 14:30:32,113 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-07-20 14:30:32,120 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:32,120 - config - INFO - 检测到游戏配置文件变更: monsters
2025-07-20 14:30:32,121 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:32,154 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-07-20 14:30:32,155 - ConnectionManager - INFO - Redis连接池状态 (Worker 87448): 使用中=2, 可用=0, 总计=2
2025-07-20 14:30:32,156 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 87448): 2/2 (100.0%)
2025-07-20 14:30:32,156 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 87448): 连接中
2025-07-20 14:30:32,157 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 542d1ab0)
2025-07-20 14:30:32,157 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 542d1ab0)
2025-07-20 14:30:32,157 - ConnectionManager - INFO - Worker 85348 开始消费广播消息，消费者标签: ctag1.01389d8c02f14364be45f8fcd053fd1a
2025-07-20 14:30:32,157 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-07-20 14:30:32,158 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-07-20 14:30:32,158 - game_server - INFO - Monster cooldown manager initialized (Worker: 87448)
2025-07-20 14:30:32,159 - game_server - INFO - 初始化完成 (Worker: 85348)
[32mINFO[0m:     Application startup complete.
2025-07-20 14:30:32,160 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-07-20 14:30:32,167 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:32,167 - config - INFO - 检测到游戏配置文件变更: monsters
2025-07-20 14:30:32,168 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:32,194 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-07-20 14:30:32,194 - ConnectionManager - INFO - Redis连接池状态 (Worker 85340): 使用中=2, 可用=0, 总计=2
2025-07-20 14:30:32,195 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 85340): 2/2 (100.0%)
2025-07-20 14:30:32,195 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 85340): 连接中
2025-07-20 14:30:32,196 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 9a76a1ba)
2025-07-20 14:30:32,196 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9a76a1ba)
2025-07-20 14:30:32,197 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-07-20 14:30:32,197 - game_server - INFO - Monster cooldown manager initialized (Worker: 85340)
2025-07-20 14:30:32,198 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-07-20 14:30:32,202 - ConnectionManager - INFO - Worker 85348 开始消费个人消息，消费者标签: ctag1.509659cdf4ef44bc9fb70103c60a642f
2025-07-20 14:30:32,205 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-07-20 14:30:32,206 - config - INFO - 检测到游戏配置文件变更: monsters
2025-07-20 14:30:32,206 - config - ERROR - 游戏配置文件格式错误: config/game/monsters.json
2025-07-20 14:30:32,216 - ConnectionManager - INFO - Worker 87448 开始消费广播消息，消费者标签: ctag1.71b20cd1d8ac41a486d7065154f51f88
2025-07-20 14:30:32,216 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-07-20 14:30:32,217 - game_server - INFO - 初始化完成 (Worker: 87448)
[32mINFO[0m:     Application startup complete.
2025-07-20 14:30:32,239 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 85348)
2025-07-20 14:30:32,240 - ConnectionManager - INFO - Worker 85340 开始消费广播消息，消费者标签: ctag1.4ede59ab55c64731891642829ae1b129
2025-07-20 14:30:32,242 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-07-20 14:30:32,242 - game_server - INFO - 初始化完成 (Worker: 85340)
[32mINFO[0m:     Application startup complete.
2025-07-20 14:30:32,262 - ConnectionManager - INFO - Worker 87448 开始消费个人消息，消费者标签: ctag1.cb147921260e442192e53e0057a687d9
2025-07-20 14:30:32,283 - ConnectionManager - INFO - Worker 85340 开始消费个人消息，消费者标签: ctag1.d938e02b058e4b64847015a05b03f16e
2025-07-20 14:30:32,305 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 87448)
2025-07-20 14:30:32,313 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 85340)
[TaskQueue] 正在监听队列: task_queue
[TaskQueue] 正在监听队列: task_queue
[TaskQueue] 正在监听队列: task_queue
[TaskQueue] 正在监听队列: task_queue
2025-07-20 14:30:59,930 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:30:59,930 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:30:59,992 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:30:59,992 - ConnectionManager - INFO - 连接状态 (Worker 85340): 活跃连接数=0, 用户数=0
2025-07-20 14:31:00,226 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:31:00,383 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:31:00,460 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:31:00,491 - ConnectionManager - INFO - 连接状态 (Worker 85340): 活跃连接数=0, 用户数=0
2025-07-20 14:31:05,081 - auth - INFO - 用户 42ggg22 登录成功，token: eyJhbGciOi...
[32mINFO[0m:     127.0.0.1:52134 - "[1mPOST /login HTTP/1.1[0m" [32m200 OK[0m
2025-07-20 14:31:05,136 - game_server - INFO - WebSocket 连接尝试，token: eyJhbGciOi..., worker: 85348
[32mINFO[0m:     ('127.0.0.1', 52136) - "WebSocket /ws/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0MmdnZzIyIiwiZXhwIjoxNzUzMDc5NDY1fQ.NMdXov0ljzuQw_Jtp51_tbrZlQJ4HusDABhxp40QBJ4" [accepted]
[32mINFO[0m:     connection open
2025-07-20 14:31:05,180 - ConnectionManager - INFO - 已发布用户登录通知到Redis (用户: 42ggg22, Worker: 85348)
2025-07-20 14:31:05,341 - game_database - INFO - 从缓存获取道具列表: [{'_id': '687a1c3c5fb0881db877726a', 'id': 'c38a38fd-80c9-4665-b155-571fba7b0148', 'defid': 81999, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-18T18:04:42.328000', 'updated_at': '2025-07-18T18:04:42.328000', 'quantity': 199}, {'_id': '6878960218ce174fa2a36e23', 'id': '9e933f15-3c65-49ed-9bd1-74bb9319aa3c', 'defid': 82000, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-17T14:19:46.815000', 'updated_at': '2025-07-17T14:19:46.815000', 'quantity': 482}, {'_id': '6873784110a4ca386237f9da', 'id': 'c50e95ad-8643-4e7e-94a0-a877c2d9384c', 'defid': 82001, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-13T17:11:29.424000', 'updated_at': '2025-07-17T14:10:37.222000', 'quantity': 1102}, {'_id': '6873784110a4ca386237f9db', 'id': '9d91a472-5619-4b7c-8f38-aa96e6f2f632', 'defid': 82002, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-13T17:11:29.424000', 'updated_at': '2025-07-13T17:11:29.424000', 'quantity': 10}, {'_id': '6873784110a4ca386237f9dc', 'id': '2a6b29c9-60c8-4b37-9d67-43c28aff1700', 'defid': 82003, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-13T17:11:29.424000', 'updated_at': '2025-07-13T17:11:29.424000', 'quantity': 100}, {'_id': '6873784110a4ca386237f9dd', 'id': 'f31dbfd5-ccac-4fd0-8870-6825fe21a8b5', 'defid': 82004, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-13T17:11:29.424000', 'updated_at': '2025-07-13T17:11:29.424000', 'quantity': 1000}, {'_id': '6873784110a4ca386237f9de', 'id': '15192639-69a4-4741-a4c5-cf3b66d9eda8', 'defid': 82005, 'owner': '42ggg22', 'type': <ItemType.ITEM: 'item'>, 'attributes': {}, 'created_at': '2025-07-13T17:11:29.424000', 'updated_at': '2025-07-13T17:11:29.424000', 'quantity': 10000}]
2025-07-20 14:31:05,382 - game_database - INFO - 从缓存获取装备列表: [{'_id': '68739d09e46e366248aaf42e', 'id': '6e77fe60-9e1e-49b2-89ca-062591fb1592', 'defid': 969, 'owner': '42ggg22', 'type': <ItemType.EQUIPMENT: 'equipment'>, 'attributes': {}, 'created_at': '2025-07-13T19:48:25.477000', 'updated_at': '2025-07-13T19:48:25.477000', 'level': 1}, {'_id': '687c503deefe7a74466fe95f', 'defid': 966, 'attributes': {'part': 'unknown', 'equipment_data': {'level': 1, 'star': 1, 'quality': 1, 'holes': 0, 'state': 1, 'locked': False, 'runes': [], 'rune_word_id': None, 'properties': {'main': {}, 'random': [], 'rune_word': [], 'power': [], 'enchant': []}}}, 'id': 'bd86e419-0ef5-4268-9bf5-4c641ce06231', 'owner': '42ggg22', 'type': <ItemType.EQUIPMENT: 'equipment'>, 'quantity': 1, 'created_at': '2025-07-20T10:11:09.018123', 'updated_at': '2025-07-20T10:11:09.041000'}]
2025-07-20 14:31:05,422 - game_database - INFO - 从缓存获取符文列表: [{'_id': '68737863d184e40f72a1d155', 'id': '8aae5c93-9ed4-4869-b91b-e12a8f54564f', 'defid': 40066, 'owner': '42ggg22', 'type': <ItemType.RUNE: 'rune'>, 'attributes': {}, 'created_at': '2025-07-13T17:12:02.990000', 'updated_at': '2025-07-13T17:12:02.990000', 'quantity': 2}]
2025-07-20 14:31:05,423 - ConnectionManager - INFO - 用户 42ggg22 连接成功 (Token: eyJhbGciOi, Worker: 85348)
2025-07-20 14:31:05,433 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 1752657309.6秒)
2025-07-20 14:31:29,941 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:31:29,941 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:31:30,002 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:31:30,533 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:31:30,684 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:31:30,762 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:31:30,887 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:31:31,971 - ConnectionManager - INFO - Redis连接池状态 (Worker 90324): 使用中=2, 可用=0, 总计=2
2025-07-20 14:31:31,973 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 90324): 2/2 (100.0%)
2025-07-20 14:31:31,973 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 90324): 连接中
2025-07-20 14:31:32,123 - ConnectionManager - INFO - Redis连接池状态 (Worker 85348): 使用中=2, 可用=1, 总计=3
2025-07-20 14:31:32,124 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 85348): 连接中
2025-07-20 14:31:32,170 - ConnectionManager - INFO - Redis连接池状态 (Worker 87448): 使用中=2, 可用=0, 总计=2
2025-07-20 14:31:32,171 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 87448): 2/2 (100.0%)
2025-07-20 14:31:32,171 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 87448): 连接中
2025-07-20 14:31:59,955 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:31:59,955 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:32:00,018 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:32:00,080 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:32:00,172 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:32:00,831 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:32:00,973 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:32:07,902 - general_service_distributed - ERROR - 抽卡失败: Traceback (most recent call last):
  File "D:\python_evns\game_server\general_service_distributed.py", line 287, in draw_general
    lock = await self._acquire_general_lock(player_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python_evns\game_server\general_service_distributed.py", line 49, in _acquire_general_lock
    raise Exception("Redis客户端未初始化，无法获取分布式锁")
Exception: Redis客户端未初始化，无法获取分布式锁

2025-07-20 14:32:29,971 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:32:29,971 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:32:30,034 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:32:30,097 - ConnectionManager - INFO - 连接状态 (Worker 90324): 活跃连接数=0, 用户数=0
2025-07-20 14:32:30,221 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=1, 用户数=1
2025-07-20 14:32:30,395 - ConnectionManager - INFO - 连接状态 (Worker 87448): 活跃连接数=0, 用户数=0
2025-07-20 14:32:30,457 - ConnectionManager - INFO - 连接状态 (Worker 85348): 活跃连接数=0, 用户数=0
2025-07-20 14:32:31,990 - ConnectionManager - INFO - Redis连接池状态 (Worker 90324): 使用中=2, 可用=0, 总计=2
2025-07-20 14:32:31,991 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 90324): 2/2 (100.0%)
2025-07-20 14:32:31,991 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 90324): 连接中
2025-07-20 14:32:32,130 - ConnectionManager - INFO - Redis连接池状态 (Worker 85348): 使用中=2, 可用=1, 总计=3
2025-07-20 14:32:32,130 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 85348): 连接中
2025-07-20 14:32:32,175 - ConnectionManager - INFO - Redis连接池状态 (Worker 87448): 使用中=2, 可用=0, 总计=2
2025-07-20 14:32:32,176 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 87448): 2/2 (100.0%)
2025-07-20 14:32:32,176 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 87448): 连接中
