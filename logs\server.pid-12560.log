2025-08-05 20:55:19,894 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:20,631 - models - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:20,665 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:21,094 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:21,110 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:21,128 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:21,147 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:21,164 - auth - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,877 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,924 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,947 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,959 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,972 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:23,972 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 363a23ab)
2025-08-05 20:55:23,983 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,016 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,030 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,038 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,046 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,128 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,141 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,150 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,160 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,169 - game_server - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:24,170 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 20:55:24,170 - msgManager - INFO - Monster handlers registered
2025-08-05 20:55:24,171 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 20:55:24,174 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 20:55:24,185 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 20:55:24,185 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 20:55:24,186 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 739719a8)
2025-08-05 20:55:24,241 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 20:55:24,289 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 20:55:24,290 - game_server - INFO - 模板引擎初始化成功
2025-08-05 20:55:24,292 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:55:24,293 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 12560)
2025-08-05 20:55:24,294 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 20:55:24,508 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:55:24,513 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 20:55:25,043 - ConnectionManager - INFO - 后台任务已启动 (Worker 12560)
2025-08-05 20:55:25,043 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 12560)
2025-08-05 20:55:25,051 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:55:25,051 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:55:25,052 - game_server - INFO - 游戏配置加载完成 (Worker: 12560)
2025-08-05 20:55:25,052 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:55:29,430 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:55:29,431 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=2, 可用=0, 总计=2
2025-08-05 20:55:29,431 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 12560): 2/2 (100.0%)
2025-08-05 20:55:29,432 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 20:55:29,476 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 739719a8)
2025-08-05 20:55:29,477 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 739719a8)
2025-08-05 20:55:29,484 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:29,486 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 20:55:29,487 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 20:55:29,494 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:55:29,494 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 20:55:29,495 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:55:29,496 - ConnectionManager - INFO - Worker 12560 开始消费广播消息，消费者标签: ctag1.1f8074f6d50742339d093061d13ce7ca
2025-08-05 20:55:29,543 - ConnectionManager - INFO - Worker 12560 开始消费个人消息，消费者标签: ctag1.cb9253a1786749998e3300d9d4879d1b
2025-08-05 20:55:29,627 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 12560)
2025-08-05 20:55:29,723 - distributed_lock - INFO - Worker 12560 成功获取锁: scheduler_initialization
2025-08-05 20:55:29,724 - game_server_scheduler_integration - INFO - Worker 12560 获得调度器初始化权限
2025-08-05 20:55:29,728 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 20:55:29,728 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 20:55:29,731 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 20:55:29,731 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 20:55:29,732 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 20:55:29,732 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 20:55:29,732 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 20:55:29,733 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 20:55:29,733 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 20:55:29,734 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 20:55:29,734 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 20:55:29,734 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 20:55:29,735 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 20:55:29,735 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 20:55:29,735 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 20:55:29,736 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 20:55:29,826 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 20:55:29,826 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 20:55:29,826 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 20:55:29,827 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 20:55:30,131 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 20:55:30,250 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 20:55:30,250 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 20:55:30,251 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 20:55:30,251 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 20:55:30,252 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 20:55:30,252 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:55:30,343 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 20:55:30,433 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:55:30,478 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:55:30,478 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 20:55:30,479 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 20:55:30,479 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 20:55:30,480 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 20:55:30,480 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 20:55:30,484 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,484 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 20:55:30,484 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 20:55:30,487 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,487 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 20:55:30,488 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 20:55:30,622 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,622 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 20:55:30,625 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 20:55:30,775 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,775 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 20:55:30,776 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 20:55:30,776 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,778 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 20:55:30,778 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 20:55:30,779 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:55:30,779 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 20:55:30,779 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 20:55:30,780 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 20:55:30,781 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,781 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,781 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,782 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,782 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,782 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:55:30,783 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 20:55:30,783 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 20:55:30,783 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 20:55:30,784 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 20:55:30,784 - game_server_scheduler_integration - INFO - Worker 12560 调度器初始化成功
2025-08-05 20:55:30,830 - game_server - INFO - 统一调度器初始化成功 (Worker: 12560)
2025-08-05 20:55:30,837 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:55:30,838 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 20:55:30,838 - game_server - INFO - 日志清理管理器已启动 (Worker: 12560)
2025-08-05 20:55:30,838 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:55:30,841 - game_server - INFO - Monster cooldown manager initialized (Worker: 12560)
2025-08-05 20:55:30,988 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:55:30,988 - game_server - INFO - 公会系统初始化成功 (Worker: 12560)
2025-08-05 20:55:30,989 - game_server - INFO - 邮件系统初始化成功 (Worker: 12560)
2025-08-05 20:55:30,991 - game_server - INFO - 商店系统初始化成功 (Worker: 12560)
2025-08-05 20:55:30,991 - game_server - INFO - 初始化完成 (Worker: 12560)
2025-08-05 20:55:40,777 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:55:50 CST)" (scheduled at 2025-08-05 20:55:40.775088+08:00)
2025-08-05 20:55:40,821 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:55:40,822 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:55:40,950 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:55:41,313 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:55:41,314 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:55:41,314 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:55:41,315 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:55:41,359 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:55:50 CST)" executed successfully
2025-08-05 20:55:50,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:00 CST)" (scheduled at 2025-08-05 20:55:50.775088+08:00)
2025-08-05 20:55:50,824 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:55:50,824 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:55:50,950 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:55:51,306 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:55:51,306 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:55:51,307 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:55:51,308 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:55:51,351 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:00 CST)" executed successfully
2025-08-05 20:56:09,764 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:56:09,767 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:56:30 CST)" was missed by 0:00:09.144468
2025-08-05 20:56:09,768 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:10 CST)" was missed by 0:00:08.993646
2025-08-05 20:56:10,789 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:20 CST)" (scheduled at 2025-08-05 20:56:10.775088+08:00)
2025-08-05 20:56:10,833 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:56:10,833 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:10,966 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:56:11,312 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:56:11,312 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:56:11,315 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:56:11,316 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:11,361 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:20 CST)" executed successfully
2025-08-05 20:56:20,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:30 CST)" (scheduled at 2025-08-05 20:56:20.775088+08:00)
2025-08-05 20:56:20,824 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:56:20,824 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:20,966 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:56:21,333 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:56:21,333 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:56:21,334 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:56:21,334 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:21,377 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:30 CST)" executed successfully
2025-08-05 20:56:29,440 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=2, 可用=2, 总计=4
2025-08-05 20:56:29,443 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 20:56:30,489 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:57:30 CST)" (scheduled at 2025-08-05 20:56:30.487539+08:00)
2025-08-05 20:56:30,534 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 20:56:30,534 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:30,535 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 20:56:30,536 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 20:56:30,629 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:57:00 CST)" (scheduled at 2025-08-05 20:56:30.622531+08:00)
2025-08-05 20:56:30,676 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:56:30,677 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:30,786 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:40 CST)" (scheduled at 2025-08-05 20:56:30.775088+08:00)
2025-08-05 20:56:30,809 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 20:56:30,877 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:56:30,878 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:30,880 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:56:31,015 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:56:31,156 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:56:31,157 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 20:56:31,158 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:56:31,159 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:56:31,159 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:31,202 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:57:00 CST)" executed successfully
2025-08-05 20:56:31,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:56:31,384 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:56:31,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:56:31,398 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:31,449 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:40 CST)" executed successfully
2025-08-05 20:56:31,497 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:56:31,499 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:56:31,505 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 0
2025-08-05 20:56:31,505 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.97秒
2025-08-05 20:56:31,506 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:56:31,506 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:31,549 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:57:30 CST)" executed successfully
2025-08-05 20:56:36,679 - distributed_lock - INFO - Worker 12560 成功获取锁: shop:item:create:shop_6f5c8d6413fe:82026
2025-08-05 20:56:36,680 - shop_service - INFO - Worker 12560 获取商品配置创建锁: shop_6f5c8d6413fe:82026
2025-08-05 20:56:37,437 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:56:37,485 - shop_database_manager - INFO - 商品配置创建成功: shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 20:56:37,577 - shop_service - INFO - Worker 12560 商品配置创建成功: shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 20:56:38,717 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:56:39,779 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:56:40,686 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:56:40,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:50 CST)" (scheduled at 2025-08-05 20:56:40.775088+08:00)
2025-08-05 20:56:40,826 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:56:40,827 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:40,969 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:56:41,330 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:56:41,331 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:56:41,331 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:56:41,333 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:41,378 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:56:50 CST)" executed successfully
2025-08-05 20:56:50,778 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:00 CST)" (scheduled at 2025-08-05 20:56:50.775088+08:00)
2025-08-05 20:56:50,824 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:56:50,824 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:56:50,957 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:56:51,359 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:56:51,360 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.40秒
2025-08-05 20:56:51,360 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:56:51,361 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:56:51,407 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:00 CST)" executed successfully
2025-08-05 20:57:00,069 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:57:00,627 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:57:30 CST)" (scheduled at 2025-08-05 20:57:00.622531+08:00)
2025-08-05 20:57:00,675 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:57:00,676 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:00,782 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:10 CST)" (scheduled at 2025-08-05 20:57:00.775088+08:00)
2025-08-05 20:57:00,812 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 20:57:00,960 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:00,960 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:01,089 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:01,166 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:57:01,167 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 20:57:01,169 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:57:01,170 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:57:01,171 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:01,217 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:57:30 CST)" executed successfully
2025-08-05 20:57:01,429 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:01,430 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:57:01,430 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:01,431 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:01,473 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:10 CST)" executed successfully
2025-08-05 20:57:05,207 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:57:08,031 - shop_api - INFO - [ShopAPI] 获取商店详情: shop_d346cca3b749
2025-08-05 20:57:09,781 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:57:10,786 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:20 CST)" (scheduled at 2025-08-05 20:57:10.775088+08:00)
2025-08-05 20:57:10,830 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:10,830 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:10,958 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:11,299 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:11,300 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:57:11,300 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:11,301 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:11,344 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:20 CST)" executed successfully
2025-08-05 20:57:20,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:30 CST)" (scheduled at 2025-08-05 20:57:20.775088+08:00)
2025-08-05 20:57:20,828 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:20,828 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:20,953 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:21,294 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:21,295 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:57:21,295 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:21,296 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:21,342 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:30 CST)" executed successfully
2025-08-05 20:57:29,451 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=2, 可用=3, 总计=5
2025-08-05 20:57:29,451 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 20:57:30,270 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:57:30,488 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:58:30 CST)" (scheduled at 2025-08-05 20:57:30.487539+08:00)
2025-08-05 20:57:30,531 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 20:57:30,531 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:30,531 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 20:57:30,629 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:58:00 CST)" (scheduled at 2025-08-05 20:57:30.622531+08:00)
2025-08-05 20:57:30,671 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:57:30,671 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:30,765 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:57:30,766 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:57:30,783 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:40 CST)" (scheduled at 2025-08-05 20:57:30.775088+08:00)
2025-08-05 20:57:30,801 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 20:57:30,830 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:30,831 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:30,983 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:31,172 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:57:31,172 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 20:57:31,173 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 20:57:31,175 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:57:31,176 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:31,219 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:58:00 CST)" executed successfully
2025-08-05 20:57:31,401 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:31,401 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.42秒
2025-08-05 20:57:31,402 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:31,402 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:31,451 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:40 CST)" executed successfully
2025-08-05 20:57:31,790 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 0
2025-08-05 20:57:31,790 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.26秒
2025-08-05 20:57:31,792 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:57:31,793 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:31,841 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:58:30 CST)" executed successfully
2025-08-05 20:57:39,786 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:57:40,786 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:50 CST)" (scheduled at 2025-08-05 20:57:40.775088+08:00)
2025-08-05 20:57:40,958 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:40,960 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:41,262 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:46,856 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:46,856 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 5.59秒
2025-08-05 20:57:46,857 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:46,857 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:47,259 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:57:50 CST)" executed successfully
2025-08-05 20:57:50,780 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:58:00 CST)" (scheduled at 2025-08-05 20:57:50.775088+08:00)
2025-08-05 20:57:50,832 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:57:50,836 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:57:50,968 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:57:51,320 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:57:51,323 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:57:51,341 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:57:51,348 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:57:51,395 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:58:00 CST)" executed successfully
2025-08-05 20:58:00,468 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:58:00,624 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:58:30 CST)" (scheduled at 2025-08-05 20:58:00.622531+08:00)
2025-08-05 20:58:00,758 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:58:00,758 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:58:00,779 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:58:10 CST)" (scheduled at 2025-08-05 20:58:00.775088+08:00)
2025-08-05 20:58:00,958 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:58:00,958 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:58:01,358 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 20:58:01,556 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 20:58:01,557 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 20:58:01,557 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 20:58:02,461 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:58:02,461 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 20:58:02,462 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 1.10秒
2025-08-05 20:58:02,462 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:58:02,462 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:58:02,804 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:58:30 CST)" executed successfully
2025-08-05 20:58:03,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:58:04,077 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:58:04,077 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:58:04,078 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:58:04,078 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:58:04,125 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:58:10 CST)" executed successfully
2025-08-05 20:59:12,016 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:59:12,016 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=2, 可用=3, 总计=5
2025-08-05 20:59:12,017 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 20:59:12,018 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:20 CST)" was missed by 0:00:01.243056
2025-08-05 20:59:12,018 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:59:30 CST)" was missed by 0:00:41.530847
2025-08-05 20:59:12,018 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:59:30 CST)" was missed by 0:00:11.396270
2025-08-05 20:59:12,118 - CacheInvalidationManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:59:12,162 - CacheInvalidationManager - INFO - 缓存失效通知管理器初始化完成 (Worker 12560)
2025-08-05 20:59:12,688 - auth - INFO - 用户 dsadjdj23 登录成功，token: eyJhbGciOi...
2025-08-05 20:59:12,761 - game_server - INFO - WebSocket 连接尝试，token: eyJhbGciOi..., worker: 12560
2025-08-05 20:59:12,854 - ConnectionManager - INFO - 已发布用户登录通知到Redis (用户: dsadjdj23, Worker: 12560)
2025-08-05 20:59:13,289 - game_manager - INFO - 开始初始化游戏管理器 (Worker: 12560)
2025-08-05 20:59:13,289 - game_manager - INFO - 创建PlayerSessionManager...
2025-08-05 20:59:13,290 - game_manager - INFO - 创建GameNotificationManager...
2025-08-05 20:59:13,290 - game_manager - INFO - 获取UserCacheManager...
2025-08-05 20:59:13,291 - game_manager - INFO - 获取ItemCacheManager...
2025-08-05 20:59:13,333 - game_manager - INFO - 获取GuildServiceDistributed...
2025-08-05 20:59:13,335 - game_manager - INFO - 获取ConnectionManager...
2025-08-05 20:59:13,341 - game_manager - INFO - 游戏管理器初始化完成 (Worker: 12560)
2025-08-05 20:59:13,342 - game_manager - INFO - 处理玩家登录: dsadjdj23 (Worker: 12560)
2025-08-05 20:59:13,553 - player_session_manager - INFO - 创建玩家会话: dsadjdj23 (Worker: 12560)
2025-08-05 20:59:13,869 - guild_cache_manager - INFO - GuildCacheManager实例已创建
2025-08-05 20:59:14,039 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 1754353522.0秒)
2025-08-05 20:59:14,626 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 20:59:14,672 - mail_database_manager - INFO - 数据库中共有 12 个未过期的邮件模板
2025-08-05 20:59:14,721 - mail_database_manager - INFO - 用户 dsadjdj23 已处理的模板ID: {'template_7fd5d6e6b55c4142', 'template_575fc5088c474399', 'template_46f1fcab221b4a38', 'template_c999a774d9014253', 'template_e827e75c8beb4d5d', 'template_6e47793807b84b13', 'template_c466e0ddaa8c44f2', 'template_0acd29605c994701', 'template_14981f8399fe4572', 'template_0738ee4b4a2d4983', 'template_b0a095478f034b26', 'template_62e4594c1bf04b4b'}
2025-08-05 20:59:14,767 - mail_database_manager - INFO - 模板 template_6e47793807b84b13 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,767 - mail_database_manager - INFO - 模板 template_c466e0ddaa8c44f2 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,768 - mail_database_manager - INFO - 模板 template_c999a774d9014253 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,768 - mail_database_manager - INFO - 模板 template_575fc5088c474399 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,768 - mail_database_manager - INFO - 模板 template_0acd29605c994701 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,769 - mail_database_manager - INFO - 模板 template_14981f8399fe4572 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,769 - mail_database_manager - INFO - 模板 template_46f1fcab221b4a38 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,770 - mail_database_manager - INFO - 模板 template_7fd5d6e6b55c4142 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,770 - mail_database_manager - INFO - 模板 template_0738ee4b4a2d4983 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,770 - mail_database_manager - INFO - 模板 template_62e4594c1bf04b4b 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,771 - mail_database_manager - INFO - 模板 template_e827e75c8beb4d5d 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,771 - mail_database_manager - INFO - 模板 template_b0a095478f034b26 已被用户 dsadjdj23 处理，跳过
2025-08-05 20:59:14,771 - mail_database_manager - INFO - 用户 dsadjdj23 有 0 个未处理的邮件模板
2025-08-05 20:59:14,772 - mail_service_distributed - INFO - 用户 dsadjdj23 登录时发现 0 个未处理的邮件模板
2025-08-05 20:59:14,772 - mail_cache_manager - INFO - MailCacheManager实例已创建
2025-08-05 20:59:14,901 - game_manager - INFO - 玩家登录处理完成: dsadjdj23
2025-08-05 20:59:14,902 - ConnectionManager - INFO - 用户 dsadjdj23 连接成功 (Token: eyJhbGciOi, Worker: 12560)
2025-08-05 20:59:20,787 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:30 CST)" (scheduled at 2025-08-05 20:59:20.775088+08:00)
2025-08-05 20:59:20,840 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:59:20,841 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:20,970 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:59:21,321 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:59:21,321 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:59:21,322 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:59:21,322 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:21,365 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:30 CST)" executed successfully
2025-08-05 20:59:23,727 - MessageIdempotencyManager - INFO - 日志系统初始化成功 (进程 ID: 12560)
2025-08-05 20:59:23,727 - MessageIdempotencyManager - INFO - 消息幂等性管理器初始化完成
2025-08-05 20:59:30,180 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 20:59:30,305 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 20:59:30,503 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:00:30 CST)" (scheduled at 2025-08-05 20:59:30.487539+08:00)
2025-08-05 20:59:30,546 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 20:59:30,547 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:30,547 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 20:59:30,628 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:00:00 CST)" (scheduled at 2025-08-05 20:59:30.622531+08:00)
2025-08-05 20:59:30,671 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:59:30,672 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:30,780 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:40 CST)" (scheduled at 2025-08-05 20:59:30.775088+08:00)
2025-08-05 20:59:30,791 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 20:59:30,792 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 20:59:30,802 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 20:59:30,831 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:59:30,831 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:30,974 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:59:31,146 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:59:31,147 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 20:59:31,148 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:59:31,148 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:59:31,149 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:31,194 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:00:00 CST)" executed successfully
2025-08-05 20:59:31,324 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 1
2025-08-05 20:59:31,324 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.78秒
2025-08-05 20:59:31,325 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:59:31,326 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:31,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:59:31,332 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:59:31,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:59:31,333 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:31,370 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:00:30 CST)" executed successfully
2025-08-05 20:59:31,377 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:40 CST)" executed successfully
2025-08-05 20:59:40,787 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:50 CST)" (scheduled at 2025-08-05 20:59:40.775088+08:00)
2025-08-05 20:59:40,833 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:59:40,834 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:40,970 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:59:41,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:59:41,332 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:59:41,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:59:41,333 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:41,381 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:59:50 CST)" executed successfully
2025-08-05 20:59:42,026 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 20:59:50,780 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:00 CST)" (scheduled at 2025-08-05 20:59:50.775088+08:00)
2025-08-05 20:59:50,826 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:59:50,827 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:59:50,961 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:59:51,317 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:59:51,317 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:59:51,318 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:59:51,318 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 20:59:51,363 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:00 CST)" executed successfully
2025-08-05 20:59:55,549 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:59:56,366 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:59:58,331 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:00:00,568 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 21:00:00,625 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:00:30 CST)" (scheduled at 2025-08-05 21:00:00.622531+08:00)
2025-08-05 21:00:00,671 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:00:00,673 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:00:00,673 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:00,782 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:10 CST)" (scheduled at 2025-08-05 21:00:00.775088+08:00)
2025-08-05 21:00:00,807 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:00:00,827 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:00,827 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:00,962 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:01,156 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:00:01,157 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:00:01,160 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:00:01,162 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:00:01,164 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:01,215 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:00:30 CST)" executed successfully
2025-08-05 21:00:01,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:01,326 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:00:01,326 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:01,327 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:01,372 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:10 CST)" executed successfully
2025-08-05 21:00:10,782 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:20 CST)" (scheduled at 2025-08-05 21:00:10.775088+08:00)
2025-08-05 21:00:10,825 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:10,826 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:10,953 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:11,296 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:11,296 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:00:11,297 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:11,297 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:11,342 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:20 CST)" executed successfully
2025-08-05 21:00:12,023 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=3, 可用=2, 总计=5
2025-08-05 21:00:12,024 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 21:00:12,038 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 21:00:20,784 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:30 CST)" (scheduled at 2025-08-05 21:00:20.775088+08:00)
2025-08-05 21:00:20,828 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:20,828 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:20,958 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:21,298 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:21,298 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:00:21,300 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:21,301 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:21,344 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:30 CST)" executed successfully
2025-08-05 21:00:30,497 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:01:30 CST)" (scheduled at 2025-08-05 21:00:30.487539+08:00)
2025-08-05 21:00:30,542 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 21:00:30,542 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:30,543 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 21:00:30,547 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 21:00:30,606 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:00:30,637 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:01:00 CST)" (scheduled at 2025-08-05 21:00:30.622531+08:00)
2025-08-05 21:00:30,680 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:00:30,680 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:30,771 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:00:30,772 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:00:30,789 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:40 CST)" (scheduled at 2025-08-05 21:00:30.775088+08:00)
2025-08-05 21:00:30,814 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:00:30,838 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:30,839 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:30,971 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:31,159 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:00:31,159 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:00:31,161 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:00:31,161 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:00:31,162 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:31,204 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:01:00 CST)" executed successfully
2025-08-05 21:00:31,328 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:31,330 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:00:31,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:31,335 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:31,381 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:40 CST)" executed successfully
2025-08-05 21:00:32,095 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 1
2025-08-05 21:00:32,095 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.55秒
2025-08-05 21:00:32,096 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:00:32,097 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:32,141 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:01:30 CST)" executed successfully
2025-08-05 21:00:40,776 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:50 CST)" (scheduled at 2025-08-05 21:00:40.775088+08:00)
2025-08-05 21:00:40,826 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:40,826 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:40,962 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:41,330 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:41,330 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:00:41,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:41,332 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:41,378 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:00:50 CST)" executed successfully
2025-08-05 21:00:42,049 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=1, 用户数=1
2025-08-05 21:00:46,412 - game_server - INFO - WebSocket 断开，用户: dsadjdj23, token: eyJhbGciOi..., 代码: 1006, 原因: , worker: 12560
2025-08-05 21:00:46,641 - ConnectionManager - INFO - 正在关闭WebSocket连接 (Token: eyJhbGciOi, 状态: WebSocketState.CONNECTED)
2025-08-05 21:00:46,641 - ConnectionManager - WARNING - 关闭WebSocket连接失败: Unexpected ASGI message 'websocket.close', after sending 'websocket.close' or response already completed.
2025-08-05 21:00:46,641 - ConnectionManager - INFO - 用户 dsadjdj23 断开连接 (Token: eyJhbGciOi, Worker: 12560)
2025-08-05 21:00:46,642 - game_manager - INFO - 处理玩家登出: dsadjdj23, 原因: disconnect (Worker: 12560)
2025-08-05 21:00:46,774 - player_session_manager - INFO - 移除玩家会话: dsadjdj23 (Worker: 12560)
2025-08-05 21:00:46,819 - game_manager - INFO - 玩家登出处理完成: dsadjdj23
2025-08-05 21:00:50,786 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:00 CST)" (scheduled at 2025-08-05 21:00:50.775088+08:00)
2025-08-05 21:00:50,831 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:00:50,832 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:00:50,969 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:00:51,323 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:00:51,323 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:00:51,323 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:00:51,324 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:00:51,372 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:00 CST)" executed successfully
2025-08-05 21:01:00,630 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:01:30 CST)" (scheduled at 2025-08-05 21:01:00.622531+08:00)
2025-08-05 21:01:00,677 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:01:00,677 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:00,783 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:10 CST)" (scheduled at 2025-08-05 21:01:00.775088+08:00)
2025-08-05 21:01:00,813 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:01:00,816 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:01:00,829 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:01:00,829 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:00,919 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:01:00,971 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:01:01,173 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:01:01,173 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:01:01,174 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 21:01:01,174 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:01:01,175 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:01,223 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:01:30 CST)" executed successfully
2025-08-05 21:01:01,312 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:01:01,312 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:01:01,313 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:01:01,313 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:01,359 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:10 CST)" executed successfully
2025-08-05 21:01:10,787 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:20 CST)" (scheduled at 2025-08-05 21:01:10.775088+08:00)
2025-08-05 21:01:10,879 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:01:10,880 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:11,097 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:01:11,567 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:01:11,567 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.47秒
2025-08-05 21:01:11,568 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:01:11,569 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:11,611 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:20 CST)" executed successfully
2025-08-05 21:01:12,036 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=3, 可用=3, 总计=6
2025-08-05 21:01:12,036 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 21:01:12,051 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:01:20,784 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:30 CST)" (scheduled at 2025-08-05 21:01:20.775088+08:00)
2025-08-05 21:01:20,830 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:01:20,831 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:20,962 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:01:21,313 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:01:21,313 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:01:21,314 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:01:21,314 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:21,371 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:30 CST)" executed successfully
2025-08-05 21:01:30,064 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:01:30,156 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:01:30,496 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:02:30 CST)" (scheduled at 2025-08-05 21:01:30.487539+08:00)
2025-08-05 21:01:30,539 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 21:01:30,540 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:30,541 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 21:01:30,634 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:02:00 CST)" (scheduled at 2025-08-05 21:01:30.622531+08:00)
2025-08-05 21:01:30,680 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:01:30,681 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:30,771 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:01:30,771 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:01:30,787 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:40 CST)" (scheduled at 2025-08-05 21:01:30.775088+08:00)
2025-08-05 21:01:30,807 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:01:30,835 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:01:30,835 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:30,968 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:01:31,147 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:01:31,147 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:01:31,148 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:01:31,149 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:01:31,150 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:31,193 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:02:00 CST)" executed successfully
2025-08-05 21:01:31,334 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:01:31,334 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:01:31,335 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:01:31,338 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:31,382 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:40 CST)" executed successfully
2025-08-05 21:01:32,031 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 1
2025-08-05 21:01:32,031 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.49秒
2025-08-05 21:01:32,032 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:01:32,032 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:32,079 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:02:30 CST)" executed successfully
2025-08-05 21:01:40,779 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:50 CST)" (scheduled at 2025-08-05 21:01:40.775088+08:00)
2025-08-05 21:01:40,825 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:01:40,826 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:01:40,962 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:01:41,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:01:41,325 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:01:41,325 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:01:41,326 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:01:41,372 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:01:50 CST)" executed successfully
2025-08-05 21:01:42,053 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:02:03,111 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:10 CST)" was missed by 0:00:02.***********-08-05 21:02:03,111 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:02:30 CST)" was missed by 0:00:02.489410
2025-08-05 21:02:10,783 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:20 CST)" (scheduled at 2025-08-05 21:02:10.775088+08:00)
2025-08-05 21:02:10,830 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:02:10,831 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:10,965 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:02:11,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:02:11,325 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:02:11,326 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:02:11,327 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:11,372 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:20 CST)" executed successfully
2025-08-05 21:02:12,040 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=3, 可用=3, 总计=6
2025-08-05 21:02:12,040 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 21:02:20,787 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:30 CST)" (scheduled at 2025-08-05 21:02:20.775088+08:00)
2025-08-05 21:02:20,836 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:02:20,837 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:20,971 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:02:21,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:02:21,332 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:02:21,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:02:21,333 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:21,383 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:30 CST)" executed successfully
2025-08-05 21:02:30,368 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:02:30,369 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:02:30,489 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:03:30 CST)" (scheduled at 2025-08-05 21:02:30.487539+08:00)
2025-08-05 21:02:30,534 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 21:02:30,535 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:30,535 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 21:02:30,628 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:03:00 CST)" (scheduled at 2025-08-05 21:02:30.622531+08:00)
2025-08-05 21:02:30,673 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:02:30,674 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:30,732 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:02:30,732 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:02:30,782 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:40 CST)" (scheduled at 2025-08-05 21:02:30.775088+08:00)
2025-08-05 21:02:30,811 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:02:30,867 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:02:30,868 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:30,999 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:02:31,163 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:02:31,163 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:02:31,164 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:02:31,166 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:02:31,166 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:31,211 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:03:00 CST)" executed successfully
2025-08-05 21:02:31,345 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:02:31,345 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:02:31,346 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:02:31,346 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:31,389 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:40 CST)" executed successfully
2025-08-05 21:02:32,666 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 1
2025-08-05 21:02:32,667 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.13秒
2025-08-05 21:02:32,667 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:02:32,668 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:32,714 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:03:30 CST)" executed successfully
2025-08-05 21:02:33,121 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:02:40,780 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:50 CST)" (scheduled at 2025-08-05 21:02:40.775088+08:00)
2025-08-05 21:02:40,825 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:02:40,825 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:40,951 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:02:41,300 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:02:41,301 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:02:41,302 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:02:41,302 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:41,345 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:02:50 CST)" executed successfully
2025-08-05 21:02:50,776 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:00 CST)" (scheduled at 2025-08-05 21:02:50.775088+08:00)
2025-08-05 21:02:50,818 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:02:50,818 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:02:50,946 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:02:51,296 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:02:51,297 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:02:51,297 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:02:51,298 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:02:51,343 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:00 CST)" executed successfully
2025-08-05 21:03:00,635 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:00,637 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:00,638 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:03:30 CST)" (scheduled at 2025-08-05 21:03:00.622531+08:00)
2025-08-05 21:03:00,681 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:03:00,682 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:00,788 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:10 CST)" (scheduled at 2025-08-05 21:03:00.775088+08:00)
2025-08-05 21:03:00,811 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:03:00,833 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:00,833 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:00,972 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:01,165 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:03:01,166 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:03:01,166 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 21:03:01,167 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:03:01,168 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:01,213 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:03:30 CST)" executed successfully
2025-08-05 21:03:01,339 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:01,339 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:03:01,340 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:01,340 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:01,386 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:10 CST)" executed successfully
2025-08-05 21:03:03,126 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:10,789 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:20 CST)" (scheduled at 2025-08-05 21:03:10.775088+08:00)
2025-08-05 21:03:10,834 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:10,834 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:10,968 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:11,350 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:11,351 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 21:03:11,351 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:11,352 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:11,398 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:20 CST)" executed successfully
2025-08-05 21:03:12,056 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=3, 可用=3, 总计=6
2025-08-05 21:03:12,059 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 21:03:20,779 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:30 CST)" (scheduled at 2025-08-05 21:03:20.775088+08:00)
2025-08-05 21:03:20,825 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:20,826 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:20,959 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:21,314 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:21,315 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:03:21,316 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:21,316 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:21,365 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:30 CST)" executed successfully
2025-08-05 21:03:30,499 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:04:30 CST)" (scheduled at 2025-08-05 21:03:30.487539+08:00)
2025-08-05 21:03:30,623 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:04:00 CST)" (scheduled at 2025-08-05 21:03:30.622531+08:00)
2025-08-05 21:03:30,666 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:03:30,667 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:30,777 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:40 CST)" (scheduled at 2025-08-05 21:03:30.775088+08:00)
2025-08-05 21:03:30,793 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:03:30,803 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:push_online
2025-08-05 21:03:30,806 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:30,807 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行推送在线人数任务
2025-08-05 21:03:30,867 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:30,868 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:30,914 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:30,914 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:30,941 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:03:30,941 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:03:30,997 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:31,156 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:03:31,157 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:03:31,157 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 21:03:31,158 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:03:31,160 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:31,213 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:04:00 CST)" executed successfully
2025-08-05 21:03:31,333 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:31,334 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:03:31,334 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:31,334 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:31,379 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:40 CST)" executed successfully
2025-08-05 21:03:32,166 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 在线人数推送完成，当前在线: 1
2025-08-05 21:03:32,166 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.36秒
2025-08-05 21:03:32,167 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:03:32,167 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:32,210 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:04:30 CST)" executed successfully
2025-08-05 21:03:33,154 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:03:40,784 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:50 CST)" (scheduled at 2025-08-05 21:03:40.775088+08:00)
2025-08-05 21:03:40,829 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:40,829 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:40,955 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:41,550 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:41,550 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.60秒
2025-08-05 21:03:41,551 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:41,551 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:41,597 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:03:50 CST)" executed successfully
2025-08-05 21:03:50,784 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:00 CST)" (scheduled at 2025-08-05 21:03:50.775088+08:00)
2025-08-05 21:03:50,832 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:03:50,832 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:03:50,955 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:03:51,303 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:03:51,304 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:03:51,305 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:03:51,306 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:03:51,352 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:00 CST)" executed successfully
2025-08-05 21:04:00,211 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:04:00,212 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:04:00,628 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:04:30 CST)" (scheduled at 2025-08-05 21:04:00.622531+08:00)
2025-08-05 21:04:00,669 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:04:00,669 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:04:00,780 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:10 CST)" (scheduled at 2025-08-05 21:04:00.775088+08:00)
2025-08-05 21:04:00,793 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 开始执行怪物冷却持久化任务
2025-08-05 21:04:00,825 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:04:00,826 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:04:00,960 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:04:01,133 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:04:01,134 - scheduler_tasks_unified - INFO - [定时任务] Worker 12560 怪物冷却持久化完成
2025-08-05 21:04:01,134 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:04:01,135 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:04:01,135 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:04:01,177 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:04:30 CST)" executed successfully
2025-08-05 21:04:01,582 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:04:01,583 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.62秒
2025-08-05 21:04:01,583 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:04:01,584 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:04:01,881 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:10 CST)" executed successfully
2025-08-05 21:04:03,159 - ConnectionManager - INFO - 连接状态 (Worker 12560): 活跃连接数=0, 用户数=0
2025-08-05 21:04:10,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:20 CST)" (scheduled at 2025-08-05 21:04:10.775088+08:00)
2025-08-05 21:04:10,829 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:04:10,829 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:04:10,964 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:04:11,330 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:04:11,330 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:04:11,330 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:04:11,331 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:04:11,383 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:20 CST)" executed successfully
2025-08-05 21:04:12,072 - ConnectionManager - INFO - Redis连接池状态 (Worker 12560): 使用中=3, 可用=3, 总计=6
2025-08-05 21:04:12,073 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 12560): 连接中
2025-08-05 21:04:20,781 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:30 CST)" (scheduled at 2025-08-05 21:04:20.775088+08:00)
2025-08-05 21:04:20,835 - distributed_lock - INFO - Worker 12560 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:04:20,835 - distributed_task - INFO - Worker 12560 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:04:20,969 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:04:21,328 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:04:21,328 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:04:21,329 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:04:21,329 - distributed_task - INFO - Worker 12560 任务执行完成: direct_wrapper
2025-08-05 21:04:21,374 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:04:30 CST)" executed successfully
2025-08-05 21:04:25,708 - game_server - INFO - 关闭服务器... (Worker: 12560)
2025-08-05 21:04:25,709 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 21:04:26,061 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:04:26,061 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 21:04:26,062 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 21:04:26,063 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 21:04:26,064 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 21:04:26,064 - game_server - INFO - 日志清理管理器已停止
2025-08-05 21:04:26,065 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 21:04:26,069 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 21:04:26,069 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 21:04:26,070 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 21:04:26,070 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 21:04:26,071 - game_server - INFO - 统一调度器已关闭
2025-08-05 21:04:26,072 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 21:04:26,074 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 12560)
2025-08-05 21:04:26,163 - game_server - INFO - 服务器资源已清理 (Worker: 12560)
