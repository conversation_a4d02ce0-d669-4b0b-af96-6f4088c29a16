/**
 * 商店管理系统 - 商店管理逻辑
 * 负责商店的增删改查操作和界面交互
 */

class ShopManager {
    constructor() {
        this.shops = [];
        this.filteredShops = [];
        this.currentShop = null;
        this.isLoading = false;
        
        // 初始化
        this.init();
    }

    /**
     * 初始化管理器
     */
    async init() {
        try {
            await this.loadShops();
            this.bindEvents();
            console.log('[ShopManager] 初始化完成');
        } catch (error) {
            console.error('[ShopManager] 初始化失败:', error);
            this.showMessage('系统初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 表单提交事件
        const shopForm = document.getElementById('shopForm');
        if (shopForm) {
            shopForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // 移除点击空白区域关闭模态框的功能，避免误操作
        // 用户必须点击关闭按钮或按ESC键才能关闭

        // ESC键关闭模态框（保留此功能，因为这是用户主动操作）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        console.log('[ShopManager] 事件绑定完成 - 已禁用点击空白关闭功能');
    }

    // ==================== 数据加载方法 ====================

    /**
     * 加载商店列表
     */
    async loadShops() {
        try {
            this.setLoading(true);
            
            const response = await shopAPI.getShops();
            
            if (response.success) {
                this.shops = response.data || [];
                this.filteredShops = [...this.shops];
                this.renderShopList();
                this.updateShopCount();
                console.log(`[ShopManager] 加载了 ${this.shops.length} 个商店`);
            } else {
                throw new Error(response.message || '加载商店列表失败');
            }
            
        } catch (error) {
            console.error('[ShopManager] 加载商店列表失败:', error);
            this.showMessage('加载商店列表失败: ' + error.message, 'error');
            this.showEmptyState();
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 刷新商店列表
     */
    async refreshShopList() {
        console.log('[ShopManager] 刷新商店列表');
        await this.loadShops();
        this.showMessage('商店列表已刷新', 'success');
    }

    // ==================== 界面渲染方法 ====================

    /**
     * 渲染商店列表
     */
    renderShopList() {
        const shopList = document.getElementById('shopList');
        const emptyState = document.getElementById('emptyState');
        
        if (!shopList) return;

        if (this.filteredShops.length === 0) {
            shopList.innerHTML = '';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';

        shopList.innerHTML = this.filteredShops.map(shop => this.createShopCard(shop)).join('');
    }

    /**
     * 创建商店卡片HTML
     * @param {Object} shop - 商店数据
     * @returns {string} HTML字符串
     */
    createShopCard(shop) {
        const statusClass = shop.is_active ? 'active' : 'inactive';
        const statusText = shop.is_active ? '激活' : '禁用';
        const typeText = this.getShopTypeText(shop.shop_type);
        
        return `
            <div class="shop-card" data-shop-id="${shop.shop_id}">
                <div class="shop-card-header">
                    <div class="shop-info">
                        <h3 class="shop-title">${this.escapeHtml(shop.shop_name)}</h3>
                        <div class="shop-id">${this.escapeHtml(shop.shop_id)}</div>
                    </div>
                    <div class="shop-status">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                        <span class="shop-type">${typeText}</span>
                    </div>
                </div>
                
                ${shop.description ? `<div class="shop-description">${this.escapeHtml(shop.description)}</div>` : ''}
                
                <div class="shop-meta">
                    <span>排序: ${shop.sort_order || 0}</span>
                    <span>创建: ${this.formatDate(shop.created_at)}</span>
                </div>
                
                <div class="shop-actions">
                    <button class="btn btn-sm btn-secondary" onclick="shopManager.viewShopItems('${shop.shop_id}')">
                        <i class="icon-view"></i>
                        商品管理
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="shopManager.editShop('${shop.shop_id}')">
                        <i class="icon-edit"></i>
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="shopManager.deleteShop('${shop.shop_id}')">
                        <i class="icon-delete"></i>
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const shopList = document.getElementById('shopList');
        const emptyState = document.getElementById('emptyState');
        
        if (shopList) shopList.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
    }

    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.isLoading = loading;
        const loadingState = document.getElementById('loadingState');
        const shopList = document.getElementById('shopList');
        
        if (loadingState) {
            loadingState.style.display = loading ? 'flex' : 'none';
        }
        
        if (shopList && loading) {
            shopList.innerHTML = '';
        }
    }

    /**
     * 更新商店数量显示
     */
    updateShopCount() {
        const shopCount = document.getElementById('shopCount');
        if (shopCount) {
            shopCount.textContent = this.filteredShops.length;
        }
    }

    // ==================== 商店操作方法 ====================

    /**
     * 打开新增商店表单
     */
    openShopForm() {
        this.currentShop = null;
        this.resetForm();
        
        const modal = document.getElementById('shopModal');
        const modalTitle = document.getElementById('modalTitle');
        
        if (modalTitle) modalTitle.textContent = '新增商店';
        if (modal) {
            modal.classList.add('show');
            modal.style.display = 'flex';
        }
        
        // 聚焦到第一个输入框
        setTimeout(() => {
            const firstInput = document.getElementById('shopId');
            if (firstInput) firstInput.focus();
        }, 100);
        
        console.log('[ShopManager] 打开新增商店表单');
    }

    /**
     * 编辑商店
     * @param {string} shopId - 商店ID
     */
    async editShop(shopId) {
        try {
            const shop = this.shops.find(s => s.shop_id === shopId);
            if (!shop) {
                throw new Error('商店不存在');
            }

            this.currentShop = shop;
            this.fillForm(shop);
            
            const modal = document.getElementById('shopModal');
            const modalTitle = document.getElementById('modalTitle');
            
            if (modalTitle) modalTitle.textContent = '编辑商店';
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
            
            // 禁用商店ID输入框
            const shopIdInput = document.getElementById('shopId');
            if (shopIdInput) shopIdInput.disabled = true;
            
            console.log('[ShopManager] 打开编辑商店表单:', shopId);
            
        } catch (error) {
            console.error('[ShopManager] 编辑商店失败:', error);
            this.showMessage('编辑商店失败: ' + error.message, 'error');
        }
    }

    /**
     * 删除商店
     * @param {string} shopId - 商店ID
     */
    deleteShop(shopId) {
        const shop = this.shops.find(s => s.shop_id === shopId);
        if (!shop) {
            this.showMessage('商店不存在', 'error');
            return;
        }

        // 显示确认删除对话框
        const deleteModal = document.getElementById('deleteModal');
        const deleteShopName = document.getElementById('deleteShopName');
        
        if (deleteShopName) deleteShopName.textContent = shop.shop_name;
        if (deleteModal) {
            deleteModal.classList.add('show');
            deleteModal.style.display = 'flex';
            deleteModal.dataset.shopId = shopId;
        }
        
        console.log('[ShopManager] 准备删除商店:', shopId);
    }

    /**
     * 确认删除商店
     */
    async confirmDelete() {
        const deleteModal = document.getElementById('deleteModal');
        const shopId = deleteModal?.dataset.shopId;
        
        if (!shopId) return;

        try {
            const response = await shopAPI.deleteShop(shopId);
            
            if (response.success) {
                this.closeDeleteModal();
                await this.loadShops();
                this.showMessage('商店删除成功', 'success');
                console.log('[ShopManager] 商店删除成功:', shopId);
            } else {
                throw new Error(response.message || '删除失败');
            }
            
        } catch (error) {
            console.error('[ShopManager] 删除商店失败:', error);
            this.showMessage('删除商店失败: ' + error.message, 'error');
        }
    }

    /**
     * 跳转到商品管理页面
     * @param {string} shopId - 商店ID
     */
    viewShopItems(shopId) {
        console.log('[ShopManager] 跳转到商品管理:', shopId);
        window.location.href = `items.html?shop_id=${encodeURIComponent(shopId)}`;
    }

    // ==================== 表单处理方法 ====================

    /**
     * 处理表单提交
     * @param {Event} event - 表单提交事件
     */
    async handleFormSubmit(event) {
        event.preventDefault();
        
        try {
            const formData = this.getFormData();
            this.validateFormData(formData);
            
            let response;
            if (this.currentShop) {
                // 更新商店
                response = await shopAPI.updateShop(this.currentShop.shop_id, formData);
                console.log('[ShopManager] 更新商店:', this.currentShop.shop_id);
            } else {
                // 创建商店
                response = await shopAPI.createShop(formData);
                console.log('[ShopManager] 创建商店:', formData.shop_id);
            }
            
            if (response.success) {
                this.closeShopForm();
                await this.loadShops();
                const action = this.currentShop ? '更新' : '创建';
                this.showMessage(`商店${action}成功`, 'success');
            } else {
                throw new Error(response.message || '操作失败');
            }
            
        } catch (error) {
            console.error('[ShopManager] 表单提交失败:', error);
            this.showMessage('操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
        const form = document.getElementById('shopForm');
        const formData = new FormData(form);
        
        const data = {
            shop_id: formData.get('shop_id')?.trim(),
            shop_name: formData.get('shop_name')?.trim(),
            shop_type: formData.get('shop_type'),
            description: formData.get('description')?.trim() || '',
            icon: formData.get('icon')?.trim() || '',
            is_active: formData.has('is_active'),
            sort_order: parseInt(formData.get('sort_order')) || 0,
            access_conditions: this.parseJSONField(formData.get('access_conditions')),
            refresh_config: this.parseJSONField(formData.get('refresh_config')),
            ui_config: this.parseJSONField(formData.get('ui_config'))
        };
        
        return data;
    }

    /**
     * 验证表单数据
     * @param {Object} data - 表单数据
     */
    validateFormData(data) {
        if (!data.shop_id) {
            throw new Error('商店ID不能为空');
        }
        
        if (!data.shop_name) {
            throw new Error('商店名称不能为空');
        }
        
        if (!data.shop_type) {
            throw new Error('请选择商店类型');
        }
        
        // 验证商店ID格式
        if (!/^[a-zA-Z0-9_]+$/.test(data.shop_id)) {
            throw new Error('商店ID只能包含字母、数字和下划线');
        }
        
        // 检查商店ID是否重复（新增时）
        if (!this.currentShop && this.shops.some(shop => shop.shop_id === data.shop_id)) {
            throw new Error('商店ID已存在');
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 解析JSON字段
     * @param {string} value - JSON字符串
     * @returns {Object|null} 解析后的对象
     */
    parseJSONField(value) {
        if (!value || !value.trim()) return {};
        
        try {
            return JSON.parse(value.trim());
        } catch (error) {
            throw new Error('JSON格式错误: ' + value);
        }
    }

    /**
     * 获取商店类型文本
     * @param {string} type - 商店类型
     * @returns {string} 类型文本
     */
    getShopTypeText(type) {
        const typeMap = {
            'normal': '普通商店',
            'guild': '公会商店',
            'vip': 'VIP商店',
            'event': '活动商店',
            'arena': '竞技场商店'
        };
        return typeMap[type] || type;
    }

    /**
     * 格式化日期
     * @param {string} dateString - 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDate(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        } catch (error) {
            return dateString;
        }
    }

    /**
     * HTML转义
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        if (!text) return '';
        
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;
        
        container.appendChild(messageEl);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }

    // ==================== 表单和模态框方法 ====================

    /**
     * 重置表单
     */
    resetForm() {
        const form = document.getElementById('shopForm');
        if (form) {
            form.reset();

            // 启用商店ID输入框
            const shopIdInput = document.getElementById('shopId');
            if (shopIdInput) shopIdInput.disabled = false;

            // 设置默认值
            const sortOrderInput = document.getElementById('sortOrder');
            if (sortOrderInput) sortOrderInput.value = '0';

            const isActiveInput = document.getElementById('isActive');
            if (isActiveInput) isActiveInput.checked = true;
        }
    }

    /**
     * 填充表单数据
     * @param {Object} shop - 商店数据
     */
    fillForm(shop) {
        const fields = [
            'shopId', 'shopName', 'shopType', 'description',
            'icon', 'sortOrder'
        ];

        const mapping = {
            'shopId': 'shop_id',
            'shopName': 'shop_name',
            'shopType': 'shop_type',
            'description': 'description',
            'icon': 'icon',
            'sortOrder': 'sort_order'
        };

        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            const dataKey = mapping[fieldId];

            if (element && shop[dataKey] !== undefined) {
                element.value = shop[dataKey];
            }
        });

        // 处理复选框
        const isActiveInput = document.getElementById('isActive');
        if (isActiveInput) {
            isActiveInput.checked = shop.is_active;
        }

        // 处理JSON字段
        const jsonFields = [
            { id: 'accessConditions', key: 'access_conditions' },
            { id: 'refreshConfig', key: 'refresh_config' },
            { id: 'uiConfig', key: 'ui_config' }
        ];

        jsonFields.forEach(({ id, key }) => {
            const element = document.getElementById(id);
            if (element && shop[key]) {
                element.value = JSON.stringify(shop[key], null, 2);
            }
        });
    }

    /**
     * 关闭商店表单
     */
    closeShopForm() {
        const modal = document.getElementById('shopModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
        }
        this.resetForm();
        this.currentShop = null;
    }

    /**
     * 关闭删除确认对话框
     */
    closeDeleteModal() {
        const modal = document.getElementById('deleteModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
            delete modal.dataset.shopId;
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        this.closeShopForm();
        this.closeDeleteModal();
    }

    // ==================== 筛选和搜索方法 ====================

    /**
     * 筛选商店
     */
    filterShops() {
        const typeFilter = document.getElementById('shopTypeFilter')?.value;
        const statusFilter = document.getElementById('statusFilter')?.value;
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim();

        this.filteredShops = this.shops.filter(shop => {
            // 类型筛选
            if (typeFilter && shop.shop_type !== typeFilter) {
                return false;
            }

            // 状态筛选
            if (statusFilter !== '') {
                const isActive = statusFilter === 'true';
                if (shop.is_active !== isActive) {
                    return false;
                }
            }

            // 搜索筛选
            if (searchTerm) {
                const searchFields = [
                    shop.shop_name,
                    shop.shop_id,
                    shop.description
                ].filter(Boolean);

                const matchesSearch = searchFields.some(field =>
                    field.toLowerCase().includes(searchTerm)
                );

                if (!matchesSearch) {
                    return false;
                }
            }

            return true;
        });

        this.renderShopList();
        this.updateShopCount();

        console.log(`[ShopManager] 筛选结果: ${this.filteredShops.length}/${this.shops.length}`);
    }

    /**
     * 搜索商店
     */
    searchShops() {
        // 防抖处理
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.filterShops();
        }, 300);
    }
}

// ==================== 全局函数 ====================

/**
 * 打开商店表单 - 全局函数
 */
function openShopForm() {
    if (window.shopManager) {
        window.shopManager.openShopForm();
    }
}

/**
 * 关闭商店表单 - 全局函数
 */
function closeShopForm() {
    if (window.shopManager) {
        window.shopManager.closeShopForm();
    }
}

/**
 * 保存商店 - 全局函数
 * @param {Event} event - 表单提交事件
 */
function saveShop(event) {
    if (window.shopManager) {
        window.shopManager.handleFormSubmit(event);
    }
}

/**
 * 关闭删除确认对话框 - 全局函数
 */
function closeDeleteModal() {
    if (window.shopManager) {
        window.shopManager.closeDeleteModal();
    }
}

/**
 * 确认删除 - 全局函数
 */
function confirmDelete() {
    if (window.shopManager) {
        window.shopManager.confirmDelete();
    }
}

/**
 * 刷新商店列表 - 全局函数
 */
function refreshShopList() {
    if (window.shopManager) {
        window.shopManager.refreshShopList();
    }
}

/**
 * 筛选商店 - 全局函数
 */
function filterShops() {
    if (window.shopManager) {
        window.shopManager.filterShops();
    }
}

/**
 * 搜索商店 - 全局函数
 */
function searchShops() {
    if (window.shopManager) {
        window.shopManager.searchShops();
    }
}

// 创建全局实例
window.shopManager = new ShopManager();
