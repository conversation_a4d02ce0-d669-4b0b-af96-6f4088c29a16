#!/usr/bin/env python3
"""
测试购买流程修复
验证货币服务和道具创建的Redis连接问题是否已解决
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import setup_logger

logger = setup_logger(__name__)


async def test_currency_service():
    """测试货币服务"""
    print("🪙 测试货币服务...")
    
    try:
        from currency_service import CurrencyService
        
        currency_service = CurrencyService()
        
        # 测试获取货币数量
        print("1. 测试获取货币数量...")
        amount = await currency_service.get_currency_amount("test_player", "gold")
        print(f"✅ 获取货币数量成功: {amount}")
        
        # 测试增加货币
        print("2. 测试增加货币...")
        result = await currency_service.add_currency("test_player", "gold", 100)
        print(f"✅ 增加货币结果: {result}")
        
        # 测试扣除货币
        print("3. 测试扣除货币...")
        result = await currency_service.deduct_currency("test_player", "gold", 50)
        print(f"✅ 扣除货币结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 货币服务测试失败: {str(e)}")
        logger.error(f"货币服务测试失败: {str(e)}")
        return False


async def test_shop_purchase_service():
    """测试商店购买服务"""
    print("\n🛒 测试商店购买服务...")
    
    try:
        from shop_purchase_service import ShopPurchaseService, PurchaseRequest
        from shop_models import ShopItemConfig
        from datetime import datetime
        
        # 创建购买服务
        purchase_service = ShopPurchaseService()
        
        # 创建模拟的商品配置
        mock_config = ShopItemConfig(
            config_id="test_item_001",
            shop_id="test_shop",
            item_template_id="82001",  # 使用数字ID
            item_quantity=1,
            item_quality=1,
            price_config={
                "currency_type": "gold",
                "base_price": 100
            },
            purchase_limit={},
            availability={},
            refresh_weight=1,
            refresh_probability=1.0,
            is_active=True,
            sort_order=1,
            display_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 创建购买请求
        request = PurchaseRequest(
            player_id="test_player",
            config_id="test_item_001",
            quantity=1
        )
        
        # 测试道具实例创建的核心逻辑
        print("1. 测试道具实例创建逻辑...")
        
        # 模拟购买事务的道具创建部分
        from ItemCacheManager import Item
        from mongodb_manager import MongoDBManager
        from redis_manager import RedisManager
        
        # 获取数据库连接
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        # 获取Redis连接 (使用修复后的方式)
        redis_manager = await RedisManager.get_instance()
        redis_client = await redis_manager.get_redis()
        
        print("✅ 数据库和Redis连接获取成功")
        
        # 测试创建道具实例
        print("2. 测试创建道具实例...")

        defid = int(mock_config.item_template_id)
        item = Item(
            owner="test_player",
            defid=defid,
            type="item",
            quantity=mock_config.item_quantity or 1
        )

        # 这里只测试对象创建，不实际保存到数据库
        print(f"✅ 道具实例创建成功: owner={item.owner}, defid={item.defid}, quantity={item.quantity}")

        # 测试道具发放逻辑（模拟）
        print("3. 测试道具发放逻辑...")

        # 模拟购买事务的道具发放部分
        from shop_purchase_service import PurchaseTransaction

        # 创建模拟的购买事务
        class MockPurchaseService:
            def __init__(self):
                self.item_service = None  # 模拟没有外部item_service的情况

        mock_service = MockPurchaseService()
        mock_transaction = PurchaseTransaction(request, mock_service)
        mock_transaction.item_instances = ["item_001", "item_002"]  # 模拟道具实例ID

        # 测试通知方法
        try:
            await mock_transaction._notify_item_changes()
            print("✅ 道具变更通知测试成功")
        except Exception as e:
            print(f"⚠️ 道具变更通知测试失败（可能是正常的）: {str(e)}")

        return True
        
    except Exception as e:
        print(f"❌ 商店购买服务测试失败: {str(e)}")
        logger.error(f"商店购买服务测试失败: {str(e)}")
        return False


async def test_redis_connection():
    """测试Redis连接"""
    print("\n🔗 测试Redis连接...")
    
    try:
        from redis_manager import RedisManager
        
        # 获取Redis管理器
        redis_manager = await RedisManager.get_instance()
        redis_client = await redis_manager.get_redis()
        
        # 测试ping
        await redis_client.ping()
        print("✅ Redis连接正常")
        
        # 测试基本操作
        await redis_client.set("test_fix_key", "test_value", ex=10)
        value = await redis_client.get("test_fix_key")
        print(f"✅ Redis操作正常: {value}")
        
        # 清理
        await redis_client.delete("test_fix_key")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {str(e)}")
        logger.error(f"Redis连接测试失败: {str(e)}")
        return False


async def test_mongodb_connection():
    """测试MongoDB连接"""
    print("\n🍃 测试MongoDB连接...")
    
    try:
        from mongodb_manager import MongoDBManager
        
        # 获取MongoDB管理器
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        # 测试ping
        await db.command("ping")
        print("✅ MongoDB连接正常")
        
        return True
        
    except Exception as e:
        print(f"❌ MongoDB连接测试失败: {str(e)}")
        logger.error(f"MongoDB连接测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🔧 开始测试购买流程修复...")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试Redis连接
    results.append(await test_redis_connection())
    
    # 2. 测试MongoDB连接
    results.append(await test_mongodb_connection())
    
    # 3. 测试货币服务
    results.append(await test_currency_service())
    
    # 4. 测试商店购买服务
    results.append(await test_shop_purchase_service())
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    test_names = [
        "Redis连接",
        "MongoDB连接", 
        "货币服务",
        "商店购买服务"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！购买流程修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {str(e)}")
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
