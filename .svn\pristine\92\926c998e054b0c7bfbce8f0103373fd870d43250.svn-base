# -*- coding: utf-8 -*-
"""
分布式装备服务 - 适配多Worker模式
使用Redis作为共享存储和缓存，确保多Worker环境下的数据一致性
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime
import logging
from equipment_v2 import EquipmentItem, EquipState, EquipQuality, PropertyType
from enums import ItemType
from game_database import DatabaseManager, Item
from service_locator import ServiceLocator
from logger_config import setup_logger
from distributed_lock import DistributedLock

logger = setup_logger(__name__)

class DistributedEquipmentService:
    """
    分布式装备服务 - 适配多Worker模式
    使用Redis作为共享存储，确保多Worker环境下的数据一致性
    """
    
    def __init__(self):
        self._db_manager = None
        self._worker_id = str(uuid.uuid4())[:8]  # 每个Worker的唯一标识
        self._cache_subscription = None
        self._cache_handlers = {}
        logger.info(f"DistributedEquipmentService initialized (Worker: {self._worker_id})")

    def _get_db_manager(self) -> DatabaseManager:
        """获取数据库管理器"""
        if self._db_manager:
            return self._db_manager
        
        db_manager = ServiceLocator.get("db_manager")
        if not db_manager:
            raise RuntimeError("Database manager not available")
        return db_manager

    def _get_redis_client(self):
        """获取Redis客户端"""
        db_manager = self._get_db_manager()
        if not db_manager.redis_client:
            raise RuntimeError("Redis client not available")
        return db_manager.redis_client

    def _get_equipment_key(self, player_id: str, item_id: str) -> str:
        """获取装备在Redis中的键"""
        return f"equipment:{player_id}:{item_id}"

    def _get_player_equipments_key(self, player_id: str) -> str:
        """获取玩家装备列表在Redis中的键"""
        return f"player_equipments:{player_id}"

    def _get_player_equipped_key(self, player_id: str) -> str:
        """获取玩家已穿戴装备在Redis中的键"""
        return f"player_equipped:{player_id}"

    def _get_equipment_lock_key(self, player_id: str, item_id: str) -> str:
        """获取装备操作锁的键"""
        return f"equipment_lock:{player_id}:{item_id}"

    def _get_player_lock_key(self, player_id: str) -> str:
        """获取玩家操作锁的键"""
        return f"player_lock:{player_id}"

    async def _load_equipment_from_db(self, player_id: str, item_id: str) -> Optional[EquipmentItem]:
        """从数据库加载装备数据"""
        try:
            db_manager = self._get_db_manager()
            result = await db_manager.get_user_item(username=player_id, item_id=item_id)
            
            if result["success"] and result["item"]:
                equipment = EquipmentItem(**result["item"])
                # 缓存到Redis
                await self._cache_equipment(player_id, equipment)
                return equipment
            return None
        except Exception as e:
            logger.error(f"Error loading equipment {item_id} for player {player_id}: {str(e)}")
            return None

    async def _cache_equipment(self, player_id: str, equipment: EquipmentItem):
        """缓存装备数据到Redis"""
        try:
            redis_client = self._get_redis_client()
            equipment_key = self._get_equipment_key(player_id, equipment.id)
            
            # 序列化装备数据
            equipment_data = equipment.serialize_equipment()
            equipment_data["_cached_at"] = time.time()
            equipment_data["_worker_id"] = self._worker_id
            
            # 缓存装备数据，设置过期时间
            await redis_client.setex(
                equipment_key, 
                3600,  # 1小时过期
                json.dumps(equipment_data, ensure_ascii=False)
            )
            
            # 更新玩家装备列表
            player_equipments_key = self._get_player_equipments_key(player_id)
            await redis_client.sadd(player_equipments_key, equipment.id)
            await redis_client.expire(player_equipments_key, 3600)
            
            # 如果装备已穿戴，更新已穿戴列表
            if equipment.equipment_state == EquipState.EQUIPPED:
                part = equipment.get_part()
                if part and part != "unknown":
                    player_equipped_key = self._get_player_equipped_key(player_id)
                    await redis_client.hset(player_equipped_key, part, equipment.id)
                    await redis_client.expire(player_equipped_key, 3600)
            
            logger.debug(f"Cached equipment {equipment.id} for player {player_id}")
            
        except Exception as e:
            logger.error(f"Error caching equipment {equipment.id} for player {player_id}: {str(e)}")

    async def _get_cached_equipment(self, player_id: str, item_id: str) -> Optional[EquipmentItem]:
        """从Redis缓存获取装备数据"""
        try:
            redis_client = self._get_redis_client()
            equipment_key = self._get_equipment_key(player_id, item_id)
            
            cached_data = await redis_client.get(equipment_key)
            if cached_data:
                equipment_data = json.loads(cached_data)
                # 移除缓存相关字段
                equipment_data.pop("_cached_at", None)
                equipment_data.pop("_worker_id", None)
                
                equipment = EquipmentItem(**equipment_data)
                return equipment
            return None
        except Exception as e:
            logger.error(f"Error getting cached equipment {item_id} for player {player_id}: {str(e)}")
            return None

    async def _invalidate_equipment_cache(self, player_id: str, item_id: str):
        """使装备缓存失效"""
        try:
            redis_client = self._get_redis_client()
            equipment_key = self._get_equipment_key(player_id, item_id)
            await redis_client.delete(equipment_key)
            
            # 发布缓存失效通知
            await self._publish_cache_invalidation(player_id, item_id)
            
            logger.debug(f"Invalidated equipment cache {item_id} for player {player_id}")
        except Exception as e:
            logger.error(f"Error invalidating equipment cache {item_id} for player {player_id}: {str(e)}")

    async def _publish_cache_invalidation(self, player_id: str, item_id: str):
        """发布缓存失效通知"""
        try:
            redis_client = self._get_redis_client()
            message = {
                "type": "equipment_cache_invalidation",
                "player_id": player_id,
                "item_id": item_id,
                "worker_id": self._worker_id,
                "timestamp": time.time()
            }
            await redis_client.publish("equipment_cache_channel", json.dumps(message))
        except Exception as e:
            logger.error(f"Error publishing cache invalidation: {str(e)}")

    async def _subscribe_to_cache_invalidation(self):
        """订阅缓存失效通知"""
        try:
            redis_client = self._get_redis_client()
            pubsub = redis_client.pubsub()
            await pubsub.subscribe("equipment_cache_channel")
            
            async def handle_cache_messages():
                while True:
                    try:
                        message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                        if message and message["type"] == "message":
                            data = json.loads(message["data"])
                            if data["type"] == "equipment_cache_invalidation":
                                # 忽略自己发布的消息
                                if data["worker_id"] != self._worker_id:
                                    await self._handle_cache_invalidation(data)
                    except Exception as e:
                        logger.error(f"Error handling cache message: {str(e)}")
                        await asyncio.sleep(1)
            
            # 启动消息处理任务
            asyncio.create_task(handle_cache_messages())
            logger.info(f"Subscribed to equipment cache invalidation (Worker: {self._worker_id})")
            
        except Exception as e:
            logger.error(f"Error subscribing to cache invalidation: {str(e)}")

    async def _handle_cache_invalidation(self, data: dict):
        """处理缓存失效通知"""
        try:
            player_id = data["player_id"]
            item_id = data["item_id"]
            
            # 清除本地缓存（如果有的话）
            # 这里可以添加本地缓存清理逻辑
            
            logger.debug(f"Handled cache invalidation for equipment {item_id} (player: {player_id})")
        except Exception as e:
            logger.error(f"Error handling cache invalidation: {str(e)}")

    async def initialize(self):
        """初始化分布式装备服务"""
        try:
            # 订阅缓存失效通知
            await self._subscribe_to_cache_invalidation()
            logger.info(f"DistributedEquipmentService initialized (Worker: {self._worker_id})")
        except Exception as e:
            logger.error(f"Error initializing DistributedEquipmentService: {str(e)}")

    async def add_equipment(self, player_id: str, defid: int, level: int = 1, 
                          quality: int = 1, holes: int = 0, 
                          attributes: Optional[Dict[str, Any]] = None) -> Tuple[bool, str, Optional[EquipmentItem]]:
        """
        为玩家添加装备
        
        Args:
            player_id: 玩家ID
            defid: 装备定义ID
            level: 装备等级
            quality: 装备品质
            holes: 孔位数量
            attributes: 额外属性
            
        Returns:
            Tuple[bool, str, Optional[EquipmentItem]]: (是否成功, 消息, 装备对象)
        """
        try:
            # 使用分布式锁保护玩家操作
            redis_client = self._get_redis_client()
            lock_key = self._get_player_lock_key(player_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                # 创建装备物品
                equipment = EquipmentItem.create_equipment(
                    defid=defid,
                    owner=player_id,
                    level=level,
                    quality=EquipQuality(quality),
                    holes=holes,
                    attributes=attributes
                )
                
                # 保存到数据库
                db_manager = self._get_db_manager()
                saved_equipment = await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                
                # 缓存到Redis
                await self._cache_equipment(player_id, saved_equipment)
                
                logger.info(f"Added equipment {saved_equipment.id} (defid: {defid}) for player {player_id}")
                return True, "添加装备成功", saved_equipment
                
        except Exception as e:
            logger.error(f"Error adding equipment for player {player_id}: {str(e)}")
            return False, f"添加装备失败：{str(e)}", None

    async def remove_equipment(self, player_id: str, item_id: str) -> Tuple[bool, str]:
        """
        移除玩家的装备
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                # 获取装备
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                if equipment.equipment_state == EquipState.EQUIPPED:
                    return False, "请先卸下装备"

                if equipment.equipment_locked:
                    return False, "装备已锁定"

                # 从数据库删除装备
                db_manager = self._get_db_manager()
                success = await Item.delete(player_id, item_id, db_manager.db, db_manager.redis_client)

                if success:
                    # 清除缓存
                    await self._invalidate_equipment_cache(player_id, item_id)
                    
                    # 从玩家装备列表中移除
                    player_equipments_key = self._get_player_equipments_key(player_id)
                    await redis_client.srem(player_equipments_key, item_id)
                    
                    logger.info(f"Removed equipment {item_id} for player {player_id}")
                    return True, "删除成功"
                else:
                    return False, "删除失败"
                    
        except Exception as e:
            logger.error(f"Error removing equipment for player {player_id}: {str(e)}")
            return False, f"删除失败：{str(e)}"

    async def get_equipment(self, player_id: str, item_id: str) -> Optional[EquipmentItem]:
        """获取玩家的指定装备"""
        try:
            # 先从缓存获取
            equipment = await self._get_cached_equipment(player_id, item_id)
            if equipment:
                return equipment
            
            # 缓存未命中，从数据库加载
            equipment = await self._load_equipment_from_db(player_id, item_id)
            return equipment
            
        except Exception as e:
            logger.error(f"Error getting equipment {item_id} for player {player_id}: {str(e)}")
            return None

    async def get_equipment_by_part(self, player_id: str, part: str) -> Optional[EquipmentItem]:
        """根据部位获取玩家已穿戴的装备"""
        try:
            redis_client = self._get_redis_client()
            player_equipped_key = self._get_player_equipped_key(player_id)
            
            item_id = await redis_client.hget(player_equipped_key, part)
            if item_id:
                return await self.get_equipment(player_id, item_id)
            return None
            
        except Exception as e:
            logger.error(f"Error getting equipment by part {part} for player {player_id}: {str(e)}")
            return None

    async def get_all_equipments(self, player_id: str) -> List[EquipmentItem]:
        """获取玩家的所有装备"""
        try:
            redis_client = self._get_redis_client()
            player_equipments_key = self._get_player_equipments_key(player_id)
            
            # 获取玩家所有装备ID
            item_ids = await redis_client.smembers(player_equipments_key)
            
            equipments = []
            for item_id in item_ids:
                equipment = await self.get_equipment(player_id, item_id)
                if equipment:
                    equipments.append(equipment)
            
            return equipments
            
        except Exception as e:
            logger.error(f"Error getting all equipments for player {player_id}: {str(e)}")
            return []

    async def get_equipped_equipments(self, player_id: str) -> List[EquipmentItem]:
        """获取玩家所有已穿戴的装备"""
        try:
            redis_client = self._get_redis_client()
            player_equipped_key = self._get_player_equipped_key(player_id)
            
            # 获取所有已穿戴的装备
            equipped_items = await redis_client.hgetall(player_equipped_key)
            
            equipments = []
            for part, item_id in equipped_items.items():
                equipment = await self.get_equipment(player_id, item_id)
                if equipment:
                    equipments.append(equipment)
            
            return equipments
            
        except Exception as e:
            logger.error(f"Error getting equipped equipments for player {player_id}: {str(e)}")
            return []

    async def equip(self, player_id: str, item_id: str) -> Tuple[bool, str]:
        """
        穿戴装备
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 获取装备部位
                part = equipment.get_part()
                if not part or part == "unknown":
                    return False, "装备部位信息无效"

                # 如果该部位已经有装备，先卸下
                current_equipment = await self.get_equipment_by_part(player_id, part)
                if current_equipment:
                    await self.unequip(player_id, current_equipment.id)

                # 穿戴装备
                success, msg = equipment.equip()
                if success:
                    # 更新Redis中的已穿戴装备
                    player_equipped_key = self._get_player_equipped_key(player_id)
                    await redis_client.hset(player_equipped_key, part, item_id)
                    await redis_client.expire(player_equipped_key, 3600)
                    
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} equipped {item_id} to {part}")
                    return True, f"穿戴成功到{part}"
                else:
                    return False, msg
                    
        except Exception as e:
            logger.error(f"Error equipping equipment for player {player_id}: {str(e)}")
            return False, f"穿戴失败：{str(e)}"

    async def unequip(self, player_id: str, item_id: str) -> Tuple[bool, str]:
        """
        卸下装备
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 卸下装备
                success, msg = equipment.unequip()
                if success:
                    # 从Redis中移除已穿戴装备
                    part = equipment.get_part()
                    player_equipped_key = self._get_player_equipped_key(player_id)
                    await redis_client.hdel(player_equipped_key, part)
                    
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} unequipped {item_id} from {part}")
                    return True, "卸下成功"
                else:
                    return False, msg
                    
        except Exception as e:
            logger.error(f"Error unequipping equipment for player {player_id}: {str(e)}")
            return False, f"卸下失败：{str(e)}"

    async def get_total_properties(self, player_id: str) -> Dict[str, int]:
        """
        计算玩家所有已穿戴装备的总属性
        
        Args:
            player_id: 玩家ID
            
        Returns:
            Dict[str, int]: 属性总和
        """
        try:
            total_props = {}
            equipped_equipments = await self.get_equipped_equipments(player_id)
            
            for equipment in equipped_equipments:
                equip_props = equipment.get_total_properties()
                for key, value in equip_props.items():
                    total_props[key] = total_props.get(key, 0) + value
            
            return total_props
            
        except Exception as e:
            logger.error(f"Error calculating total properties for player {player_id}: {str(e)}")
            return {}

    async def intensify_equipment(self, player_id: str, item_id: str, 
                                use_blessing_stone: bool = False) -> Tuple[bool, str]:
        """
        强化装备
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            use_blessing_stone: 是否使用祝福石
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 强化装备
                success, msg = equipment.intensify(use_blessing_stone)
                if success:
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} intensified equipment {item_id} to level {equipment.equipment_level}")
                
                return success, msg
                
        except Exception as e:
            logger.error(f"Error intensifying equipment for player {player_id}: {str(e)}")
            return False, f"强化失败：{str(e)}"

    async def upgrade_star(self, player_id: str, item_id: str) -> Tuple[bool, str]:
        """
        装备升星
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 升星装备
                success, msg = equipment.upgrade_star()
                if success:
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} upgraded star for equipment {item_id} to {equipment.equipment_star}")
                
                return success, msg
                
        except Exception as e:
            logger.error(f"Error upgrading star for player {player_id}: {str(e)}")
            return False, f"升星失败：{str(e)}"

    async def inlay_rune(self, player_id: str, item_id: str, 
                        rune_def_id: int, hole_index: int) -> Tuple[bool, str]:
        """
        镶嵌符文
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            rune_def_id: 符文定义ID
            hole_index: 孔位索引
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 镶嵌符文
                success, msg = equipment.inlay_rune(rune_def_id, hole_index)
                if success:
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} inlayed rune {rune_def_id} to equipment {item_id} at hole {hole_index}")
                
                return success, msg
                
        except Exception as e:
            logger.error(f"Error inlaying rune for player {player_id}: {str(e)}")
            return False, f"镶嵌失败：{str(e)}"

    async def remove_rune(self, player_id: str, item_id: str, 
                         hole_index: int) -> Tuple[bool, str]:
        """
        移除符文
        
        Args:
            player_id: 玩家ID
            item_id: 装备ID
            hole_index: 孔位索引
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 使用分布式锁保护装备操作
            redis_client = self._get_redis_client()
            lock_key = self._get_equipment_lock_key(player_id, item_id)
            lock = DistributedLock(redis_client, lock_key, ttl=30)
            
            async with lock:
                equipment = await self.get_equipment(player_id, item_id)
                if not equipment:
                    return False, "装备不存在"

                # 移除符文
                success, msg = equipment.remove_rune(hole_index)
                if success:
                    # 更新缓存
                    await self._cache_equipment(player_id, equipment)
                    
                    # 更新数据库
                    db_manager = self._get_db_manager()
                    await equipment.save_equipment(db_manager.db, db_manager.redis_client)
                    
                    logger.info(f"Player {player_id} removed rune from equipment {item_id} at hole {hole_index}")
                
                return success, msg
                
        except Exception as e:
            logger.error(f"Error removing rune for player {player_id}: {str(e)}")
            return False, f"移除失败：{str(e)}"

    async def get_serialized_data(self, player_id: str) -> Dict[str, Any]:
        """
        获取玩家的序列化装备数据，用于发送给客户端
        
        Args:
            player_id: 玩家ID
            
        Returns:
            Dict[str, Any]: 序列化的数据
        """
        try:
            equipments = await self.get_all_equipments(player_id)
            equipped_equipments = await self.get_equipped_equipments(player_id)
            
            equipments_data = [equip.serialize_equipment() for equip in equipments]
            
            # 构建已穿戴装备映射
            equipped_data = {}
            for equip in equipped_equipments:
                part = equip.get_part()
                if part and part != "unknown":
                    equipped_data[part] = equip.id
            
            total_properties = await self.get_total_properties(player_id)
            
            return {
                "equipments": equipments_data,
                "equipped": equipped_data,
                "total_properties": total_properties,
                "equipment_count": len(equipments),
                "equipped_count": len(equipped_data)
            }
            
        except Exception as e:
            logger.error(f"Error getting serialized data for player {player_id}: {str(e)}")
            return {
                "equipments": [],
                "equipped": {},
                "total_properties": {},
                "equipment_count": 0,
                "equipped_count": 0
            }

    async def get_equipment_summary(self, player_id: str) -> Dict[str, Any]:
        """
        获取玩家的装备摘要信息
        
        Args:
            player_id: 玩家ID
            
        Returns:
            Dict[str, Any]: 摘要信息
        """
        try:
            equipments = await self.get_all_equipments(player_id)
            equipped_equipments = await self.get_equipped_equipments(player_id)
            
            if not equipments:
                return {
                    "total_equipments": 0,
                    "equipped_count": 0,
                    "total_level": 0,
                    "total_star": 0,
                    "average_quality": 0,
                    "total_properties": {}
                }
            
            total_level = sum(equip.equipment_level for equip in equipments)
            total_star = sum(equip.equipment_star for equip in equipments)
            avg_quality = sum(equip.equipment_quality.value for equip in equipments) / len(equipments)
            
            return {
                "total_equipments": len(equipments),
                "equipped_count": len(equipped_equipments),
                "total_level": total_level,
                "total_star": total_star,
                "average_quality": round(avg_quality, 2),
                "total_properties": await self.get_total_properties(player_id)
            }
            
        except Exception as e:
            logger.error(f"Error getting equipment summary for player {player_id}: {str(e)}")
            return {
                "total_equipments": 0,
                "equipped_count": 0,
                "total_level": 0,
                "total_star": 0,
                "average_quality": 0,
                "total_properties": {}
            }

    async def clear_player_cache(self, player_id: str):
        """
        清除玩家的装备缓存
        
        Args:
            player_id: 玩家ID
        """
        try:
            redis_client = self._get_redis_client()
            
            # 获取玩家所有装备ID
            player_equipments_key = self._get_player_equipments_key(player_id)
            item_ids = await redis_client.smembers(player_equipments_key)
            
            # 清除所有装备缓存
            for item_id in item_ids:
                equipment_key = self._get_equipment_key(player_id, item_id)
                await redis_client.delete(equipment_key)
            
            # 清除玩家装备列表和已穿戴装备
            await redis_client.delete(player_equipments_key)
            await redis_client.delete(self._get_player_equipped_key(player_id))
            
            logger.info(f"Cleared equipment cache for player {player_id}")
            
        except Exception as e:
            logger.error(f"Error clearing equipment cache for player {player_id}: {str(e)}")

    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计
        """
        try:
            redis_client = self._get_redis_client()
            
            # 获取所有玩家装备列表键
            player_keys = await redis_client.keys("player_equipments:*")
            total_players = len(player_keys)
            
            total_equipments = 0
            total_equipped = 0
            
            for player_key in player_keys:
                player_id = player_key.split(":")[1]
                
                # 统计装备数量
                equipments = await redis_client.smembers(player_key)
                total_equipments += len(equipments)
                
                # 统计已穿戴装备数量
                equipped_key = self._get_player_equipped_key(player_id)
                equipped_count = await redis_client.hlen(equipped_key)
                total_equipped += equipped_count
            
            return {
                "total_players": total_players,
                "total_equipments": total_equipments,
                "total_equipped": total_equipped,
                "worker_id": self._worker_id
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {
                "total_players": 0,
                "total_equipments": 0,
                "total_equipped": 0,
                "worker_id": self._worker_id
            }

# 全局分布式装备服务实例
distributed_equipment_service = DistributedEquipmentService()

# 示例用法
if __name__ == '__main__':
    async def main():
        # 使用分布式装备服务
        service = distributed_equipment_service
        
        # 初始化服务
        await service.initialize()
        
        # 为不同玩家管理装备
        player1 = "player_001"
        player2 = "player_002"
        
        # 为玩家1添加装备
        success, msg, equipment = await service.add_equipment(
            player1, defid=1001, level=1, quality=2, holes=3
        )
        
        if success:
            print(f"为玩家 {player1} 添加装备成功")
            
            # 穿戴装备
            success, msg = await service.equip(player1, equipment.id)
            print(f"穿戴结果: {msg}")
            
            # 获取装备数据
            data = await service.get_serialized_data(player1)
            print(f"玩家 {player1} 的装备数据: {data}")
        
        # 为玩家2添加装备
        success, msg, equipment2 = await service.add_equipment(
            player2, defid=1002, level=5, quality=3, holes=2
        )
        
        if success:
            print(f"为玩家 {player2} 添加装备成功")
            
            # 获取缓存统计
            stats = await service.get_cache_stats()
            print(f"缓存统计: {stats}")

    # 运行示例
    asyncio.run(main()) 