"""
邮件系统缓存管理器
处理邮件相关的Redis缓存操作和分布式锁
"""

import json
import logging
from typing import Optional, List, Dict, Any
from redis_manager import RedisManager
from distributed_lock import DistributedLock
from mail_models import Mail, MailAttachment, MailListItem

logger = logging.getLogger(__name__)


class MailCacheManager:
    """邮件缓存管理器"""
    
    _instance = None
    _lock = None
    
    # 缓存键前缀
    MAIL_LIST_PREFIX = "mail:list:"           # 玩家邮件列表
    MAIL_INFO_PREFIX = "mail:info:"           # 邮件详情
    MAIL_ATTACHMENTS_PREFIX = "mail:attach:"  # 邮件附件
    UNREAD_COUNT_PREFIX = "mail:unread:"      # 未读邮件数量
    MAIL_LOCK_PREFIX = "mail:lock:"           # 邮件操作锁
    
    # 缓存过期时间（秒）
    MAIL_LIST_TTL = 1800        # 邮件列表缓存30分钟
    MAIL_INFO_TTL = 3600        # 邮件详情缓存1小时
    ATTACHMENTS_TTL = 3600      # 附件缓存1小时
    UNREAD_COUNT_TTL = 300      # 未读数量缓存5分钟
    
    def __init__(self):
        self.redis_client = None
    
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            if cls._lock is None:
                import asyncio
                cls._lock = asyncio.Lock()
            
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    logger.info("MailCacheManager实例已创建")
        
        return cls._instance
    
    async def _get_redis(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            redis_manager = await RedisManager.get_instance()
            self.redis_client = await redis_manager.get_redis()
        return self.redis_client
    
    # ==================== 邮件列表缓存 ====================
    
    async def get_cached_mail_list(self, player_id: str, page: int, limit: int) -> Optional[List[MailListItem]]:
        """获取缓存的邮件列表"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_LIST_PREFIX}{player_id}:{page}:{limit}"
            value = await redis.get(key)
            
            if value:
                mails_data = json.loads(value)
                return [MailListItem(**mail_data) for mail_data in mails_data]
            return None
            
        except Exception as e:
            logger.error(f"获取缓存邮件列表时发生错误: {str(e)}")
            return None
    
    async def cache_mail_list(self, player_id: str, mails: List[MailListItem], page: int, limit: int):
        """缓存邮件列表"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_LIST_PREFIX}{player_id}:{page}:{limit}"
            
            mails_data = [mail.to_dict() for mail in mails]
            value = json.dumps(mails_data, ensure_ascii=False)
            
            await redis.setex(key, self.MAIL_LIST_TTL, value)
            
        except Exception as e:
            logger.error(f"缓存邮件列表时发生错误: {str(e)}")
    
    async def invalidate_mail_list(self, player_id: str):
        """清除邮件列表缓存"""
        try:
            redis = await self._get_redis()
            pattern = f"{self.MAIL_LIST_PREFIX}{player_id}:*"
            
            keys = await redis.keys(pattern)
            if keys:
                await redis.delete(*keys)
                logger.debug(f"清除邮件列表缓存: {len(keys)} 个键")
            
        except Exception as e:
            logger.error(f"清除邮件列表缓存时发生错误: {str(e)}")
    
    # ==================== 邮件详情缓存 ====================
    
    async def get_cached_mail_info(self, mail_id: str) -> Optional[Mail]:
        """获取缓存的邮件详情"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_INFO_PREFIX}{mail_id}"
            value = await redis.get(key)
            
            if value:
                mail_data = json.loads(value)
                return Mail.from_dict(mail_data)
            return None
            
        except Exception as e:
            logger.error(f"获取缓存邮件详情时发生错误: {str(e)}")
            return None
    
    async def cache_mail_info(self, mail: Mail):
        """缓存邮件详情"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_INFO_PREFIX}{mail.mail_id}"
            
            value = json.dumps(mail.to_dict(), ensure_ascii=False)
            await redis.setex(key, self.MAIL_INFO_TTL, value)
            
        except Exception as e:
            logger.error(f"缓存邮件详情时发生错误: {str(e)}")
    
    async def invalidate_mail_info(self, mail_id: str):
        """清除邮件详情缓存"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_INFO_PREFIX}{mail_id}"
            await redis.delete(key)
            
        except Exception as e:
            logger.error(f"清除邮件详情缓存时发生错误: {str(e)}")
    
    # ==================== 附件缓存 ====================
    
    async def get_cached_mail_attachments(self, mail_id: str) -> Optional[List[MailAttachment]]:
        """获取缓存的邮件附件"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_ATTACHMENTS_PREFIX}{mail_id}"
            value = await redis.get(key)
            
            if value:
                attachments_data = json.loads(value)
                return [MailAttachment.from_dict(attachment_data) for attachment_data in attachments_data]
            return None
            
        except Exception as e:
            logger.error(f"获取缓存邮件附件时发生错误: {str(e)}")
            return None
    
    async def cache_mail_attachments(self, mail_id: str, attachments: List[MailAttachment]):
        """缓存邮件附件"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_ATTACHMENTS_PREFIX}{mail_id}"
            
            attachments_data = [attachment.to_dict() for attachment in attachments]
            value = json.dumps(attachments_data, ensure_ascii=False)
            
            await redis.setex(key, self.ATTACHMENTS_TTL, value)
            
        except Exception as e:
            logger.error(f"缓存邮件附件时发生错误: {str(e)}")
    
    async def invalidate_mail_attachments(self, mail_id: str):
        """清除邮件附件缓存"""
        try:
            redis = await self._get_redis()
            key = f"{self.MAIL_ATTACHMENTS_PREFIX}{mail_id}"
            await redis.delete(key)
            
        except Exception as e:
            logger.error(f"清除邮件附件缓存时发生错误: {str(e)}")
    
    # ==================== 未读数量缓存 ====================
    
    async def get_cached_unread_count(self, player_id: str) -> Optional[int]:
        """获取缓存的未读邮件数量"""
        try:
            redis = await self._get_redis()
            key = f"{self.UNREAD_COUNT_PREFIX}{player_id}"
            value = await redis.get(key)
            
            if value is not None:
                return int(value)
            return None
            
        except Exception as e:
            logger.error(f"获取缓存未读邮件数量时发生错误: {str(e)}")
            return None
    
    async def cache_unread_count(self, player_id: str, count: int):
        """缓存未读邮件数量"""
        try:
            redis = await self._get_redis()
            key = f"{self.UNREAD_COUNT_PREFIX}{player_id}"
            
            await redis.setex(key, self.UNREAD_COUNT_TTL, str(count))
            
        except Exception as e:
            logger.error(f"缓存未读邮件数量时发生错误: {str(e)}")
    
    async def invalidate_unread_count(self, player_id: str):
        """清除未读邮件数量缓存"""
        try:
            redis = await self._get_redis()
            key = f"{self.UNREAD_COUNT_PREFIX}{player_id}"
            await redis.delete(key)
            
        except Exception as e:
            logger.error(f"清除未读邮件数量缓存时发生错误: {str(e)}")
    
    # ==================== 分布式锁 ====================
    
    async def acquire_mail_lock(self, player_id: str, operation: str, timeout: int = 30) -> DistributedLock:
        """获取邮件操作锁"""
        try:
            redis = await self._get_redis()
            lock_key = f"{self.MAIL_LOCK_PREFIX}{player_id}:{operation}"
            
            lock = DistributedLock(redis, lock_key, timeout)
            return lock
            
        except Exception as e:
            logger.error(f"获取邮件操作锁时发生错误: {str(e)}")
            raise
    
    # ==================== 批量缓存清理 ====================
    
    async def invalidate_all_mail_cache(self, player_id: str):
        """清除玩家所有邮件相关缓存"""
        try:
            await self.invalidate_mail_list(player_id)
            await self.invalidate_unread_count(player_id)
            logger.debug(f"清除玩家所有邮件缓存: {player_id}")
            
        except Exception as e:
            logger.error(f"清除玩家所有邮件缓存时发生错误: {str(e)}")
    
    async def invalidate_mail_and_attachments(self, mail_id: str):
        """清除邮件及其附件缓存"""
        try:
            await self.invalidate_mail_info(mail_id)
            await self.invalidate_mail_attachments(mail_id)
            logger.debug(f"清除邮件及附件缓存: {mail_id}")
            
        except Exception as e:
            logger.error(f"清除邮件及附件缓存时发生错误: {str(e)}")
