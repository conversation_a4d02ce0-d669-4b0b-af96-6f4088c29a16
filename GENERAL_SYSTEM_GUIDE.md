# 武将系统使用指南

## 概述

武将系统是基于现有分布式架构实现的服务器权威武将管理系统，支持多worker环境下的并发操作，提供完整的武将生命周期管理功能。系统已集成现有的配置系统，使用 `cfg_career.json`、`cfg_cardsPackage.json` 和 `cfg_cardsExpect.json` 配置文件来管理武将模板、卡包和抽卡池。

## 系统架构

### 核心组件

1. **数据模型层** (`general_models.py`)
   - 武将数据模型
   - 属性计算模型
   - 装备管理模型
   - 技能管理模型
   - 阵容管理模型

2. **服务层** (`general_service_distributed.py`)
   - 分布式武将服务
   - 业务逻辑处理
   - 缓存管理
   - 分布式锁保护

3. **消息处理层** (`general_handlers_distributed.py`)
   - WebSocket消息处理器
   - 参数验证
   - 错误处理
   - 响应格式化

4. **缓存层** (`game_database.py`)
   - 武将缓存管理器
   - 分布式缓存一致性
   - 缓存失效策略

5. **配置系统** (`config.py`)
   - 游戏配置管理
   - 武将模板配置
   - 卡包和卡池配置
   - 自动配置监控和重载

### 技术特性

- **分布式锁保护**: 使用Redis实现分布式锁，确保并发安全
- **缓存一致性**: 多层缓存策略，保证数据一致性
- **多worker支持**: 支持多进程环境下的并发操作
- **服务器权威**: 所有武将操作都在服务器端进行验证和处理
- **配置系统集成**: 使用现有配置系统管理武将数据

## 数据库设计

### MongoDB集合

1. **generals** - 武将数据
   ```javascript
   {
     "_id": ObjectId,
     "general_id": "uuid",
     "player_id": "player123",
     "template_id": "warrior_001",
     "name": "关羽",
     "avatar": "guanyu.png",
     "general_type": "warrior",
     "rarity": "legendary",
     "level": 50,
     "exp": 1250,
     "exp_max": 1500,
     "star": 5,
     "awaken_level": 2,
     "state": "idle",
     "is_locked": false,
     "deploy_position": null,
     "properties": {
       "hp": 5000,
       "attack": 800,
       "defense": 400,
       "speed": 120,
       "intelligence": 80,
       "leadership": 90
     },
     "equipment": {
       "weapon_id": "sword_001",
       "armor_id": "armor_001",
       "accessory_id": "ring_001"
     },
     "skills": [...],
     "soldiers": [...],
     "created_at": ISODate,
     "updated_at": ISODate
   }
   ```

2. **formations** - 阵容数据
   ```javascript
   {
     "_id": ObjectId,
     "player_id": "player123",
     "formation_id": "formation_001",
     "formation_name": "主力阵容",
     "generals": [
       {
         "general_id": "general_001",
         "position": 0
       }
     ],
     "is_active": true,
     "created_at": ISODate,
     "updated_at": ISODate
   }
   ```

3. **general_templates** - 武将模板
   ```javascript
   {
     "_id": ObjectId,
     "template_id": "warrior_001",
     "name": "关羽",
     "avatar": "guanyu.png",
     "general_type": "warrior",
     "rarity": "legendary",
     "base_properties": {
       "hp": 1000,
       "attack": 100,
       "defense": 50,
       "speed": 80,
       "intelligence": 60,
       "leadership": 70
     },
     "base_skills": [...]
   }
   ```

4. **general_draw_pools** - 抽卡池配置（已废弃，现在使用配置文件）
   ```javascript
   {
     "_id": ObjectId,
     "pool_type": "normal",
     "pool_name": "普通抽卡池",
     "templates": [
       {
         "template_id": "warrior_001",
         "name": "关羽",
         "weight": 10,
         "rarity": "legendary"
       }
     ]
   }
   ```

### 配置文件设计

系统现在使用配置文件来管理武将模板、卡包和卡池，不再依赖数据库存储：

1. **武将模板配置** (`config/game/cfg_career.json`)
   ```json
   {
     "id": 1000,
     "name": "吕布",
     "paramsid": 1,
     "kind": 1,
     "race": 4,
     "soldier": 2,
     "star": 5,
     "ani": 1,
     "skill": 2000,
     "pos": "物",
     "baseid": 1000
   }
   ```

2. **卡包配置** (`config/game/cfg_cardsPackage.json`)
   ```json
   {
     "id": 1,
     "name": "名将",
     "cfg_id": 100,
     "icon": "faces-004",
     "kindtype": "main",
     "expact": "5:5,4:10,3:85",
     "pulic_luck": "5|4,5",
     "guard": 40,
     "item_id": 82001,
     "ec_item_num": 1314320,
     "free_time": 39600
   }
   ```

3. **卡池配置** (`config/game/cfg_cardsExpect.json`)
   ```json
   {
     "id": 1,
     "pack_id": 100,
     "role_id": 1000,
     "expect": 20,
     "conditions": 0
   }
   ```

### 索引设计

```javascript
// generals集合索引
db.generals.createIndex({"player_id": 1})
db.generals.createIndex({"general_id": 1})
db.generals.createIndex({"player_id": 1, "general_id": 1})
db.generals.createIndex({"template_id": 1})
db.generals.createIndex({"state": 1})
db.generals.createIndex({"created_at": 1})

// formations集合索引
db.formations.createIndex({"player_id": 1})
db.formations.createIndex({"formation_id": 1})

// general_templates集合索引
db.general_templates.createIndex({"template_id": 1})
db.general_templates.createIndex({"general_type": 1})
db.general_templates.createIndex({"rarity": 1})

// general_draw_pools集合索引
db.general_draw_pools.createIndex({"pool_type": 1})
```

## API接口文档

### 1. 获取武将列表

**消息ID**: `GET_GENERALS` (20)

**请求参数**:
```json
{
  "msgId": 20,
  "data": {
    "player_id": "player123"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "generals": [
      {
        "general_id": "general_001",
        "name": "关羽",
        "level": 50,
        "star": 5,
        "rarity": "legendary",
        "fight_power": 15000
      }
    ],
    "count": 1
  }
}
```

### 2. 抽卡

**消息ID**: `DRAW_GENERAL` (21)

**请求参数**:
```json
{
  "msgId": 21,
  "data": {
    "package_id": 1
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_002",
      "name": "吕布_001",
      "level": 1,
      "star": 5,
      "rarity": "legendary"
    },
    "package_id": 1,
    "package_name": "名将",
    "rarity": "legendary",
    "star": 5
  }
}
```

**说明**:
- `package_id`: 卡包ID，对应 `cfg_cardsPackage.json` 中的配置
- 系统会根据卡包ID从 `cfg_cardsExpect.json` 中获取抽卡池
- 根据期望值计算抽取概率
- 从 `cfg_career.json` 中获取武将模板创建武将实例

### 3. 合成武将

**消息ID**: `SYNTHESIS_GENERAL` (22)

**请求参数**:
```json
{
  "msgId": 22,
  "data": {
    "player_id": "player123",
    "general_ids": ["general_001", "general_002"]
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "new_general": {
      "general_id": "general_003",
      "name": "合成武将_001",
      "rarity": "legendary"
    },
    "consumed_generals": ["general_001", "general_002"],
    "old_rarity": "epic",
    "new_rarity": "legendary"
  }
}
```

### 4. 武将升级

**消息ID**: `LEVEL_UP_GENERAL` (23)

**请求参数**:
```json
{
  "msgId": 23,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "exp_amount": 500
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "level": 51,
      "exp": 250
    },
    "level_up_result": {
      "leveled_up": true,
      "new_level": 51,
      "exp_gained": 500,
      "current_exp": 250,
      "exp_max": 1600
    }
  }
}
```

### 5. 武将升星

**消息ID**: `STAR_UP_GENERAL` (24)

**请求参数**:
```json
{
  "msgId": 24,
  "data": {
    "player_id": "player123",
    "general_id": "general_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "star": 6
    },
    "new_star": 6
  }
}
```

### 6. 武将觉醒

**消息ID**: `AWAKEN_GENERAL` (25)

**请求参数**:
```json
{
  "msgId": 25,
  "data": {
    "player_id": "player123",
    "general_id": "general_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "awaken_level": 3
    },
    "new_awaken_level": 3
  }
}
```

### 7. 武将训练

**消息ID**: `TRAIN_GENERAL` (26)

**请求参数**:
```json
{
  "msgId": 26,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "training_type": "attack"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "state": "training"
    },
    "training_type": "attack",
    "training_start_time": "2024-01-01T10:00:00"
  }
}
```

### 8. 上阵武将

**消息ID**: `DEPLOY_GENERAL` (27)

**请求参数**:
```json
{
  "msgId": 27,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "position": 0
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "state": "deployed",
      "deploy_position": 0
    },
    "position": 0
  }
}
```

### 9. 下阵武将

**消息ID**: `RETREAT_GENERAL` (28)

**请求参数**:
```json
{
  "msgId": 28,
  "data": {
    "player_id": "player123",
    "general_id": "general_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "state": "idle",
      "deploy_position": null
    }
  }
}
```

### 10. 获取阵容

**消息ID**: `GET_FORMATION` (29)

**请求参数**:
```json
{
  "msgId": 29,
  "data": {
    "player_id": "player123"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "player_id": "player123",
    "formation_id": "formation_001",
    "formation_name": "主力阵容",
    "generals": [
      {
        "general_id": "general_001",
        "position": 0
      }
    ],
    "is_active": true
  }
}
```

### 11. 保存阵容

**消息ID**: `SAVE_FORMATION` (30)

**请求参数**:
```json
{
  "msgId": 30,
  "data": {
    "player_id": "player123",
    "formation_data": {
      "formation_id": "formation_001",
      "formation_name": "主力阵容",
      "generals": [
        {
          "general_id": "general_001",
          "position": 0
        }
      ],
      "is_active": true
    }
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "player_id": "player123",
    "formation_id": "formation_001",
    "formation_name": "主力阵容",
    "generals": [...],
    "is_active": true,
    "updated_at": "2024-01-01T10:00:00"
  }
}
```

### 12. 装备武将

**消息ID**: `EQUIP_GENERAL` (31)

**请求参数**:
```json
{
  "msgId": 31,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "equipment_type": "weapon",
    "item_id": "sword_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "equipment": {
        "weapon_id": "sword_001",
        "weapon_name": "武器_sword_001"
      }
    },
    "equipment_type": "weapon",
    "item_id": "sword_001"
  }
}
```

### 13. 卸下装备

**消息ID**: `UNEQUIP_GENERAL` (32)

**请求参数**:
```json
{
  "msgId": 32,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "equipment_type": "weapon"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "equipment": {
        "weapon_id": null,
        "weapon_name": null
      }
    },
    "equipment_type": "weapon"
  }
}
```

### 14. 改变武将状态

**消息ID**: `CHANGE_GENERAL_STATE` (33)

**请求参数**:
```json
{
  "msgId": 33,
  "data": {
    "player_id": "player123",
    "general_id": "general_001",
    "new_state": "training"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "state": "training"
    },
    "new_state": "training"
  }
}
```

### 15. 锁定武将

**消息ID**: `LOCK_GENERAL` (34)

**请求参数**:
```json
{
  "msgId": 34,
  "data": {
    "player_id": "player123",
    "general_id": "general_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "is_locked": true
    }
  }
}
```

### 16. 解锁武将

**消息ID**: `UNLOCK_GENERAL` (35)

**请求参数**:
```json
{
  "msgId": 35,
  "data": {
    "player_id": "player123",
    "general_id": "general_001"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "general": {
      "general_id": "general_001",
      "is_locked": false
    }
  }
}
```

## 缓存策略

### 缓存层级

1. **武将列表缓存** (TTL: 1小时)
   - 键: `game:v2:generals:{player_id}`
   - 存储玩家所有武将的基本信息

2. **武将详情缓存** (TTL: 24小时)
   - 键: `game:v2:generals:{player_id}:{general_id}`
   - 存储武将的完整信息

3. **武将属性缓存** (TTL: 10分钟)
   - 键: `game:v2:generals:{player_id}:{general_id}:properties`
   - 存储计算后的武将属性

4. **武将战斗力缓存** (TTL: 10分钟)
   - 键: `game:v2:generals:{player_id}:{general_id}:fight_power`
   - 存储计算后的武将战斗力

5. **阵容缓存** (TTL: 1小时)
   - 键: `game:v2:formation:{player_id}`
   - 存储玩家的阵容信息

### 缓存失效策略

- **写操作**: 更新相关缓存，使计算缓存失效
- **删除操作**: 删除所有相关缓存
- **批量操作**: 使玩家武将列表缓存失效

## 分布式锁机制

### 锁类型

1. **玩家锁**: `general_lock:{player_id}`
   - 用于玩家级别的操作（抽卡、合成、阵容管理）
   - 超时时间: 30秒

2. **武将锁**: `general_lock:{player_id}:{general_id}`
   - 用于单个武将的操作（升级、装备、状态变更）
   - 超时时间: 30秒

### 锁使用场景

- **创建武将**: 玩家锁
- **武将操作**: 武将锁
- **阵容操作**: 玩家锁
- **批量操作**: 玩家锁

## 错误处理

### 常见错误码

1. **参数错误**
   - 缺少必要参数
   - 参数格式错误
   - 参数值无效

2. **权限错误**
   - 武将不属于该玩家
   - 武将状态不允许操作
   - 操作条件不满足

3. **系统错误**
   - 数据库连接失败
   - 缓存操作失败
   - 分布式锁获取失败

### 错误响应格式

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

## 性能优化

### 数据库优化

1. **索引优化**
   - 为常用查询字段创建索引
   - 复合索引优化多字段查询

2. **查询优化**
   - 使用投影减少数据传输
   - 批量操作减少数据库访问

### 缓存优化

1. **缓存预热**
   - 系统启动时预加载热门数据
   - 定期刷新缓存数据

2. **缓存策略**
   - 分层缓存减少计算开销
   - 智能失效策略

### 并发优化

1. **锁粒度优化**
   - 最小化锁范围
   - 避免死锁情况

2. **异步处理**
   - 非关键操作异步执行
   - 批量操作优化

## 部署说明

### 环境要求

- Python 3.8+
- Redis 6.0+
- MongoDB 4.4+
- FastAPI

### 配置项

1. **数据库配置**
   ```ini
   [database]
   mongodb_uri = mongodb://localhost:27017
   redis_uri = redis://localhost:6379
   ```

2. **缓存配置**
   ```ini
   [cache]
   general_list_ttl = 3600
   general_detail_ttl = 86400
   general_properties_ttl = 600
   formation_ttl = 3600
   ```

3. **锁配置**
   ```ini
   [lock]
   general_lock_timeout = 30
   operation_timeout = 25
   ```

### 启动步骤

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **初始化数据库**
   ```bash
   python -m scripts.init_database
   ```

3. **启动服务**
   ```bash
   python game_server.py
   ```

## 扩展性设计

### 插件化架构

1. **技能系统**
   - 可扩展的技能效果
   - 技能组合机制

2. **装备系统**
   - 装备套装效果
   - 装备强化系统

3. **武将进化**
   - 多阶段进化
   - 特殊进化条件

### 配置驱动

1. **武将模板**
   - 配置文件定义武将属性
   - 动态加载武将模板

2. **抽卡池配置**
   - 配置文件定义抽卡概率
   - 动态调整抽卡池

3. **升级规则**
   - 配置文件定义升级经验
   - 动态调整升级曲线

## 监控和日志

### 日志级别

- **DEBUG**: 详细调试信息
- **INFO**: 一般操作信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息

### 监控指标

1. **性能指标**
   - 响应时间
   - 并发数
   - 错误率

2. **业务指标**
   - 武将创建数
   - 抽卡次数
   - 升级次数

3. **系统指标**
   - 缓存命中率
   - 数据库连接数
   - 内存使用量

## 测试指南

### 单元测试

```python
import pytest
from general_service_distributed import GeneralServiceDistributed

@pytest.mark.asyncio
async def test_create_general():
    service = GeneralServiceDistributed(db_manager)
    result = await service.create_general("player123", "warrior_001", "关羽")
    assert result.success == True
    assert result.data["name"] == "关羽"
```

### 集成测试

```python
@pytest.mark.asyncio
async def test_general_lifecycle():
    # 创建武将
    # 升级武将
    # 装备武将
    # 上阵武将
    # 下阵武将
    pass
```

### 压力测试

```python
@pytest.mark.asyncio
async def test_concurrent_operations():
    # 并发创建武将
    # 并发升级武将
    # 并发装备武将
    pass
```

## 常见问题

### Q1: 如何处理武将数据不一致？

A: 系统使用分布式锁和缓存失效机制确保数据一致性。如果发现不一致，可以调用缓存失效接口强制刷新。

### Q2: 如何优化大量武将的查询性能？

A: 使用分页查询，结合索引优化。对于大量数据，考虑使用聚合管道进行预计算。

### Q3: 如何处理分布式环境下的并发冲突？

A: 使用Redis分布式锁，设置合理的超时时间，避免死锁情况。

### Q4: 如何扩展武将属性系统？

A: 在`GeneralProperties`模型中添加新属性，更新属性计算逻辑，扩展战斗力计算公式。

### Q5: 如何实现武将技能系统？

A: 在`GeneralSkill`模型中定义技能效果，实现技能触发逻辑，支持技能组合和连击。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础武将CRUD功能
- 抽卡和合成系统
- 装备和阵容管理
- 分布式锁和缓存支持

### 计划功能
- 武将技能系统
- 武将进化系统
- 武将羁绊系统
- 武将皮肤系统
- 武将排行榜

## 配置系统使用指南

### 配置文件管理

系统使用现有的配置系统来管理武将相关的数据，不再依赖数据库存储模板和抽卡池配置。

#### 1. 武将模板配置

**文件路径**: `config/game/cfg_career.json`

**配置示例**:
```json
{
  "id": 1000,
  "name": "吕布",
  "paramsid": 1,
  "kind": 1,
  "race": 4,
  "soldier": 2,
  "star": 5,
  "ani": 1,
  "skill": 2000,
  "pos": "物",
  "baseid": 1000
}
```

**字段说明**:
- `id`: 武将模板ID
- `name`: 武将名称
- `paramsid`: 参数ID，用于计算属性
- `star`: 星级，决定稀有度
- `ani`: 动画ID
- `skill`: 技能ID
- `pos`: 定位

#### 2. 卡包配置

**文件路径**: `config/game/cfg_cardsPackage.json`

**配置示例**:
```json
{
  "id": 1,
  "name": "名将",
  "cfg_id": 100,
  "icon": "faces-004",
  "kindtype": "main",
  "expact": "5:5,4:10,3:85",
  "pulic_luck": "5|4,5",
  "guard": 40,
  "item_id": 82001,
  "ec_item_num": 1314320,
  "free_time": 39600
}
```

**字段说明**:
- `id`: 卡包ID
- `name`: 卡包名称
- `cfg_id`: 配置ID
- `expact`: 期望值配置（格式：星级:概率,星级:概率）
- `item_id`: 消耗道具ID
- `ec_item_num`: 消耗道具数量

#### 3. 卡池配置

**文件路径**: `config/game/cfg_cardsExpect.json`

**配置示例**:
```json
{
  "id": 1,
  "pack_id": 100,
  "role_id": 1000,
  "expect": 20,
  "conditions": 0
}
```

**字段说明**:
- `id`: 配置ID
- `pack_id`: 卡包ID
- `role_id`: 武将模板ID
- `expect`: 期望值（抽取概率权重）
- `conditions`: 抽取条件

### 配置API使用

#### 获取武将模板

```python
from config import config

# 获取所有武将模板
templates = config.get_general_templates()

# 根据ID获取武将模板
template = config.get_general_template_by_id(1000)

# 根据稀有度获取武将模板
rare_templates = config.get_general_templates_by_rarity(5)
```

#### 获取卡包配置

```python
# 获取所有卡包
packages = config.get_card_packages()

# 根据ID获取卡包
package = config.get_card_package_by_id(1)
```

#### 获取抽卡池

```python
# 获取指定卡包的抽卡池
draw_pool = config.get_draw_pool_by_package(1)

# 获取卡包的所有期望值配置
expectations = config.get_card_expectations_by_package(1)
```

### 配置热重载

系统支持配置文件的自动监控和热重载：

```python
# 重新加载指定配置
config.reload_game_config("cfg_career")

# 重新加载所有配置
config.reload_game_config()

# 启动配置监控（自动重载）
await config.monitor_game_configs(interval=60)
```

### 配置验证

在修改配置文件时，请确保：

1. **JSON格式正确**: 使用JSON验证工具检查语法
2. **ID唯一性**: 确保武将模板ID、卡包ID等唯一
3. **数据完整性**: 确保卡池配置中的武将模板存在
4. **概率合理性**: 确保抽卡概率总和合理

### 配置最佳实践

1. **版本控制**: 将配置文件纳入版本控制
2. **备份策略**: 定期备份配置文件
3. **测试环境**: 在测试环境验证配置变更
4. **灰度发布**: 逐步发布配置变更
5. **监控告警**: 监控配置加载状态

### 故障排除

#### 配置加载失败

1. 检查文件路径是否正确
2. 验证JSON格式是否有效
3. 确认文件编码为UTF-8
4. 检查文件权限

#### 抽卡异常

1. 验证卡包ID是否存在
2. 检查卡池配置是否完整
3. 确认武将模板是否存在
4. 验证期望值配置是否合理

#### 配置重载失败

1. 检查文件是否被占用
2. 验证新配置格式是否正确
3. 确认有足够的系统权限
4. 查看错误日志获取详细信息 