import ast
import re
import os
from pathlib import Path
from typing import Dict, List, Tuple
import argparse

def parse_comments(source: str) -> Dict[int, str]:
    """解析源代码中的行注释，返回行号到注释的映射。"""
    comments = {}
    lines = source.splitlines()
    for i, line in enumerate(lines, 1):
        comment_pos = line.find("#")
        if comment_pos != -1:
            comment = line[comment_pos + 1:].strip()
            if comment:
                comments[i] = comment
    return comments

def parse_enums(file_path: str) -> List[Tuple[str, str, List[Tuple[str, str, str]]]]:
    """解析 Python 文件中的枚举定义，返回 (类名, 基类, [(成员名, 值, 注释)])。"""
    file_path = Path(file_path)
    # 如果不是绝对路径，尝试相对于脚本目录解析
    if not file_path.is_absolute():
        script_dir = Path(__file__).parent
        file_path = script_dir / file_path
    file_path = file_path.resolve()
    
    if not file_path.exists():
        raise FileNotFoundError(
            f"输入文件未找到: {file_path}\n"
            f"请确保文件存在或提供完整路径（如 D:\\python_evns\\game_server\\enums.py）"
        )
    
    with open(file_path, "r", encoding="utf-8") as f:
        source = f.read()
    
    comments = parse_comments(source)
    tree = ast.parse(source)
    enums = []

    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            base_class = next((b.id for b in node.bases if isinstance(b, ast.Name)), None)
            if base_class not in ("IntEnum", "str"):
                continue

            members = []
            for item in node.body:
                if isinstance(item, ast.Assign):
                    name = item.targets[0].id
                    value = None
                    if isinstance(item.value, ast.Num):
                        value = str(item.value.n)
                    elif isinstance(item.value, ast.Str):
                        value = f'"{item.value.s}"'
                    elif isinstance(item.value, ast.Call) and item.value.func.id == "auto":
                        value = "auto()"
                    comment = comments.get(item.lineno, "")
                    members.append((name, value, comment))
            
            enums.append((node.name, base_class, members))
    
    return enums

def generate_lua_table(enum_name: str, base_class: str, members: List[Tuple[str, str, str]], auto_counter: int) -> Tuple[str, int]:
    """将枚举转换为 Lua 表，处理 auto() 值。"""
    lines = [f"{enum_name} = {{"]
    current_auto = auto_counter

    for name, value, comment in members:
        if value == "auto()":
            value = str(current_auto)
            current_auto += 1
        comment_str = f" -- {comment}" if comment else ""
        lines.append(f"    {name} = {value},{comment_str}")
    
    lines.append("}")
    return "\n".join(lines), current_auto

def convert_to_lua(input_file: str, output_dir: str = ".", output_file: str = "enums.lua"):
    """将 Python 枚举文件转换为 Lua 文件，输出到指定目录。"""
    # 解析输入文件路径
    input_file = Path(input_file)
    
    # 确保输出目录存在
    output_dir_path = Path(output_dir).resolve()
    output_dir_path.mkdir(parents=True, exist_ok=True)
    
    # 构建输出文件路径
    output_path = output_dir_path / output_file

    print(f"正在转换 {input_file} 到 {output_path}...")
    
    enums = parse_enums(input_file)
    if not enums:
        print("输入文件中未找到枚举定义。")
        return

    lua_content = ["-- 自动生成的 Lua 枚举，从 Python 枚举转换而来"]

    auto_counter = 1  # 用于处理 auto() 的递增计数
    for enum_name, base_class, members in enums:
        lua_table, auto_counter = generate_lua_table(enum_name, base_class, members, auto_counter)
        lua_content.append(lua_table)
        lua_content.append("")  # 添加空行以提高可读性

    with open(output_path, "w", encoding="utf-8") as f:
        f.write("\n".join(lua_content))
    print(f"Lua 文件已生成: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="将 Python 枚举转换为 Lua 表")
    parser.add_argument("--input-file", default="enums.py", help="包含枚举的 Python 输入文件")
    parser.add_argument("--output-dir", default=".", help="生成的 Lua 文件输出目录")
    parser.add_argument("--output-file", default="enums.lua", help="生成的 Lua 文件名")
    args = parser.parse_args()

    try:
        convert_to_lua(args.input_file, args.output_dir, args.output_file)
    except FileNotFoundError as e:
        print(f"错误: {e}")
    except Exception as e:
        print(f"意外错误: {e}")

if __name__ == "__main__":
    main()