// 连接到admin数据库
db = db.getSiblingDB('admin');

// 认证为管理员
db.auth('gameadmin', 'gamepassword');

// 创建游戏服务器数据库
db = db.getSiblingDB('game_server');

// 创建应用程序用户
db.createUser({
  user: 'game_user',
  pwd: 'game_password',
  roles: [
    { role: 'readWrite', db: 'game_server' }
  ]
});

// 创建必要的集合
db.createCollection('users');
db.createCollection('items');
db.createCollection('logs');

// 创建索引
db.users.createIndex({ "id": 1 }, { unique: true });
db.users.createIndex({ "created_at": 1 });

db.items.createIndex({ "owner": 1 });
db.items.createIndex({ "defid": 1 });
db.items.createIndex({ "id": 1 }, { unique: true });
db.items.createIndex({ "type": 1 });
db.items.createIndex({ "created_at": 1 });

// 添加一些初始配置数据
db.config.insertOne({
  "type": "server_config",
  "max_users": 10000,
  "max_connections_per_user": 1,
  "heartbeat_timeout": 120,
  "version": "1.0.0",
  "created_at": new Date()
});

print('MongoDB initialization completed successfully'); 