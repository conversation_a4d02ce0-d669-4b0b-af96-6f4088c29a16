"""
商店系统WebSocket处理器
"""

import json
import logging
from typing import Dict, Any
from fastapi import WebSocket
from datetime import datetime
from base_handlers import MessageHandler
from enums import MessageId
from models import MessageModel
from shop_service import ShopService
from shop_models import PurchaseRequest
from utils import handle_error

logger = logging.getLogger(__name__)


class ShopWebSocketHandler:
    """商店WebSocket处理器"""
    
    def __init__(self):
        self.shop_service = ShopService()
        
        # 注册消息处理器
        self.handlers = {
            MessageId.SHOP_GET_LIST: ShopGetListHandler(),
            MessageId.SHOP_GET_ITEMS: ShopGetItemsHandler(),
            MessageId.SHOP_GET_ITEM_DETAIL: ShopGetItemDetailHandler(),
            MessageId.SHOP_PURCHASE: ShopPurchaseHandler(),
            MessageId.SHOP_PREVIEW_PURCHASE: ShopPreviewPurchaseHandler(),
            MessageId.SHOP_GET_HISTORY: ShopGetHistoryHandler(),
            MessageId.SHOP_GET_LIMITS: ShopGetLimitsHandler(),
        }
    
    def set_external_services(self, currency_service, player_service):
        """设置外部服务依赖"""
        self.shop_service.set_external_services(currency_service, player_service)
    
    async def handle_message(self, msg_id: int, data: dict, websocket: WebSocket, 
                           username: str, token: str, connection_manager) -> dict:
        """处理商店相关消息"""
        try:
            handler = self.handlers.get(msg_id)
            if not handler:
                return {"error": f"未知的商店消息类型: {msg_id}"}
            
            # 设置商店服务
            handler.shop_service = self.shop_service
            
            return await handler.handle(data, websocket, username, token, connection_manager)
            
        except Exception as e:
            logger.error(f"处理商店消息失败: {str(e)}")
            return {"error": f"处理失败: {str(e)}"}


class ShopGetListHandler(MessageHandler):
    """获取商店列表处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        return True  # 获取商店列表不需要额外参数
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理获取商店列表"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid shop list request")
                return await self.error_handler(ValueError("Invalid request"), data)
            
            # 获取商店列表
            shops = await self.shop_service.get_available_shops(username)
            
            # 构造响应数据
            shop_list = []
            for shop in shops:
                shop_list.append({
                    "shop_id": shop.shop_id,
                    "shop_name": shop.shop_name,
                    "shop_type": shop.shop_type,
                    "description": shop.description,
                    "icon": shop.icon,
                    "sort_order": shop.sort_order,
                    "ui_config": shop.ui_config
                })
            
            response = MessageModel(
                msgId=MessageId.SHOP_GET_LIST,
                data={                    
                    "shops": shop_list,
                    "total": len(shop_list),
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"获取商店列表失败: {str(e)}")
            await handle_error(websocket, f"获取商店列表失败: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": str(e)}
            )
            return error_response


class ShopGetItemsHandler(MessageHandler):
    """获取商店商品处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        return isinstance(data.get("shop_id"), str) and data.get("shop_id").strip()
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理获取商店商品"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid shop items request")
                return await self.error_handler(ValueError("Invalid request"), data)

            shop_id = data["shop_id"]

            # 检查商店是否存在
            shop = await self.shop_service.get_shop(shop_id)
            if not shop:
                await handle_error(websocket, "商店不存在")
                return await self.error_handler(ValueError("Shop not found"), data)
            
            # 获取商品列表
            items = await self.shop_service.get_shop_items(shop_id, username)
            
            # 为每个商品获取详细信息
            item_details = []
            for item in items:
                detail = await self.shop_service.get_item_detail(item.config_id, username)
                if detail:
                    # 构建商品信息，避免字段重复
                    config = detail["config"]
                    price_info = detail["price_info"]
                    limit_status = detail["limit_status"]

                    item_details.append({
                        "config_id": config.config_id,
                        "item_template_id": config.item_template_id,
                        "item_quantity": config.item_quantity,
                        "item_quality": config.item_quality,
                        "original_price": price_info.original_price,
                        "final_price": price_info.final_price,
                        "currency_type": price_info.currency_type,
                        "discount_amount": price_info.discount_amount,
                        "discounts_applied": price_info.discounts_applied,
                        "can_purchase": detail["can_purchase"],
                        "limit_remaining": limit_status.remaining,
                        "limit_type": limit_status.limit_type,
                        "sort_order": config.sort_order,
                        "display_config": config.display_config
                    })
            
            response = MessageModel(
                msgId=MessageId.SHOP_GET_ITEMS,
                data={
                    "shop_id": shop_id,
                    "shop_name": shop.shop_name,
                    "items": item_details,
                    "total": len(item_details),
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"获取商店商品失败: {str(e)}")
            await handle_error(websocket, f"获取商店商品失败: {str(e)}")
            return await self.error_handler(e, data)


class ShopGetItemDetailHandler(MessageHandler):
    """获取商品详情处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        return isinstance(data.get("config_id"), str) and data.get("config_id").strip()
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理获取商品详情"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid item detail request")
                return await self.error_handler(ValueError("Invalid request"), data)

            config_id = data["config_id"]

            # 获取商品详情
            detail = await self.shop_service.get_item_detail(config_id, username)
            if not detail:
                await handle_error(websocket, "商品不存在或无权访问")
                return await self.error_handler(ValueError("Item not found"), data)
            
            config = detail["config"]
            price_info = detail["price_info"]
            limit_status = detail["limit_status"]
            
            response = MessageModel(
                msgId=MessageId.SHOP_GET_ITEM_DETAIL,
                data={
                    "config_id": config.config_id,
                    "shop_id": config.shop_id,
                    "item_template_id": config.item_template_id,
                    "item_quantity": config.item_quantity,
                    "item_quality": config.item_quality,
                    "price_info": {
                        "original_price": price_info.original_price,
                        "final_price": price_info.final_price,
                        "currency_type": price_info.currency_type,
                        "discount_amount": price_info.discount_amount,
                        "discounts_applied": price_info.discounts_applied
                    },
                    "limit_status": {
                        "can_purchase": limit_status.can_purchase,
                        "remaining": limit_status.remaining,
                        "current_count": limit_status.current_count,
                        "limit_count": limit_status.limit_count,
                        "limit_type": limit_status.limit_type
                    },
                    "can_purchase": detail["can_purchase"],
                    "availability": config.availability,
                    "display_config": config.display_config,
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"获取商品详情失败: {str(e)}")
            await handle_error(websocket, f"获取商品详情失败: {str(e)}")
            return await self.error_handler(e, data)


class ShopPurchaseHandler(MessageHandler):
    """购买商品处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        config_id = data.get("config_id")
        quantity = data.get("quantity")
        
        if not isinstance(config_id, str) or not config_id.strip():
            return False
        if not isinstance(quantity, int) or quantity <= 0:
            return False
        
        return True
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理购买商品"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid purchase request")
                return await self.error_handler(ValueError("Invalid request"), data)
            
            config_id = data["config_id"]
            quantity = data["quantity"]
            metadata = data.get("metadata", {"source": "websocket"})
            
            # 执行购买
            result = await self.shop_service.purchase_item(
                player_id=username,
                config_id=config_id,
                quantity=quantity,
                metadata=metadata
            )
            
            if result.success:
                # 购买成功，发送成功响应
                response = MessageModel(
                    msgId=MessageId.SHOP_PURCHASE_SUCCESS,
                    data={
                        "purchase_id": result.purchase_id,
                        "items": result.items,
                        "purchase_info": result.data,
                        "timestamp": datetime.now().isoformat()
                    }
                )

                await websocket.send_text(json.dumps(response.model_dump()))

                # 可以在这里广播购买成功事件给其他相关玩家
                # 例如公会商店购买、限量商品等

                return response
            else:
                # 购买失败
                await handle_error(websocket, f"购买失败: {result.error}")
                return await self.error_handler(ValueError(result.error), data)

        except Exception as e:
            logger.error(f"购买商品失败: {str(e)}")
            await handle_error(websocket, f"购买商品失败: {str(e)}")
            return await self.error_handler(e, data)


class ShopPreviewPurchaseHandler(MessageHandler):
    """预览购买处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        config_id = data.get("config_id")
        quantity = data.get("quantity")
        
        if not isinstance(config_id, str) or not config_id.strip():
            return False
        if not isinstance(quantity, int) or quantity <= 0:
            return False
        
        return True
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理预览购买"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid preview request")
                return await self.error_handler(ValueError("Invalid request"), data)
            
            config_id = data["config_id"]
            quantity = data["quantity"]
            
            # 预览购买
            preview = await self.shop_service.preview_purchase(username, config_id, quantity)
            
            response = MessageModel(
                msgId=MessageId.SHOP_PREVIEW_PURCHASE,
                data={
                    "preview": preview,
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"预览购买失败: {str(e)}")
            await handle_error(websocket, f"预览购买失败: {str(e)}")
            return await self.error_handler(e, data)


class ShopGetHistoryHandler(MessageHandler):
    """获取购买历史处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        return True  # 获取历史不需要额外验证
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理获取购买历史"""
        try:
            limit = data.get("limit", 50)
            offset = data.get("offset", 0)
            
            # 获取购买历史
            history = await self.shop_service.get_purchase_history(username, limit, offset)
            
            response = MessageModel(
                msgId=MessageId.SHOP_GET_HISTORY,
                data={
                    "purchases": history,
                    "total": len(history),
                    "limit": limit,
                    "offset": offset,
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"获取购买历史失败: {str(e)}")
            await handle_error(websocket, f"获取购买历史失败: {str(e)}")
            return await self.error_handler(e, data)


class ShopGetLimitsHandler(MessageHandler):
    """获取限购状态处理器"""
    
    async def validate(self, data: dict) -> bool:
        """验证请求数据"""
        return True  # 获取限购状态不需要额外验证
    
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, connection_manager) -> dict:
        """处理获取限购状态"""
        try:
            # 获取限购状态
            limits = await self.shop_service.get_player_limits(username)
            
            response = MessageModel(
                msgId=MessageId.SHOP_GET_LIMITS,
                data={
                    "limits": limits,
                    "timestamp": datetime.now().isoformat()
                }
            )

            await websocket.send_text(json.dumps(response.model_dump()))
            return response
            
        except Exception as e:
            logger.error(f"获取限购状态失败: {str(e)}")
            await handle_error(websocket, f"获取限购状态失败: {str(e)}")
            return await self.error_handler(e, data)


# 全局商店WebSocket处理器实例
shop_websocket_handler = ShopWebSocketHandler()


# ==================== 消息处理器注册函数 ====================

def register_shop_handlers(message_manager):
    """注册商店相关的消息处理器"""

    async def handle_shop_get_list(websocket, username: str, token: str, data: dict, connection_manager):
        """处理获取商店列表"""
        handler = ShopGetListHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_get_items(websocket, username: str, token: str, data: dict, connection_manager):
        """处理获取商店商品"""
        handler = ShopGetItemsHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_get_item_detail(websocket, username: str, token: str, data: dict, connection_manager):
        """处理获取商品详情"""
        handler = ShopGetItemDetailHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_purchase(websocket, username: str, token: str, data: dict, connection_manager):
        """处理购买商品"""
        handler = ShopPurchaseHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_preview_purchase(websocket, username: str, token: str, data: dict, connection_manager):
        """处理预览购买"""
        handler = ShopPreviewPurchaseHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_get_history(websocket, username: str, token: str, data: dict, connection_manager):
        """处理获取购买历史"""
        handler = ShopGetHistoryHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    async def handle_shop_get_limits(websocket, username: str, token: str, data: dict, connection_manager):
        """处理获取限购状态"""
        handler = ShopGetLimitsHandler()
        handler.shop_service = shop_websocket_handler.shop_service
        return await handler.handle(data, websocket, username, token, connection_manager)

    # 注册所有商店相关的消息处理器
    message_manager.register_handler(MessageId.SHOP_GET_LIST, handle_shop_get_list)
    message_manager.register_handler(MessageId.SHOP_GET_ITEMS, handle_shop_get_items)
    message_manager.register_handler(MessageId.SHOP_GET_ITEM_DETAIL, handle_shop_get_item_detail)
    message_manager.register_handler(MessageId.SHOP_PURCHASE, handle_shop_purchase)
    message_manager.register_handler(MessageId.SHOP_PREVIEW_PURCHASE, handle_shop_preview_purchase)
    message_manager.register_handler(MessageId.SHOP_GET_HISTORY, handle_shop_get_history)
    message_manager.register_handler(MessageId.SHOP_GET_LIMITS, handle_shop_get_limits)

    logger.info("商店相关消息处理器注册完成")


# ==================== 商店事件广播函数 ====================

async def broadcast_shop_event(connection_manager, event_type: str, data: dict, target_players: list = None):
    """广播商店事件"""
    try:
        if event_type == "purchase_success":
            message = MessageModel(
                msgId=MessageId.SHOP_PURCHASE_SUCCESS,
                data=data
            ).model_dump()
        elif event_type == "limits_reset":
            message = MessageModel(
                msgId=MessageId.SHOP_LIMITS_RESET,
                data=data
            ).model_dump()
        elif event_type == "shop_refreshed":
            message = MessageModel(
                msgId=MessageId.SHOP_REFRESHED,
                data=data
            ).model_dump()
        elif event_type == "discount_updated":
            message = MessageModel(
                msgId=MessageId.SHOP_DISCOUNT_UPDATED,
                data=data
            ).model_dump()
        elif event_type == "item_updated":
            message = MessageModel(
                msgId=MessageId.SHOP_ITEM_UPDATED,
                data=data
            ).model_dump()
        else:
            logger.warning(f"未知的商店事件类型: {event_type}")
            return

        if target_players:
            # 发送给指定玩家
            for player_id in target_players:
                await connection_manager.send_personal_message(message, player_id)
        else:
            # 广播给所有在线玩家
            await connection_manager.broadcast(message)

        logger.info(f"商店事件广播完成: {event_type}")

    except Exception as e:
        logger.error(f"广播商店事件失败: {str(e)}")


def set_shop_external_services(currency_service, player_service):
    """设置商店系统的外部服务依赖"""
    shop_websocket_handler.set_external_services(currency_service, player_service)
