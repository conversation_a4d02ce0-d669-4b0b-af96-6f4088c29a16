"""
公会通知推送服务
负责向玩家推送公会相关的信息变更通知
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from models import MessageModel
from enums import MessageId
from service_locator import ServiceLocator
from UserCacheManager import UserCacheManager
from guild_models import GuildMember, Guild

logger = logging.getLogger(__name__)


class GuildNotificationService:
    """公会通知推送服务"""
    
    def __init__(self):
        self.user_cache = None
        self.connection_manager = None
    
    async def _get_user_cache(self):
        """获取用户缓存管理器"""
        if self.user_cache is None:
            self.user_cache = await UserCacheManager.get_instance()
        return self.user_cache
    
    async def _get_connection_manager(self):
        """获取连接管理器"""
        if self.connection_manager is None:
            self.connection_manager = ServiceLocator.get("conn_manager")
        return self.connection_manager

    # ==================== 公会信息推送 ====================
    
    async def push_guild_info_to_player(self, player_id: str, guild_info: Dict[str, Any] = None) -> bool:
        """向单个玩家推送公会信息"""
        try:
            connection_manager = await self._get_connection_manager()
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False
            
            # 构建推送消息
            message = MessageModel(
                msgId=MessageId.GUILD_NOTIFICATION,
                success=True,
                data={
                    "type": "guild_info_update",
                    "guild_info": guild_info,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            # 发送给指定玩家
            await connection_manager.send_personal_message_to_user(message, player_id)
            logger.debug(f"公会信息已推送给玩家: {player_id}")
            return True
            
        except Exception as e:
            logger.error(f"推送公会信息给玩家失败: {player_id}, 错误: {str(e)}")
            return False

    async def push_guild_info_to_members(self, member_list: List[str], guild_info: Dict[str, Any] = None) -> Dict[str, bool]:
        """向多个成员推送公会信息"""
        results = {}
        for player_id in member_list:
            results[player_id] = await self.push_guild_info_to_player(player_id, guild_info)
        return results

    # ==================== 用户缓存更新 ====================
    
    async def update_player_guild_cache(self, player_id: str, guild_info: Dict[str, Any] = None) -> bool:
        """更新玩家的公会信息缓存"""
        try:
            user_cache = await self._get_user_cache()
            if not user_cache:
                logger.error("用户缓存管理器不可用")
                return False
            
            # 更新用户缓存中的公会信息
            success = await user_cache.update_guild_info(player_id, guild_info)
            if success:
                logger.debug(f"玩家公会信息缓存已更新: {player_id}")
            else:
                logger.warning(f"更新玩家公会信息缓存失败: {player_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新玩家公会信息缓存失败: {player_id}, 错误: {str(e)}")
            return False

    async def batch_update_members_cache(self, updates: List[Dict[str, Any]]) -> Dict[str, bool]:
        """批量更新成员的公会信息缓存"""
        try:
            user_cache = await self._get_user_cache()
            if not user_cache:
                logger.error("用户缓存管理器不可用")
                return {}
            
            # 批量更新
            results = await user_cache.batch_update_guild_info(updates)
            logger.info(f"批量更新公会信息缓存完成，成功: {sum(results.values())}, 失败: {len(results) - sum(results.values())}")
            return results
            
        except Exception as e:
            logger.error(f"批量更新公会信息缓存失败: {str(e)}")
            return {}

    # ==================== 综合操作 ====================
    
    async def notify_guild_join(self, player_id: str, guild: Guild, member: GuildMember) -> bool:
        """通知玩家加入公会"""
        try:
            # 构建公会信息
            guild_info = {
                "guild_id": guild.guild_id,
                "guild_name": guild.name,
                "guild_level": guild.level,
                "position": member.position,
                "position_name": self._get_position_name(member.position),
                "joined_at": member.joined_at.isoformat(),
                "member_count": guild.member_count,
                "max_members": guild.max_members
            }
            
            # 更新缓存
            cache_success = await self.update_player_guild_cache(player_id, guild_info)
            
            # 推送通知
            push_success = await self.push_guild_info_to_player(player_id, guild_info)
            
            logger.info(f"玩家加入公会通知: {player_id} -> {guild.name}, 缓存: {cache_success}, 推送: {push_success}")
            return cache_success and push_success
            
        except Exception as e:
            logger.error(f"通知玩家加入公会失败: {player_id}, 错误: {str(e)}")
            return False

    async def notify_guild_leave(self, player_id: str, guild_name: str = None) -> bool:
        """通知玩家离开公会"""
        try:
            # 清除缓存中的公会信息
            cache_success = await self.update_player_guild_cache(player_id, None)
            
            # 推送离开通知
            push_success = await self.push_guild_info_to_player(player_id, None)
            
            logger.info(f"玩家离开公会通知: {player_id}, 公会: {guild_name}, 缓存: {cache_success}, 推送: {push_success}")
            return cache_success and push_success
            
        except Exception as e:
            logger.error(f"通知玩家离开公会失败: {player_id}, 错误: {str(e)}")
            return False

    async def notify_position_change(self, player_id: str, guild: Guild, new_position: str) -> bool:
        """通知玩家职位变更"""
        try:
            # 获取当前公会信息
            user_cache = await self._get_user_cache()
            current_guild_info = await user_cache.get_guild_info(player_id)
            
            if current_guild_info:
                # 更新职位信息
                current_guild_info["position"] = new_position
                current_guild_info["position_name"] = self._get_position_name(new_position)
                
                # 更新缓存
                cache_success = await self.update_player_guild_cache(player_id, current_guild_info)
                
                # 推送通知
                push_success = await self.push_guild_info_to_player(player_id, current_guild_info)
                
                logger.info(f"玩家职位变更通知: {player_id} -> {new_position}, 缓存: {cache_success}, 推送: {push_success}")
                return cache_success and push_success
            else:
                logger.warning(f"玩家没有公会信息，无法更新职位: {player_id}")
                return False
                
        except Exception as e:
            logger.error(f"通知玩家职位变更失败: {player_id}, 错误: {str(e)}")
            return False

    async def notify_guild_info_change(self, guild: Guild, member_ids: List[str]) -> Dict[str, bool]:
        """通知公会信息变更给所有成员"""
        try:
            results = {}
            
            for player_id in member_ids:
                try:
                    # 获取玩家当前公会信息
                    user_cache = await self._get_user_cache()
                    current_guild_info = await user_cache.get_guild_info(player_id)
                    
                    if current_guild_info:
                        # 更新公会基本信息，保留个人信息
                        current_guild_info.update({
                            "guild_name": guild.name,
                            "guild_level": guild.level,
                            "member_count": guild.member_count,
                            "max_members": guild.max_members
                        })
                        
                        # 更新缓存和推送
                        cache_success = await self.update_player_guild_cache(player_id, current_guild_info)
                        push_success = await self.push_guild_info_to_player(player_id, current_guild_info)
                        
                        results[player_id] = cache_success and push_success
                    else:
                        results[player_id] = False
                        
                except Exception as e:
                    logger.error(f"通知单个成员公会信息变更失败: {player_id}, 错误: {str(e)}")
                    results[player_id] = False
            
            success_count = sum(results.values())
            logger.info(f"公会信息变更通知完成: {guild.name}, 成功: {success_count}, 失败: {len(results) - success_count}")
            return results
            
        except Exception as e:
            logger.error(f"通知公会信息变更失败: {guild.guild_id}, 错误: {str(e)}")
            return {}

    # ==================== 登录时推送 ====================
    
    async def push_guild_info_on_login(self, player_id: str) -> bool:
        """玩家登录时推送公会信息"""
        try:
            user_cache = await self._get_user_cache()
            guild_info = await user_cache.get_guild_info(player_id)
            
            if guild_info:
                # 推送现有的公会信息
                success = await self.push_guild_info_to_player(player_id, guild_info)
                logger.debug(f"登录时推送公会信息: {player_id}, 成功: {success}")
                return success
            else:
                # 玩家没有公会，推送空信息
                success = await self.push_guild_info_to_player(player_id, None)
                logger.debug(f"登录时推送空公会信息: {player_id}, 成功: {success}")
                return success
                
        except Exception as e:
            logger.error(f"登录时推送公会信息失败: {player_id}, 错误: {str(e)}")
            return False

    # ==================== 辅助方法 ====================
    
    def _get_position_name(self, position: str) -> str:
        """获取职位中文名称"""
        position_names = {
            "leader": "会长",
            "vice_leader": "副会长",
            "elder": "长老",
            "member": "成员"
        }
        return position_names.get(position, "未知")

    async def send_guild_notification(self, player_ids: List[str], notification_type: str, 
                                    data: Dict[str, Any] = None) -> Dict[str, bool]:
        """发送通用公会通知"""
        try:
            connection_manager = await self._get_connection_manager()
            if not connection_manager:
                logger.error("连接管理器不可用")
                return {}
            
            message = MessageModel(
                msgId=MessageId.GUILD_NOTIFICATION,
                success=True,
                data={
                    "type": notification_type,
                    "data": data or {},
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            results = {}
            for player_id in player_ids:
                try:
                    await connection_manager.send_personal_message_to_user(message, player_id)
                    results[player_id] = True
                except Exception as e:
                    logger.error(f"发送通知给玩家失败: {player_id}, 错误: {str(e)}")
                    results[player_id] = False
            
            return results
            
        except Exception as e:
            logger.error(f"发送公会通知失败: {str(e)}")
            return {}
