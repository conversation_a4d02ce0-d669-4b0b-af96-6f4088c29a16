#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试公会系统序列化修复
验证GuildMember的player_id属性和序列化是否正常工作
"""

import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from guild_models import Guild, GuildMember, GuildPosition
from UserCacheManager import SimpleUserInfo
from BaseModelORM import BaseModelORM

def test_simple_user_info_serialization():
    """测试SimpleUserInfo序列化"""
    print("=== 测试SimpleUserInfo序列化 ===")
    
    user_info = SimpleUserInfo(
        id="player123",
        nickname="测试玩家",
        level=10,
        fight_power=1000
    )
    
    # 测试serialize方法
    serialized = user_info.serialize()
    print(f"SimpleUserInfo序列化结果: {serialized}")
    
    # 测试JSON序列化
    json_str = json.dumps(serialized, ensure_ascii=False)
    print(f"JSON序列化结果: {json_str}")
    
    return True

def test_guild_member_player_id():
    """测试GuildMember的player_id属性"""
    print("\n=== 测试GuildMember的player_id属性 ===")
    
    # 创建SimpleUserInfo
    user_info = SimpleUserInfo(
        id="player123",
        nickname="测试玩家",
        level=10,
        fight_power=1000
    )
    
    # 创建GuildMember
    member = GuildMember(
        guild_id="guild123",
        playerInfo=user_info,
        position=GuildPosition.MEMBER,
        contribution=100,
        weekly_contribution=50,
        total_contribution=500
    )
    
    # 测试player_id属性
    print(f"member.player_id: {member.player_id}")
    print(f"member.playerInfo.id: {member.playerInfo.id}")
    
    # 验证player_id属性是否正确
    assert member.player_id == "player123", f"player_id应该是'player123'，但得到'{member.player_id}'"
    assert member.player_id == member.playerInfo.id, "player_id应该等于playerInfo.id"
    
    print("✓ player_id属性测试通过")
    return True

def test_guild_member_serialization():
    """测试GuildMember序列化"""
    print("\n=== 测试GuildMember序列化 ===")
    
    # 创建SimpleUserInfo
    user_info = SimpleUserInfo(
        id="player123",
        nickname="测试玩家",
        level=10,
        fight_power=1000
    )
    
    # 创建GuildMember
    member = GuildMember(
        guild_id="guild123",
        playerInfo=user_info,
        position=GuildPosition.MEMBER,
        contribution=100,
        weekly_contribution=50,
        total_contribution=500
    )
    
    # 测试to_dict方法
    member_dict = member.to_dict()
    print(f"GuildMember.to_dict()结果: {member_dict}")
    
    # 验证player_id字段是否存在
    assert "player_id" in member_dict, "to_dict()结果中应该包含player_id字段"
    assert member_dict["player_id"] == "player123", f"player_id应该是'player123'，但得到'{member_dict['player_id']}'"
    
    # 测试JSON序列化
    json_str = json.dumps(member_dict, ensure_ascii=False)
    print(f"JSON序列化结果: {json_str}")
    
    # 验证JSON可以正常解析
    parsed = json.loads(json_str)
    assert parsed["player_id"] == "player123", "JSON解析后player_id应该正确"
    
    print("✓ GuildMember序列化测试通过")
    return True

def test_guild_member_from_dict():
    """测试GuildMember.from_dict方法"""
    print("\n=== 测试GuildMember.from_dict方法 ===")
    
    # 模拟数据库中的数据（包含player_id字段）
    db_data = {
        "guild_id": "guild123",
        "player_id": "player123",
        "player_name": "测试玩家",
        "position": "member",
        "contribution": 100,
        "weekly_contribution": 50,
        "total_contribution": 500,
        "joined_at": "2024-01-01T10:00:00",
        "last_active": "2024-01-02T15:30:00"
    }
    
    # 测试from_dict方法
    member = GuildMember.from_dict(db_data)
    print(f"从数据库数据创建的GuildMember: {member}")
    
    # 验证playerInfo是否正确创建
    assert member.playerInfo is not None, "playerInfo应该被正确创建"
    assert member.playerInfo.id == "player123", f"playerInfo.id应该是'player123'，但得到'{member.playerInfo.id}'"
    assert member.player_id == "player123", f"player_id应该是'player123'，但得到'{member.player_id}'"
    
    # 测试序列化
    member_dict = member.to_dict()
    assert "player_id" in member_dict, "序列化结果应该包含player_id字段"
    
    print("✓ GuildMember.from_dict测试通过")
    return True

def test_json_encoder():
    """测试自定义JSON编码器"""
    print("\n=== 测试自定义JSON编码器 ===")
    
    from guild_cache_manager import CustomJSONEncoder
    
    # 测试datetime序列化
    test_data = {
        "datetime_field": datetime.now(),
        "string_field": "测试字符串",
        "int_field": 123
    }
    
    # 使用自定义编码器
    json_str = json.dumps(test_data, cls=CustomJSONEncoder, ensure_ascii=False)
    print(f"自定义编码器序列化结果: {json_str}")
    
    # 验证可以正常解析
    parsed = json.loads(json_str)
    assert "datetime_field" in parsed, "datetime字段应该被正确序列化"
    
    print("✓ 自定义JSON编码器测试通过")
    return True

def main():
    """运行所有测试"""
    print("开始测试公会系统序列化修复...")
    
    tests = [
        test_simple_user_info_serialization,
        test_guild_member_player_id,
        test_guild_member_serialization,
        test_guild_member_from_dict,
        test_json_encoder
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                print(f"✗ {test.__name__} 测试失败")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} 测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试都通过了！序列化修复成功。")
        return True
    else:
        print("❌ 有测试失败，需要进一步修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
