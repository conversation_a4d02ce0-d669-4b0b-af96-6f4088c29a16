"""
公会WebSocket消息处理器
处理公会相关的WebSocket消息
"""

import logging
from typing import Dict, Any
from models import MessageModel
from enums import MessageId
from websocket_handlers import MessageHandler
from guild_service_distributed import GuildServiceDistributed
from guild_models import (
    CreateGuildRequest, UpdateGuildRequest, ApplyGuildRequest,
    ProcessApplicationRequest, ChangeMemberPositionRequest
)
from service_locator import ServiceLocator

logger = logging.getLogger(__name__)


class GuildHandlers(MessageHandler):
    """公会消息处理器"""
    
    def __init__(self):
        self.guild_service = GuildServiceDistributed()

    async def handle_create_guild(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理创建公会消息"""
        try:
            # 参数验证
            if not data.get("name"):
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "公会名称不能为空"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            

            # 创建请求对象
            request = CreateGuildRequest(
                name=data.get("name", ""),
                description=data.get("description", ""),
                logo=data.get("logo", "default")
            )
            
            # 调用服务
            result = await self.guild_service.create_guild(username, request)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_CREATE,
                    success=True,
                    data=result.data
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理创建公会消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "创建公会时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_guild_info(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取公会信息消息"""
        try:
            guild_id = data.get("guild_id")
            
            # 如果没有指定公会ID，获取玩家所在的公会
            if not guild_id:
                guild_id = await self.guild_service.get_player_guild_id(username)
                if not guild_id:
                    error_response = MessageModel(
                        msgId=MessageId.ERROR,
                        data={"error": "您还没有加入任何公会"}
                    ).model_dump()
                    await connection_manager.send_personal_message(error_response, token)
                    return error_response
            
            # 调用服务
            result = await self.guild_service.get_guild_info(guild_id, username)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_INFO,
                    success=True,
                    data=result.data
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理获取公会信息消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "获取公会信息时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_update_guild_info(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理更新公会信息消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 创建请求对象
            request = UpdateGuildRequest(
                name=data.get("name"),
                description=data.get("description"),
                logo=data.get("logo"),
                join_condition=data.get("join_condition")
            )
            
            # 调用服务
            result = await self.guild_service.update_guild_info(guild_id, username, request)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_UPDATE,
                    success=True,
                    data={"message": result.message}
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理更新公会信息消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "更新公会信息时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_disband_guild(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理解散公会消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.guild_service.disband_guild(guild_id, username)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_DISBAND,
                    success=True,
                    data={"message": result.message}
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理解散公会消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "解散公会时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_search_guilds(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理搜索公会消息"""
        try:
            keyword = data.get("keyword", "")
            page = data.get("page", 1)
            page_size = data.get("page_size", 20)
            
            # 调用服务
            result = await self.guild_service.search_guilds(keyword, page, page_size)
            
            response = MessageModel(
                msgId=MessageId.GUILD_SEARCH,
                success=result.success,
                data={
                    "guilds": result.guilds,
                    "total": result.total,
                    "page": result.page,
                    "page_size": result.page_size
                }
            ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理搜索公会消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "搜索公会时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_apply_guild(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理申请加入公会消息"""
        try:
            if not data.get("guild_id"):
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "公会ID不能为空"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 创建请求对象
            request = ApplyGuildRequest(
                guild_id=data.get("guild_id"),
                message=data.get("message", "")
            )
            
            # 调用服务
            result = await self.guild_service.apply_to_guild(username, username, request)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_APPLY,
                    success=True,
                    data={"message": result.message}
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理申请加入公会消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "申请加入公会时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_guild_members(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取公会成员列表消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.guild_service.get_guild_members(guild_id, username)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_MEMBER_LIST,
                    success=True,
                    data=result.data
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理获取公会成员列表消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "获取公会成员列表时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_leave_guild(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理离开公会消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.guild_service.leave_guild(guild_id, username)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_LEAVE,
                    success=True,
                    data={"message": result.message}
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理离开公会消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "离开公会时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_guild_applications(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取公会申请列表消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.guild_service.get_guild_applications(guild_id, username)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_APPLICATIONS,
                    success=True,
                    data=result.data
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理获取公会申请列表消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "获取公会申请列表时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_process_application(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理公会申请审批消息"""
        try:
            # 获取玩家所在的公会
            guild_id = await self.guild_service.get_player_guild_id(username)
            if not guild_id:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "您还没有加入任何公会"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            if not data.get("application_id") or not data.get("action"):
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "申请ID和操作类型不能为空"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 创建请求对象
            request = ProcessApplicationRequest(
                application_id=data.get("application_id"),
                action=data.get("action"),
                reason=data.get("reason", "")
            )
            
            # 调用服务
            result = await self.guild_service.process_application(guild_id, username, request)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.GUILD_PROCESS_APPLICATION,
                    success=True,
                    data={"message": result.message}
                ).model_dump()
            else:
                response = MessageModel(
                    msgId=MessageId.ERROR,
                    success=False,
                    data={"error": result.error}
                ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            return response
            
        except Exception as e:
            logger.error(f"处理公会申请审批消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "处理公会申请审批时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_my_guild(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取我的公会信息消息"""
        try:
            # 获取玩家所在的公会ID
            guild_id = await self.guild_service.get_player_guild_id(username)

            if not guild_id:
                # 玩家没有公会
                response = MessageModel(
                    msgId=MessageId.GET_MY_GUILD,
                    success=True,
                    data={
                        "is_in_guild": False,
                        "guild_info": None
                    }
                ).model_dump()
            else:
                # 获取公会信息
                guild_result = await self.guild_service.get_guild_info(guild_id, username)

                if guild_result.success:
                    # 获取成员信息
                    member_result = await self.guild_service.get_guild_members(guild_id, username)

                    # 找到当前玩家的成员信息
                    my_member_info = None
                    if member_result.success:
                        for member in member_result.data.get("members", []):
                            if member.get("player_id") == username:
                                my_member_info = member
                                break

                    guild_info = guild_result.data.get("guild", {})

                    response = MessageModel(
                        msgId=MessageId.GET_MY_GUILD,
                        success=True,
                        data={
                            "is_in_guild": True,
                            "guild_info": {
                                "guild_id": guild_info.get("guild_id"),
                                "guild_name": guild_info.get("name"),
                                "guild_level": guild_info.get("level"),
                                "member_count": guild_info.get("member_count"),
                                "max_members": guild_info.get("max_members"),
                                "my_position": my_member_info.get("position") if my_member_info else "member",
                                "my_position_name": my_member_info.get("position_info", {}).get("position_name") if my_member_info else "成员",
                                "joined_at": my_member_info.get("joined_at") if my_member_info else None
                            }
                        }
                    ).model_dump()
                else:
                    response = MessageModel(
                        msgId=MessageId.ERROR,
                        success=False,
                        data={"error": guild_result.error}
                    ).model_dump()

            await connection_manager.send_personal_message(response, token)
            return response

        except Exception as e:
            logger.error(f"处理获取我的公会信息消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": "获取公会信息时发生内部错误"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response
