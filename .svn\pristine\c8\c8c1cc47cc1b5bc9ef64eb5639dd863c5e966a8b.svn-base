/* 商店管理系统 - 主样式文件 */

/* CSS变量定义 */
:root {
    /* 颜色系统 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    /* 边框 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* 过渡 */
    --transition: all 0.2s ease-in-out;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--bg-secondary);
    min-height: 100vh;
}

/* 顶部导航栏 */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.header-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.header-nav {
    display: flex;
    gap: var(--spacing-6);
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-50);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

/* 主内容区域 */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-6);
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.toolbar-left h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.shop-count {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.toolbar-right {
    display: flex;
    gap: var(--spacing-3);
}

/* 筛选器 */
.filter-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    white-space: nowrap;
}

.filter-group select,
.filter-group input[type="text"] {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
    transition: var(--transition);
    min-width: 120px;
}

.filter-group select:focus,
.filter-group input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* 商店列表容器 */
.shop-list-container {
    position: relative;
    min-height: 400px;
}

.shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-6);
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.empty-state p {
    margin-bottom: var(--spacing-6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-container {
        padding: 0 var(--spacing-4);
        flex-direction: column;
        height: auto;
        padding-top: var(--spacing-4);
        padding-bottom: var(--spacing-4);
    }
    
    .header-nav {
        margin-top: var(--spacing-3);
    }
    
    .main-content {
        padding: var(--spacing-4);
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }
    
    .toolbar-left,
    .toolbar-right {
        text-align: center;
    }
    
    .filter-bar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .shop-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-3);
    }
    
    .toolbar,
    .filter-bar {
        padding: var(--spacing-3);
    }
}
