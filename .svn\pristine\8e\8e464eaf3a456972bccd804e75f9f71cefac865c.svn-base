import functools
import asyncio
from typing import Callable, Any
from distributed_lock import DistributedLock

# DistributedLock 现在自动管理Redis连接，不需要传入 redis_client

def distributed_task(redis_client=None, lock_key: str = None, ttl: int = 30):
    """
    分布式定时任务装饰器。
    :param redis_client: Redis 客户端（已废弃，保留兼容性）
    :param lock_key: 锁的唯一key（默认用函数名）
    :param ttl: 锁超时时间（秒）
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            key = lock_key or f"lock:scheduled:{func.__name__}"
            # 新版本的DistributedLock自动管理Redis连接，不需要传递redis_client
            lock = DistributedLock(key, ttl)
            try:
                async with lock:
                    return await func(*args, **kwargs)
            except TimeoutError:
                # 未获取到锁，直接跳过
                return None
        return wrapper
    return decorator