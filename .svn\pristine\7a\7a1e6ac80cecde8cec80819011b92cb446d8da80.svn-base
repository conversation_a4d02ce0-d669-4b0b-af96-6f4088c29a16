-- [Comment]
-- jnmo
equipData = class("equipData")
equipData.__index = equipData
EQUIP_STATE_UNEQUIP = 1  -- 未穿戴
EQUIP_STATE_EQUIPED = 2  -- 已穿戴
EQUIP_STATE_BROKEN = 3 -- 已损坏
EQUIP_STATE_SAVE  = 4-- 保留的 不显示
MAX_INT_LEVEL = 9
MAX_MASTERY_LEVEL = 9
UN_LOCK_SKILL_LEVEL = 5
QUA_RUNEW = 7
QUA_BEST = 6
QUA_PERFACE = 5
QUA_SUPERIOR = 4
QUA_ORDINARY = 3
QUA_WEAK = 2
QUA_BAD = 1
QUA_NON = 0
quatext = {
    [QUA_RUNEW] = "永恒",
    [QUA_BEST] = "传世",
    [QUA_PERFACE] = "稀世",
    [QUA_SUPERIOR] = "罕见",
    [QUA_ORDINARY] = "精良",
    [QUA_WEAK] = "优秀",
    [QUA_NON] = "普通",
    [QUA_BAD] = "粗糙",
}
quacolor = {
    [QUA_RUNEW] = "A52A2A",
    [QUA_BEST] = "FF0000",
    [QUA_PERFACE] = "eb6100",
    [QUA_SUPERIOR] = "601986",
    [QUA_ORDINARY] = "00a0ea",
    [QUA_WEAK] = "22ac38",
    [QUA_NON] = "040404",
    [QUA_BAD] = "040404",
}
rune_pos_text = {
    [1] = "壹",
    [2] = "贰",
    [3] = "叁",
    [4] = "肆",
    [5] = "伍",
    [6] = "陆",
}
equip_pro_num = 10
pro_key = {
    pro_key_main = "pro_key_main",
    pro_key_title = "pro_key_title",
    pro_key_lv1 = "pro_key_lv1",
    pro_key_lv2 = "pro_key_lv2",
    pro_key_lv3 = "pro_key_lv3",
    pro_key_lv4 = "pro_key_lv4",
    pro_key_lv5 = "pro_key_lv5",
    pro_key_lv6 = "pro_key_lv6",
    pro_key_lv7 = "pro_key_lv7",
    pro_key_lv8 = "pro_key_lv8",
    pro_key_lv9 = "pro_key_lv9",
    pro_key_lv10 = "pro_key_lv10",
    pro_key_rune1 = "pro_key_rune1",
    pro_key_rune2 = "pro_key_rune2",
    pro_key_rune3 = "pro_key_rune3",
    pro_key_rune4 = "pro_key_rune4",
    pro_key_rune5 = "pro_key_rune5",
    pro_key_rune6 = "pro_key_rune6",
    pro_key_rune7 = "pro_key_rune7",
    pro_key_rune8 = "pro_key_rune8",
    pro_key_rune9 = "pro_key_rune9",
    pro_key_rune10 = "pro_key_rune10",
    pro_key_horse1 = "pro_key_horse1",
    pro_key_horse2 = "pro_key_horse2",
    pro_key_stage1 = "pro_key_stage1",
    pro_key_stage2 = "pro_key_stage2",
    pro_key_stage3 = "pro_key_stage3",
    pro_key_stage4 = "pro_key_stage4",
}
function equipData:ctor(defid)
    print("equipData:ctor()")
    print("装备构造 " .. defid)
    self.defid = me.toNum(defid)
    self.itemid = me.guid()
    self.state = EQUIP_STATE_UNEQUIP
    -- 等级随机
    self.level_rand = 0

    -- 剩余潜能 重铸一次消耗一点
    self.cap_count = 0
    -- 随机潜能 1-3
    self.cap_rand = 0
    -- 随机的主属性
    self.main_rand = 0
    -- 随机主属性魔法
    self.magic_rand = 0
    self.intensify_rand = 0
    -- 武器的剩余强化次数
    self.intensify_count = 0
    -- 武器的强化数据
    self.int_imgbue_def = { }
    self.int_imgbue_dmg = { }
    self.int_imgbue = { }

    self.rand_imgbue = { }
    self.exp = 0
    -- 品质
    self.quality = QUA_NON
    -- 已经附魔的符文defid
    self.rune_inlay = { }
    -- 附魔数据
    self.rune_add_data = { }
    -- 时效性
    if cfg[CfgType.WEAPON][self.defid].time then
        self.time = me.sysTime() / 1000 + cfg[CfgType.WEAPON][self.defid].time
    end

    self.locked = false

    self.isnew = 1
    -- 灵
    self.souldata = { }
    self.soulqua = 0
    self.soulkey = ""
    self.soulnum = 1
    self.eroleid = ""

    self.property = { }
    -- 星级
    self.star_ec = ec_int(1)

    self.level = ec_int(0)
    -- 孔数
    self.hole_num = ec_int(0)
    -- 是否符文之语
    self.bruneword = 0


end
function equipData:initFromServerData(data)
    if not data then return end
    self.itemid = data.id
    print("装备初始化 " .. self.itemid)
end
local bless = { 1, 2, 3, 5, 7, 10, 15, 19, 25, 34, 47, 60, 72, 86, 100 }
function equipData:getBless(level)
    if level == 0 or level > 15 then
        return 0
    end
    return bless[level]
end
function equipData:updateMainProByLevel()
    local level = self:getLevel()
    local maxBless = self:getBless(15)
    local curBless = self:getBless(level)
    local pro = self.property[pro_key.pro_key_main]
    if pro.bsVal == nil then
        pro.bsVal = pro.val
        local ps = me.split(pro.mx, "-")
        if ps then
            pro.bsMin = tonumber(ps[1])
            pro.bsMax = tonumber(ps[2])
        end
    end
    local per = 1 + curBless / maxBless
    pro.val = math.floor(pro.bsVal * per)
    pro.mx = math.floor(pro.bsMin * per) .. "-" .. math.floor(pro.bsMax * per)
    gameLogic:getInstance():saveGame()
end
function equipData:getMainProByTmpLevel(level)
    local maxBless = self:getBless(15)
    local curBless = self:getBless(level)
    local pro = self.property[pro_key.pro_key_main]
    local per = 1 + curBless / maxBless
    return getImgdueVal(pro.key, pro.val, math.floor(pro.bsVal or pro.val * per))
end
function equipData:dismantle(idata,u1)

end
function equipData:upStar(idata,u1)    
    local pro = tonumber(idata.pro)
    local odds_add = 0
    if u1 and getItemNum(82159) > 0 then
        odds_add = 10
    else
        odds_add = 0
    end
    pro = pro + odds_add
    pro = math.min(100, pro)
    if u1 and getItemNum(82158) <= 0 then
        showTips("强化祝福石不足")
        return false
    end
    local b, id = redIntScrollItem(idata)
    if b then
        if u1 then
            redItem(82158)
        end
        if me.getRandom(100) <= pro then
            self.defid = idata.tagid    
            self:resetMainPro()
            self:updateMainProByLevel()        
            showTips("升星成功")
        else
            showTips("升星失败")
        end        
        gameLogic:getInstance():saveGame()
        return true
    else
        showTips(cfg[CfgType.ITEM][id].name .. "不足")
        return false
    end
end
function equipData:doIntensify(idata,u1)
    if idata.imgbue == "up_star" then
         return self:upStar(idata,u1)
    end
    if self:getLevel() > 15 then
        showTips("强化已达最大值")
        return false
    end
    local pro = self:getIntensifyOdds(idata, u1)
    if u1 and getItemNum(82158) <= 0 then
        showTips("强化祝福石不足")
        return false
    end
    local b, id = redIntScrollItem(idata)
    if b then
        if u1 then
            redItem(82158)
        end
        if me.getRandom(100) <= pro then
            local x = me.getRandomBetween(idata.min, idata.max)
            self:addLevel(x)
            showTips("强化成功,加持+" .. x)
        else
            local x = me.getRandom(100)
            if x <= 15 then
                if self:getLevel() >= 2 then
                    self:redLevel(2)
                    showTips("强化失败，加持-2")
                end
            elseif x <= 65 then
                if self:getLevel() >= 1 then
                    self:redLevel(1)
                    showTips("强化失败，加持-1")
                end
            else
                showTips("强化失败，加持不变")
            end
        end
        self:updateMainProByLevel()
        gameLogic:getInstance():saveGame()
        return true
    else
        showTips(cfg[CfgType.ITEM][id].name .. "不足")
        return false
    end
end
-- 獲取强化成功率
function equipData:getIntensifyOdds(data, u1)
    local int_imgbue = data.imgbue
    local pro = tonumber(data.pro)
    local odds_add = 0
    if int_imgbue == "sup_dmg" or int_imgbue == "sup_def" then
        -- 武器加持
        local max = data.max
        -- 大加
        pro = math.max(tonumber(data.pro) - self:getLevel() * 5, 5)
    end
    if u1 and getItemNum(82159) > 0 then
        odds_add = 10
    else
        odds_add = 0
    end
    pro = pro + odds_add
    pro = math.min(100, pro)
    return pro
end
function equipData:isArmor()
    return self:getDef().kind == 2 and self:getDef().mtype >= 2 and self:getDef().mtype <= 6
end
function equipData:getBaseProName()
    if self:getDef().kind == EQUIP_KIND_WEAPON then
        if self.property[pro_key["pro_key_lv1"]] then
            local key = self.property[pro_key["pro_key_lv1"]].key
            if key == "def" then
                return "防"
            end
            if key == "dex" then
                return "速"
            end
            if key == "con" then
                return "体"
            end
            if key == "pow" then
                return "武"
            end
            if key == "int" then
                return "智"
            end
            if key == "trt" then
                return "疗"
            end
        end
    elseif self:getDef().kind == EQUIP_KIND_ARMOR then
        if self.property[pro_key.pro_key_main] then
            local key = self.property[pro_key.pro_key_main].key
            if key == "def" or key == "def_p" then
                return "防"
            end
            if key == "dex" or key == "dex_p" then
                return "速"
            end
            if key == "con" or key == "con_p" then
                return "体"
            end
            if key == "pow" or key == "pow_p" then
                return "武"
            end
            if key == "int" or key == "int_p" then
                return "智"
            end
            if key == "cirt_odd" or key == "cirt_hit" then
                return "暴"
            end
            if key == "trt" or key == "trtad" then
                return "疗"
            end
            if key == "mdmg_p" or key == "pdmg_p" then
                return "伤"
            end
        end

    end
    return ""
end
function equipData:getQuality()
    return self.quality
end
function equipData:getSoulVal()
    if self.soulqua and self.soulqua > 0 then
        return self.souldata[self.soulkey] or 1
    end
    return 0
end
function equipData:isTimeOut()
    if self:getDef().time and self:getDef().time > 0 and tonumber(self:getDef().time) == 8888888 then
        return false
    else
        return self:getDef().time and self:getDef().time > 0 and me.sysTime() / 1000 > self.time
    end
end
function equipData:isActive(baseid)
    return self:getDef().limit_role == nil or(self:getDef().limit_role and self:getDef().limit_role == baseid)
end
function equipData:getLevel()
--    if ec_int(self.level) > 15 then
--        self:setEquipLevel(15)
--        self:updateMainProByLevel()
--        gameLogic:getInstance():saveGame()
--    end
    return ec_int(self.level)
end
function equipData:getStar()
    return ec_int(self.star_ec)
end
function equipData:setStar(star)
    self.star_ec = ec_int( star )
end 
function equipData:addLevel(num)
    self.level = ec_add(self.level, num)
    if ec_int(self.level) > 15 then
        self:setEquipLevel(15)
    end
end
function equipData:redLevel(num)
    if ec_int(self.level) >= num then
        self.level = ec_red(self.level, num)
    else
        self:setEquipLevel(0)
    end
end
function equipData:setEquipLevel(x)
    self.level = ec_int(x)
end
function equipData:updateRuneInlayData()
    self.rune_add_data = { }
    for key, var in pairs(self.rune_inlay) do
        local rdata = cfg[CfgType.RUNE][var]
        local radd_id = me.split(rdata.add_id, ",")
        local radd_var = me.split(rdata.add_var, ",")
        for k, v in pairs(radd_id) do
            if self.rune_add_data[v] == nil then
                self.rune_add_data[v] = 0
            end
            self.rune_add_data[v] = self.rune_add_data[v] + radd_var[k]
        end
    end
end
-- 获取孔数
function equipData:getHoleNum()
    if ec_int(self.hole_num) > 6 then
        self.hole_num = ec_int(0)
    end
    return ec_int(self.hole_num)
end
function equipData:setHoleNum(h)
    self.hole_num = ec_int(h)
end
function equipData:addHoleNum()
    if ec_int(self.hole_num) < 5 then
        self.hole_num = ec_add(self.hole_num, 1)
    end
end
-- 镶嵌
function equipData:inlay(rdefid)
    if #self.rune_inlay < self:getHoleNum() then
        table.insert(self.rune_inlay, rdefid)
        redRune(rdefid)
        self:updateRuneInlayData()
        taskMgr:getInstance():checkCurMainTask(TASK_TYPE_INLAY, 1)
        taskMgr:getInstance():checkCurMainTask(TASK_TYPE_COSTRUNE,1,rdefid)
        if #self.rune_inlay == self:getHoleNum() then
            -- 镶嵌满了
            self:runeWorld()
        end
    else
        showTips("孔数不足")
    end
end
-- 是否有可提取威能
function equipData:havePowers()
    for var = 1, 4 do
        local idata = self.property[pro_key["pro_key_stage" .. var]]
        if idata and idata.irune and not idata.lock then
            return true
        end
    end
    return false
end
-- 是否已经附魔威能
function equipData:haveInlyPowers()
    for var = 1, 4 do
        local idata = self.property[pro_key["pro_key_stage" .. var]]
        if idata and idata.irune and idata.lock then
            return true
        end
    end
    return false
end
function equipData:clearThePowers()
    for var = 1, 4 do
        local idata = self.property[pro_key["pro_key_stage" .. var]]
        if idata and idata.irune and idata.lock then
            self.property[pro_key["pro_key_stage" .. var]] = nil
        end
    end
end
function equipData:getThePowers()
    for var = 1, 4 do
        local idata = self.property[pro_key["pro_key_stage" .. var]]
        if idata and idata.irune then
            return idata
        end
    end
    return nil
end
function equipData:changeThePowers(id)
    local bchange = false
    for var = 1, 4 do
        local idata = self.property[pro_key["pro_key_stage" .. var]]
        if idata and idata.irune then
            local rdata = user.PowersSaveDatas[id]
            if rdata then
                idata.key = rdata.key
                idata.val = rdata.val
                idata.mx = rdata.mx
                idata.min = rdata.min
                idata.defid = rdata.defid
                idata.name = rdata.name
                idata.qua = rdata.qua
                idata.irune = rdata.irune
                idata.lock = true
            end
            user.PowersSaveDatas[id] = nil
            gameLogic:getInstance():saveGame()
            bchange = true
            break
        end
    end
    if bchange == false then
        local idata = { }
        local rdata = user.PowersSaveDatas[id]
        if rdata then
            idata.key = rdata.key
            idata.val = rdata.val
            idata.mx = rdata.mx
            idata.min = rdata.min
            idata.defid = rdata.defid
            idata.name = rdata.name
            idata.qua = rdata.qua
            idata.irune = rdata.irune
            idata.lock = true
        end
        user.PowersSaveDatas[id] = nil
        gameLogic:getInstance():saveGame()
        bchange = true
        self.property[pro_key["pro_key_stage1"]] = idata
    end
end
function equipData:runeWorld()
    -- if self.quality == QUA_NON then
    local rw = cfg[CfgType.RUNEWORLD]
    for key, var in pairs(rw) do
        if var.pos == self:getDef().mtype and
            var.holes == self:getHoleNum() and
            var.condition == table.concat(self.rune_inlay, ":") then
            self.quality = QUA_RUNEW
            local pros = cfg[CfgType.RUNEWORLD_PRO][var.pro_id]
            for k, v in pairs(pros) do
                local msg = { }
                msg.key = v.key
                if v.rand then
                    msg.val = v.val + me.getRandom(v.rand)
                else
                    msg.val = v.val
                end
                if v.rand then
                    msg.mx = v.val .. "-" ..(v.val + v.rand)
                end
                self.property[pro_key["pro_key_rune" .. k]] = msg
            end
            self.bruneword = var.id
            gameLogic:getInstance():save( { SAVE_ARMOR_DATA })
            taskMgr:getInstance():checkCurMainTask(TASK_TYPE_RUNEWORLD,1)
            return
        end
    end
    -- end
end
function equipData:reRandRuneWorld()
    local var = cfg[CfgType.RUNEWORLD][self.bruneword]
    if var then
        local pros = cfg[CfgType.RUNEWORLD_PRO][var.pro_id]
        for k, v in pairs(pros) do
            local msg = { }
            msg.key = v.key
            if v.rand then
                msg.val = v.val + me.getRandom(v.rand)
            else
                msg.val = v.val
            end
            if v.rand then
                msg.mx = v.val .. "-" ..(v.val + v.rand)
            end
            self.property[pro_key["pro_key_rune" .. k]] = msg
        end
        gameLogic:getInstance():save( { SAVE_ARMOR_DATA })
        bombBridge.upLog("reruneworld", var.id, 0)
    end
end
function equipData:getMadeSoltPro()
      local rand_pro = { "def", "pow", "int", "con", "dex" }
      local pros = {}
      for i = 1, equip_pro_num do
           local pro = self.property[pro_key["pro_key_lv" .. i]]
           if pro and pro.solt ~= nil then               
                table.insert(pros,pro)                
           end
      end
      if #pros == 0 then
          for i = 1, equip_pro_num do
               local pro = self.property[pro_key["pro_key_lv" .. i]]
               if pro then
                   if pro.key ~= "sk"
                    and pro.key ~=  "def" 
                    and pro.key ~=  "pow" 
                    and pro.key ~=  "int" 
                    and pro.key ~=  "con" 
                    and pro.key ~=  "dex" then
                        table.insert(pros,pro)
                    end
               end
          end
      end
      return pros
end
function equipData:reAddMadeSoltPro(pros)
     if #pros <= 5 then
         for key, var in pairs(pros) do
              for i = 1, equip_pro_num do                       
                   if self.property[pro_key["pro_key_lv" .. i]] == nil then
                        self.property[pro_key["pro_key_lv" .. i]] = var
                        break
                   end
                   
              end
         end
     end
end
function equipData:comRuneWorld(wdata)
    local var = cfg[CfgType.RUNEWORLD][self.bruneword]
    if var then       
        for i = 1, 10 do
            if self.property[pro_key["pro_key_rune" .. i]] then
                self.property[pro_key["pro_key_rune" .. i]] = math.max(self.property[pro_key["pro_key_rune" .. i]].val, wdata.property[pro_key["pro_key_rune" .. i]].val)
            end
        end
        wdata.state = EQUIP_STATE_BROKEN
        gameLogic:getInstance():save( { SAVE_ARMOR_DATA })
        bombBridge.upLog("comruneworld", var.id, 0)
    end

end
function equipData:seeRuneWorld(id)

    local var = cfg[CfgType.RUNEWORLD][id]

    self.quality = QUA_RUNEW
    local pros = cfg[CfgType.RUNEWORLD_PRO][var.pro_id]
    for k, v in pairs(pros) do
        local msg = { }
        msg.key = v.key
        if v.rand then
            msg.val = v.val + me.getRandom(v.rand)
        else
            msg.val = v.val
        end
        if v.rand then
            msg.mx = v.val .. "-" ..(v.val + v.rand)
        end
        self.property[pro_key["pro_key_rune" .. k]] = msg
    end
    self.bruneword = var.id
    -- end
end
function equipData:unInlay(rdefid)
    addRune(rdefid)
    table.removebyvalue(self.rune_inlay, rdefid, false)
    self:updateRuneInlayData()
end
function equipData:getInlayNums()
    return #self.rune_inlay
end
function equipData:getDef()
    return cfg[CfgType.WEAPON][self.defid]
end
--- kind = 1 武力，2 智力
function equipData:getFightPower(kind)
    local tmp = me.copyTab(pro_arr)
    local pv = 0
    for key, var in pairs(tmp) do
        if key == "sk" then
            pv = pv + 5000
        else
            pv = pv + getFightCheck(key, kind) * self:getImgdueVar(key)
        end
    end
    return pv
end
function equipData:getSaveString()
    local msg = { }
    msg.defid = self.defid
    msg.itemid = self.itemid
    msg.state = self.state
    msg.quality = self.quality
    msg.main_rand = self.main_rand
    msg.cap_rand = self.cap_rand
    msg.exp = self.exp
    msg.level = self.level
    msg.magic_rand = self.magic_rand
    msg.cap_count = self.cap_count
    msg.intensify_count = self.intensify_count
    msg.intensify_rand = self.intensify_rand
    msg.hole_num = self.hole_num
    msg.rune_inlay = self.rune_inlay
    msg.bruneword = self.bruneword or 0
    -- 表
    msg.int_imgbue_def = self.int_imgbue_def
    -- 表
    msg.int_imgbue_dmg = self.int_imgbue_dmg
    -- 表
    msg.int_imgbue = self.int_imgbue
    -- 表
    msg.time = self.time or 0
    msg.locked = self.locked or false
    msg.isnew = self.isnew or 0
    msg.level_rand = self.level_rand or 0
    msg.souldata = self.souldata or { }
    -- 表
    msg.soulqua = self.soulqua or 0
    msg.soulkey = self.soulkey or ""
    msg.soulnum = self.soulnum or 1
    msg.eroleid = self.eroleid or ""

    msg.property = self.property or { }
    msg.star_ec = self.star_ec or ec_int(1)
    return me.cjson.encode(msg)
end
function equipData:loadSaveString(str)
    local msg = me.cjson.decode(str)
    self.defid = msg.defid
    self.itemid = msg.itemid
    self.state = msg.state
    self.quality = msg.quality
    self.main_rand = msg.main_rand
    self.cap_rand = msg.cap_rand
    self.exp = msg.exp
    self.level = msg.level or ec_int(0)
    self.magic_rand = msg.magic_rand
    self.cap_count = msg.cap_count
    self.intensify_count = msg.intensify_count
    self.intensify_rand = msg.intensify_rand
    self.hole_num = msg.hole_num
    self.rune_inlay = msg.rune_inlay
    self.int_imgbue_def = msg.int_imgbue_def or { }
    self.int_imgbue_dmg = msg.int_imgbue_dmg or { }
    self.int_imgbue = msg.int_imgbue or { }
    self.time = msg.time or 0
    self.isnew = 0
    self.locked = msg.locked or false
    self.level_rand = msg.level_rand or 0
    self.souldata = msg.souldata or { }
    self.bruneword = msg.bruneword or 0
    -- 表
    self.soulqua = msg.soulqua or 0
    self.soulkey = msg.soulkey or ""
    self.soulnum = msg.soulnum or 1
    self.eroleid = msg.eroleid or ""
    self.property = msg.property or { }
    self.star_ec = msg.star_ec or ec_int(0)
    self:updateRuneInlayData()
end
function equipData:init()
    print("equipData:init")
    return true
end
function equipData:isLocked()
    return self.locked
end
-- 获取武器的某项附加属性
function equipData:getImgdueVar(name)
    local v = 0
    v =(self.souldata[name] or 0)
    return(self.rune_add_data[name] or 0) +(self:getImgdue()[name] or 0) + v
end
function equipData:resetImgbue()
    self.imgbue = nil
end
function equipData:getMaxLevel()
    if tonumber(self:getDef().kind) == 1 then
        return WEAPON_MAX_LEVEL
    elseif tonumber(self:getDef().kind) == 2 then
        return ARMOR_MAX_LEVEL
    end
end
function equipData:addRandImdue(key, v)
    if self.int_imgbue[key] == nil then
        self.int_imgbue[key] = 0
    end
    self.int_imgbue[key] = self.int_imgbue[key] + v
end
function equipData:getImgdue()
    -- 附加属性
    --        if self.imgbue == nil then
    --            self.imgbue = { }
    --            if self:getDef().imgbue then
    --                local t = me.split(self:getDef().imgbue, ",")
    --                if t then
    --                    for key, var in pairs(t) do
    --                        local imgbue = me.split(var, ":")
    --                        if imgbue then
    --                            self.imgbue[imgbue[1]] = imgbue[2]
    --                        end
    --                    end
    --                end
    --            end
    --            -- 加上强化属性
    --            for key, var in pairs(self.int_imgbue) do
    --                if self.imgbue[key] == nil then
    --                    self.imgbue[key] = 0
    --                end
    --                self.imgbue[key] = self.imgbue[key] + var
    --            end
    --        end
    --        return self.imgbue
    self.imgbue = { }
    self.skills = { }
    local bclear = false
    for key, var in pairs(self.property) do    
            if var.key then    
                if var.key ~= "sk" then

                    if self.imgbue[var.key] == nil then
                        self.imgbue[var.key] = 0
                    end                    
                    self.imgbue[var.key] = self.imgbue[var.key] + var.val
                    if isPercentVal(var.key) then
                        if var.val > 300 then
                             bombBridge.upLog("err_pro_".. var.key,var.val,0)
                        end
                    end
                else
                    table.insert(self.skills, var.val)
                end
            else
                bclear = true
            end
    end
    if bclear  then
        self.state = EQUIP_STATE_SAVE
    end
    if self:getDef().imgbue then
        local t = me.split(self:getDef().imgbue, ",")
        if t then
            for key, var in pairs(t) do
                local imgbue = me.split(var, ":")

                if imgbue then
                    if imgbue[1] ~= "sk" then
                        if self.imgbue[imgbue[1]] == nil then
                            self.imgbue[imgbue[1]] = 0
                        end
                        self.imgbue[imgbue[1]] = self.imgbue[imgbue[1]] + tonumber(imgbue[2])
                    else
                        table.insert(self.skills, tonumber(imgbue[2]))
                    end
                end
            end
        end
    end
    return self.imgbue
end
-- 获取装备实际穿戴等级
function equipData:getElevel()
    local def = self:getDef()
    return math.max(1, def.elevel + self.level_rand)
end

function equipData:getSoulPrice()
    local p_price = self:getDef().elevel * 50
    return p_price
end
function equipData:getPropertyStrByKey(key)
    if self.property[pro_key[key]].key == "sk" then
        local sdata = cfg[CfgType.SKILL][tonumber(self.property[pro_key[key]].val)]
        return sdata.level .. "级" .. sdata.name
    else
        return getImgdueVal(self.property[pro_key[key]].key, self.property[pro_key[key]].val, self.property[pro_key[key]].mx)
    end
end
function equipData:bHaveProTitle()
    return self.property[pro_key.pro_key_title] ~= nil
end
-- 卖出价值
function equipData:getSellVal()
    if self:getDef().kind == EQUIP_KIND_ARMOR then
        local lv = self:getLevel()
        local st = self:getStar()
        local sum = 0
        if lv > 0 then
            for var = 1, lv do
                sum = sum +(lv + 1) * math.pow(2, st - 1)
            end
            return math.floor(sum * 0.8)
        else
            return math.pow(2, st - 1)
        end
    elseif self:getDef().kind == EQUIP_KIND_WEAPON then
        return math.pow(2, self.quality - 1) * 500 + self:getElevel() * 20
    end
    return 0
end
function equipData:getForgingCost()
    local lv = self:getLevel()
    local st = ec_int(self.star_ec)
    return(lv + 1) * math.pow(2, st - 1) * 2
end
function equipData:getForgingOdds()
    local lv = self:getLevel()
    if lv <= 9 then
        return ec_int(100)
    else
        return ec_int(100 -(lv - 9) * 10)
    end
end
-- 强化
function equipData:doForging()
    if self:getLevel() >= 15 then
        showTips("已达最大等级")
        return
    end
    local cost = self:getForgingCost()
    if getItemNum(82244) >= cost then
        redItem(82244, cost)
        if me.getRandom(100) <= ec_int(self:getForgingOdds()) then
            self:addLevel(1)
            local lv = self:getLevel()
            local star = self:getStar()
            -- 加主属性
            for key, var in pairs(cfg_runeproperty_all) do
                if self.property[pro_key.pro_key_main].key == var.pkey then
                    self.property[pro_key.pro_key_main].val = self.property[pro_key.pro_key_main].val + var["streng_v" .. star]
                    break
                end
            end

            if lv == 3 or lv == 6 or lv == 9 or lv == 12 or lv == 15 then
                if lv == self.quality * 3 then
                    self.quality = math.min(QUA_PERFACE, lv / 3 + 1)
                    -- local data = { }
                    for i = 1, equip_pro_num do
                        if self.property[pro_key["pro_key_lv" .. i]] == nil then
                            -- self.property[pro_key["pro_key_lv" .. i]] = self:getRuneProperty(1, data)[1]
                            self.property[pro_key["pro_key_lv" .. i]] = self:getRuneProperty(1)[1]
                            -- 只加一条
                            break
                            -- else
                            -- table.insert(data, self.property[pro_key["pro_key_lv" .. i]].key)
                        end
                    end
                else

                    local ks = { }
                    for i = 1, equip_pro_num do
                        if self.property[pro_key["pro_key_lv" .. i]] then
                            table.insert(ks, self.property[pro_key["pro_key_lv" .. i]].key)
                        end
                    end
                    local rd = me.getRandom(#ks * 50)
                    local sum = 0
                    local ck = nil
                    for key, var in pairs(ks) do
                        if sum + 50 >= rd then
                            ck = var
                            break
                        else
                            sum = sum + 50
                        end
                    end
                    for i = 1, equip_pro_num do
                        if self.property[pro_key["pro_key_lv" .. i]] and self.property[pro_key["pro_key_lv" .. i]].key == ck then
                            local p = self:getRunePropertyByKey(ck)
                            local mx = self.property[pro_key["pro_key_lv" .. i]].mx
                            if mx and p.mx then
                                -- 这里是增加参考最大值和最小值
                                local xs = me.split(mx, "-")
                                local ps = me.split(p.mx, "-")
                                if xs and ps then
                                    xs[1] = tonumber(xs[1]) + tonumber(ps[1])
                                    xs[2] = tonumber(xs[2]) + tonumber(ps[2])
                                    self.property[pro_key["pro_key_lv" .. i]].mx = xs[1] .. "-" .. xs[2]
                                end
                            end
                            self.property[pro_key["pro_key_lv" .. i]].val = self.property[pro_key["pro_key_lv" .. i]].val + p.val
                            break
                        end
                    end
                end
            end
        else
            showTips("强化失败")
        end
        gameLogic:getInstance():saveGame()
    else
        me.showMessageDialog("符文精华不足，请前往背包分解符文获得")
    end
end
function equipData:getProSlot()
    if self.property[pro_key["pro_key_lv1"]] then
        return self.property[pro_key["pro_key_lv1"]].key
    else
        return nil
    end
end
--
function equipData:addSoulAblityByGroup(horsegp)
     if horsegp then
        local tb = cfg[CfgType.CFG_ABILITYGROUP][horsegp]
        if tb then
            local rd = me.getRandom(tb.expect)
            local idx = 0
            for key, var in pairs(tb) do
                if type(var) == "table" then
                    idx = idx + var.expect
                    if idx >= rd then
                        local msg = cfg[CfgType.CFG_EQUIPABILITY][var.abilityid]
                        if msg then
                            local ks = { }
                            ks.key = msg.key
                            if key ~= "sk" then                                
                                ks.mx = msg.min .. "-" .. msg.max
                                ks.val = me.getRandomBetween(msg.min, msg.max)                                
                            else
                                ks.val = msg.min
                            end                                
                            self.soulkey = msg.key
                            self.souldata[self.soulkey] =  ks.val
                        end
                        return
                    end
                end
            end
        end
    end
end
function equipData:getStageImgbueProPer()
    if ec_int(user.stage) == 1 then
          return 0
    elseif  ec_int(user.stage) == 2 then
          return 0
    elseif  ec_int(user.stage) == 3 then
          return 0.3
    elseif  ec_int(user.stage) == 4 then  
         return 0.5
    end
end
function equipData:addStageAbility(irune)
    local horsegp = self:getStageAbilityGroup()
    if horsegp then
        local tb = cfg[CfgType.CFG_ABILITYGROUP][horsegp]
        if tb then
            local rd = me.getRandom(tb.expect)
            local idx = 0
            for key, var in pairs(tb) do
                if type(var) == "table" then
                    idx = idx + var.expect
                    if idx >= rd then
                        local msg = cfg[CfgType.CFG_EQUIPABILITY][var.abilityid]
                        if msg then
                            local ks = { }
                            ks.key = msg.key
                            if key ~= "sk" then
                                if irune then
                                    local xper = 1
                                    if self.quality == 5 then
                                        xper = 1.1
                                    end
                                    if self:getDef().elevel >= 150 then
                                        xper = xper + 0.1
                                    elseif self:getDef().elevel >= 140 then
                                        xper = xper + 0.1
                                    elseif self:getDef().elevel >= 130 then
                                        xper = xper + 0.1
                                    elseif self:getDef().elevel >= 120 then
                                        xper = xper + 0.1
                                    end
                                    xper = xper + self:getStageImgbueProPer()
                                    if xper > 1 then
                                        xper = me.getRandomBetween(100, xper * 100) / 100
                                    end
                                    local min = math.floor(msg.min * xper)
                                    local max = math.floor(msg.max * xper)
                                    ks.mx = min .. "-" .. max
                                    ks.val = me.getRandomBetween(min, max)
                                else
                                    local xper = 1
                                    xper = xper + self:getStageImgbueProPer()
                                    local min = math.floor(msg.min * xper)
                                    local max = math.floor(msg.max * xper)
                                    ks.mx = min .. "-" .. max
                                    ks.val = me.getRandomBetween(min, max)                                 
                                end
                            else
                                ks.val = msg.min
                            end
                            ks.min = msg.min
                            ks.defid = msg.id
                            ks.name = msg.name
                            ks.qua = msg.qua
                            ks.irune = irune
                            for i = 1, 4 do
                                if self.property[pro_key["pro_key_stage" .. i]] == nil then
                                    self.property[pro_key["pro_key_stage" .. i]] = ks
                                    return
                                end
                            end
                        end
                        return
                    end
                end
            end
        end
    end
end
function equipData:isSameName(data)
    return self:getDef().id == data:getDef().id 
end
function equipData:addHoseAbility()
    if self:getDef().horsegp then
        local tb = cfg[CfgType.CFG_ABILITYGROUP][self:getDef().horsegp]
        if tb then
            local rd = me.getRandom(tb.expect)
            local idx = 0
            for key, var in pairs(tb) do
                if type(var) == "table" then
                    idx = idx + var.expect
                    if idx >= rd then
                        local msg = cfg[CfgType.CFG_EQUIPABILITY][var.abilityid]                       
                        if msg then
                            local ks = { }
                            ks.key = msg.key                           
                            if msg.key ~= "sk" then
                                ks.mx = msg.min .. "-" .. msg.max
                                ks.val = me.getRandomBetween(msg.min, msg.max)
                            else
                                ks.val = msg.min
                            end
                            ks.name = msg.name
                            ks.qua = msg.qua
                            for i = 1, 2 do
                                if self.property[pro_key["pro_key_horse" .. i]] == nil then
                                    self.property[pro_key["pro_key_horse" .. i]] = ks
                                    return
                                end
                            end
                        end
                        return
                    end
                end
            end
        end
    end
end
--获取坐骑词条的品质
function equipData:getHorseAbility()
    for i = 1, 2 do
           if self.property[pro_key["pro_key_horse" .. i]]  then
               local ks =  self.property[pro_key["pro_key_horse" .. i]] 
               return ks.qua - 3
            end
    end
    return 0
end
local rand_pro = { "def", "pow", "int", "con", "dex" }
-- 白色，绿色，蓝色，紫色，橙色，红色期望
local expect = {
    70,30,5
    -- ,10,10,5
}

function equipData:initForEquip(star_ec)    
    self.star_ec = star_ec or RandStageEquip()
    local holeMaxPer = 1.2
    local holeMinPer = 0.8
    local holeXPer = 0.2
    -- 坐骑随机参数

    local MainXper = 0.3
    local quaXPer = 0.5
    local quaMinPer = 0.85

    if self.star_ec == ec_int(2) then
        holeMaxPer = 1.3
        holeMinPer = 0.9
        holeXPer = 0.3

        MainXper = 0.35
        quaXPer = 0.55
        quaMinPer = 0.9
    elseif self.star_ec == ec_int(3) then
        holeMaxPer = 1.4
        holeMinPer = 1.0
        holeXPer = 0.4
        MainXper = 0.4
        quaXPer = 0.62
        quaMinPer = 0.96
    elseif self.star_ec == ec_int(4) then
        holeMaxPer = 1.5
        holeMinPer = 1.1
        holeXPer = 0.5
        MainXper = 0.43
        quaXPer = 0.7
        quaMinPer = 1.04
    end

    -- 坐骑
    if self:getDef().mtype == 7 then
        local q = getRandByExpect( { 1000, 800, 100, 60, 10 })
        self.quality = q
        if ec_int(self:getDef().ec_star) == 1 then

            if self.quality > 1 then
                self:addEquipPro(self.quality - 1, holeMaxPer, holeMinPer)
            end
        else
            self.quality = math.max(self.quality, ec_int(self:getDef().ec_star))
            if self.quality > 1 then
                self:addEquipPro(self.quality - 1, holeMaxPer - 0.2 +(self.quality - 4) * holeXPer, holeMinPer + 0.05)
            end
        end
        if self.property[pro_key.pro_key_main] == nil then
            self.property[pro_key.pro_key_main] = { }
        end
        self.property[pro_key.pro_key_main].key = self:getDef().main_key
        self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
        self.property[pro_key.pro_key_main].val = me.getRandomBetween(math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper), math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper))

        self.cap_rand = 1
        self.cap_count = 1
        self.intensify_rand = 1
        self.intensify_count = 1
        -- 随机孔数
        local hole = getRandByExpect( { 70, 30, 10, 10, 5, 2 })
        self.hole_num = ec_int(hole - 1)
        if self.quality == 4 then
            if me.getRandom(100) <= 50 then                
                self:addHoseAbility()
            end
        elseif self.quality >= 5 then            
            self:addHoseAbility()
        end
    else
        if ec_int(self:getDef().ec_star) == 1 then
            local q = getRandByExpect(expect)
            self.quality = q
            if self.quality >= 1 then
                self:addEquipPro(1, self.quality * quaXPer, quaMinPer)
            end
        else
            self.quality = ec_int(self:getDef().ec_star)
            if self.quality >= 1 then
                self:addEquipPro(1, self.quality * quaXPer, quaMinPer)
            end
        end
        if self.property[pro_key.pro_key_main] == nil then
            self.property[pro_key.pro_key_main] = { }
        end
        self.property[pro_key.pro_key_main].key = self:getDef().main_key
        self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
        self.property[pro_key.pro_key_main].val = me.getRandomBetween(math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper), math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper))
        if self.star_ec >= ec_int(2) and self.quality >= 4 then
            local stageNum = self:getStageAbilityNum()     
            for var = 1, stageNum do
                local irune = false
                if var == 1 then
                    irune = true
                end
                self:addStageAbility(irune)
            end
        end
        self.cap_rand = 1
        self.cap_count = 1
        self.intensify_rand = 1
        self.intensify_count = 1
        -- 随机孔数
        local hole = getRandByExpect( { 70, 30, 10, 10, 5, 2 })
        self.hole_num = ec_int(hole - 1)
    end
end
function equipData:getStageAbilityGroup()
    local mtype = self:getDef().mtype
    local rtype = self:getDef().rtype
    if self.star_ec == ec_int(2) then
        if mtype == 1 then
            if rtype == 1 then
                return 4
            else
                return 5
            end
        else
            if rtype == 1 then
                return 6
            else
                return 7
            end
        end
    elseif self.star_ec == ec_int(3) then
        if mtype == 1 then
            if rtype == 1 then
                return 4
            else
                return 5
            end
        else
            if rtype == 1 then
                return 6
            else
                return 7
            end
        end
    elseif self.star_ec == ec_int(4) then
        if mtype == 1 then
            if rtype == 1 then
                return 4
            else
                return 5
            end
        else
            if rtype == 1 then
                return 6
            else
                return 7
            end
        end
    end

end
function equipData:getStageAbilityNum()
    if self.quality == 3 then
        return 1
    elseif self.quality == 4 then
        return 3
    elseif self.quality == 5 then
        return 4
    elseif self.quality == 6 then
        return 5
    end
    return 0
end
function equipData:initForEquipForMade(stage)
    stage = stage == 0 and 1 or stage
    self.star_ec = ec_int(stage)
    local MainXper = 0.33
    local quaXPer = 0.53
    local holeMaxPer = 1.2
    local holeMinPer = 0.8
    local holeXPer = 0.2

    if self.star_ec == ec_int(2) then
        MainXper = 0.36
        quaXPer = 0.56
    elseif self.star_ec == ec_int(3) then
        MainXper = 0.4
        quaXPer = 0.6
    elseif self.star_ec == ec_int(4) then
        MainXper = 0.45
        quaXPer = 0.65
    end

    if ec_int(self:getDef().ec_star) == 1 then
        local q = getRandByExpect(expect)
        self.quality = q
        if self.quality >= 1 then
            self:addEquipPro(1, self.quality * quaXPer)
        end
    else
        self.quality = ec_int(self:getDef().ec_star)
        if self.quality >= 1 then
            self:addEquipPro(1, self.quality * quaXPer)
        end
    end

    -- 坐骑
    if self:getDef().mtype == 7 then
        local q = getRandByExpect( { 1000, 800, 100, 60, 10 })
        self.quality = q
        if ec_int(self:getDef().ec_star) == 1 then
            if self.quality > 1 then
                self:addEquipPro(self.quality - 1, holeMaxPer, holeMinPer)
            end
        else
            self.quality = math.max(self.quality, ec_int(self:getDef().ec_star))
            if self.quality > 1 then
                self:addEquipPro(self.quality - 1, holeMaxPer - 0.2 +(self.quality - 4) * holeXPer, holeMinPer + 0.05)
            end
        end
        if self.property[pro_key.pro_key_main] == nil then
            self.property[pro_key.pro_key_main] = { }
        end
        self.property[pro_key.pro_key_main].key = self:getDef().main_key
        self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
        self.property[pro_key.pro_key_main].val = me.getRandomBetween(math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper), math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper))

        self.cap_rand = 1
        self.cap_count = 1
        self.intensify_rand = 1
        self.intensify_count = 1
        -- 随机孔数
        local hole = getRandByExpect( { 70, 30, 10, 10, 5, 2 })
        self.hole_num = ec_int(hole - 1)
        if self.quality == 4 then
            if me.getRandom(100) <= 50 then             
                self:addHoseAbility()
            end
        elseif self.quality >= 5 then           
            self:addHoseAbility()
        end
    else
        if self.property[pro_key.pro_key_main] == nil then
            self.property[pro_key.pro_key_main] = { }
        end
        self.property[pro_key.pro_key_main].key = self:getDef().main_key
        self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
        self.property[pro_key.pro_key_main].val = me.getRandomBetween(math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper), math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper))

        if self.star_ec >= ec_int(2) and self.quality >= 4 then
            local stageNum = self:getStageAbilityNum()       
            for var = 1, stageNum do
                local irune = false
                if var == 1 then
                    irune = true
                end
                self:addStageAbility(irune)
            end
        end
        self.cap_rand = 1
        self.cap_count = 1
        self.intensify_rand = 1

        self.intensify_count = 1
        -- 随机孔数
        local hole = getRandByExpect( { 70, 30, 10, 10, 5, 2 })
        self.hole_num = ec_int(hole - 1)
    end
end
-- 刻印几率
local soul_rand = {
    70,50,30,15,10,5
}
function equipData:doSoul()
    local qua = getRandByExpect(soul_rand)
    self.soulqua = qua
    self.soulkey = rand_pro[me.getRandomBetween(1, #rand_pro)]
    self.soulnum = self.soulnum + 1
    self.souldata = { }    
    self.souldata[self.soulkey] = me.getRandomBetween(math.floor(self:getEquipPro().min - self:getEquipPro().min *(6 - qua) / 6),
    math.floor(self:getEquipPro().max - self:getEquipPro().max *(6 - qua) / 6))
    taskMgr:getInstance():checkCurMainTask(TASK_TYPE_SOUL, 1)
    if qua == 6 then
        if me.getRandom(100) <= 30 then
             local index = me.getRandomBetween(2,7) 
             self:addSoulAblityByGroup(index)
        end
    end
end
function equipData:getEquipPro()
    local level = self:getDef().elevel - self:getDef().elevel % 10
    if cfg[CfgType.EQUIP_PRO][level] then
        return cfg[CfgType.EQUIP_PRO][level]
    else
        return { min = 10, max = 20 }
    end
end
function equipData:addEquipPro(num, per, minper)
    -- 武器不出歪属性
    local rand_pro = { "def", "pow", "int", "con", "dex" }
    if self:getDef().kind == EQUIP_KIND_WEAPON then
        if self:getDef().rtype then
            if self:getDef().rtype == 2 then
                rand_pro = { "def", "int", "con", "dex" }
            elseif self:getDef().rtype == 1 then
                rand_pro = { "def", "pow", "con", "dex" }
            end
        end
    end
    minper = minper or 1
    per = per or 1
    if num > 0 then
        for var = 1, num do
            local ks = { }
            ks.key = rand_pro[me.getRandomBetween(1, #rand_pro)]
            ks.mx = math.ceil(self:getEquipPro().min * per * minper) .. "-" .. math.ceil(self:getEquipPro().max * per)
            ks.val = me.getRandomBetween(math.ceil(self:getEquipPro().min * per * minper), math.ceil(self:getEquipPro().max * per))
            for i = 1, equip_pro_num do
                if self.property[pro_key["pro_key_lv" .. i]] == nil then
                    self.property[pro_key["pro_key_lv" .. i]] = ks
                    break
                end
            end
        end
    end
end
-- 锻造附加
function equipData:addMadeSlotData(adddata)
    for key, var in pairs(adddata) do
        local itemdata = cfg[CfgType.ITEM][var]
        local slotdata = cfg[CfgType.CFG_SLOT][itemdata.useVar]
        local min = slotdata.min_val
        local max = slotdata.max_val
        local cur = 0
        if slotdata.blevel and slotdata.blevel == 1 then
            min = math.floor(min * self:getDef().elevel / slotdata.maxLevel)
            max = math.floor(max * self:getDef().elevel / slotdata.maxLevel)
        end
        local ks = { }
        if slotdata.kind == 1 then
            cur = me.getRandomBetween(min, max)
            ks.mx = min .. "-" .. max
        elseif slotdata.kind == 2 then
            cur = min
        end
        ks.key = slotdata.key
        ks.val = cur
        ks.solt = itemdata.useVar
        for i = 1, equip_pro_num do
            if self.property[pro_key["pro_key_lv" .. i]] == nil then
                self.property[pro_key["pro_key_lv" .. i]] = ks
                break
            end
        end
    end
end
function equipData:addLimitPro(key,val,id)
       local ks = { }        
        ks.key = key
        ks.val = tonumber( val )
        ks.solt = tonumber( id )
        for i = 1, equip_pro_num do
            if self.property[pro_key["pro_key_lv" .. i]] == nil then
                self.property[pro_key["pro_key_lv" .. i]] = ks
                break
            end
        end
end
function equipData:initRandForEquip(args)
    self.star_ec = RandStageEquip()
    local MainXper = 0.3
    if self.star_ec == ec_int(2) then
        MainXper = 0.36
    elseif self.star_ec == ec_int(3) then
        MainXper = 0.4
    elseif self.star_ec == ec_int(4) then
        MainXper = 0.45
    end

    if ec_int(self:getDef().ec_star) == 1 then
        local q = getRandByExpect(expect)
        self.quality = q
        if self.quality >= 1 then
            self.property[pro_key["pro_key_lv1"]] = { key = "non", val = "??" }
        end
    else
        self.quality = ec_int(self:getDef().ec_star)
        if self.quality >= 1 then
            self.property[pro_key["pro_key_lv1"]] = { key = "non", val = "??" }
        end
    end
    if self.property[pro_key.pro_key_main] == nil then
        self.property[pro_key.pro_key_main] = { }
    end
    self.property[pro_key.pro_key_main].key = self:getDef().main_key
    self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
    self.property[pro_key.pro_key_main].val = "??"
    self.cap_rand = 1
    self.cap_count = 1
    self.intensify_rand = 1
    self.intensify_count = 1
end
function equipData:resetMainPro()
    self.star_ec = RandStageEquip()
    local MainXper = 0.3
    if self.star_ec == ec_int(2) then
        MainXper = 0.36
    elseif self.star_ec == ec_int(3) then
        MainXper = 0.4
    elseif self.star_ec == ec_int(4) then
        MainXper = 0.45
    end
    if self.property[pro_key.pro_key_main] == nil then
            self.property[pro_key.pro_key_main] = { }
    end
    self.property[pro_key.pro_key_main].key = self:getDef().main_key
    self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper)
    self.property[pro_key.pro_key_main].val = me.getRandomBetween(math.ceil(self:getDef().main_min *(1 + self.quality) * MainXper), math.ceil(self:getDef().main_max *(1 + self.quality) * MainXper))
end
function equipData:initRandForEquipForMade(args)
    self.star_ec = RandStageEquip()
    local per = 0.33
    if self.star_ec == ec_int(2) then
        per = 0.36
    elseif self.star_ec == ec_int(3) then
        per = 0.4
    elseif self.star_ec == ec_int(4) then
        per = 0.45
    end
    if ec_int(self:getDef().ec_star) == 1 then
        local q = getRandByExpect(expect)
        self.quality = q
        if self.quality >= 1 then
            self.property[pro_key["pro_key_lv1"]] = { key = "non", val = "??" }
        end
    else
        self.quality = ec_int(self:getDef().ec_star)
        if self.quality >= 1 then
            self.property[pro_key["pro_key_lv1"]] = { key = "non", val = "??" }
        end
    end
    if self.property[pro_key.pro_key_main] == nil then
        self.property[pro_key.pro_key_main] = { }
    end
    self.property[pro_key.pro_key_main].key = self:getDef().main_key

    self.property[pro_key.pro_key_main].mx = math.ceil(self:getDef().main_min *(1 + self.quality) * per) .. "-" .. math.ceil(self:getDef().main_max *(1 + self.quality) * per)
    self.property[pro_key.pro_key_main].val = "??"
    -- me.getRandomBetween( math.ceil( self:getDef().main_min * (1 +  self.quality)*0.3) ,math.ceil( self:getDef().main_max * (1 +  self.quality)*0.3 ))
    self.cap_rand = 1
    self.cap_count = 1
    self.intensify_rand = 1
    self.intensify_count = 1
end
function equipData:initForRandRune()
    self.quality = QUA_BAD
    -- 星级
    self.star_ec = self:getDef().ec_star
    -- 随机主属性 1
    self.property[pro_key.pro_key_main] = { key = "non", val = "??" }

    -- 随机前缀
    if me.getRandom(100) <= 20 then
        self.property[pro_key.pro_key_title] = { key = "non", val = "??" }
        -- self:getRuneProperty(1)[1]
    end
    local pnum = 4
    if pnum > 0 then
        -- 品质附加属性
        local ps = self:getRuneProperty(pnum)
        for i = 1, pnum do
            self.property[pro_key["pro_key_lv" .. i]] = { key = "non", val = "??" }
        end
    end
    self.cap_rand = 1
    self.cap_count = 1
    self.intensify_rand = 1
    self.intensify_count = 1
end
-- 刻印几率
local rune_rand = {
    55,30,5,4,1
}
function equipData:initForRune()
    -- 随机颜色
    local rd = me.getRandom(10000)
    local qua = getRandByExpect(rune_rand)
    self.quality = qua
    local pnum = math.max(0, qua - 1)

    -- 星级
    self.star_ec = self:getDef().ec_star

    -- 随机主属性 1
    self.property[pro_key.pro_key_main] = self:getMainRuneProperty()

    -- 随机前缀
    if me.getRandom(100) <= 20 then
        self.property[pro_key.pro_key_title] = self:getMainRuneProperty()
        -- self:getRuneProperty(1)[1]
    end
    if pnum > 0 then
        -- 品质附加属性
        local ps = self:getRuneProperty(pnum)
        for i = 1, pnum do
            self.property[pro_key["pro_key_lv" .. i]] = ps[i]
        end
    end
    self.cap_rand = 1
    self.cap_count = 1
    self.intensify_rand = 1
    self.intensify_count = 1
end
-- 生成指定颜色的符文
function equipData:initForPerfectRune(qua)
    self.quality = qua
    local pnum = math.max(0, qua - 1)

    -- 星级
    self.star_ec = self:getDef().ec_star

    -- 随机主属性 1
    self.property[pro_key.pro_key_main] = self:getMainRuneProperty()
    -- 随机前缀
    self.property[pro_key.pro_key_title] = self:getMainRuneProperty()
    if pnum > 0 then
        -- 品质附加属性
        local ps = self:getRuneProperty(pnum)
        for i = 1, pnum do
            self.property[pro_key["pro_key_lv" .. i]] = ps[i]
        end
    end
    self.cap_rand = 1
    self.cap_count = 1
    self.intensify_rand = 1
    self.intensify_count = 1
end
function equipData:getMainRuneProperty()
    local pos = self:getDef().mtype
    local star = ec_int(self.star_ec)
    local expect = me.getRandom(cfg[CfgType.RUNEPROPERTY][pos].expect)
    local mexp = 0
    local pkey = nil
    local pval = nil
    for key, var in pairs(cfg[CfgType.RUNEPROPERTY][pos]) do
        if type(var) == "table" then
            if mexp + var.expect >= expect then
                pkey = var.pkey
                pval = var["star_v" .. star]
                break
            else
                mexp = mexp + var.expect
            end
        end
    end
    return { key = pkey, val = pval }
end
function equipData:getRunePropertyByKey(vkey)
    local pos = self:getDef().mtype
    local star = ec_int(self.star_ec)
    local pkey = nil
    local pval = nil
    local tmp1 = me.copyTab(cfg_runeproperty_all)
    local ks = { }
    for key, var in pairs(tmp1) do
        if var.pkey == vkey then
            ks.mx = var["star_min" .. star] .. "-" .. var["star_max" .. star]
            ks.val = me.getRandomBetween(var["star_min" .. star], var["star_max" .. star])
            ks.key = vkey
            break
        end
    end
    return ks
end
function equipData:getRuneProperty(num, data)
    local pos = self:getDef().mtype
    local star = ec_int(self.star_ec)
    local pkey = nil
    local pval = nil
    local tmp1 = me.copyTab(cfg_runeproperty_all)
    local tmp2 = { }
    local tmp_expect = cfg_runeproperty_expect
    if data then
        -- 排除的
        for key, var in pairs(cfg_runeproperty_all) do
            local pc = false
            for k, v in pairs(data) do
                if v == var.pkey then
                    pc = true
                    tmp_expect = tmp_expect - var.expect
                    break
                end
            end
            if pc == false then
                table.insert(tmp2, var)
            end
        end
        tmp1 = me.copyTab(tmp2)

    end
    local ks = { }
    for xs = 1, num do
        ks[xs] = { }
        local mexp = 0
        local expect = me.getRandom(tmp_expect)
        for key, var in pairs(tmp1) do
            if mexp + var.expect >= expect then
                ks[xs].key = var.pkey
                ks[xs].mx = var["star_min" .. star] .. "-" .. var["star_max" .. star]
                ks[xs].val = me.getRandomBetween(var["star_min" .. star], var["star_max" .. star])
                tmp_expect = tmp_expect - var.expect
                table.remove(tmp1, key)
                break
            else
                mexp = mexp + var.expect
            end
        end
    end
    return ks
end
function equipData:setLockState(b)
    if b == nil then
        self.locked = not self.locked
    else
        self.locked = b
    end
end
-- 获取孔数
function equipData:getIntensify()
    return self.intensify_count
end
function equipData:getSpd()
    return self:getDef().spd
end
function equipData:getQualityText()
    return quatext[me.toNum(self.quality)]
end
function equipData:getQualityColor()
    print(self.quality)
    if self.bruneword > 0 then
        return quacolor[QUA_RUNEW]
    else
        return quacolor[me.toNum(self.quality)]
    end
end
function equipData:resetEquip()
    if self.bruneword == 0 then
        self.quality = ec_int(self:getDef().ec_star)
        for i = 1, 10 do
            self.property[pro_key["pro_key_rune" .. i]] = nil
        end
        for k, v in pairs(self.rune_inlay) do
            addRune(v)
        end
        self.rune_inlay = { }
        self:updateRuneInlayData()
    end
end
--重置神纹
function equipData:resetEquipRune()
    if self.bruneword ~= 0 then
        self.quality = ec_int(self:getDef().ec_star)
        for i = 1, 10 do
            self.property[pro_key["pro_key_rune" .. i]] = nil
        end
        self.bruneword = 0
        self.rune_inlay = { }
        self:updateRuneInlayData()
    end
end
function equipData:create(defid)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            m:initForRune()
        elseif m:getDef().kind == EQUIP_KIND_WEAPON then
            m:initForEquip()
        end
        return m
    end
    return nil
end
function equipData:createForNext(defid, ec)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            m:initForRune()
        elseif m:getDef().kind == EQUIP_KIND_WEAPON then
            if ec > 5 then
                m:initForEquip()
            else
                m:initForEquip(ec_int(ec))
            end
        end
        return m
    end
    return nil
end
function equipData:createForMade(defid, stage)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            m:initForRune()
        elseif m:getDef().kind == EQUIP_KIND_WEAPON then
            m:initForEquipForMade(stage)
        end
        return m
    end
    return nil
end
function equipData:createForPerfectRune(defid, qua)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            qua = qua or 1
            m:initForPerfectRune(qua)
        end
        return m
    end
    return nil
end
function equipData:createUnknown(defid)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            m:initForRandRune()
        elseif m:getDef().kind == EQUIP_KIND_WEAPON then
            m:initRandForEquip()
        end
        return m
    end
    return nil
end
function equipData:createUnknownForMade(defid)
    local m = equipData.new(defid)
    if m and m:init() then
        if m:getDef().kind == EQUIP_KIND_ARMOR then
            m:initForRandRune()
        elseif m:getDef().kind == EQUIP_KIND_WEAPON then
            m:initRandForEquipForMade()
        end
        return m
    end
    return nil
end

