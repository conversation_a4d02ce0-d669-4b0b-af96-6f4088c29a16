# -*- coding: utf-8 -*-
"""
基于现有Item类的装备扩展系统
在保持Item类通用性的基础上，为装备类型添加专门的功能
"""

from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime
import time
import random
import uuid
from enum import IntEnum
from game_database import Item, ItemType
from enums import ItemType as ItemTypeEnum
from logger_config import setup_logger

logger = setup_logger(__name__)

class EquipState(IntEnum):
    """装备状态枚举"""
    UNEQUIPPED = 1  # 未穿戴
    EQUIPPED = 2    # 已穿戴
    BROKEN = 3      # 已损坏
    RESERVED = 4    # 保留的，不显示

class EquipQuality(IntEnum):
    """装备品质枚举"""
    WHITE = 1      # 白色 - 普通
    GREEN = 2      # 绿色 - 优秀
    BLUE = 3       # 蓝色 - 精良
    PURPLE = 4     # 紫色 - 史诗
    ORANGE = 5     # 橙色 - 传说
    RED = 6        # 红色 - 神话

class PropertyType(str):
    """属性类型"""
    MAIN = "main"           # 主属性
    RANDOM = "random"       # 随机属性
    RUNE_WORD = "rune_word" # 符文之语属性
    POWER = "power"         # 威能属性
    ENCHANT = "enchant"     # 附魔属性

class EquipmentMixin:
    """
    装备功能混入类
    为Item类添加装备特有的功能，而不改变Item类的基础结构
    """
    
    def __init__(self):
        """初始化装备混入功能"""
        self._equipment_initialized = False
    
    def _ensure_equipment_attributes(self):
        """确保装备属性存在"""
        if not self._equipment_initialized:
            # 初始化装备特有的属性
            if not hasattr(self, 'attributes'):
                self.attributes = {}
            
            # 确保装备基础属性存在
            if 'equipment_data' not in self.attributes:
                self.attributes['equipment_data'] = {
                    'level': 1,
                    'star': 1,
                    'quality': EquipQuality.WHITE.value,
                    'holes': 0,
                    'state': EquipState.UNEQUIPPED.value,
                    'locked': False,
                    'runes': [],
                    'rune_word_id': None,
                    'properties': {
                        PropertyType.MAIN: {},
                        PropertyType.RANDOM: [],
                        PropertyType.RUNE_WORD: [],
                        PropertyType.POWER: [],
                        PropertyType.ENCHANT: []
                    }
                }
            
            self._equipment_initialized = True
    
    @property
    def equipment_level(self) -> int:
        """获取装备等级"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('level', 1)
    
    @equipment_level.setter
    def equipment_level(self, value: int):
        """设置装备等级"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['level'] = max(1, min(100, value))
    
    @property
    def equipment_star(self) -> int:
        """获取装备星级"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('star', 1)
    
    @equipment_star.setter
    def equipment_star(self, value: int):
        """设置装备星级"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['star'] = max(1, min(10, value))
    
    @property
    def equipment_quality(self) -> EquipQuality:
        """获取装备品质"""
        self._ensure_equipment_attributes()
        quality_value = self.attributes.get('equipment_data', {}).get('quality', EquipQuality.WHITE.value)
        return EquipQuality(quality_value)
    
    @equipment_quality.setter
    def equipment_quality(self, value: EquipQuality):
        """设置装备品质"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['quality'] = value.value
    
    @property
    def equipment_holes(self) -> int:
        """获取孔位数量"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('holes', 0)
    
    @equipment_holes.setter
    def equipment_holes(self, value: int):
        """设置孔位数量"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['holes'] = max(0, min(6, value))
    
    @property
    def equipment_state(self) -> EquipState:
        """获取装备状态"""
        self._ensure_equipment_attributes()
        state_value = self.attributes.get('equipment_data', {}).get('state', EquipState.UNEQUIPPED.value)
        return EquipState(state_value)
    
    @equipment_state.setter
    def equipment_state(self, value: EquipState):
        """设置装备状态"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['state'] = value.value
    
    @property
    def equipment_locked(self) -> bool:
        """获取装备锁定状态"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('locked', False)
    
    @equipment_locked.setter
    def equipment_locked(self, value: bool):
        """设置装备锁定状态"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['locked'] = value
    
    @property
    def equipment_runes(self) -> List[Optional[int]]:
        """获取镶嵌的符文"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('runes', [])
    
    @equipment_runes.setter
    def equipment_runes(self, value: List[Optional[int]]):
        """设置镶嵌的符文"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['runes'] = value
    
    @property
    def equipment_rune_word_id(self) -> Optional[int]:
        """获取符文之语ID"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('rune_word_id')
    
    @equipment_rune_word_id.setter
    def equipment_rune_word_id(self, value: Optional[int]):
        """设置符文之语ID"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['rune_word_id'] = value
    
    @property
    def equipment_properties(self) -> Dict[str, Any]:
        """获取装备属性"""
        self._ensure_equipment_attributes()
        return self.attributes.get('equipment_data', {}).get('properties', {
            PropertyType.MAIN: {},
            PropertyType.RANDOM: [],
            PropertyType.RUNE_WORD: [],
            PropertyType.POWER: [],
            PropertyType.ENCHANT: []
        })
    
    @equipment_properties.setter
    def equipment_properties(self, value: Dict[str, Any]):
        """设置装备属性"""
        self._ensure_equipment_attributes()
        self.attributes['equipment_data']['properties'] = value
    
    def get_total_properties(self) -> Dict[str, int]:
        """计算所有属性的总和"""
        properties = self.equipment_properties
        total = {}
        
        # 合并所有属性
        for prop_type, props in properties.items():
            if isinstance(props, dict):
                for key, value in props.items():
                    if isinstance(value, (int, float)):
                        total[key] = total.get(key, 0) + value
            elif isinstance(props, list):
                for prop in props:
                    if isinstance(prop, dict):
                        key = prop.get('key')
                        value = prop.get('value', 0)
                        if key and isinstance(value, (int, float)):
                            total[key] = total.get(key, 0) + value
        
        return total
    
    def can_equip(self) -> bool:
        """检查是否可以穿戴"""
        return (self.equipment_state == EquipState.UNEQUIPPED and 
                not self.equipment_locked and 
                self.equipment_state != EquipState.BROKEN)
    
    def can_unequip(self) -> bool:
        """检查是否可以卸下"""
        return self.equipment_state == EquipState.EQUIPPED
    
    def can_intensify(self) -> bool:
        """检查是否可以强化"""
        return (self.equipment_level < 100 and 
                not self.equipment_locked and 
                self.equipment_state != EquipState.BROKEN)
    
    def can_upgrade_star(self) -> bool:
        """检查是否可以升星"""
        return (self.equipment_star < 10 and 
                not self.equipment_locked and 
                self.equipment_state != EquipState.BROKEN)
    
    def can_inlay_rune(self) -> bool:
        """检查是否可以镶嵌符文"""
        return (not self.equipment_locked and 
                self.equipment_state != EquipState.BROKEN and
                len([r for r in self.equipment_runes if r is not None]) < self.equipment_holes)
    
    def equip(self) -> Tuple[bool, str]:
        """穿戴装备"""
        if not self.can_equip():
            return False, "装备无法穿戴"
        
        self.equipment_state = EquipState.EQUIPPED
        return True, "穿戴成功"
    
    def unequip(self) -> Tuple[bool, str]:
        """卸下装备"""
        if not self.can_unequip():
            return False, "装备未穿戴"
        
        self.equipment_state = EquipState.UNEQUIPPED
        return True, "卸下成功"
    
    def intensify(self, use_blessing_stone: bool = False) -> Tuple[bool, str]:
        """强化装备"""
        if not self.can_intensify():
            return False, "装备无法强化"
        
        # 计算成功率
        base_success_rate = max(0.1, 1.0 - (self.equipment_level - 1) * 0.05)
        if use_blessing_stone:
            base_success_rate += 0.2
        
        if random.random() < base_success_rate:
            self.equipment_level += 1
            logger.info(f"装备 {self.id} 强化成功，等级: {self.equipment_level}")
            return True, f"强化成功，等级提升到 {self.equipment_level}"
        else:
            # 强化失败，可能降级
            if random.random() < 0.3:  # 30%概率降级
                self.equipment_level = max(1, self.equipment_level - 1)
                logger.warning(f"装备 {self.id} 强化失败并降级，等级: {self.equipment_level}")
                return False, f"强化失败，等级降到 {self.equipment_level}"
            else:
                logger.info(f"装备 {self.id} 强化失败")
                return False, "强化失败"
    
    def upgrade_star(self) -> Tuple[bool, str]:
        """装备升星"""
        if not self.can_upgrade_star():
            return False, "装备无法升星"
        
        # 计算成功率
        success_rate = max(0.1, 1.0 - (self.equipment_star - 1) * 0.1)
        
        if random.random() < success_rate:
            self.equipment_star += 1
            logger.info(f"装备 {self.id} 升星成功，星级: {self.equipment_star}")
            return True, f"升星成功，星级提升到 {self.equipment_star}"
        else:
            logger.info(f"装备 {self.id} 升星失败")
            return False, "升星失败"
    
    def inlay_rune(self, rune_def_id: int, hole_index: int) -> Tuple[bool, str]:
        """镶嵌符文"""
        if not self.can_inlay_rune():
            return False, "装备无法镶嵌符文"
        
        if not (0 <= hole_index < self.equipment_holes):
            return False, "无效的孔位"
        
        # 确保符文列表长度足够
        while len(self.equipment_runes) <= hole_index:
            self.equipment_runes.append(None)
        
        if self.equipment_runes[hole_index] is not None:
            return False, "该孔位已有符文"
        
        self.equipment_runes[hole_index] = rune_def_id
        self._check_rune_word()
        
        logger.info(f"装备 {self.id} 镶嵌符文成功，孔位: {hole_index}, 符文: {rune_def_id}")
        return True, "镶嵌成功"
    
    def remove_rune(self, hole_index: int) -> Tuple[bool, str]:
        """移除符文"""
        if not (0 <= hole_index < len(self.equipment_runes)):
            return False, "无效的孔位"
        
        if self.equipment_runes[hole_index] is None:
            return False, "该孔位没有符文"
        
        removed_rune = self.equipment_runes[hole_index]
        self.equipment_runes[hole_index] = None
        self._check_rune_word()
        
        logger.info(f"装备 {self.id} 移除符文成功，孔位: {hole_index}, 符文: {removed_rune}")
        return True, f"移除符文成功，获得符文: {removed_rune}"
    
    def lock(self) -> Tuple[bool, str]:
        """锁定装备"""
        if self.equipment_locked:
            return False, "装备已锁定"
        
        self.equipment_locked = True
        logger.info(f"装备 {self.id} 已锁定")
        return True, "锁定成功"
    
    def unlock(self) -> Tuple[bool, str]:
        """解锁装备"""
        if not self.equipment_locked:
            return False, "装备未锁定"
        
        self.equipment_locked = False
        logger.info(f"装备 {self.id} 已解锁")
        return True, "解锁成功"
    
    def _check_rune_word(self):
        """检查是否激活了符文之语"""
        if not self.equipment_runes or len(self.equipment_runes) < 2:
            return
        
        # 过滤掉None值
        active_runes = [rune for rune in self.equipment_runes if rune is not None]
        if len(active_runes) < 2:
            return
        
        # 这里应该根据符文组合检查符文之语配置
        rune_pattern = "".join(map(str, active_runes))
        logger.debug(f"检查符文之语: {rune_pattern}")
        
        # 示例：如果符文组合匹配某个符文之语，设置rune_word_id
        # self.equipment_rune_word_id = get_runeword_id(rune_pattern)
    
    def get_part(self) -> str:
        """获取装备部位"""
        return self.attributes.get('part', 'unknown')
    
    def serialize_equipment(self) -> Dict[str, Any]:
        """序列化装备数据，用于发送给客户端"""
        self._ensure_equipment_attributes()
        
        data = {
            'id': self.id,
            'defid': self.defid,
            'owner': self.owner,
            'type': self.type,
            'level': self.equipment_level,
            'star': self.equipment_star,
            'quality': self.equipment_quality.value,
            'holes': self.equipment_holes,
            'state': self.equipment_state.value,
            'locked': self.equipment_locked,
            'runes': self.equipment_runes,
            'rune_word_id': self.equipment_rune_word_id,
            'properties': self.equipment_properties,
            'total_properties': self.get_total_properties(),
            'can_equip': self.can_equip(),
            'can_unequip': self.can_unequip(),
            'can_intensify': self.can_intensify(),
            'can_upgrade_star': self.can_upgrade_star(),
            'can_inlay_rune': self.can_inlay_rune(),
            'part': self.get_part(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        
        return data

class EquipmentItem(Item, EquipmentMixin):
    """
    装备物品类
    继承Item类并混入EquipmentMixin，获得装备特有功能
    """
    
    def __init__(self, **data):
        # 调用Item的初始化
        super().__init__(**data)
        # 调用EquipmentMixin的初始化
        EquipmentMixin.__init__(self)
        
        # 确保类型为装备
        if self.type != ItemTypeEnum.EQUIPMENT:
            self.type = ItemTypeEnum.EQUIPMENT
        
        # 初始化装备属性
        self._ensure_equipment_attributes()
    
    @classmethod
    def create_equipment(cls, defid: int, owner: str, level: int = 1, 
                        quality: EquipQuality = EquipQuality.WHITE, 
                        holes: int = 0, attributes: Optional[Dict[str, Any]] = None) -> 'EquipmentItem':
        """
        创建装备物品
        
        Args:
            defid: 装备定义ID
            owner: 拥有者
            level: 装备等级
            quality: 装备品质
            holes: 孔位数量
            attributes: 额外属性
            
        Returns:
            EquipmentItem: 装备物品实例
        """
        # 准备属性
        equip_attributes = attributes or {}
        equip_attributes['part'] = equip_attributes.get('part', 'unknown')
        
        # 创建装备物品
        equipment = cls(
            defid=defid,
            owner=owner,
            type=ItemTypeEnum.EQUIPMENT,
            attributes=equip_attributes
        )
        
        # 设置装备特有属性
        equipment.equipment_level = level
        equipment.equipment_quality = quality
        equipment.equipment_holes = holes
        
        # 初始化装备属性
        equipment._initialize_equipment_properties()
        
        return equipment
    
    def _initialize_equipment_properties(self):
        """初始化装备属性"""
        try:
            # 根据品质生成随机属性
            random_prop_count = self.equipment_quality.value - 1
            for _ in range(random_prop_count):
                self._add_random_property()
                
        except Exception as e:
            logger.error(f"初始化装备属性失败: {str(e)}")
    
    def _add_random_property(self):
        """添加随机属性"""
        random_props = [
            {"key": "strength", "value": random.randint(1, 10)},
            {"key": "agility", "value": random.randint(1, 10)},
            {"key": "intelligence", "value": random.randint(1, 10)},
            {"key": "vitality", "value": random.randint(1, 10)},
            {"key": "crit_chance", "value": random.randint(1, 5)},
            {"key": "crit_damage", "value": random.randint(5, 20)},
            {"key": "dodge", "value": random.randint(1, 5)},
            {"key": "block", "value": random.randint(1, 5)}
        ]
        
        prop = random.choice(random_props)
        properties = self.equipment_properties
        properties[PropertyType.RANDOM].append(prop)
        self.equipment_properties = properties
    
    async def save_equipment(self, db, redis_client) -> 'EquipmentItem':
        """保存装备到数据库"""
        # 确保装备属性已初始化
        self._ensure_equipment_attributes()
        
        # 调用父类的save方法
        return await super().save(db, redis_client)
    
    @classmethod
    async def load_equipment(cls, item_id: str, owner: str, db, redis_client) -> Optional['EquipmentItem']:
        """从数据库加载装备"""
        try:
            # 从数据库查询物品
            item_data = await db.items.find_one({"id": item_id, "owner": owner, "type": ItemTypeEnum.EQUIPMENT})
            if not item_data:
                return None
            
            # 创建装备物品实例
            equipment = cls(**item_data)
            return equipment
            
        except Exception as e:
            logger.error(f"加载装备失败: {str(e)}")
            return None

# 示例用法
if __name__ == '__main__':
    import asyncio
    
    async def test_equipment():
        # 创建装备
        equipment = EquipmentItem.create_equipment(
            defid=1001,
            owner="player_001",
            level=5,
            quality=EquipQuality.BLUE,
            holes=3,
            attributes={"part": "weaon", "name": "测试武器"}
        )
        
        print(f"创建装备: {equipment.id}")
        print(f"装备等级: {equipment.equipment_level}")
        print(f"装备品质: {equipment.equipment_quality.name}")
        print(f"孔位数量: {equipment.equipment_holes}")
        
        # 强化装备
        success, msg = equipment.intensify()
        print(f"强化结果: {msg}")
        
        # 镶嵌符文
        success, msg = equipment.inlay_rune(501, 0)
        print(f"镶嵌结果: {msg}")
        
        # 穿戴装备
        success, msg = equipment.equip()
        print(f"穿戴结果: {msg}")
        
        # 序列化
        data = equipment.serialize_equipment()
        print(f"序列化数据: {data}")
    
    asyncio.run(test_equipment()) 