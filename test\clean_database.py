import asyncio
import motor.motor_asyncio
import redis.asyncio as redis
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def clean_database():
    try:
        # 连接 MongoDB
        mongo_client = motor.motor_asyncio.AsyncIOMotorClient('***************************************************')
        db = mongo_client.game_server

        # 删除所有 test_user_* 和 test2_user_* 用户
        result = await db.users.delete_many({"id": {"$regex": "^game:v1:test?_user_.*"}})
        logger.info(f"从 MongoDB 删除 {result.deleted_count} 个测试用户")

        # 删除所有的道具
        result = await db.items.delete_many({})       
       
        
        logger.info(f"从 MongoDB 删除 {result.deleted_count} 个测试用户的道具")

        # 连接 Redis
        redis_client = redis.Redis.from_url('redis://:jnmogod@101.35.12.106:6379/0')
        
        # 删除所有用户和道具缓存
        keys = await redis_client.keys("game:v1:users:test*_user_*")
        keys += await redis_client.keys("game:v1:items:test*_user_*:*")
        if keys:
            await redis_client.delete(*keys)
            logger.info(f"从 Redis 删除 {len(keys)} 个测试缓存键: {keys}")
        else:
            logger.info("Redis 中无测试缓存键")

        # 关闭连接
        await redis_client.aclose()
        mongo_client.close()
        logger.info("数据库清理完成")
    except Exception as e:
        logger.error(f"清理数据库失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(clean_database())