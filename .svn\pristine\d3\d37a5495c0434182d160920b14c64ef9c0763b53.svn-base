"""
邮件系统数据库管理器
处理邮件相关的数据库操作
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from mongodb_manager import MongoDBManager
from mail_models import (
    Mail, MailAttachment, MailType, MailStatus, AttachmentStatus,
    MailListItem, MailConstants, MailTemplate, UserTemplateProcessed
)

logger = logging.getLogger(__name__)


class MailDatabaseManager:
    """邮件数据库管理器"""
    
    def __init__(self):
        self.mails_collection = "mails"
        self.attachments_collection = "mail_attachments"
        self.templates_collection = "mail_templates"
        self.user_template_processed_collection = "user_template_processed"
        self._indexes_created = False  # 标记是否已创建索引
    
    async def _get_db(self):
        """获取数据库连接"""
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()

        # 首次使用时自动创建索引
        if not self._indexes_created:
            await self._ensure_indexes_created(db)
            self._indexes_created = True

        return db

    async def _ensure_indexes_created(self, db):
        """确保数据库索引已创建"""
        try:
            # 邮件模板索引
            templates_collection = db[self.templates_collection]

            # 检查索引是否已存在，避免重复创建
            try:
                existing_indexes = await templates_collection.list_indexes().to_list(length=None)
                index_names = [idx.get('name', '') for idx in existing_indexes]

                if 'template_id_1' not in index_names:
                    await templates_collection.create_index("template_id", unique=True, name="template_id_1")
                    logger.info("创建邮件模板 template_id 索引")

                if 'expire_at_1' not in index_names:
                    await templates_collection.create_index("expire_at", name="expire_at_1")
                    logger.info("创建邮件模板 expire_at 索引")

                if 'created_at_1' not in index_names:
                    await templates_collection.create_index("created_at", name="created_at_1")
                    logger.info("创建邮件模板 created_at 索引")

                if 'target_type_1' not in index_names:
                    await templates_collection.create_index("target_type", name="target_type_1")
                    logger.info("创建邮件模板 target_type 索引")

            except Exception as e:
                logger.debug(f"创建邮件模板索引时发生错误: {str(e)}")

            # 用户模板处理记录索引
            try:
                processed_collection = db[self.user_template_processed_collection]

                existing_processed_indexes = await processed_collection.list_indexes().to_list(length=None)
                processed_index_names = [idx.get('name', '') for idx in existing_processed_indexes]

                if 'user_template_unique' not in processed_index_names:
                    await processed_collection.create_index(
                        [("user_id", 1), ("template_id", 1)],
                        unique=True,
                        name="user_template_unique"
                    )
                    logger.info("创建用户模板处理记录复合索引")

                if 'template_id_1' not in processed_index_names:
                    await processed_collection.create_index("template_id", name="template_id_1")
                    logger.info("创建用户模板处理记录 template_id 索引")

                if 'processed_at_1' not in processed_index_names:
                    await processed_collection.create_index("processed_at", name="processed_at_1")
                    logger.info("创建用户模板处理记录 processed_at 索引")

                if 'user_id_1' not in processed_index_names:
                    await processed_collection.create_index("user_id", name="user_id_1")
                    logger.info("创建用户模板处理记录 user_id 索引")

            except Exception as e:
                logger.debug(f"创建用户模板处理记录索引时发生错误: {str(e)}")

            logger.info("邮件模板数据库索引检查完成")

        except Exception as e:
            logger.warning(f"数据库索引初始化时发生错误: {str(e)}")
            # 不抛出异常，避免影响正常功能
    
    def generate_mail_id(self) -> str:
        """生成邮件ID"""
        return f"mail_{uuid.uuid4().hex[:16]}"
    
    def generate_attachment_id(self) -> str:
        """生成附件ID"""
        return f"attach_{uuid.uuid4().hex[:16]}"
    
    # ==================== 邮件基础操作 ====================
    
    async def create_mail(self, mail: Mail) -> bool:
        """创建邮件"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            mail_data = mail.to_dict()
            result = await collection.insert_one(mail_data)
            
            if result.inserted_id:
                logger.info(f"邮件创建成功: {mail.mail_id}")
                return True
            else:
                logger.error(f"邮件创建失败: {mail.mail_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建邮件时发生错误: {str(e)}")
            return False
    
    async def get_mail_by_id(self, mail_id: str) -> Optional[Mail]:
        """根据ID获取邮件"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            mail_doc = await collection.find_one({"mail_id": mail_id})
            if mail_doc:
                return Mail.from_dict(mail_doc)
            return None
            
        except Exception as e:
            logger.error(f"获取邮件时发生错误: {str(e)}")
            return None
    
    async def get_player_mails(self, player_id: str, page: int = 1, limit: int = 20, 
                              include_deleted: bool = False) -> List[MailListItem]:
        """获取玩家邮件列表"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            # 构建查询条件
            query = {"receiver_id": player_id}
            if not include_deleted:
                query["status"] = {"$ne": MailStatus.DELETED}
            
            # 计算跳过数量
            skip = (page - 1) * limit
            
            # 查询邮件列表
            cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(limit)
            mails = []
            
            async for mail_doc in cursor:
                mail = Mail.from_dict(mail_doc)
                mails.append(MailListItem.from_mail(mail))
            
            return mails
            
        except Exception as e:
            logger.error(f"获取玩家邮件列表时发生错误: {str(e)}")
            return []
    
    async def get_unread_count(self, player_id: str) -> int:
        """获取未读邮件数量"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            count = await collection.count_documents({
                "receiver_id": player_id,
                "status": MailStatus.UNREAD
            })
            
            return count
            
        except Exception as e:
            logger.error(f"获取未读邮件数量时发生错误: {str(e)}")
            return 0
    
    async def update_mail_status(self, mail_id: str, status: MailStatus, 
                                read_at: Optional[datetime] = None) -> bool:
        """更新邮件状态"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            update_data = {"status": status}
            if read_at:
                update_data["read_at"] = read_at
            if status == MailStatus.DELETED:
                update_data["deleted_at"] = datetime.now()
            
            result = await collection.update_one(
                {"mail_id": mail_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新邮件状态时发生错误: {str(e)}")
            return False
    
    async def update_attachment_status(self, mail_id: str, attachment_status: AttachmentStatus) -> bool:
        """更新附件状态"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            result = await collection.update_one(
                {"mail_id": mail_id},
                {"$set": {"attachment_status": attachment_status}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新附件状态时发生错误: {str(e)}")
            return False
    
    async def delete_mails(self, mail_ids: List[str]) -> int:
        """批量删除邮件（软删除）"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            result = await collection.update_many(
                {"mail_id": {"$in": mail_ids}},
                {"$set": {
                    "status": MailStatus.DELETED,
                    "deleted_at": datetime.now()
                }}
            )
            
            return result.modified_count
            
        except Exception as e:
            logger.error(f"批量删除邮件时发生错误: {str(e)}")
            return 0
    
    # ==================== 附件操作 ====================
    
    async def create_attachments(self, attachments: List[MailAttachment]) -> bool:
        """创建邮件附件"""
        try:
            if not attachments:
                return True
                
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            attachment_docs = [attachment.to_dict() for attachment in attachments]
            result = await collection.insert_many(attachment_docs)
            
            if len(result.inserted_ids) == len(attachments):
                logger.info(f"附件创建成功: {len(attachments)} 个")
                return True
            else:
                logger.error(f"附件创建失败: 期望 {len(attachments)} 个，实际 {len(result.inserted_ids)} 个")
                return False
                
        except Exception as e:
            logger.error(f"创建邮件附件时发生错误: {str(e)}")
            return False
    
    async def get_mail_attachments(self, mail_id: str) -> List[MailAttachment]:
        """获取邮件附件"""
        try:
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            cursor = collection.find({"mail_id": mail_id})
            attachments = []
            
            async for attachment_doc in cursor:
                attachments.append(MailAttachment.from_dict(attachment_doc))
            
            return attachments
            
        except Exception as e:
            logger.error(f"获取邮件附件时发生错误: {str(e)}")
            return []
    
    async def claim_attachments(self, mail_id: str) -> bool:
        """领取邮件附件"""
        try:
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            result = await collection.update_many(
                {"mail_id": mail_id, "claimed": False},
                {"$set": {
                    "claimed": True,
                    "claimed_at": datetime.now()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"领取邮件附件时发生错误: {str(e)}")
            return False
    
    # ==================== 系统管理 ====================
    
    async def cleanup_expired_mails(self) -> int:
        """清理过期邮件"""
        try:
            db = await self._get_db()
            mails_collection = db[self.mails_collection]
            attachments_collection = db[self.attachments_collection]
            
            now = datetime.now()
            
            # 查找过期邮件
            expired_mails = []
            cursor = mails_collection.find({
                "expire_at": {"$lt": now},
                "status": {"$ne": MailStatus.DELETED}
            })
            
            async for mail_doc in cursor:
                expired_mails.append(mail_doc["mail_id"])
            
            if not expired_mails:
                return 0
            
            # 删除过期邮件
            mail_result = await mails_collection.delete_many({
                "mail_id": {"$in": expired_mails}
            })
            
            # 删除相关附件
            attachment_result = await attachments_collection.delete_many({
                "mail_id": {"$in": expired_mails}
            })
            
            logger.info(f"清理过期邮件: {mail_result.deleted_count} 封邮件，{attachment_result.deleted_count} 个附件")
            return mail_result.deleted_count
            
        except Exception as e:
            logger.error(f"清理过期邮件时发生错误: {str(e)}")
            return 0
    
    async def get_all_player_ids(self) -> List[str]:
        """获取所有玩家ID（用于广播邮件）"""
        try:
            db = await self._get_db()

            # 尝试多个可能的用户表名
            possible_collections = ["users", "user_data", "players", "accounts"]
            player_ids = []

            for collection_name in possible_collections:
                try:
                    collection = db[collection_name]

                    # 检查集合是否存在且有数据
                    count = await collection.count_documents({})
                    if count == 0:
                        continue

                    logger.info(f"找到用户集合: {collection_name}，包含 {count} 个文档")

                    # 尝试不同的字段名
                    possible_id_fields = ["id", "username", "_id", "user_id", "player_id"]

                    for id_field in possible_id_fields:
                        try:
                            # 查询一个文档来检查字段是否存在
                            sample_doc = await collection.find_one({}, {id_field: 1})
                            if sample_doc and sample_doc.get(id_field):
                                logger.info(f"使用字段 '{id_field}' 从集合 '{collection_name}' 获取用户ID")

                                # 获取所有用户ID
                                cursor = collection.find({}, {id_field: 1})
                                async for user_doc in cursor:
                                    user_id = user_doc.get(id_field)
                                    if user_id and isinstance(user_id, str):
                                        player_ids.append(user_id)
                                    elif user_id:
                                        player_ids.append(str(user_id))

                                logger.info(f"成功获取 {len(player_ids)} 个玩家ID")
                                return player_ids

                        except Exception as field_error:
                            logger.debug(f"字段 '{id_field}' 在集合 '{collection_name}' 中不可用: {str(field_error)}")
                            continue

                except Exception as collection_error:
                    logger.debug(f"集合 '{collection_name}' 不可用: {str(collection_error)}")
                    continue

            # 如果没有找到合适的用户数据，返回空列表
            logger.warning("未找到可用的用户集合，广播邮件将不会发送给任何用户")
            return []

        except Exception as e:
            logger.error(f"获取所有玩家ID时发生错误: {str(e)}")
            return []

    # ==================== 邮件模板操作 ====================

    def generate_template_id(self) -> str:
        """生成模板ID"""
        return f"template_{uuid.uuid4().hex[:16]}"

    async def create_mail_template(self, template: MailTemplate) -> bool:
        """创建邮件模板"""
        try:
            db = await self._get_db()
            collection = db[self.templates_collection]

            template_data = template.to_dict()
            result = await collection.insert_one(template_data)

            if result.inserted_id:
                logger.info(f"邮件模板创建成功: {template.template_id}")
                return True
            else:
                logger.error(f"邮件模板创建失败: {template.template_id}")
                return False

        except Exception as e:
            logger.error(f"创建邮件模板时发生错误: {str(e)}")
            return False

    async def get_unprocessed_templates(self, user_id: str) -> List[MailTemplate]:
        """获取用户未处理的邮件模板"""
        try:
            db = await self._get_db()
            templates_collection = db[self.templates_collection]
            processed_collection = db[self.user_template_processed_collection]

            # 查询所有未过期的模板
            now = datetime.now()
            all_templates_cursor = templates_collection.find({
                "expire_at": {"$gt": now}
            })

            # 查询用户已处理的模板ID
            processed_cursor = processed_collection.find(
                {"user_id": user_id},
                {"template_id": 1}
            )
            processed_template_ids = set()
            async for doc in processed_cursor:
                processed_template_ids.add(doc["template_id"])

            # 过滤出未处理的模板
            unprocessed_templates = []
            async for template_doc in all_templates_cursor:
                template = MailTemplate.from_dict(template_doc)

                # 检查是否已处理
                if template.template_id in processed_template_ids:
                    continue

                # 检查是否符合目标用户条件
                if self._should_user_receive_template(user_id, template):
                    unprocessed_templates.append(template)

            return unprocessed_templates

        except Exception as e:
            logger.error(f"获取未处理模板时发生错误: {str(e)}")
            return []

    def _should_user_receive_template(self, user_id: str, template: MailTemplate) -> bool:
        """检查用户是否应该接收此模板"""
        if template.target_type == "all":
            return True
        elif template.target_type == "specific" and template.target_users:
            return user_id in template.target_users
        return False

    async def mark_template_processed(self, user_id: str, template_id: str) -> bool:
        """标记用户已处理某个模板"""
        try:
            db = await self._get_db()
            collection = db[self.user_template_processed_collection]

            record = UserTemplateProcessed(
                user_id=user_id,
                template_id=template_id,
                processed_at=datetime.now()
            )

            # 使用upsert避免重复插入
            result = await collection.update_one(
                {"user_id": user_id, "template_id": template_id},
                {"$set": record.to_dict()},
                upsert=True
            )

            return result.acknowledged

        except Exception as e:
            logger.error(f"标记模板已处理时发生错误: {str(e)}")
            return False

    async def mark_templates_processed_batch(self, user_ids: List[str], template_id: str) -> int:
        """批量标记用户已处理某个模板"""
        try:
            db = await self._get_db()
            collection = db[self.user_template_processed_collection]

            now = datetime.now()
            operations = []

            for user_id in user_ids:
                record = UserTemplateProcessed(
                    user_id=user_id,
                    template_id=template_id,
                    processed_at=now
                )

                operations.append({
                    "updateOne": {
                        "filter": {"user_id": user_id, "template_id": template_id},
                        "update": {"$set": record.to_dict()},
                        "upsert": True
                    }
                })

            if operations:
                result = await collection.bulk_write(operations)
                return result.upserted_count + result.modified_count

            return 0

        except Exception as e:
            logger.error(f"批量标记模板已处理时发生错误: {str(e)}")
            return 0

    # ==================== 数据清理操作 ====================

    async def cleanup_expired_templates(self) -> int:
        """清理过期的邮件模板"""
        try:
            db = await self._get_db()
            templates_collection = db[self.templates_collection]
            processed_collection = db[self.user_template_processed_collection]

            now = datetime.now()

            # 查找过期模板
            expired_templates_cursor = templates_collection.find({
                "expire_at": {"$lt": now}
            }, {"template_id": 1})

            expired_template_ids = []
            async for template_doc in expired_templates_cursor:
                expired_template_ids.append(template_doc["template_id"])

            if not expired_template_ids:
                return 0

            # 删除过期模板
            template_result = await templates_collection.delete_many({
                "template_id": {"$in": expired_template_ids}
            })

            # 删除相关的处理记录
            processed_result = await processed_collection.delete_many({
                "template_id": {"$in": expired_template_ids}
            })

            logger.info(f"清理过期模板: {template_result.deleted_count} 个模板，{processed_result.deleted_count} 条处理记录")
            return template_result.deleted_count

        except Exception as e:
            logger.error(f"清理过期模板时发生错误: {str(e)}")
            return 0

    async def cleanup_old_processed_records(self, days_old: int = 30) -> int:
        """清理旧的处理记录"""
        try:
            db = await self._get_db()
            processed_collection = db[self.user_template_processed_collection]

            cutoff_date = datetime.now() - timedelta(days=days_old)

            # 删除旧的处理记录
            result = await processed_collection.delete_many({
                "processed_at": {"$lt": cutoff_date}
            })

            logger.info(f"清理旧处理记录: {result.deleted_count} 条记录")
            return result.deleted_count

        except Exception as e:
            logger.error(f"清理旧处理记录时发生错误: {str(e)}")
            return 0

    async def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        try:
            db = await self._get_db()
            templates_collection = db[self.templates_collection]
            processed_collection = db[self.user_template_processed_collection]

            now = datetime.now()

            # 统计模板数量
            total_templates = await templates_collection.count_documents({})
            active_templates = await templates_collection.count_documents({
                "expire_at": {"$gt": now}
            })
            expired_templates = total_templates - active_templates

            # 统计处理记录数量
            total_processed = await processed_collection.count_documents({})

            # 统计最近7天的处理记录
            week_ago = now - timedelta(days=7)
            recent_processed = await processed_collection.count_documents({
                "processed_at": {"$gte": week_ago}
            })

            return {
                "total_templates": total_templates,
                "active_templates": active_templates,
                "expired_templates": expired_templates,
                "total_processed_records": total_processed,
                "recent_processed_records": recent_processed
            }

        except Exception as e:
            logger.error(f"获取模板统计信息时发生错误: {str(e)}")
            return {}


