version: '3.8'

services:
  app:
    build: ./app
    volumes:
      - ./app:/app
    expose:
      - "8000"
    environment:
      - PYTHONUNBUFFERED=1
      - MAX_SAVES=${MAX_SAVES:-50}
    env_file:
      - app.env
    restart: unless-stopped
  schedulesystem:
    build: ./schedulesystem
    expose:
      - "8001"
    environment:
      - PYTHONUNBUFFERED=1
      - MONGO_URI=************************************************
      - SECRET_KEY=your-secure-key-32-chars-long
    restart: unless-stopped
  game_server:
    build:
      context: ./game_server
      dockerfile: Dockerfile
    expose:
      - "8002"
    environment:
      - PYTHONUNBUFFERED=1
      - MONGO_URI=************************************************
      - SECRET_KEY=your-secure-key-32-chars-long
    restart: unless-stopped
  nginx:
    image: nginx:latest
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./schedulesystem/:/www/server/fastapi/schedulesystem/
    depends_on:
      - app
      - schedulesystem
    restart: unless-stopped

networks:
  default:
    driver: bridge