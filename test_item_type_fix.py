# -*- coding: utf-8 -*-
"""
Test ShopItemConfig item_type field fix
"""

import logging
from datetime import datetime
from shop_models import ShopItemConfig, ItemType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_without_item_type():
    """Test data without item_type field (old data)"""
    logger.info("Test: Data without item_type field")
    
    data = {
        "config_id": "test_001",
        "shop_id": "shop_001",
        "slot_id": 1,
        "item_template_id": "10001",
        "item_quantity": 1,
        "item_quality": 3,
        # No item_type field
        "price_config": {"currency": "gold", "amount": 100},
        "purchase_limit": None,
        "availability": {},
        "refresh_weight": 100,
        "refresh_probability": 1.0,
        "is_active": True,
        "sort_order": 0,
        "display_config": {},
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    try:
        config = ShopItemConfig.from_dict(data)
        logger.info("Success: {}".format(config.config_id))
        logger.info("Item type: {}".format(config.item_type))
        logger.info("Is default: {}".format(config.item_type == ItemType.ITEM))

        # Test serialization
        serialized = config.to_dict()
        logger.info("Serialized item_type: {}".format(serialized['item_type']))
        
        return True
    except Exception as e:
        logger.error("Failed: {}".format(str(e)))
        return False

def test_with_item_type():
    """Test data with item_type field"""
    logger.info("Test: Data with item_type field")
    
    data = {
        "config_id": "test_002",
        "shop_id": "shop_001",
        "slot_id": 2,
        "item_template_id": "10002",
        "item_quantity": 1,
        "item_quality": 2,
        "item_type": "equipment",
        "price_config": {"currency": "gold", "amount": 200},
        "purchase_limit": None,
        "availability": {},
        "refresh_weight": 100,
        "refresh_probability": 1.0,
        "is_active": True,
        "sort_order": 0,
        "display_config": {},
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    try:
        config = ShopItemConfig.from_dict(data)
        logger.info("Success: {}".format(config.config_id))
        logger.info("Item type: {}".format(config.item_type))
        logger.info("Is equipment: {}".format(config.item_type == ItemType.EQUIPMENT))

        # Test serialization
        serialized = config.to_dict()
        logger.info("Serialized item_type: {}".format(serialized['item_type']))
        
        return True
    except Exception as e:
        logger.error("Failed: {}".format(str(e)))
        return False

def test_direct_creation():
    """Test direct creation"""
    logger.info("Test: Direct creation with default item_type")
    
    try:
        config = ShopItemConfig(
            config_id="test_003",
            shop_id="shop_001",
            slot_id=3,
            item_template_id="10003",
            item_quantity=1,
            item_quality=1,
            # item_type will use default value
            price_config={"currency": "gold", "amount": 300},
            purchase_limit=None,
            availability={},
            refresh_weight=100,
            refresh_probability=1.0,
            is_active=True,
            sort_order=0,
            display_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        logger.info("Success: {}".format(config.config_id))
        logger.info("Item type: {}".format(config.item_type))
        logger.info("Is default: {}".format(config.item_type == ItemType.ITEM))
        
        return True
    except Exception as e:
        logger.error("Failed: {}".format(str(e)))
        return False

def main():
    """Run all tests"""
    logger.info("Starting ShopItemConfig item_type field fix tests")
    logger.info("=" * 50)
    
    tests = [
        test_without_item_type,
        test_with_item_type,
        test_direct_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        logger.info("")
        if test():
            passed += 1
        logger.info("-" * 30)
    
    logger.info("")
    logger.info(f"Test results: {passed}/{total} passed")
    
    if passed == total:
        logger.info("All tests passed! Fix successful!")
    else:
        logger.error("Some tests failed")

if __name__ == "__main__":
    main()
