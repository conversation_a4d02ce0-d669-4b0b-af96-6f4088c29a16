#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始部署游戏服务器...${NC}"

# 创建部署目录
DEPLOY_DIR="../game_server"
echo -e "${GREEN}创建部署目录: ${DEPLOY_DIR}${NC}"
mkdir -p $DEPLOY_DIR

# 复制必要文件
echo -e "${GREEN}复制项目文件...${NC}"
cp -r *.py $DEPLOY_DIR/
cp -r requirements.txt $DEPLOY_DIR/
cp -r game_server.dockerfile $DEPLOY_DIR/Dockerfile
cp -r .dockerignore $DEPLOY_DIR/
mkdir -p $DEPLOY_DIR/config
cp -r config/* $DEPLOY_DIR/config/

# 创建日志目录
echo -e "${GREEN}创建日志目录...${NC}"
mkdir -p $DEPLOY_DIR/logs

# 检查是否需要创建配置文件
if [ ! -f "$DEPLOY_DIR/config/server.ini" ]; then
    echo -e "${GREEN}创建配置文件...${NC}"
    cp config/server.ini.example $DEPLOY_DIR/config/server.ini
    echo -e "${YELLOW}请检查并修改配置文件: $DEPLOY_DIR/config/server.ini${NC}"
fi

# 确保权限正确
echo -e "${GREEN}设置文件权限...${NC}"
chmod -R 755 $DEPLOY_DIR
chmod -R 777 $DEPLOY_DIR/logs

echo -e "${GREEN}部署完成!${NC}"
echo -e "${YELLOW}游戏服务器已部署到: ${DEPLOY_DIR}${NC}"
echo -e "${YELLOW}请确保在docker-compose.yml中配置了game_server服务${NC}"
echo -e "${YELLOW}可以通过以下命令启动服务:${NC}"
echo -e "${GREEN}cd .. && docker-compose up -d game_server${NC}" 