# 多Worker模式下装备系统使用指南

## 🎯 **概述**

`DistributedEquipmentService` 是专门为多Worker环境设计的装备管理系统，解决了传统单例模式在多进程环境下的数据一致性问题。

## 🚀 **核心特性**

### 1. **分布式缓存**
- 使用Redis作为共享缓存存储
- 所有Worker共享同一份装备数据
- 自动缓存失效和同步机制

### 2. **分布式锁保护**
- 使用Redis分布式锁确保并发安全
- 防止多Worker同时操作同一装备
- 避免数据竞争和状态不一致

### 3. **缓存失效通知**
- 使用Redis Pub/Sub机制
- 一个Worker的修改自动通知其他Worker
- 确保缓存数据实时同步

### 4. **Worker隔离**
- 每个Worker有唯一标识
- 避免Worker间的直接依赖
- 支持动态Worker扩展

## 📋 **配置要求**

### 1. **Redis配置**
```ini
[Redis]
host = your_redis_host
port = 6379
password = your_redis_password
db = 0
max_connections = 1000
socket_timeout = 5
socket_connect_timeout = 5
```

### 2. **Worker配置**
```ini
[Server]
workers = 4  # 根据CPU核心数调整
```

## 🔧 **初始化步骤**

### 1. **在game_server.py中初始化**
```python
from equipment_service_distributed import distributed_equipment_service

@app.on_event("startup")
async def startup_event():
    try:
        logger.info(f"启动服务器，初始化数据库和连接管理器... (Worker: {os.getpid()})")
        await db_manager.startup()
        await connection_manager.initialize()
        
        # 初始化分布式装备服务
        await distributed_equipment_service.initialize()
        logger.info(f"分布式装备服务初始化完成 (Worker: {os.getpid()})")
        
        # ... 其他初始化代码
    except Exception as e:
        logger.error(f"启动时初始化失败: {str(e)}")
```

### 2. **注册消息处理器**
```python
from equipment_handlers_distributed import register_equipment_handlers_distributed

# 在msgManager.py中注册
def register_all_handlers():
    # ... 其他处理器注册
    register_equipment_handlers_distributed(msg_manager)
```

## 💻 **使用示例**

### 1. **基本操作**
```python
# 添加装备
success, msg, equipment = await distributed_equipment_service.add_equipment(
    "player_001", defid=1001, level=1, quality=2, holes=3
)

# 穿戴装备
success, msg = await distributed_equipment_service.equip("player_001", equipment.id)

# 强化装备
success, msg = await distributed_equipment_service.intensify_equipment(
    "player_001", equipment.id, use_blessing_stone=False
)

# 获取装备数据
equip_data = await distributed_equipment_service.get_serialized_data("player_001")
```

### 2. **WebSocket消息处理**
```python
# 客户端发送穿戴装备请求
{
    "msgId": 2001,
    "data": {
        "item_id": "equipment_123"
    }
}

# 服务端响应
{
    "msgId": 2001,
    "data": {
        "success": true,
        "message": "穿戴成功到武器"
    }
}
```

## 🔒 **并发安全机制**

### 1. **分布式锁策略**
```python
# 装备操作锁
equipment_lock_key = f"equipment_lock:{player_id}:{item_id}"

# 玩家操作锁
player_lock_key = f"player_lock:{player_id}"
```

### 2. **锁的粒度**
- **装备级锁**: 单个装备操作（强化、升星、镶嵌等）
- **玩家级锁**: 玩家整体操作（添加装备、批量操作等）

### 3. **锁超时设置**
- 装备操作锁: 30秒
- 玩家操作锁: 30秒
- 自动释放机制防止死锁

## 📊 **缓存管理**

### 1. **缓存键结构**
```
equipment:{player_id}:{item_id}          # 装备数据
player_equipments:{player_id}            # 玩家装备列表
player_equipped:{player_id}              # 玩家已穿戴装备
equipment_lock:{player_id}:{item_id}     # 装备操作锁
player_lock:{player_id}                  # 玩家操作锁
```

### 2. **缓存过期策略**
- 装备数据: 1小时
- 装备列表: 1小时
- 已穿戴装备: 1小时
- 操作锁: 30秒

### 3. **缓存失效机制**
```python
# 自动失效通知
await distributed_equipment_service._publish_cache_invalidation(player_id, item_id)

# 订阅失效通知
await distributed_equipment_service._subscribe_to_cache_invalidation()
```

## 🔍 **监控和调试**

### 1. **缓存统计**
```python
# 获取缓存统计信息
stats = await distributed_equipment_service.get_cache_stats()
print(f"缓存统计: {stats}")
# 输出: {
#   "total_players": 100,
#   "total_equipments": 500,
#   "total_equipped": 300,
#   "worker_id": "abc12345"
# }
```

### 2. **装备摘要**
```python
# 获取玩家装备摘要
summary = await distributed_equipment_service.get_equipment_summary("player_001")
print(f"装备摘要: {summary}")
# 输出: {
#   "total_equipments": 10,
#   "equipped_count": 6,
#   "total_level": 45,
#   "total_star": 12,
#   "average_quality": 2.5,
#   "total_properties": {"attack": 150, "defense": 80}
# }
```

### 3. **日志监控**
```python
# 查看Worker日志
tail -f logs/server.pid-*.log | grep "DistributedEquipmentService"

# 查看Redis连接状态
redis-cli info clients
```

## ⚡ **性能优化**

### 1. **连接池配置**
```python
# Redis连接池优化
max_connections = min(cpu_count, workers) * 30
```

### 2. **批量操作**
```python
# 批量获取装备数据
equipments = await distributed_equipment_service.get_all_equipments(player_id)

# 批量更新缓存
for equipment in equipments:
    await distributed_equipment_service._cache_equipment(player_id, equipment)
```

### 3. **异步处理**
```python
# 异步缓存更新
asyncio.create_task(distributed_equipment_service._cache_equipment(player_id, equipment))
```

## 🚨 **故障处理**

### 1. **Redis连接失败**
```python
try:
    await distributed_equipment_service.initialize()
except Exception as e:
    logger.error(f"Redis连接失败: {str(e)}")
    # 降级到数据库直接操作
```

### 2. **分布式锁获取失败**
```python
# 自动重试机制
lock = DistributedLock(redis_client, lock_key, ttl=30)
if not await lock.acquire(timeout=10):
    return False, "操作过于频繁，请稍后再试"
```

### 3. **缓存数据不一致**
```python
# 强制刷新缓存
await distributed_equipment_service.clear_player_cache(player_id)
equipment = await distributed_equipment_service._load_equipment_from_db(player_id, item_id)
```

## 📈 **扩展建议**

### 1. **水平扩展**
- 增加Worker数量
- 使用Redis集群
- 负载均衡配置

### 2. **垂直扩展**
- 增加Redis内存
- 优化数据库索引
- 使用SSD存储

### 3. **监控告警**
- Redis连接监控
- 缓存命中率监控
- 操作延迟监控

## 🎯 **最佳实践**

1. **合理设置Worker数量**: 建议Worker数量 = CPU核心数 * 2
2. **监控Redis性能**: 定期检查Redis内存使用和连接数
3. **定期清理缓存**: 设置定时任务清理过期缓存
4. **错误处理**: 所有操作都要有完善的错误处理机制
5. **日志记录**: 记录关键操作的日志，便于问题排查

## 🔄 **迁移指南**

### 从单例模式迁移
1. 替换 `EquipmentService` 为 `DistributedEquipmentService`
2. 更新消息处理器注册
3. 添加初始化代码
4. 测试多Worker环境下的功能

### 数据迁移
1. 现有数据无需迁移，直接从数据库加载
2. 缓存会在首次访问时自动建立
3. 建议在低峰期进行迁移

---

通过使用 `DistributedEquipmentService`，您的游戏服务器可以在多Worker环境下稳定运行，确保装备数据的一致性和并发安全。 