2025-08-05 21:06:25,062 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:25,884 - models - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:25,919 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:26,320 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:26,334 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:26,350 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:26,367 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:26,384 - auth - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,122 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,178 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,236 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,249 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,263 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,263 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: d3e3fd1f)
2025-08-05 21:06:29,275 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,317 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,333 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,346 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,358 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,452 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,465 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,477 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,487 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,497 - game_server - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:29,500 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 21:06:29,501 - msgManager - INFO - Monster handlers registered
2025-08-05 21:06:29,502 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 21:06:29,504 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 21:06:29,513 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 21:06:29,513 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 21:06:29,516 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: fcd00f91)
2025-08-05 21:06:29,562 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 21:06:29,605 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 21:06:29,605 - game_server - INFO - 模板引擎初始化成功
2025-08-05 21:06:29,609 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:06:29,610 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 40408)
2025-08-05 21:06:29,611 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 21:06:29,787 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 21:06:29,792 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:06:30,311 - ConnectionManager - INFO - 后台任务已启动 (Worker 40408)
2025-08-05 21:06:30,312 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 40408)
2025-08-05 21:06:30,320 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:06:30,320 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:06:30,321 - game_server - INFO - 游戏配置加载完成 (Worker: 40408)
2025-08-05 21:06:30,322 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:06:37,982 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 21:06:37,983 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=2, 可用=0, 总计=2
2025-08-05 21:06:37,983 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 40408): 2/2 (100.0%)
2025-08-05 21:06:37,984 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:06:38,028 - ConnectionManager - INFO - Worker 40408 开始消费广播消息，消费者标签: ctag1.49e01265cbda4a6aa5d46c38f09d224f
2025-08-05 21:06:38,030 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: fcd00f91)
2025-08-05 21:06:38,030 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: fcd00f91)
2025-08-05 21:06:38,038 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:38,040 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 21:06:38,041 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 21:06:38,048 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:06:38,049 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 21:06:38,049 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:06:38,077 - ConnectionManager - INFO - Worker 40408 开始消费个人消息，消费者标签: ctag1.1c53dc4b017341b0b391bdb8a49a5b1b
2025-08-05 21:06:38,156 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 40408)
2025-08-05 21:06:38,248 - distributed_lock - INFO - Worker 40408 成功获取锁: scheduler_initialization
2025-08-05 21:06:38,249 - game_server_scheduler_integration - INFO - Worker 40408 获得调度器初始化权限
2025-08-05 21:06:38,253 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 21:06:38,253 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 21:06:38,254 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 21:06:38,254 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 21:06:38,255 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 21:06:38,255 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 21:06:38,255 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 21:06:38,255 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 21:06:38,256 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 21:06:38,257 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 21:06:38,257 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 21:06:38,258 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 21:06:38,258 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 21:06:38,258 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 21:06:38,259 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 21:06:38,259 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 21:06:38,340 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 21:06:38,341 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 21:06:38,341 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 21:06:38,342 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 21:06:38,646 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 21:06:38,729 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 21:06:38,729 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 21:06:38,730 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 21:06:38,730 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 21:06:38,730 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 21:06:38,731 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:06:38,811 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 21:06:38,934 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:06:38,934 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 21:06:38,935 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 21:06:38,935 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 21:06:38,936 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 21:06:38,936 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 21:06:38,941 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:38,941 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:06:38,941 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:06:38,942 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:38,943 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:06:38,943 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:06:39,064 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:39,065 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:06:39,067 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:06:39,195 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:39,196 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:06:39,196 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:06:39,197 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:39,198 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:06:39,199 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:06:39,200 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:39,200 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:06:39,201 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:06:39,201 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 21:06:39,202 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,203 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,203 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,204 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,204 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,204 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:39,205 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 21:06:39,206 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 21:06:39,206 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 21:06:39,207 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 21:06:39,207 - game_server_scheduler_integration - INFO - Worker 40408 调度器初始化成功
2025-08-05 21:06:39,251 - game_server - INFO - 统一调度器初始化成功 (Worker: 40408)
2025-08-05 21:06:39,260 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:39,261 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 21:06:39,261 - game_server - INFO - 日志清理管理器已启动 (Worker: 40408)
2025-08-05 21:06:39,261 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:06:39,262 - game_server - INFO - Monster cooldown manager initialized (Worker: 40408)
2025-08-05 21:06:39,410 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:06:39,410 - game_server - INFO - 公会系统初始化成功 (Worker: 40408)
2025-08-05 21:06:39,411 - game_server - INFO - 邮件系统初始化成功 (Worker: 40408)
2025-08-05 21:06:39,413 - game_server - INFO - 商店系统初始化成功 (Worker: 40408)
2025-08-05 21:06:39,413 - game_server - INFO - 初始化完成 (Worker: 40408)
2025-08-05 21:06:48,354 - CacheInvalidationManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:06:48,395 - CacheInvalidationManager - INFO - 缓存失效通知管理器初始化完成 (Worker 40408)
2025-08-05 21:06:48,481 - game_server - INFO - 获取用户信息，用户 dsadjdj23，返回字段: ['id', 'created_at', 'nickname']
2025-08-05 21:06:49,205 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:06:59 CST)" (scheduled at 2025-08-05 21:06:49.195816+08:00)
2025-08-05 21:06:49,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:06:49,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:06:49,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:06:49,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:06:49,723 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:06:49,725 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:06:49,726 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:06:49,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:06:59 CST)" executed successfully
2025-08-05 21:06:52,440 - utils - ERROR - 未登录
2025-08-05 21:06:59,208 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:09 CST)" (scheduled at 2025-08-05 21:06:59.195816+08:00)
2025-08-05 21:06:59,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:06:59,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:06:59,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:06:59,733 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:06:59,734 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:06:59,734 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:06:59,735 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:06:59,777 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:09 CST)" executed successfully
2025-08-05 21:07:00,334 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:07:00,442 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:07:00,642 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:07:09,077 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:07:39 CST)" (scheduled at 2025-08-05 21:07:09.063911+08:00)
2025-08-05 21:07:09,120 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:07:09,121 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:09,203 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:19 CST)" (scheduled at 2025-08-05 21:07:09.195816+08:00)
2025-08-05 21:07:09,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:09,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:09,247 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:07:09,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:09,587 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:07:09,588 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:07:09,588 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:07:09,589 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:07:09,592 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:09,635 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:07:39 CST)" executed successfully
2025-08-05 21:07:09,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:09,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:07:09,699 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:09,700 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:09,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:19 CST)" executed successfully
2025-08-05 21:07:11,960 - game_server - INFO - WebSocket 连接尝试，token: eyJhbGciOi..., worker: 40408
2025-08-05 21:07:12,046 - ConnectionManager - INFO - 已发布用户登录通知到Redis (用户: dsadjdj23, Worker: 40408)
2025-08-05 21:07:12,312 - game_manager - INFO - 开始初始化游戏管理器 (Worker: 40408)
2025-08-05 21:07:12,312 - game_manager - INFO - 创建PlayerSessionManager...
2025-08-05 21:07:12,312 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 21:07:12,313 - game_manager - INFO - 创建GameNotificationManager...
2025-08-05 21:07:12,314 - game_manager - INFO - 获取UserCacheManager...
2025-08-05 21:07:12,315 - game_manager - INFO - 获取ItemCacheManager...
2025-08-05 21:07:12,357 - game_manager - INFO - 获取GuildServiceDistributed...
2025-08-05 21:07:12,358 - game_manager - INFO - 获取ConnectionManager...
2025-08-05 21:07:12,358 - game_manager - INFO - 游戏管理器初始化完成 (Worker: 40408)
2025-08-05 21:07:12,358 - game_manager - INFO - 处理玩家登录: dsadjdj23 (Worker: 40408)
2025-08-05 21:07:12,605 - player_session_manager - INFO - 创建玩家会话: dsadjdj23 (Worker: 40408)
2025-08-05 21:07:12,955 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 1754353521.7秒)
2025-08-05 21:07:13,022 - guild_cache_manager - INFO - GuildCacheManager实例已创建
2025-08-05 21:07:13,797 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 21:07:13,843 - mail_database_manager - INFO - 数据库中共有 12 个未过期的邮件模板
2025-08-05 21:07:13,894 - mail_database_manager - INFO - 用户 dsadjdj23 已处理的模板ID: {'template_c466e0ddaa8c44f2', 'template_0acd29605c994701', 'template_e827e75c8beb4d5d', 'template_7fd5d6e6b55c4142', 'template_46f1fcab221b4a38', 'template_c999a774d9014253', 'template_62e4594c1bf04b4b', 'template_0738ee4b4a2d4983', 'template_b0a095478f034b26', 'template_6e47793807b84b13', 'template_575fc5088c474399', 'template_14981f8399fe4572'}
2025-08-05 21:07:13,940 - mail_database_manager - INFO - 模板 template_6e47793807b84b13 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,940 - mail_database_manager - INFO - 模板 template_c466e0ddaa8c44f2 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,941 - mail_database_manager - INFO - 模板 template_c999a774d9014253 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,942 - mail_database_manager - INFO - 模板 template_575fc5088c474399 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,942 - mail_database_manager - INFO - 模板 template_0acd29605c994701 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,943 - mail_database_manager - INFO - 模板 template_14981f8399fe4572 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,944 - mail_database_manager - INFO - 模板 template_46f1fcab221b4a38 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,944 - mail_database_manager - INFO - 模板 template_7fd5d6e6b55c4142 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,945 - mail_database_manager - INFO - 模板 template_0738ee4b4a2d4983 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,945 - mail_database_manager - INFO - 模板 template_62e4594c1bf04b4b 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,945 - mail_database_manager - INFO - 模板 template_e827e75c8beb4d5d 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,946 - mail_database_manager - INFO - 模板 template_b0a095478f034b26 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:07:13,946 - mail_database_manager - INFO - 用户 dsadjdj23 有 0 个未处理的邮件模板
2025-08-05 21:07:13,946 - mail_service_distributed - INFO - 用户 dsadjdj23 登录时发现 0 个未处理的邮件模板
2025-08-05 21:07:13,947 - mail_cache_manager - INFO - MailCacheManager实例已创建
2025-08-05 21:07:14,074 - game_manager - INFO - 玩家登录处理完成: dsadjdj23
2025-08-05 21:07:14,075 - ConnectionManager - INFO - 用户 dsadjdj23 连接成功 (Token: eyJhbGciOi, Worker: 40408)
2025-08-05 21:07:19,208 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:29 CST)" (scheduled at 2025-08-05 21:07:19.195816+08:00)
2025-08-05 21:07:19,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:19,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:19,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:19,707 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:19,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:07:19,708 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:19,708 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:19,752 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:29 CST)" executed successfully
2025-08-05 21:07:22,987 - MessageIdempotencyManager - INFO - 日志系统初始化成功 (进程 ID: 40408)
2025-08-05 21:07:22,988 - MessageIdempotencyManager - INFO - 消息幂等性管理器初始化完成
2025-08-05 21:07:29,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:39 CST)" (scheduled at 2025-08-05 21:07:29.195816+08:00)
2025-08-05 21:07:29,245 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:29,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:29,369 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:29,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:29,706 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:29,708 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:29,711 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:29,752 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:39 CST)" executed successfully
2025-08-05 21:07:30,090 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:07:30,091 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:07:30,335 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:07:32,902 - distributed_lock - INFO - Worker 40408 成功获取锁: purchase:lock:dsadjdj23:shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:07:33,655 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:07:34,426 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:07:35,196 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:07:36,909 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 40408, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************.nwFvBgxi0g1SFeFCABTKO5b87ZQEJiiEIOVkT8hrR3g', 'last_active': '2025-08-05T21:07:12.047423'}
2025-08-05 21:07:36,909 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:07:36,910 - currency_service - INFO - 扣除货币成功: dsadjdj23 -80 gold
2025-08-05 21:07:36,910 - shop_purchase_service - INFO - item_data = {item_data}
2025-08-05 21:07:37,988 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=4, 可用=1, 总计=5
2025-08-05 21:07:37,989 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:07:38,011 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 40408, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************.nwFvBgxi0g1SFeFCABTKO5b87ZQEJiiEIOVkT8hrR3g', 'last_active': '2025-08-05T21:07:12.047423'}
2025-08-05 21:07:38,012 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: add, 资产类型: ItemType.ITEM
2025-08-05 21:07:38,199 - shop_database_manager - INFO - 购买记录创建成功: purchase_6ee455d34d5748a1
2025-08-05 21:07:38,199 - shop_event_broadcaster - INFO - 购买成功事件已发送给玩家: dsadjdj23
2025-08-05 21:07:38,201 - shop_purchase_service - INFO - 购买成功: purchase_6ee455d34d5748a1, 玩家: dsadjdj23, 商品: shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:07:38,444 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:07:38,446 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:07:38,946 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:08:38 CST)" (scheduled at 2025-08-05 21:07:38.942754+08:00)
2025-08-05 21:07:38,991 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:07:38,991 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:38,992 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:07:39,071 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:09 CST)" (scheduled at 2025-08-05 21:07:39.063911+08:00)
2025-08-05 21:07:39,112 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:07:39,112 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:39,141 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:07:39,142 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:07:39,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:49 CST)" (scheduled at 2025-08-05 21:07:39.195816+08:00)
2025-08-05 21:07:39,243 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:07:39,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:39,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:39,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:39,586 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:07:39,587 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:07:39,587 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:07:39,588 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:07:39,589 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:39,633 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:09 CST)" executed successfully
2025-08-05 21:07:39,727 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:39,728 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:07:39,728 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:39,729 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:39,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:49 CST)" executed successfully
2025-08-05 21:07:39,783 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 1
2025-08-05 21:07:39,783 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.79秒
2025-08-05 21:07:39,784 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:07:39,784 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:39,825 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:08:38 CST)" executed successfully
2025-08-05 21:07:49,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:59 CST)" (scheduled at 2025-08-05 21:07:49.195816+08:00)
2025-08-05 21:07:49,249 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:49,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:49,374 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:49,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:49,709 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:49,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:49,711 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:49,756 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:59 CST)" executed successfully
2025-08-05 21:07:50,985 - distributed_lock - INFO - Worker 40408 成功获取锁: purchase:lock:dsadjdj23:shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:07:52,731 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 40408, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************.nwFvBgxi0g1SFeFCABTKO5b87ZQEJiiEIOVkT8hrR3g', 'last_active': '2025-08-05T21:07:12.047423'}
2025-08-05 21:07:52,732 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:07:52,732 - currency_service - INFO - 扣除货币成功: dsadjdj23 -100 gold
2025-08-05 21:07:52,732 - shop_purchase_service - INFO - item_data = {item_data}
2025-08-05 21:07:53,771 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 40408, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************.nwFvBgxi0g1SFeFCABTKO5b87ZQEJiiEIOVkT8hrR3g', 'last_active': '2025-08-05T21:07:12.047423'}
2025-08-05 21:07:53,771 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: add, 资产类型: ItemType.ITEM
2025-08-05 21:07:53,944 - shop_database_manager - INFO - 购买记录创建成功: purchase_47bf93c8039c45b7
2025-08-05 21:07:53,945 - shop_event_broadcaster - INFO - 购买成功事件已发送给玩家: dsadjdj23
2025-08-05 21:07:53,945 - shop_purchase_service - INFO - 购买成功: purchase_47bf93c8039c45b7, 玩家: dsadjdj23, 商品: shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:07:59,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:09 CST)" (scheduled at 2025-08-05 21:07:59.195816+08:00)
2025-08-05 21:07:59,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:59,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:59,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:59,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:59,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:07:59,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:59,712 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:07:59,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:09 CST)" executed successfully
2025-08-05 21:08:00,344 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:08:00,389 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:08:00,390 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:08:09,075 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:39 CST)" (scheduled at 2025-08-05 21:08:09.063911+08:00)
2025-08-05 21:08:09,116 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:08:09,117 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:09,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:19 CST)" (scheduled at 2025-08-05 21:08:09.195816+08:00)
2025-08-05 21:08:09,239 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:09,239 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:09,247 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:08:09,369 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:09,586 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:08:09,586 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:08:09,586 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:08:09,587 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:08:09,589 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:08:09,630 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:39 CST)" executed successfully
2025-08-05 21:08:09,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:09,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:08:09,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:09,719 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:08:09,764 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:19 CST)" executed successfully
2025-08-05 21:08:57,355 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:08:57,356 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=2, 总计=5
2025-08-05 21:08:57,357 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:08:57,358 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:59 CST)" was missed by 0:00:08.162410
2025-08-05 21:08:57,358 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:09:38 CST)" was missed by 0:00:18.415760
2025-08-05 21:08:57,358 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:09 CST)" was missed by 0:00:18.294933
2025-08-05 21:08:57,359 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:08:57,359 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:08:59,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:09 CST)" (scheduled at 2025-08-05 21:08:59.195816+08:00)
2025-08-05 21:08:59,238 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:59,238 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:59,361 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:59,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:59,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:08:59,709 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:59,710 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:08:59,750 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:09 CST)" executed successfully
2025-08-05 21:09:00,394 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:09:00,395 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:09:09,073 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:39 CST)" (scheduled at 2025-08-05 21:09:09.063911+08:00)
2025-08-05 21:09:09,114 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:09:09,114 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:09,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:19 CST)" (scheduled at 2025-08-05 21:09:09.195816+08:00)
2025-08-05 21:09:09,235 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:09:09,255 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:09,255 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:09,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:09,567 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:09:09,567 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:09:09,568 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:09:09,569 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:09:09,569 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:09,609 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:39 CST)" executed successfully
2025-08-05 21:09:09,731 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:09,731 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:09,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:09,736 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:09,783 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:19 CST)" executed successfully
2025-08-05 21:09:12,832 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:09:13,653 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:09:15,743 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:09:19,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:29 CST)" (scheduled at 2025-08-05 21:09:19.195816+08:00)
2025-08-05 21:09:19,587 - mongodb_manager - WARNING - MongoDBManager: ping失败，重试初始化，第1次: *************:27017: timed out (configured timeouts: socketTimeoutMS: 3000.0ms, connectTimeoutMS: 3000.0ms)
2025-08-05 21:09:20,371 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 21:09:20,573 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:20,574 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:20,873 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:21,871 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:21,872 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.00秒
2025-08-05 21:09:21,872 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:21,873 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:21,970 - distributed_lock - INFO - Worker 40408 成功获取锁: reset:lock:dsadjdj23:daily
2025-08-05 21:09:21,971 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:29 CST)" executed successfully
2025-08-05 21:09:27,361 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:09:29,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:39 CST)" (scheduled at 2025-08-05 21:09:29.195816+08:00)
2025-08-05 21:09:29,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:29,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:29,376 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:29,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:29,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:09:29,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:29,719 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:29,763 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:39 CST)" executed successfully
2025-08-05 21:09:30,639 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=1, 用户数=1
2025-08-05 21:09:30,641 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:09:37,764 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:09:37,765 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:09:38,043 - game_server - INFO - WebSocket 断开，用户: dsadjdj23, token: eyJhbGciOi..., 代码: 1006, 原因: , worker: 40408
2025-08-05 21:09:38,253 - ConnectionManager - INFO - 正在关闭WebSocket连接 (Token: eyJhbGciOi, 状态: WebSocketState.CONNECTED)
2025-08-05 21:09:38,253 - ConnectionManager - WARNING - 关闭WebSocket连接失败: Unexpected ASGI message 'websocket.close', after sending 'websocket.close' or response already completed.
2025-08-05 21:09:38,255 - ConnectionManager - INFO - 用户 dsadjdj23 断开连接 (Token: eyJhbGciOi, Worker: 40408)
2025-08-05 21:09:38,255 - game_manager - INFO - 处理玩家登出: dsadjdj23, 原因: disconnect (Worker: 40408)
2025-08-05 21:09:38,380 - player_session_manager - INFO - 移除玩家会话: dsadjdj23 (Worker: 40408)
2025-08-05 21:09:38,420 - game_manager - INFO - 玩家登出处理完成: dsadjdj23
2025-08-05 21:09:38,957 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:10:38 CST)" (scheduled at 2025-08-05 21:09:38.942754+08:00)
2025-08-05 21:09:38,998 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:09:38,998 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:38,999 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:09:39,067 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:09 CST)" (scheduled at 2025-08-05 21:09:39.063911+08:00)
2025-08-05 21:09:39,110 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:09:39,110 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:39,149 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:09:39,149 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:09:39,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:49 CST)" (scheduled at 2025-08-05 21:09:39.195816+08:00)
2025-08-05 21:09:39,244 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:09:39,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:39,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:39,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:39,573 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:09:39,573 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:09:39,577 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:09:39,577 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:09:39,578 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:39,624 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:09 CST)" executed successfully
2025-08-05 21:09:39,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:39,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:09:39,711 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:39,711 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:39,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:49 CST)" executed successfully
2025-08-05 21:09:40,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:09:40,034 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.04秒
2025-08-05 21:09:40,035 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:09:40,035 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:40,078 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:10:38 CST)" executed successfully
2025-08-05 21:09:49,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:59 CST)" (scheduled at 2025-08-05 21:09:49.195816+08:00)
2025-08-05 21:09:49,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:49,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:49,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:49,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:49,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:49,723 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:49,724 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:49,768 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:59 CST)" executed successfully
2025-08-05 21:09:57,366 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=2, 总计=5
2025-08-05 21:09:57,366 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:09:57,367 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:09:59,208 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:09 CST)" (scheduled at 2025-08-05 21:09:59.195816+08:00)
2025-08-05 21:09:59,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:59,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:59,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:59,735 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:59,736 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:59,736 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:59,737 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:09:59,784 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:09 CST)" executed successfully
2025-08-05 21:10:01,117 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:01,129 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:09,072 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:39 CST)" (scheduled at 2025-08-05 21:10:09.063911+08:00)
2025-08-05 21:10:09,115 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:10:09,115 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:09,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:19 CST)" (scheduled at 2025-08-05 21:10:09.195816+08:00)
2025-08-05 21:10:09,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:09,247 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:09,248 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:10:09,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:09,586 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:10:09,586 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:10:09,587 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:10:09,587 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:10:09,588 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:09,632 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:39 CST)" executed successfully
2025-08-05 21:10:09,698 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:09,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:10:09,699 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:09,699 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:09,743 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:19 CST)" executed successfully
2025-08-05 21:10:19,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:29 CST)" (scheduled at 2025-08-05 21:10:19.195816+08:00)
2025-08-05 21:10:19,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:19,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:19,386 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:19,750 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:19,751 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:10:19,752 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:19,753 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:19,797 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:29 CST)" executed successfully
2025-08-05 21:10:27,371 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:29,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:39 CST)" (scheduled at 2025-08-05 21:10:29.195816+08:00)
2025-08-05 21:10:29,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:29,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:29,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:29,745 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:29,745 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:10:29,746 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:29,746 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:29,791 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:39 CST)" executed successfully
2025-08-05 21:10:30,119 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:30,150 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:37,876 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:10:37,878 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:10:38,951 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:11:38 CST)" (scheduled at 2025-08-05 21:10:38.942754+08:00)
2025-08-05 21:10:39,075 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:09 CST)" (scheduled at 2025-08-05 21:10:39.063911+08:00)
2025-08-05 21:10:39,140 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:10:39,141 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:39,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:49 CST)" (scheduled at 2025-08-05 21:10:39.195816+08:00)
2025-08-05 21:10:39,346 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:10:39,346 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:39,347 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:10:39,355 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:10:39,782 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:10:39,782 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:10:40,128 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:10:40,128 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:10:40,129 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.77秒
2025-08-05 21:10:40,129 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:10:40,130 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:40,151 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 1
2025-08-05 21:10:40,151 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.80秒
2025-08-05 21:10:40,151 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:10:40,152 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:40,171 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:09 CST)" executed successfully
2025-08-05 21:10:40,197 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:11:38 CST)" executed successfully
2025-08-05 21:10:41,086 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:41,086 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:41,229 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:41,603 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:41,604 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 21:10:41,606 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:41,607 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:41,652 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:49 CST)" executed successfully
2025-08-05 21:10:49,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:59 CST)" (scheduled at 2025-08-05 21:10:49.195816+08:00)
2025-08-05 21:10:49,254 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:49,255 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:49,397 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:49,758 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:49,758 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:10:49,759 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:49,759 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:49,806 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:59 CST)" executed successfully
2025-08-05 21:10:57,376 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:10:57,377 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:10:57,378 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:10:59,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:09 CST)" (scheduled at 2025-08-05 21:10:59.195816+08:00)
2025-08-05 21:10:59,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:59,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:59,384 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:59,745 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:59,745 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:10:59,746 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:59,746 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:10:59,793 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:09 CST)" executed successfully
2025-08-05 21:11:00,372 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:00,403 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:09,079 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:39 CST)" (scheduled at 2025-08-05 21:11:09.063911+08:00)
2025-08-05 21:11:09,125 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:11:09,126 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:09,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:19 CST)" (scheduled at 2025-08-05 21:11:09.195816+08:00)
2025-08-05 21:11:09,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:09,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:09,263 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:11:09,369 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:09,617 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:11:09,617 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:11:09,619 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 21:11:09,620 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:11:09,621 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:09,666 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:39 CST)" executed successfully
2025-08-05 21:11:09,703 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:09,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:09,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:09,704 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:09,746 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:19 CST)" executed successfully
2025-08-05 21:11:19,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:29 CST)" (scheduled at 2025-08-05 21:11:19.195816+08:00)
2025-08-05 21:11:19,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:19,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:19,378 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:19,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:19,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:19,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:19,712 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:19,754 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:29 CST)" executed successfully
2025-08-05 21:11:27,383 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:29,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:39 CST)" (scheduled at 2025-08-05 21:11:29.195816+08:00)
2025-08-05 21:11:29,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:29,241 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:29,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:29,692 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:29,694 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:29,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:29,698 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:29,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:39 CST)" executed successfully
2025-08-05 21:11:30,587 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:30,618 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:37,711 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:11:37,711 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:11:38,956 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:12:38 CST)" (scheduled at 2025-08-05 21:11:38.942754+08:00)
2025-08-05 21:11:39,078 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:09 CST)" (scheduled at 2025-08-05 21:11:39.063911+08:00)
2025-08-05 21:11:39,119 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:11:39,120 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:39,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:49 CST)" (scheduled at 2025-08-05 21:11:39.195816+08:00)
2025-08-05 21:11:39,242 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:11:39,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:39,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:39,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:39,496 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:11:39,497 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:39,497 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:11:39,577 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:11:39,577 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:11:39,578 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:11:39,578 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:11:39,578 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:39,622 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:09 CST)" executed successfully
2025-08-05 21:11:39,644 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:11:39,645 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:11:39,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:39,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:39,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:39,719 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:39,759 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:49 CST)" executed successfully
2025-08-05 21:11:40,279 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:11:40,279 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.78秒
2025-08-05 21:11:40,280 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:11:40,281 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:40,322 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:12:38 CST)" executed successfully
2025-08-05 21:11:49,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:59 CST)" (scheduled at 2025-08-05 21:11:49.195816+08:00)
2025-08-05 21:11:49,253 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:49,254 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:49,380 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:49,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:49,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:49,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:49,715 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:49,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:59 CST)" executed successfully
2025-08-05 21:11:57,385 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:11:57,386 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:11:57,387 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:11:59,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:09 CST)" (scheduled at 2025-08-05 21:11:59.195816+08:00)
2025-08-05 21:11:59,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:59,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:59,376 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:59,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:59,705 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:59,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:59,706 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:11:59,746 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:09 CST)" executed successfully
2025-08-05 21:12:00,805 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:00,820 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:09,067 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:39 CST)" (scheduled at 2025-08-05 21:12:09.063911+08:00)
2025-08-05 21:12:09,113 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:12:09,113 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:09,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:19 CST)" (scheduled at 2025-08-05 21:12:09.195816+08:00)
2025-08-05 21:12:09,239 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:09,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:09,240 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:12:09,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:09,573 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:12:09,573 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:12:09,575 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:12:09,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:12:09,577 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:09,619 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:39 CST)" executed successfully
2025-08-05 21:12:09,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:09,704 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:12:09,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:09,705 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:09,748 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:19 CST)" executed successfully
2025-08-05 21:12:19,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:29 CST)" (scheduled at 2025-08-05 21:12:19.195816+08:00)
2025-08-05 21:12:19,253 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:19,253 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:19,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:19,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:19,713 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:12:19,714 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:19,714 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:19,756 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:29 CST)" executed successfully
2025-08-05 21:12:27,403 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:29,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:39 CST)" (scheduled at 2025-08-05 21:12:29.195816+08:00)
2025-08-05 21:12:29,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:29,247 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:29,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:29,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:29,700 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:12:29,700 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:29,701 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:29,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:39 CST)" executed successfully
2025-08-05 21:12:30,006 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:30,021 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:38,507 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:12:38,507 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:12:38,955 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:13:38 CST)" (scheduled at 2025-08-05 21:12:38.942754+08:00)
2025-08-05 21:12:39,079 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:09 CST)" (scheduled at 2025-08-05 21:12:39.063911+08:00)
2025-08-05 21:12:39,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:49 CST)" (scheduled at 2025-08-05 21:12:39.195816+08:00)
2025-08-05 21:12:39,368 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:39,369 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:39,556 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:12:39,557 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:39,558 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:12:39,599 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:39,772 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:12:39,772 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:39,895 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:12:40,000 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:12:40,000 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:12:40,040 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:40,041 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.44秒
2025-08-05 21:12:40,041 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:40,042 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:40,083 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:49 CST)" executed successfully
2025-08-05 21:12:40,245 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:12:40,246 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:12:40,247 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:12:40,247 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:12:40,248 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:40,289 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:09 CST)" executed successfully
2025-08-05 21:12:40,464 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:12:40,465 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.91秒
2025-08-05 21:12:40,465 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:12:40,466 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:40,508 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:13:38 CST)" executed successfully
2025-08-05 21:12:49,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:59 CST)" (scheduled at 2025-08-05 21:12:49.195816+08:00)
2025-08-05 21:12:49,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:49,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:49,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:49,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:49,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:12:49,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:49,713 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:12:49,754 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:59 CST)" executed successfully
2025-08-05 21:12:57,399 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:12:57,401 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:12:57,407 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:12:59,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:09 CST)" (scheduled at 2025-08-05 21:12:59.195816+08:00)
2025-08-05 21:12:59,876 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:59,876 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:00,221 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:00,236 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:00,476 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:01,875 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:01,876 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.40秒
2025-08-05 21:13:01,876 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:01,876 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:01,977 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:09 CST)" executed successfully
2025-08-05 21:13:09,069 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:39 CST)" (scheduled at 2025-08-05 21:13:09.063911+08:00)
2025-08-05 21:13:09,111 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:13:09,111 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:09,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:19 CST)" (scheduled at 2025-08-05 21:13:09.195816+08:00)
2025-08-05 21:13:09,232 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:13:09,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:09,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:09,373 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:09,557 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:13:09,558 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:13:09,558 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:13:09,559 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:13:09,559 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:09,602 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:39 CST)" executed successfully
2025-08-05 21:13:09,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:09,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:13:09,699 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:09,700 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:09,748 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:19 CST)" executed successfully
2025-08-05 21:13:19,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:29 CST)" (scheduled at 2025-08-05 21:13:19.195816+08:00)
2025-08-05 21:13:19,254 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:19,254 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:19,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:19,708 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:19,708 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:13:19,709 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:19,709 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:19,752 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:29 CST)" executed successfully
2025-08-05 21:13:27,414 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:29,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:39 CST)" (scheduled at 2025-08-05 21:13:29.195816+08:00)
2025-08-05 21:13:30,434 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:30,458 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:38,953 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:14:38 CST)" (scheduled at 2025-08-05 21:13:38.942754+08:00)
2025-08-05 21:13:39,000 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:13:39,000 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:39,002 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:13:39,078 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:14:09 CST)" (scheduled at 2025-08-05 21:13:39.063911+08:00)
2025-08-05 21:13:39,120 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:13:39,121 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:39,152 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:13:39,152 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:13:39,202 - apscheduler.scheduler - WARNING - Execution of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:39 CST)" skipped: maximum number of running instances reached (1)
2025-08-05 21:13:39,244 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:13:39,325 - distributed_lock - INFO - Worker 40408 获取锁超时: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:39,325 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:49 CST)" executed successfully
2025-08-05 21:13:39,575 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:13:39,575 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:13:39,576 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:13:39,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:13:39,576 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:39,617 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:14:09 CST)" executed successfully
2025-08-05 21:13:39,840 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:13:39,840 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.84秒
2025-08-05 21:13:39,841 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:13:39,842 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:39,886 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:14:38 CST)" executed successfully
2025-08-05 21:13:44,850 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 21:13:45,570 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:13:49,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:59 CST)" (scheduled at 2025-08-05 21:13:49.195816+08:00)
2025-08-05 21:13:57,407 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:13:57,408 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:13:57,429 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:13:57,875 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:57,876 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:58,003 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:58,345 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:58,346 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:13:58,355 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:58,356 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:58,399 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:59 CST)" executed successfully
2025-08-05 21:13:59,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:09 CST)" (scheduled at 2025-08-05 21:13:59.195816+08:00)
2025-08-05 21:13:59,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:59,241 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:59,362 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:59,698 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:59,698 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:13:59,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:59,699 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:13:59,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:09 CST)" executed successfully
2025-08-05 21:14:00,651 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:00,698 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:09,074 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:14:39 CST)" (scheduled at 2025-08-05 21:14:09.063911+08:00)
2025-08-05 21:14:09,116 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:14:09,116 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:09,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:19 CST)" (scheduled at 2025-08-05 21:14:09.195816+08:00)
2025-08-05 21:14:09,237 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:14:09,279 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:09,280 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:09,399 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:09,565 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:14:09,565 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:14:09,566 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:14:09,566 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:14:09,567 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:09,607 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:14:39 CST)" executed successfully
2025-08-05 21:14:09,728 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:09,729 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:14:09,730 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:09,731 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:09,771 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:19 CST)" executed successfully
2025-08-05 21:14:19,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:29 CST)" (scheduled at 2025-08-05 21:14:19.195816+08:00)
2025-08-05 21:14:19,238 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:19,239 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:19,363 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:19,694 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:19,694 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:14:19,695 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:19,695 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:19,737 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:29 CST)" executed successfully
2025-08-05 21:14:27,444 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:29,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:39 CST)" (scheduled at 2025-08-05 21:14:29.195816+08:00)
2025-08-05 21:14:29,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:29,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:29,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:29,700 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:29,701 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:14:29,701 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:29,702 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:29,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:39 CST)" executed successfully
2025-08-05 21:14:30,857 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:30,919 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:38,956 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:15:38 CST)" (scheduled at 2025-08-05 21:14:38.942754+08:00)
2025-08-05 21:14:38,999 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:14:38,999 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:38,999 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:14:39,065 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:15:09 CST)" (scheduled at 2025-08-05 21:14:39.063911+08:00)
2025-08-05 21:14:39,110 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:14:39,110 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:39,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:49 CST)" (scheduled at 2025-08-05 21:14:39.195816+08:00)
2025-08-05 21:14:39,234 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:14:39,234 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:14:39,238 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:14:39,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:39,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:39,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:39,574 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:14:39,575 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:14:39,576 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:14:39,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:14:39,577 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:39,622 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:15:09 CST)" executed successfully
2025-08-05 21:14:39,735 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:39,736 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:14:39,736 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:39,736 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:39,780 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:49 CST)" executed successfully
2025-08-05 21:14:40,115 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:14:40,115 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.12秒
2025-08-05 21:14:40,116 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:14:40,116 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:40,161 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:15:38 CST)" executed successfully
2025-08-05 21:14:49,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:59 CST)" (scheduled at 2025-08-05 21:14:49.195816+08:00)
2025-08-05 21:14:49,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:49,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:49,374 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:49,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:49,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:14:49,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:49,718 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:49,760 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:14:59 CST)" executed successfully
2025-08-05 21:14:57,419 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:14:57,420 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:14:57,451 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:14:59,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:09 CST)" (scheduled at 2025-08-05 21:14:59.195816+08:00)
2025-08-05 21:14:59,242 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:14:59,242 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:14:59,366 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:14:59,695 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:14:59,697 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:14:59,697 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:14:59,697 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:14:59,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:09 CST)" executed successfully
2025-08-05 21:15:00,053 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:00,116 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:09,065 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:15:39 CST)" (scheduled at 2025-08-05 21:15:09.063911+08:00)
2025-08-05 21:15:09,107 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:15:09,109 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:09,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:19 CST)" (scheduled at 2025-08-05 21:15:09.195816+08:00)
2025-08-05 21:15:09,236 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:15:09,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:09,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:09,362 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:09,576 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:15:09,576 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:15:09,577 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:15:09,577 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:15:09,577 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:09,618 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:15:39 CST)" executed successfully
2025-08-05 21:15:09,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:09,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:15:09,700 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:09,701 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:09,742 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:19 CST)" executed successfully
2025-08-05 21:15:19,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:29 CST)" (scheduled at 2025-08-05 21:15:19.195816+08:00)
2025-08-05 21:15:19,242 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:19,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:19,365 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:19,693 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:19,693 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:15:19,694 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:19,694 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:19,735 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:29 CST)" executed successfully
2025-08-05 21:15:27,457 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:29,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:39 CST)" (scheduled at 2025-08-05 21:15:29.195816+08:00)
2025-08-05 21:15:29,258 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:29,258 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:29,392 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:29,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:29,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:15:29,721 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:29,722 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:29,762 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:39 CST)" executed successfully
2025-08-05 21:15:30,272 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:30,318 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:38,946 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:16:38 CST)" (scheduled at 2025-08-05 21:15:38.942754+08:00)
2025-08-05 21:15:38,987 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:15:38,988 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:38,988 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:15:39,069 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:16:09 CST)" (scheduled at 2025-08-05 21:15:39.063911+08:00)
2025-08-05 21:15:39,110 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:15:39,110 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:39,189 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:15:39,189 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:15:39,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:49 CST)" (scheduled at 2025-08-05 21:15:39.195816+08:00)
2025-08-05 21:15:39,232 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:15:39,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:39,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:39,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:39,558 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:15:39,559 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:15:39,559 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:15:39,560 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:15:39,560 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:39,603 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:16:09 CST)" executed successfully
2025-08-05 21:15:39,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:39,704 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:15:39,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:39,705 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:39,751 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:49 CST)" executed successfully
2025-08-05 21:15:40,965 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:15:40,966 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.98秒
2025-08-05 21:15:40,966 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:15:40,966 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:41,008 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:16:38 CST)" executed successfully
2025-08-05 21:15:49,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:59 CST)" (scheduled at 2025-08-05 21:15:49.195816+08:00)
2025-08-05 21:15:49,256 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:49,257 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:49,384 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:49,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:49,713 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:15:49,714 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:49,715 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:49,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:15:59 CST)" executed successfully
2025-08-05 21:15:57,433 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:15:57,434 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:15:57,463 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:15:59,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:09 CST)" (scheduled at 2025-08-05 21:15:59.195816+08:00)
2025-08-05 21:15:59,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:15:59,242 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:15:59,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:15:59,700 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:15:59,701 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:15:59,702 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:15:59,702 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:15:59,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:09 CST)" executed successfully
2025-08-05 21:16:00,468 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:00,515 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:09,078 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:16:39 CST)" (scheduled at 2025-08-05 21:16:09.063911+08:00)
2025-08-05 21:16:09,121 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:16:09,122 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:09,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:19 CST)" (scheduled at 2025-08-05 21:16:09.195816+08:00)
2025-08-05 21:16:09,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:09,247 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:09,247 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:16:09,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:09,582 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:16:09,583 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:16:09,584 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:16:09,584 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:16:09,585 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:09,625 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:16:39 CST)" executed successfully
2025-08-05 21:16:09,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:09,706 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:16:09,706 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:09,707 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:09,749 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:19 CST)" executed successfully
2025-08-05 21:16:19,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:29 CST)" (scheduled at 2025-08-05 21:16:19.195816+08:00)
2025-08-05 21:16:19,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:19,249 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:19,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:19,695 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:19,695 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.32秒
2025-08-05 21:16:19,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:19,699 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:19,739 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:29 CST)" executed successfully
2025-08-05 21:16:27,472 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:29,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:39 CST)" (scheduled at 2025-08-05 21:16:29.195816+08:00)
2025-08-05 21:16:29,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:29,253 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:29,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:29,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:29,715 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:16:29,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:29,716 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:29,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:39 CST)" executed successfully
2025-08-05 21:16:30,692 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:30,786 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:38,953 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:17:38 CST)" (scheduled at 2025-08-05 21:16:38.942754+08:00)
2025-08-05 21:16:38,996 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:16:38,997 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:38,997 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:16:39,075 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:17:09 CST)" (scheduled at 2025-08-05 21:16:39.063911+08:00)
2025-08-05 21:16:39,121 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:16:39,123 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:39,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:49 CST)" (scheduled at 2025-08-05 21:16:39.195816+08:00)
2025-08-05 21:16:39,213 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:16:39,214 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:16:39,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:39,242 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:39,247 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:16:39,365 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:39,581 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:16:39,581 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:16:39,582 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:16:39,582 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:16:39,583 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:39,624 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:17:09 CST)" executed successfully
2025-08-05 21:16:39,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:39,700 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:16:39,702 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:39,704 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:39,747 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:49 CST)" executed successfully
2025-08-05 21:16:40,294 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:16:40,295 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.30秒
2025-08-05 21:16:40,295 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:16:40,296 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:40,340 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:17:38 CST)" executed successfully
2025-08-05 21:16:49,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:59 CST)" (scheduled at 2025-08-05 21:16:49.195816+08:00)
2025-08-05 21:16:49,238 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:49,239 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:49,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:49,962 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:49,962 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.60秒
2025-08-05 21:16:49,963 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:49,963 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:50,014 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:16:59 CST)" executed successfully
2025-08-05 21:16:57,439 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:16:57,441 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:16:57,484 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:16:59,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:09 CST)" (scheduled at 2025-08-05 21:16:59.195816+08:00)
2025-08-05 21:16:59,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:16:59,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:16:59,368 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:16:59,701 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:16:59,702 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:16:59,702 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:16:59,703 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:16:59,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:09 CST)" executed successfully
2025-08-05 21:17:00,887 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:00,964 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:09,064 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:17:39 CST)" (scheduled at 2025-08-05 21:17:09.063911+08:00)
2025-08-05 21:17:09,106 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:17:09,106 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:09,203 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:19 CST)" (scheduled at 2025-08-05 21:17:09.195816+08:00)
2025-08-05 21:17:09,232 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:17:09,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:09,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:09,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:09,564 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:17:09,565 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:17:09,565 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:17:09,566 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:17:09,566 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:09,610 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:17:39 CST)" executed successfully
2025-08-05 21:17:09,715 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:09,715 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:17:09,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:09,716 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:09,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:19 CST)" executed successfully
2025-08-05 21:17:19,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:29 CST)" (scheduled at 2025-08-05 21:17:19.195816+08:00)
2025-08-05 21:17:19,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:19,241 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:19,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:19,695 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:19,698 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:17:19,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:19,706 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:19,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:29 CST)" executed successfully
2025-08-05 21:17:27,492 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:29,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:39 CST)" (scheduled at 2025-08-05 21:17:29.195816+08:00)
2025-08-05 21:17:29,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:29,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:29,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:29,694 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:29,695 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:17:29,695 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:29,696 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:29,738 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:39 CST)" executed successfully
2025-08-05 21:17:30,069 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:30,177 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:38,948 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:18:38 CST)" (scheduled at 2025-08-05 21:17:38.942754+08:00)
2025-08-05 21:17:38,989 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:17:38,989 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:38,990 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:17:39,066 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:18:09 CST)" (scheduled at 2025-08-05 21:17:39.063911+08:00)
2025-08-05 21:17:39,107 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:17:39,107 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:39,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:49 CST)" (scheduled at 2025-08-05 21:17:39.195816+08:00)
2025-08-05 21:17:39,205 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:17:39,206 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:17:39,228 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:17:39,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:39,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:39,366 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:39,564 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:17:39,565 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:17:39,565 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:17:39,566 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:17:39,566 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:39,622 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:18:09 CST)" executed successfully
2025-08-05 21:17:39,707 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:39,708 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:17:39,708 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:39,709 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:39,752 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:49 CST)" executed successfully
2025-08-05 21:17:40,634 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:17:40,634 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.64秒
2025-08-05 21:17:40,634 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:17:40,635 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:40,677 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:18:38 CST)" executed successfully
2025-08-05 21:17:49,211 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:59 CST)" (scheduled at 2025-08-05 21:17:49.195816+08:00)
2025-08-05 21:17:49,255 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:49,256 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:49,385 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:49,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:49,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:17:49,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:49,716 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:49,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:17:59 CST)" executed successfully
2025-08-05 21:17:57,456 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:17:57,459 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:17:57,502 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:17:59,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:09 CST)" (scheduled at 2025-08-05 21:17:59.195816+08:00)
2025-08-05 21:17:59,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:17:59,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:17:59,368 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:17:59,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:17:59,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:17:59,700 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:17:59,700 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:17:59,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:09 CST)" executed successfully
2025-08-05 21:18:00,268 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:00,392 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:09,072 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:18:39 CST)" (scheduled at 2025-08-05 21:18:09.063911+08:00)
2025-08-05 21:18:09,114 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:18:09,114 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:09,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:19 CST)" (scheduled at 2025-08-05 21:18:09.195816+08:00)
2025-08-05 21:18:09,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:09,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:09,241 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:18:09,363 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:09,566 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:18:09,567 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:18:09,567 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:18:09,567 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:18:09,568 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:09,620 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:18:39 CST)" executed successfully
2025-08-05 21:18:09,700 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:09,700 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:18:09,701 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:09,701 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:09,742 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:19 CST)" executed successfully
2025-08-05 21:18:19,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:29 CST)" (scheduled at 2025-08-05 21:18:19.195816+08:00)
2025-08-05 21:18:19,255 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:19,256 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:19,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:19,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:19,705 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:18:19,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:19,706 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:19,748 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:29 CST)" executed successfully
2025-08-05 21:18:27,517 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:29,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:39 CST)" (scheduled at 2025-08-05 21:18:29.195816+08:00)
2025-08-05 21:18:29,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:29,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:29,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:29,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:29,705 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:18:29,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:29,705 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:29,750 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:39 CST)" executed successfully
2025-08-05 21:18:30,482 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:30,590 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:38,958 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:19:38 CST)" (scheduled at 2025-08-05 21:18:38.942754+08:00)
2025-08-05 21:18:38,999 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:18:38,999 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:38,999 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:18:39,064 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:19:09 CST)" (scheduled at 2025-08-05 21:18:39.063911+08:00)
2025-08-05 21:18:39,106 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:18:39,107 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:39,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:49 CST)" (scheduled at 2025-08-05 21:18:39.195816+08:00)
2025-08-05 21:18:39,226 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:18:39,244 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:39,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:39,248 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:18:39,255 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:18:39,369 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:39,562 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:18:39,562 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:18:39,562 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:18:39,563 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:18:39,563 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:39,604 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:19:09 CST)" executed successfully
2025-08-05 21:18:39,702 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:39,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:18:39,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:39,705 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:39,750 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:49 CST)" executed successfully
2025-08-05 21:18:40,043 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:18:40,043 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.04秒
2025-08-05 21:18:40,044 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:18:40,044 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:40,084 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:19:38 CST)" executed successfully
2025-08-05 21:18:49,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:59 CST)" (scheduled at 2025-08-05 21:18:49.195816+08:00)
2025-08-05 21:18:49,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:49,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:49,372 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:49,702 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:49,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:18:49,703 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:49,703 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:49,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:18:59 CST)" executed successfully
2025-08-05 21:18:57,479 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:18:57,480 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:18:57,526 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:18:59,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:09 CST)" (scheduled at 2025-08-05 21:18:59.195816+08:00)
2025-08-05 21:18:59,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:18:59,241 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:18:59,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:18:59,696 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:18:59,696 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:18:59,696 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:18:59,696 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:18:59,737 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:09 CST)" executed successfully
2025-08-05 21:19:00,657 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:00,781 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:09,070 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:19:39 CST)" (scheduled at 2025-08-05 21:19:09.063911+08:00)
2025-08-05 21:19:09,111 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:19:09,111 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:09,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:19 CST)" (scheduled at 2025-08-05 21:19:09.195816+08:00)
2025-08-05 21:19:09,236 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:19:09,239 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:09,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:09,365 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:09,574 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:19:09,574 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:19:09,574 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:19:09,575 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:19:09,575 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:09,616 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:19:39 CST)" executed successfully
2025-08-05 21:19:09,697 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:09,698 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:19:09,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:09,698 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:09,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:19 CST)" executed successfully
2025-08-05 21:19:19,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:29 CST)" (scheduled at 2025-08-05 21:19:19.195816+08:00)
2025-08-05 21:19:19,239 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:19,239 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:19,365 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:19,694 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:19,695 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:19:19,695 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:19,695 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:19,736 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:29 CST)" executed successfully
2025-08-05 21:19:27,533 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:29,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:39 CST)" (scheduled at 2025-08-05 21:19:29.195816+08:00)
2025-08-05 21:19:29,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:29,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:29,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:29,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:29,712 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:19:29,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:29,712 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:29,754 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:39 CST)" executed successfully
2025-08-05 21:19:30,003 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:30,898 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:38,954 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:20:38 CST)" (scheduled at 2025-08-05 21:19:38.942754+08:00)
2025-08-05 21:19:39,079 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:20:09 CST)" (scheduled at 2025-08-05 21:19:39.063911+08:00)
2025-08-05 21:19:39,089 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:19:39,090 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:39,092 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:19:39,188 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:19:39,188 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:39,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:49 CST)" (scheduled at 2025-08-05 21:19:39.195816+08:00)
2025-08-05 21:19:39,388 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:39,389 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:39,392 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:19:39,392 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:19:39,486 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:19:39,686 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 21:19:39,687 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 21:19:39,689 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 21:19:40,194 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:19:40,194 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.10秒
2025-08-05 21:19:40,194 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:19:40,195 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:40,284 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:20:38 CST)" executed successfully
2025-08-05 21:19:40,388 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:19:40,390 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:19:40,391 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.91秒
2025-08-05 21:19:40,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:19:40,393 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:40,485 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:20:09 CST)" executed successfully
2025-08-05 21:19:42,087 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:42,478 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:42,478 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 21:19:42,479 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:42,479 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:42,519 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:49 CST)" executed successfully
2025-08-05 21:19:49,208 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:59 CST)" (scheduled at 2025-08-05 21:19:49.195816+08:00)
2025-08-05 21:19:49,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:49,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:49,373 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:49,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:49,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:19:49,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:49,711 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:49,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:19:59 CST)" executed successfully
2025-08-05 21:19:57,492 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:19:57,492 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:19:57,538 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:19:59,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:09 CST)" (scheduled at 2025-08-05 21:19:59.195816+08:00)
2025-08-05 21:19:59,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:19:59,249 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:19:59,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:19:59,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:19:59,704 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:19:59,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:19:59,706 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:19:59,746 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:09 CST)" executed successfully
2025-08-05 21:20:00,417 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:00,449 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:06,621 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 21:20:06,621 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:20:06,786 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:20:09,071 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:20:39 CST)" (scheduled at 2025-08-05 21:20:09.063911+08:00)
2025-08-05 21:20:09,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:19 CST)" (scheduled at 2025-08-05 21:20:09.195816+08:00)
2025-08-05 21:20:09,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:20:09,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:09,383 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:20:09,728 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:20:09,728 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:20:09,729 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:20:09,729 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:20:09,729 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:09,772 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:20:39 CST)" executed successfully
2025-08-05 21:20:14,215 - distributed_lock - ERROR - 获取锁时发生错误: lock:scheduled:monster_cooldown_notify, 错误: Timeout reading from *************:6379
2025-08-05 21:20:14,216 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:19 CST)" executed successfully
2025-08-05 21:20:19,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:29 CST)" (scheduled at 2025-08-05 21:20:19.195816+08:00)
2025-08-05 21:20:19,395 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:20:19,395 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:19,530 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:20:19,880 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:20:19,880 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:20:19,880 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:20:19,880 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:19,924 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:29 CST)" executed successfully
2025-08-05 21:20:27,552 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:29,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:39 CST)" (scheduled at 2025-08-05 21:20:29.195816+08:00)
2025-08-05 21:20:29,245 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:20:29,245 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:29,376 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:20:29,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:20:29,722 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:20:29,722 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:20:29,722 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:29,770 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:39 CST)" executed successfully
2025-08-05 21:20:30,369 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:30,400 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:38,947 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:21:38 CST)" (scheduled at 2025-08-05 21:20:38.942754+08:00)
2025-08-05 21:20:38,995 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:20:38,995 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:38,996 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:20:39,072 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:21:09 CST)" (scheduled at 2025-08-05 21:20:39.063911+08:00)
2025-08-05 21:20:39,117 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:20:39,117 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:39,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:49 CST)" (scheduled at 2025-08-05 21:20:39.195816+08:00)
2025-08-05 21:20:39,200 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:20:39,200 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:20:39,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:20:39,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:39,248 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:20:39,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:20:39,600 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:20:39,600 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:20:39,601 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:20:39,601 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:20:39,601 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:39,649 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:21:09 CST)" executed successfully
2025-08-05 21:20:39,731 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:20:39,732 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:20:39,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:20:39,732 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:39,776 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:49 CST)" executed successfully
2025-08-05 21:20:40,650 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:20:40,651 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.66秒
2025-08-05 21:20:40,651 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:20:40,652 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:40,701 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:21:38 CST)" executed successfully
2025-08-05 21:20:49,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:59 CST)" (scheduled at 2025-08-05 21:20:49.195816+08:00)
2025-08-05 21:20:49,256 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:20:49,256 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:49,387 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:20:49,735 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:20:49,735 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:20:49,735 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:20:49,735 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:49,779 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:20:59 CST)" executed successfully
2025-08-05 21:20:57,493 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:20:57,494 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:20:57,554 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:20:59,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:09 CST)" (scheduled at 2025-08-05 21:20:59.195816+08:00)
2025-08-05 21:20:59,245 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:20:59,245 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:20:59,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:20:59,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:20:59,717 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:20:59,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:20:59,718 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:20:59,763 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:09 CST)" executed successfully
2025-08-05 21:21:00,559 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:00,590 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:09,071 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:21:39 CST)" (scheduled at 2025-08-05 21:21:09.063911+08:00)
2025-08-05 21:21:09,115 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:21:09,116 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:09,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:19 CST)" (scheduled at 2025-08-05 21:21:09.195816+08:00)
2025-08-05 21:21:09,240 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:09,240 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:09,247 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:21:09,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:09,617 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:21:09,618 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:21:09,618 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 21:21:09,618 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:21:09,618 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:09,661 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:21:39 CST)" executed successfully
2025-08-05 21:21:09,747 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:09,748 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 21:21:09,748 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:09,748 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:09,809 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:19 CST)" executed successfully
2025-08-05 21:21:19,203 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:29 CST)" (scheduled at 2025-08-05 21:21:19.195816+08:00)
2025-08-05 21:21:19,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:19,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:19,378 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:19,744 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:19,744 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:21:19,744 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:19,744 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:19,789 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:29 CST)" executed successfully
2025-08-05 21:21:27,562 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:29,205 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:39 CST)" (scheduled at 2025-08-05 21:21:29.195816+08:00)
2025-08-05 21:21:29,250 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:29,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:29,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:29,729 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:29,729 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:21:29,729 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:29,730 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:29,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:39 CST)" executed successfully
2025-08-05 21:21:30,768 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:30,814 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:38,944 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:22:38 CST)" (scheduled at 2025-08-05 21:21:38.942754+08:00)
2025-08-05 21:21:38,990 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:21:38,991 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:38,991 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:21:39,069 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:22:09 CST)" (scheduled at 2025-08-05 21:21:39.063911+08:00)
2025-08-05 21:21:39,113 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:21:39,113 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:39,201 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:49 CST)" (scheduled at 2025-08-05 21:21:39.195816+08:00)
2025-08-05 21:21:39,235 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:21:39,235 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:21:39,246 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:21:39,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:39,247 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:39,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:39,592 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:21:39,593 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:21:39,593 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:21:39,593 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:21:39,593 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:39,637 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:22:09 CST)" executed successfully
2025-08-05 21:21:39,728 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:39,729 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:21:39,729 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:39,729 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:39,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:49 CST)" executed successfully
2025-08-05 21:21:39,916 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:21:39,916 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.93秒
2025-08-05 21:21:39,917 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:21:39,917 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:39,961 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:22:38 CST)" executed successfully
2025-08-05 21:21:49,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:59 CST)" (scheduled at 2025-08-05 21:21:49.195816+08:00)
2025-08-05 21:21:49,255 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:49,256 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:49,388 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:49,738 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:49,739 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:21:49,739 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:49,739 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:49,786 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:21:59 CST)" executed successfully
2025-08-05 21:21:57,502 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:21:57,503 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:21:57,579 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:21:59,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:09 CST)" (scheduled at 2025-08-05 21:21:59.195816+08:00)
2025-08-05 21:21:59,253 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:21:59,254 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:21:59,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:21:59,735 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:21:59,737 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:21:59,738 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:21:59,739 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:21:59,783 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:09 CST)" executed successfully
2025-08-05 21:22:00,942 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:00,989 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:09,066 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:22:39 CST)" (scheduled at 2025-08-05 21:22:09.063911+08:00)
2025-08-05 21:22:09,111 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:22:09,112 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:09,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:19 CST)" (scheduled at 2025-08-05 21:22:09.195816+08:00)
2025-08-05 21:22:09,242 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:22:09,244 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:09,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:09,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:09,595 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:22:09,595 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:22:09,596 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:22:09,596 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:22:09,596 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:09,640 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:22:39 CST)" executed successfully
2025-08-05 21:22:09,726 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:09,727 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:22:09,727 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:09,727 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:09,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:19 CST)" executed successfully
2025-08-05 21:22:19,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:29 CST)" (scheduled at 2025-08-05 21:22:19.195816+08:00)
2025-08-05 21:22:19,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:19,246 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:19,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:19,727 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:19,728 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:22:19,729 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:19,729 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:19,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:29 CST)" executed successfully
2025-08-05 21:22:27,590 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:29,198 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:39 CST)" (scheduled at 2025-08-05 21:22:29.195816+08:00)
2025-08-05 21:22:29,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:29,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:29,374 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:29,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:29,720 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:22:29,720 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:29,720 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:29,769 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:39 CST)" executed successfully
2025-08-05 21:22:30,163 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:30,225 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:38,957 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:23:38 CST)" (scheduled at 2025-08-05 21:22:38.942754+08:00)
2025-08-05 21:22:39,001 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:22:39,001 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:39,002 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:22:39,066 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:23:09 CST)" (scheduled at 2025-08-05 21:22:39.063911+08:00)
2025-08-05 21:22:39,113 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:22:39,114 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:39,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:49 CST)" (scheduled at 2025-08-05 21:22:39.195816+08:00)
2025-08-05 21:22:39,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:39,249 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:39,249 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:22:39,275 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:22:39,276 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:22:39,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:39,600 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:22:39,600 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:22:39,600 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:22:39,600 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:22:39,601 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:39,648 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:23:09 CST)" executed successfully
2025-08-05 21:22:39,728 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:39,728 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:22:39,728 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:39,728 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:39,772 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:49 CST)" executed successfully
2025-08-05 21:22:39,945 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:22:39,945 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.94秒
2025-08-05 21:22:39,945 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:22:39,946 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:40,023 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:23:38 CST)" executed successfully
2025-08-05 21:22:49,205 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:59 CST)" (scheduled at 2025-08-05 21:22:49.195816+08:00)
2025-08-05 21:22:49,249 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:49,250 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:49,484 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:50,039 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:50,040 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.56秒
2025-08-05 21:22:50,040 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:50,040 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:50,084 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:22:59 CST)" executed successfully
2025-08-05 21:22:57,516 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:22:57,516 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:22:57,594 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:22:59,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:09 CST)" (scheduled at 2025-08-05 21:22:59.195816+08:00)
2025-08-05 21:22:59,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:22:59,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:22:59,373 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:22:59,723 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:22:59,724 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:22:59,724 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:22:59,724 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:22:59,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:09 CST)" executed successfully
2025-08-05 21:23:00,379 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:00,441 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:09,075 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:23:39 CST)" (scheduled at 2025-08-05 21:23:09.063911+08:00)
2025-08-05 21:23:09,119 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:23:09,119 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:09,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:19 CST)" (scheduled at 2025-08-05 21:23:09.195816+08:00)
2025-08-05 21:23:09,251 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:23:09,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:09,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:09,384 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:09,596 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:23:09,597 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:23:09,597 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:23:09,598 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:23:09,598 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:09,644 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:23:39 CST)" executed successfully
2025-08-05 21:23:09,732 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:09,732 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:23:09,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:09,732 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:09,776 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:19 CST)" executed successfully
2025-08-05 21:23:19,203 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:29 CST)" (scheduled at 2025-08-05 21:23:19.195816+08:00)
2025-08-05 21:23:19,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:19,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:19,388 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:19,738 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:19,738 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:23:19,738 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:19,739 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:19,782 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:29 CST)" executed successfully
2025-08-05 21:23:27,607 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:29,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:39 CST)" (scheduled at 2025-08-05 21:23:29.195816+08:00)
2025-08-05 21:23:29,242 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:29,242 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:29,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:29,803 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:29,804 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.41秒
2025-08-05 21:23:29,804 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:29,804 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:29,855 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:39 CST)" executed successfully
2025-08-05 21:23:30,574 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:30,636 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:38,957 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:24:38 CST)" (scheduled at 2025-08-05 21:23:38.942754+08:00)
2025-08-05 21:23:39,011 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:23:39,011 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:39,011 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:23:39,065 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:24:09 CST)" (scheduled at 2025-08-05 21:23:39.063911+08:00)
2025-08-05 21:23:39,115 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:23:39,115 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:39,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:49 CST)" (scheduled at 2025-08-05 21:23:39.195816+08:00)
2025-08-05 21:23:39,257 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:39,257 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:39,275 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:23:39,275 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:23:39,313 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:23:39,399 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 21:23:39,399 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 21:23:39,399 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 21:23:39,716 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:23:39,716 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:23:39,716 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.40秒
2025-08-05 21:23:39,717 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:23:39,717 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:39,768 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:24:09 CST)" executed successfully
2025-08-05 21:23:40,023 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:23:40,023 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.01秒
2025-08-05 21:23:40,023 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:23:40,024 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:40,075 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:24:38 CST)" executed successfully
2025-08-05 21:23:41,552 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:41,963 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:41,964 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.41秒
2025-08-05 21:23:41,964 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:41,964 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:42,007 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:49 CST)" executed successfully
2025-08-05 21:23:49,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:59 CST)" (scheduled at 2025-08-05 21:23:49.195816+08:00)
2025-08-05 21:23:49,277 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:49,277 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:49,413 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:49,774 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:49,774 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:23:49,774 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:49,775 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:49,822 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:23:59 CST)" executed successfully
2025-08-05 21:23:57,530 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:23:57,530 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:23:57,623 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:23:59,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:09 CST)" (scheduled at 2025-08-05 21:23:59.195816+08:00)
2025-08-05 21:23:59,252 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:23:59,252 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:23:59,401 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:23:59,766 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:23:59,766 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:23:59,767 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:23:59,767 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:23:59,811 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:09 CST)" executed successfully
2025-08-05 21:24:00,774 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:00,836 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:09,079 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:24:39 CST)" (scheduled at 2025-08-05 21:24:09.063911+08:00)
2025-08-05 21:24:09,124 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:24:09,124 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:09,203 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:19 CST)" (scheduled at 2025-08-05 21:24:09.195816+08:00)
2025-08-05 21:24:09,248 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:09,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:09,256 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:24:09,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:09,601 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:24:09,601 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:24:09,601 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:24:09,602 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:24:09,602 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:09,645 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:24:39 CST)" executed successfully
2025-08-05 21:24:09,734 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:09,735 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:24:09,735 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:09,735 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:09,782 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:19 CST)" executed successfully
2025-08-05 21:24:19,206 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:29 CST)" (scheduled at 2025-08-05 21:24:19.195816+08:00)
2025-08-05 21:24:19,392 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:19,392 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:19,690 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:20,490 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:20,491 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.80秒
2025-08-05 21:24:20,491 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:20,491 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:20,592 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:29 CST)" executed successfully
2025-08-05 21:24:27,638 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:29,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:39 CST)" (scheduled at 2025-08-05 21:24:29.195816+08:00)
2025-08-05 21:24:29,242 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:29,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:29,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:29,731 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:29,732 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:24:29,733 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:29,734 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:29,780 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:39 CST)" executed successfully
2025-08-05 21:24:30,030 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:30,973 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:38,952 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:25:38 CST)" (scheduled at 2025-08-05 21:24:38.942754+08:00)
2025-08-05 21:24:38,998 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:24:38,998 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:38,999 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:24:39,074 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:25:09 CST)" (scheduled at 2025-08-05 21:24:39.063911+08:00)
2025-08-05 21:24:39,119 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:24:39,120 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:39,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:49 CST)" (scheduled at 2025-08-05 21:24:39.195816+08:00)
2025-08-05 21:24:39,207 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:24:39,208 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:24:39,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:39,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:39,257 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:24:39,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:39,602 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:24:39,602 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:24:39,603 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:24:39,603 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:24:39,603 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:39,648 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:25:09 CST)" executed successfully
2025-08-05 21:24:39,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:39,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:24:39,722 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:39,722 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:39,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:49 CST)" executed successfully
2025-08-05 21:24:40,801 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:24:40,802 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.80秒
2025-08-05 21:24:40,803 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:24:40,805 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:40,854 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:25:38 CST)" executed successfully
2025-08-05 21:24:49,202 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:59 CST)" (scheduled at 2025-08-05 21:24:49.195816+08:00)
2025-08-05 21:24:49,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:49,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:49,389 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:49,914 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:49,914 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.53秒
2025-08-05 21:24:49,915 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:49,915 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:49,991 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:24:59 CST)" executed successfully
2025-08-05 21:24:57,541 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:24:57,542 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:24:57,648 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:24:59,210 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:09 CST)" (scheduled at 2025-08-05 21:24:59.195816+08:00)
2025-08-05 21:24:59,254 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:24:59,254 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:24:59,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:24:59,731 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:24:59,732 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:24:59,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:24:59,732 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:24:59,776 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:09 CST)" executed successfully
2025-08-05 21:25:00,149 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:00,222 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:09,064 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:25:39 CST)" (scheduled at 2025-08-05 21:25:09.063911+08:00)
2025-08-05 21:25:09,108 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:25:09,109 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:09,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:19 CST)" (scheduled at 2025-08-05 21:25:09.195816+08:00)
2025-08-05 21:25:09,239 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:25:09,249 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:09,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:09,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:09,588 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:25:09,589 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:25:09,590 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:25:09,591 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:25:09,591 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:09,639 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:25:39 CST)" executed successfully
2025-08-05 21:25:09,739 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:09,741 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:25:09,741 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:09,742 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:09,789 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:19 CST)" executed successfully
2025-08-05 21:25:19,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:29 CST)" (scheduled at 2025-08-05 21:25:19.195816+08:00)
2025-08-05 21:25:19,244 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:19,245 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:19,390 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:19,778 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:19,778 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 21:25:19,778 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:19,778 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:19,830 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:29 CST)" executed successfully
2025-08-05 21:25:27,656 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:29,207 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:39 CST)" (scheduled at 2025-08-05 21:25:29.195816+08:00)
2025-08-05 21:25:29,254 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:29,255 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:29,394 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:29,770 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:29,770 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 21:25:29,771 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:29,771 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:29,819 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:39 CST)" executed successfully
2025-08-05 21:25:30,380 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:30,457 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:38,952 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:26:38 CST)" (scheduled at 2025-08-05 21:25:38.942754+08:00)
2025-08-05 21:25:39,001 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:25:39,002 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:39,002 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:25:39,076 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:26:09 CST)" (scheduled at 2025-08-05 21:25:39.063911+08:00)
2025-08-05 21:25:39,123 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:25:39,123 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:39,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:49 CST)" (scheduled at 2025-08-05 21:25:39.195816+08:00)
2025-08-05 21:25:39,247 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:39,248 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:39,257 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:25:39,273 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:25:39,273 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:25:39,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:39,630 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:25:39,632 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:25:39,632 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.38秒
2025-08-05 21:25:39,634 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:25:39,634 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:39,683 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:26:09 CST)" executed successfully
2025-08-05 21:25:39,742 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:39,743 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:25:39,743 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:39,744 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:39,790 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:49 CST)" executed successfully
2025-08-05 21:25:40,005 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:25:40,005 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.00秒
2025-08-05 21:25:40,006 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:25:40,006 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:40,053 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:26:38 CST)" executed successfully
2025-08-05 21:25:49,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:59 CST)" (scheduled at 2025-08-05 21:25:49.195816+08:00)
2025-08-05 21:25:49,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:49,243 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:49,384 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:49,760 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:49,761 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 21:25:49,763 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:49,764 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:49,813 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:25:59 CST)" executed successfully
2025-08-05 21:25:57,556 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:25:57,556 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:25:57,666 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:25:59,209 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:09 CST)" (scheduled at 2025-08-05 21:25:59.195816+08:00)
2025-08-05 21:25:59,259 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:25:59,260 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:25:59,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:25:59,784 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:25:59,785 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 21:25:59,785 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:25:59,785 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:25:59,831 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:09 CST)" executed successfully
2025-08-05 21:26:00,558 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:00,635 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:09,075 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:26:39 CST)" (scheduled at 2025-08-05 21:26:09.063911+08:00)
2025-08-05 21:26:09,118 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:26:09,119 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:09,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:19 CST)" (scheduled at 2025-08-05 21:26:09.195816+08:00)
2025-08-05 21:26:09,246 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:09,247 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:09,249 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:26:09,377 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:09,597 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:26:09,597 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:26:09,597 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:26:09,598 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:26:09,598 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:09,642 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:26:39 CST)" executed successfully
2025-08-05 21:26:09,729 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:09,730 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:09,730 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:09,730 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:09,776 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:19 CST)" executed successfully
2025-08-05 21:26:19,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:29 CST)" (scheduled at 2025-08-05 21:26:19.195816+08:00)
2025-08-05 21:26:19,244 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:19,245 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:19,384 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:19,737 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:19,738 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:19,739 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:19,740 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:19,788 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:29 CST)" executed successfully
2025-08-05 21:26:27,675 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:29,196 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:39 CST)" (scheduled at 2025-08-05 21:26:29.195816+08:00)
2025-08-05 21:26:29,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:29,241 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:29,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:29,734 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:29,735 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:29,735 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:29,736 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:29,783 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:39 CST)" executed successfully
2025-08-05 21:26:30,781 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:30,827 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:38,951 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:27:38 CST)" (scheduled at 2025-08-05 21:26:38.942754+08:00)
2025-08-05 21:26:39,002 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:26:39,002 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:39,003 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:26:39,073 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:27:09 CST)" (scheduled at 2025-08-05 21:26:39.063911+08:00)
2025-08-05 21:26:39,118 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:26:39,118 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:39,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:49 CST)" (scheduled at 2025-08-05 21:26:39.195816+08:00)
2025-08-05 21:26:39,199 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:26:39,199 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:26:39,241 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:39,242 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:39,252 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:26:39,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:39,598 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:26:39,598 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:26:39,598 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:26:39,599 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:26:39,599 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:39,648 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:27:09 CST)" executed successfully
2025-08-05 21:26:39,725 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:39,726 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:39,726 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:39,726 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:39,769 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:49 CST)" executed successfully
2025-08-05 21:26:40,861 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:26:40,862 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.86秒
2025-08-05 21:26:40,862 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:26:40,862 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:40,906 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:27:38 CST)" executed successfully
2025-08-05 21:26:49,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:59 CST)" (scheduled at 2025-08-05 21:26:49.195816+08:00)
2025-08-05 21:26:49,243 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:49,244 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:49,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:49,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:49,722 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:49,722 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:49,722 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:49,766 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:26:59 CST)" executed successfully
2025-08-05 21:26:57,560 - ConnectionManager - INFO - Redis连接池状态 (Worker 40408): 使用中=3, 可用=4, 总计=7
2025-08-05 21:26:57,561 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40408): 连接中
2025-08-05 21:26:57,683 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:26:59,211 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:09 CST)" (scheduled at 2025-08-05 21:26:59.195816+08:00)
2025-08-05 21:26:59,255 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:26:59,255 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:26:59,394 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:26:59,741 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:26:59,742 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:26:59,742 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:26:59,742 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:26:59,788 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:09 CST)" executed successfully
2025-08-05 21:27:00,001 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:27:00,959 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:27:09,073 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:27:39 CST)" (scheduled at 2025-08-05 21:27:09.063911+08:00)
2025-08-05 21:27:09,118 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:27:09,119 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:09,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:19 CST)" (scheduled at 2025-08-05 21:27:09.195816+08:00)
2025-08-05 21:27:09,251 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:27:09,595 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:27:09,596 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:27:09,596 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:27:09,596 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:27:09,596 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:09,645 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:27:39 CST)" executed successfully
2025-08-05 21:27:14,203 - distributed_lock - ERROR - 获取锁时发生错误: lock:scheduled:monster_cooldown_notify, 错误: Timeout reading from *************:6379
2025-08-05 21:27:14,204 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:19 CST)" executed successfully
2025-08-05 21:27:19,199 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:29 CST)" (scheduled at 2025-08-05 21:27:19.195816+08:00)
2025-08-05 21:27:19,378 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:27:19,378 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:19,511 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:27:19,859 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:27:19,859 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:27:19,859 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:27:19,859 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:19,905 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:29 CST)" executed successfully
2025-08-05 21:27:27,696 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:27:29,197 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:39 CST)" (scheduled at 2025-08-05 21:27:29.195816+08:00)
2025-08-05 21:27:29,245 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:27:29,245 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:29,376 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:27:29,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:27:30,890 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.51秒
2025-08-05 21:27:30,890 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:27:30,890 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:30,891 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:27:30,891 - ConnectionManager - INFO - 连接状态 (Worker 40408): 活跃连接数=0, 用户数=0
2025-08-05 21:27:30,937 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:39 CST)" executed successfully
2025-08-05 21:27:38,944 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:28:38 CST)" (scheduled at 2025-08-05 21:27:38.942754+08:00)
2025-08-05 21:27:38,989 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:push_online
2025-08-05 21:27:38,989 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:38,989 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行推送在线人数任务
2025-08-05 21:27:39,069 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:28:09 CST)" (scheduled at 2025-08-05 21:27:39.063911+08:00)
2025-08-05 21:27:39,116 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:27:39,116 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:39,192 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:27:39,192 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:27:39,204 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:49 CST)" (scheduled at 2025-08-05 21:27:39.195816+08:00)
2025-08-05 21:27:39,249 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 开始执行怪物冷却持久化任务
2025-08-05 21:27:39,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:27:39,251 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:39,392 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:27:39,595 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:27:39,595 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 怪物冷却持久化完成
2025-08-05 21:27:39,595 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 21:27:39,595 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:27:39,596 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:39,643 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:28:09 CST)" executed successfully
2025-08-05 21:27:39,735 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:27:39,735 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:27:39,736 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:27:39,736 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:39,780 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:49 CST)" executed successfully
2025-08-05 21:27:40,872 - scheduler_tasks_unified - INFO - [定时任务] Worker 40408 在线人数推送完成，当前在线: 0
2025-08-05 21:27:40,873 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.88秒
2025-08-05 21:27:40,873 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:27:40,873 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:40,921 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:28:38 CST)" executed successfully
2025-08-05 21:27:49,200 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:59 CST)" (scheduled at 2025-08-05 21:27:49.195816+08:00)
2025-08-05 21:27:49,251 - distributed_lock - INFO - Worker 40408 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:27:49,253 - distributed_task - INFO - Worker 40408 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:27:49,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:27:49,731 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:27:49,731 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:27:49,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:27:49,733 - distributed_task - INFO - Worker 40408 任务执行完成: direct_wrapper
2025-08-05 21:27:49,779 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:27:59 CST)" executed successfully
