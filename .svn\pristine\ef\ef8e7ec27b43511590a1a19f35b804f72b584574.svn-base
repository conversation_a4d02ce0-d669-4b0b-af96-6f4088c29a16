"""
测试WebSocket处理器修复
"""

import asyncio
import json
import logging
from unittest.mock import AsyncMock, MagicMock
from shop_websocket_handlers import ShopGetListHandler
from shop_service import ShopService
from enums import MessageId

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_websocket_handler_fix():
    """测试WebSocket处理器修复"""
    print("🔧 测试WebSocket处理器修复...")
    
    try:
        # 创建模拟对象
        mock_websocket = AsyncMock()
        mock_connection_manager = MagicMock()
        
        # 创建处理器
        handler = ShopGetListHandler()
        
        # 模拟商店服务
        mock_shop_service = AsyncMock()
        mock_shop_service.get_available_shops.return_value = []
        handler.shop_service = mock_shop_service
        
        # 测试数据
        test_data = {}
        test_username = "test_user"
        test_token = "test_token"
        
        print("1. 测试处理器调用...")
        result = await handler.handle(
            test_data, 
            mock_websocket, 
            test_username, 
            test_token, 
            mock_connection_manager
        )
        
        print(f"2. 检查返回结果类型: {type(result)}")
        
        # 检查是否调用了websocket.send_text
        if mock_websocket.send_text.called:
            print("✅ WebSocket send_text 被正确调用")
            
            # 检查发送的消息格式
            call_args = mock_websocket.send_text.call_args[0][0]
            try:
                message_data = json.loads(call_args)
                print(f"✅ 消息格式正确: msgId={message_data.get('msgId')}")
                
                if message_data.get('msgId') == MessageId.SHOP_GET_LIST:
                    print("✅ 消息ID正确")
                else:
                    print(f"❌ 消息ID错误: 期望{MessageId.SHOP_GET_LIST}, 实际{message_data.get('msgId')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ 消息格式错误: {e}")
                print(f"发送的内容: {call_args}")
        else:
            print("❌ WebSocket send_text 未被调用")
        
        # 检查返回值
        if hasattr(result, 'msgId'):
            print(f"✅ 返回值格式正确: msgId={result.msgId}")
        else:
            print(f"❌ 返回值格式错误: {type(result)}")
        
        print("✅ WebSocket处理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        logger.error(f"测试失败: {str(e)}")
        return False


async def test_parameter_order():
    """测试参数顺序"""
    print("\n🔄 测试参数顺序...")
    
    try:
        from shop_websocket_handlers import register_shop_handlers
        
        # 创建模拟消息管理器
        class MockMessageManager:
            def __init__(self):
                self.handlers = {}
            
            def register_handler(self, msg_id, handler_func):
                self.handlers[msg_id] = handler_func
                print(f"注册处理器: {msg_id}")
        
        mock_manager = MockMessageManager()
        
        # 注册处理器
        register_shop_handlers(mock_manager)
        
        print(f"✅ 成功注册 {len(mock_manager.handlers)} 个处理器")
        
        # 测试处理器参数
        if MessageId.SHOP_GET_LIST in mock_manager.handlers:
            handler_func = mock_manager.handlers[MessageId.SHOP_GET_LIST]
            
            # 检查函数签名
            import inspect
            sig = inspect.signature(handler_func)
            params = list(sig.parameters.keys())
            
            expected_params = ['websocket', 'username', 'token', 'data', 'connection_manager']
            
            print(f"处理器参数: {params}")
            print(f"期望参数: {expected_params}")
            
            if params == expected_params:
                print("✅ 参数顺序正确")
            else:
                print("❌ 参数顺序错误")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数顺序测试失败: {str(e)}")
        return False


async def test_message_serialization():
    """测试消息序列化"""
    print("\n📦 测试消息序列化...")
    
    try:
        from models import MessageModel
        from datetime import datetime
        
        # 创建测试消息
        message = MessageModel(
            msgId=MessageId.SHOP_GET_LIST,
            data={
                "success": True,
                "shops": [],
                "total": 0,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # 测试序列化
        serialized = message.model_dump()
        print(f"✅ 消息序列化成功: {type(serialized)}")
        
        # 测试JSON序列化
        json_str = json.dumps(serialized)
        print(f"✅ JSON序列化成功: 长度={len(json_str)}")
        
        # 测试反序列化
        deserialized = json.loads(json_str)
        print(f"✅ JSON反序列化成功: msgId={deserialized.get('msgId')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息序列化测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🧪 WebSocket处理器修复测试")
    print("=" * 50)
    
    tests = [
        ("WebSocket处理器基础测试", test_websocket_handler_fix),
        ("参数顺序测试", test_parameter_order),
        ("消息序列化测试", test_message_serialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            if await test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！WebSocket处理器问题已修复")
    else:
        print("⚠️  部分测试失败，请检查修复")


if __name__ == "__main__":
    asyncio.run(main())
