"""
基础消息处理器
定义消息处理器的基类，避免循环导入
"""

import logging
import traceback
from typing import Dict, Any
from fastapi import WebSocket
from enums import MessageId

logger = logging.getLogger(__name__)


class MessageHandler:
    """消息处理器基类"""
    def __init__(self):
        self.name = self.__class__.__name__
        
    async def validate(self, data: dict) -> bool:
        """验证消息数据"""
        return True
        
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理消息"""
        raise NotImplementedError
        
    async def error_handler(self, error: Exception, data: dict) -> dict:
        """处理错误"""
        error_msg = str(error)
        logger.error(f"{self.name} 处理错误: {error_msg}, 数据: {data}")
        logger.error(traceback.format_exc())
        return {
            "msgId": MessageId.ERROR,
            "data": {"error": error_msg}
        }
