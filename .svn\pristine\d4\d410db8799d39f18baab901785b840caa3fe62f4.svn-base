"""
武将系统数据模型
基于客户端roleData.lua设计服务器权威的武将数据模型
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enums import GeneralState, GeneralType, GeneralRarity, ItemType
import uuid


class GeneralProperties(BaseModel):
    """武将战斗属性"""
    # 基础属性
    hp: int = Field(default=1000, description="生命值")
    attack: int = Field(default=100, description="攻击力")
    defense: int = Field(default=50, description="防御力")
    speed: int = Field(default=80, description="速度")
    intelligence: int = Field(default=60, description="智力")
    leadership: int = Field(default=70, description="统率")
    
    # 战斗属性
    critical_rate: float = Field(default=0.1, description="暴击率")
    critical_damage: float = Field(default=1.5, description="暴击伤害")
    dodge_rate: float = Field(default=0.05, description="闪避率")
    block_rate: float = Field(default=0.08, description="格挡率")
    
    # 装备加成
    weapon_attack: int = Field(default=0, description="武器攻击加成")
    armor_defense: int = Field(default=0, description="防具防御加成")
    accessory_speed: int = Field(default=0, description="饰品速度加成")
    
    # 称号加成
    title_attack: int = Field(default=0, description="称号攻击加成")
    title_defense: int = Field(default=0, description="称号防御加成")
    title_speed: int = Field(default=0, description="称号速度加成")
    
    # 套装加成
    set_attack: int = Field(default=0, description="套装攻击加成")
    set_defense: int = Field(default=0, description="套装防御加成")
    set_speed: int = Field(default=0, description="套装速度加成")
    
    def get_total_properties(self) -> Dict[str, Any]:
        """获取总属性（基础+加成）"""
        return {
            "hp": self.hp,
            "attack": self.attack + self.weapon_attack + self.title_attack + self.set_attack,
            "defense": self.defense + self.armor_defense + self.title_defense + self.set_defense,
            "speed": self.speed + self.accessory_speed + self.title_speed + self.set_speed,
            "intelligence": self.intelligence,
            "leadership": self.leadership,
            "critical_rate": self.critical_rate,
            "critical_damage": self.critical_damage,
            "dodge_rate": self.dodge_rate,
            "block_rate": self.block_rate
        }
    
    def calculate_fight_power(self) -> int:
        """计算战斗力"""
        total = self.get_total_properties()
        # 战斗力计算公式：基础属性加权 + 战斗属性加权
        fight_power = (
            total["hp"] * 0.1 +           # 生命值权重0.1
            total["attack"] * 2.0 +       # 攻击力权重2.0
            total["defense"] * 1.5 +      # 防御力权重1.5
            total["speed"] * 1.2 +        # 速度权重1.2
            total["intelligence"] * 1.0 + # 智力权重1.0
            total["leadership"] * 1.3 +   # 统率权重1.3
            total["critical_rate"] * 1000 + # 暴击率权重1000
            total["critical_damage"] * 500 + # 暴击伤害权重500
            total["dodge_rate"] * 800 +   # 闪避率权重800
            total["block_rate"] * 600     # 格挡率权重600
        )
        return int(fight_power)


class GeneralSkill(BaseModel):
    """武将技能"""
    skill_id: str = Field(description="技能ID")
    skill_name: str = Field(description="技能名称")
    skill_type: str = Field(description="技能类型：active/passive/special")
    skill_level: int = Field(default=1, description="技能等级")
    is_equipped: bool = Field(default=False, description="是否装备")
    cooldown: int = Field(default=0, description="冷却时间（秒）")
    description: str = Field(default="", description="技能描述")


class GeneralSoldier(BaseModel):
    """武将士兵"""
    soldier_type: str = Field(description="士兵类型")
    soldier_name: str = Field(description="士兵名称")
    soldier_level: int = Field(default=1, description="士兵等级")
    soldier_count: int = Field(default=0, description="士兵数量")
    soldier_max: int = Field(default=100, description="最大士兵数量")


class GeneralEquipment(BaseModel):
    """武将装备"""
    weapon_id: Optional[str] = Field(default=None, description="武器ID")
    weapon_name: Optional[str] = Field(default=None, description="武器名称")
    weapon_level: int = Field(default=0, description="武器等级")
    weapon_star: int = Field(default=0, description="武器星级")
    
    armor_id: Optional[str] = Field(default=None, description="防具ID")
    armor_name: Optional[str] = Field(default=None, description="防具名称")
    armor_level: int = Field(default=0, description="防具等级")
    armor_star: int = Field(default=0, description="防具星级")
    
    accessory_id: Optional[str] = Field(default=None, description="饰品ID")
    accessory_name: Optional[str] = Field(default=None, description="饰品名称")
    accessory_level: int = Field(default=0, description="饰品等级")
    accessory_star: int = Field(default=0, description="饰品星级")


class General(BaseModel):
    """武将主模型"""
    general_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="武将ID")
    player_id: str = Field(description="武将所有者")
    template_id: str = Field(description="武将模板ID")
    name: str = Field(description="武将名称")
    avatar: str = Field(default="default", description="武将头像")
    
    # 武将类型和品质
    general_type: GeneralType = Field(description="武将类型")
    rarity: GeneralRarity = Field(description="武将品质")
    
    # 基础信息
    level: int = Field(default=1, description="武将等级")
    exp: int = Field(default=0, description="当前经验")
    exp_max: int = Field(default=100, description="升级所需经验")
    star: int = Field(default=1, description="武将星级")
    awaken_level: int = Field(default=0, description="觉醒等级")
    
    # 状态
    state: GeneralState = Field(default=GeneralState.IDLE, description="武将状态")
    is_locked: bool = Field(default=False, description="是否锁定")
    deploy_position: Optional[int] = Field(default=None, description="上阵位置")
    
    # 属性
    properties: GeneralProperties = Field(default_factory=GeneralProperties, description="武将属性")
    
    # 装备
    equipment: GeneralEquipment = Field(default_factory=GeneralEquipment, description="武将装备")
    
    # 技能
    skills: List[GeneralSkill] = Field(default_factory=list, description="武将技能")
    
    # 士兵
    soldiers: List[GeneralSoldier] = Field(default_factory=list, description="武将士兵")
    
    # 称号
    title: Optional[str] = Field(default=None, description="当前称号")
    titles: List[str] = Field(default_factory=list, description="已获得称号列表")
    
    # 套装
    current_set: Optional[str] = Field(default=None, description="当前套装")
    set_bonus: Dict[str, Any] = Field(default_factory=dict, description="套装加成")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    last_training: Optional[datetime] = Field(default=None, description="最后训练时间")
    
    # 计算属性
    @property
    def fight_power(self) -> int:
        """计算战斗力"""
        return self.properties.calculate_fight_power()
    
    @property
    def total_properties(self) -> Dict[str, Any]:
        """获取总属性"""
        return self.properties.get_total_properties()
    
    def can_level_up(self) -> bool:
        """检查是否可以升级"""
        return self.exp >= self.exp_max
    
    def can_star_up(self) -> bool:
        """检查是否可以升星"""
        # 这里可以根据具体规则实现
        return self.level >= (self.star * 10)
    
    def can_awaken(self) -> bool:
        """检查是否可以觉醒"""
        # 这里可以根据具体规则实现
        return self.star >= 5 and self.awaken_level < 3
    
    def add_exp(self, exp_amount: int) -> Dict[str, Any]:
        """添加经验值，返回升级信息"""
        self.exp += exp_amount
        result = {
            "leveled_up": False,
            "new_level": self.level,
            "exp_gained": exp_amount,
            "current_exp": self.exp,
            "exp_max": self.exp_max
        }
        
        # 检查是否可以升级
        while self.can_level_up():
            self.level += 1
            self.exp -= self.exp_max
            self.exp_max = self.calculate_exp_max()
            result["leveled_up"] = True
            result["new_level"] = self.level
            result["current_exp"] = self.exp
            result["exp_max"] = self.exp_max
        
        self.updated_at = datetime.now()
        return result
    
    def calculate_exp_max(self) -> int:
        """计算升级所需经验"""
        # 简单的经验计算公式，可以根据需要调整
        return int(self.level * 100 * (1 + self.level * 0.1))
    
    def equip_weapon(self, weapon_id: str, weapon_name: str, weapon_level: int = 0, weapon_star: int = 0):
        """装备武器"""
        self.equipment.weapon_id = weapon_id
        self.equipment.weapon_name = weapon_name
        self.equipment.weapon_level = weapon_level
        self.equipment.weapon_star = weapon_star
        self.updated_at = datetime.now()
    
    def unequip_weapon(self):
        """卸下武器"""
        self.equipment.weapon_id = None
        self.equipment.weapon_name = None
        self.equipment.weapon_level = 0
        self.equipment.weapon_star = 0
        self.updated_at = datetime.now()
    
    def equip_armor(self, armor_id: str, armor_name: str, armor_level: int = 0, armor_star: int = 0):
        """装备防具"""
        self.equipment.armor_id = armor_id
        self.equipment.armor_name = armor_name
        self.equipment.armor_level = armor_level
        self.equipment.armor_star = armor_star
        self.updated_at = datetime.now()
    
    def unequip_armor(self):
        """卸下防具"""
        self.equipment.armor_id = None
        self.equipment.armor_name = None
        self.equipment.armor_level = 0
        self.equipment.armor_star = 0
        self.updated_at = datetime.now()
    
    def equip_accessory(self, accessory_id: str, accessory_name: str, accessory_level: int = 0, accessory_star: int = 0):
        """装备饰品"""
        self.equipment.accessory_id = accessory_id
        self.equipment.accessory_name = accessory_name
        self.equipment.accessory_level = accessory_level
        self.equipment.accessory_star = accessory_star
        self.updated_at = datetime.now()
    
    def unequip_accessory(self):
        """卸下饰品"""
        self.equipment.accessory_id = None
        self.equipment.accessory_name = None
        self.equipment.accessory_level = 0
        self.equipment.accessory_star = 0
        self.updated_at = datetime.now()
    
    def learn_skill(self, skill_id: str, skill_name: str, skill_type: str, description: str = ""):
        """学习技能"""
        # 检查是否已学会
        for skill in self.skills:
            if skill.skill_id == skill_id:
                return False  # 已学会
        
        new_skill = GeneralSkill(
            skill_id=skill_id,
            skill_name=skill_name,
            skill_type=skill_type,
            description=description
        )
        self.skills.append(new_skill)
        self.updated_at = datetime.now()
        return True
    
    def equip_skill(self, skill_id: str, slot: int = 0):
        """装备技能"""
        for skill in self.skills:
            if skill.skill_id == skill_id:
                skill.is_equipped = True
                self.updated_at = datetime.now()
                return True
        return False
    
    def unequip_skill(self, skill_id: str):
        """卸下技能"""
        for skill in self.skills:
            if skill.skill_id == skill_id:
                skill.is_equipped = False
                self.updated_at = datetime.now()
                return True
        return False
    
    def change_state(self, new_state: GeneralState):
        """改变武将状态"""
        self.state = new_state
        self.updated_at = datetime.now()
    
    def deploy(self, position: int):
        """上阵武将"""
        self.state = GeneralState.DEPLOYED
        self.deploy_position = position
        self.updated_at = datetime.now()
    
    def retreat(self):
        """下阵武将"""
        self.state = GeneralState.IDLE
        self.deploy_position = None
        self.updated_at = datetime.now()
    
    def lock(self):
        """锁定武将"""
        self.is_locked = True
        self.updated_at = datetime.now()
    
    def unlock(self):
        """解锁武将"""
        self.is_locked = False
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "general_id": self.general_id,
            "player_id": self.player_id,
            "template_id": self.template_id,
            "name": self.name,
            "avatar": self.avatar,
            "general_type": self.general_type.value,
            "rarity": self.rarity.value,
            "level": self.level,
            "exp": self.exp,
            "exp_max": self.exp_max,
            "star": self.star,
            "awaken_level": self.awaken_level,
            "state": self.state.value,
            "is_locked": self.is_locked,
            "deploy_position": self.deploy_position,
            "properties": self.properties.dict(),
            "equipment": self.equipment.dict(),
            "skills": [skill.dict() for skill in self.skills],
            "soldiers": [soldier.dict() for soldier in self.soldiers],
            "title": self.title,
            "titles": self.titles,
            "current_set": self.current_set,
            "set_bonus": self.set_bonus,
            "fight_power": self.fight_power,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_training": self.last_training.isoformat() if self.last_training else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'General':
        """从字典创建武将实例"""
        # 处理枚举类型
        if "state" in data and isinstance(data["state"], str):
            data["state"] = GeneralState(data["state"])
        if "general_type" in data and isinstance(data["general_type"], str):
            data["general_type"] = GeneralType(data["general_type"])
        if "rarity" in data and isinstance(data["rarity"], str):
            data["rarity"] = GeneralRarity(data["rarity"])
        
        # 处理时间戳
        if "created_at" in data and isinstance(data["created_at"], str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data and isinstance(data["updated_at"], str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"])
        if "last_training" in data and data["last_training"] and isinstance(data["last_training"], str):
            data["last_training"] = datetime.fromisoformat(data["last_training"])
        
        return cls(**data)


class GeneralCreateRequest(BaseModel):
    """创建武将请求"""
    template_id: str = Field(description="武将模板ID")
    name: str = Field(description="武将名称")
    avatar: str = Field(default="default", description="武将头像")


class GeneralUpdateRequest(BaseModel):
    """更新武将请求"""
    name: Optional[str] = Field(default=None, description="武将名称")
    avatar: Optional[str] = Field(default=None, description="武将头像")
    level: Optional[int] = Field(default=None, description="武将等级")
    exp: Optional[int] = Field(default=None, description="经验值")
    star: Optional[int] = Field(default=None, description="星级")
    state: Optional[GeneralState] = Field(default=None, description="武将状态")


class FormationData(BaseModel):
    """阵容数据"""
    player_id: str = Field(description="玩家ID")
    formation_id: str = Field(description="阵容ID")
    formation_name: str = Field(description="阵容名称")
    generals: List[str] = Field(default_factory=list, description="武将ID列表")
    is_active: bool = Field(default=False, description="是否激活")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class GeneralResponse(BaseModel):
    """武将响应"""
    success: bool = Field(description="是否成功")
    data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")
    error: Optional[str] = Field(default=None, description="错误信息") 