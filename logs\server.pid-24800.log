2025-08-06 08:43:57,313 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:00,585 - models - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:00,839 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:02,640 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:02,667 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:02,731 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:02,752 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:02,769 - auth - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,192 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,311 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,466 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,554 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,571 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,573 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 92fff161)
2025-08-06 08:44:06,589 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,655 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,711 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,725 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,741 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:06,970 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:07,010 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:07,023 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:07,037 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:07,050 - game_server - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:07,051 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-06 08:44:07,051 - msgManager - INFO - Monster handlers registered
2025-08-06 08:44:07,052 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-06 08:44:07,062 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-06 08:44:07,133 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-06 08:44:07,134 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-06 08:44:07,135 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 79ae4f95)
2025-08-06 08:44:07,293 - game_server - INFO - 邮件管理API路由注册成功
2025-08-06 08:44:07,357 - game_server - INFO - 商店系统API路由注册成功
2025-08-06 08:44:07,357 - game_server - INFO - 模板引擎初始化成功
2025-08-06 08:44:07,360 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-06 08:44:07,361 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 24800)
2025-08-06 08:44:07,361 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-06 08:44:07,533 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-06 08:44:07,558 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-06 08:44:08,060 - ConnectionManager - INFO - 后台任务已启动 (Worker 24800)
2025-08-06 08:44:08,060 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 24800)
2025-08-06 08:44:08,068 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-06 08:44:08,069 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-06 08:44:08,069 - game_server - INFO - 游戏配置加载完成 (Worker: 24800)
2025-08-06 08:44:08,069 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:44:11,724 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-06 08:44:11,725 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=0, 总计=2
2025-08-06 08:44:11,725 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 24800): 2/2 (100.0%)
2025-08-06 08:44:11,725 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:44:11,772 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 79ae4f95)
2025-08-06 08:44:11,772 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 79ae4f95)
2025-08-06 08:44:11,783 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:11,784 - game_server - INFO - 调度器监控API路由已注册
2025-08-06 08:44:11,785 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-06 08:44:11,792 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-06 08:44:11,793 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-06 08:44:11,794 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-06 08:44:11,794 - ConnectionManager - INFO - Worker 24800 开始消费广播消息，消费者标签: ctag1.28bc76297acd4438a181aed75a156b0c
2025-08-06 08:44:11,839 - ConnectionManager - INFO - Worker 24800 开始消费个人消息，消费者标签: ctag1.b7c23aad3632411cadef2a6a6aa866cd
2025-08-06 08:44:11,933 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 24800)
2025-08-06 08:44:11,987 - distributed_lock - INFO - Worker 24800 获取锁超时: scheduler_initialization
2025-08-06 08:44:11,987 - game_server_scheduler_integration - INFO - Worker 24800 未获得调度器初始化权限，跳过调度器初始化
2025-08-06 08:44:11,988 - game_server - INFO - 统一调度器初始化成功 (Worker: 24800)
2025-08-06 08:44:12,001 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 24800)
2025-08-06 08:44:12,002 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-06 08:44:12,002 - game_server - INFO - 日志清理管理器已启动 (Worker: 24800)
2025-08-06 08:44:12,003 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-06 08:44:12,003 - game_server - INFO - Monster cooldown manager initialized (Worker: 24800)
2025-08-06 08:44:12,348 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-06 08:44:12,432 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-06 08:44:12,433 - game_server - INFO - 公会系统初始化成功 (Worker: 24800)
2025-08-06 08:44:12,433 - game_server - INFO - 邮件系统初始化成功 (Worker: 24800)
2025-08-06 08:44:12,456 - game_server - INFO - 商店系统初始化成功 (Worker: 24800)
2025-08-06 08:44:12,457 - game_server - INFO - 初始化完成 (Worker: 24800)
2025-08-06 08:44:30,885 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:44:38,073 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:45:00,072 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:45:08,074 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:45:11,740 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:45:11,741 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:45:13,495 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:45:13,495 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:45:30,223 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:45:38,075 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:46:00,403 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:46:08,076 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:46:11,757 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:46:11,757 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:46:12,536 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:46:12,537 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:46:30,608 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:46:38,091 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:47:00,808 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:47:08,096 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:47:11,760 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:47:11,761 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:47:12,560 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:47:12,560 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:47:30,977 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:47:38,109 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:48:00,244 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:48:08,112 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:48:11,776 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:48:11,777 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:48:12,518 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:48:12,518 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:48:30,430 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:48:38,128 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:49:00,627 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:49:08,130 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:49:11,779 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:49:11,780 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:49:12,575 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:49:12,575 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:49:30,781 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:49:38,131 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:50:06,684 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:50:08,145 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:50:11,781 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:50:11,782 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:50:12,512 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:50:12,512 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:50:30,981 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:50:38,148 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:51:00,183 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:51:08,160 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:51:11,795 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:51:11,795 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:51:12,545 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:51:12,545 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:51:30,400 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:51:38,165 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:52:00,618 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:52:08,179 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:52:11,798 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:52:11,799 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:52:12,523 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:52:12,523 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:52:30,833 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:52:38,181 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:53:00,017 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:53:08,202 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:53:11,803 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:53:11,803 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:53:12,530 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:53:12,530 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:53:30,266 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:53:38,217 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:54:00,487 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:54:08,219 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:54:11,804 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:54:11,805 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:54:12,540 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:54:12,540 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:54:30,721 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:54:38,237 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:55:00,920 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:55:08,254 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:55:11,819 - ConnectionManager - INFO - Redis连接池状态 (Worker 24800): 使用中=2, 可用=1, 总计=3
2025-08-06 08:55:11,819 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24800): 连接中
2025-08-06 08:55:12,670 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:55:12,672 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:55:30,106 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
2025-08-06 08:55:38,257 - ConnectionManager - INFO - 连接状态 (Worker 24800): 活跃连接数=0, 用户数=0
