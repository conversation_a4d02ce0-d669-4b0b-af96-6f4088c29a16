"""
货币服务 - 商店系统依赖
"""

import logging
from typing import Dict, Optional
from ItemCacheManager import ItemCacheManager, Item
from mongodb_manager import MongoDBManager

logger = logging.getLogger(__name__)


class CurrencyService:
    """货币服务 - 基于道具系统实现"""

    def __init__(self):
        self.item_cache_manager = None

        # 货币类型映射到道具defid
        self.currency_item_mapping = {
            "gold": 82001,      # 金币道具defid
            "diamond": 81999,   # 钻石道具defid
            "arena_coin": 1003,    # 竞技场币道具defid
            "guild_coin": 1004,    # 公会币道具defid
            "honor_point": 1005,   # 荣誉点道具defid
            "exp": 1006        # 经验点道具defid
        }

    async def _get_item_cache_manager(self):
        """获取道具缓存管理器"""
        if self.item_cache_manager is None:
            self.item_cache_manager = await ItemCacheManager.get_instance()
        return self.item_cache_manager
    
    async def check_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
        """检查玩家货币是否足够"""
        try:
            # 获取对应的道具defid
            defid = self.currency_item_mapping.get(currency_type)
            if not defid:
                logger.error(f"未知的货币类型: {currency_type}")
                return False

            # 获取玩家该道具的数量
            current_amount = await self.get_currency_amount(player_id, currency_type)

            return current_amount >= amount

        except Exception as e:
            logger.error(f"检查货币失败: {str(e)}")
            return False
    
    async def get_currency_amount(self, player_id: str, currency_type: str) -> int:
        """获取玩家货币数量"""
        try:
            # 获取对应的道具defid
            defid = self.currency_item_mapping.get(currency_type)
            if not defid:
                logger.error(f"未知的货币类型: {currency_type}")
                return 0

            # 获取数据库连接
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()

            # 查找对应的货币道具
            item = await db.items.find_one({
                "owner": player_id,
                "defid": defid,
                "type": "item"
            })

            if item:
                return item.get("quantity", 0)

            return 0

        except Exception as e:
            logger.error(f"获取货币数量失败: {str(e)}")
            return 0
    
    async def deduct_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
        """扣除玩家货币"""
        try:
            # 获取对应的道具defid
            defid = self.currency_item_mapping.get(currency_type)
            if not defid:
                logger.error(f"未知的货币类型: {currency_type}")
                return False

            # 检查货币是否足够
            if not await self.check_currency(player_id, currency_type, amount):
                logger.warning(f"玩家 {player_id} 的 {currency_type} 不足，需要 {amount}")
                return False

            # 获取数据库连接
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()
            redis_manager = await db_manager.get_redis_manager()
            redis_client = await redis_manager.get_redis()

            # 使用Item类的modify_quantity方法扣除货币
            item, success = await Item.modify_quantity(
                player_id, defid, amount, db, redis_client, increase=False
            )

            if success:
                logger.info(f"扣除货币成功: {player_id} -{amount} {currency_type}")
            else:
                logger.error(f"扣除货币失败: {player_id} -{amount} {currency_type}")

            return success

        except Exception as e:
            logger.error(f"扣除货币失败: {str(e)}")
            return False
    
    async def add_currency(self, player_id: str, currency_type: str, amount: int) -> bool:
        """增加玩家货币（用于回滚）"""
        try:
            # 获取对应的道具defid
            defid = self.currency_item_mapping.get(currency_type)
            if not defid:
                logger.error(f"未知的货币类型: {currency_type}")
                return False

            # 获取数据库连接
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()
            redis_manager = await db_manager.get_redis_manager()
            redis_client = await redis_manager.get_redis()

            # 使用Item类的modify_quantity方法增加货币
            item, success = await Item.modify_quantity(
                player_id, defid, amount, db, redis_client, increase=True
            )

            if success:
                logger.info(f"增加货币成功: {player_id} +{amount} {currency_type}")
            else:
                logger.error(f"增加货币失败: {player_id} +{amount} {currency_type}")

            return success

        except Exception as e:
            logger.error(f"增加货币失败: {str(e)}")
            return False
    
    async def get_all_currencies(self, player_id: str) -> Dict[str, int]:
        """获取玩家所有货币"""
        try:
            currencies = {}
            
            # 获取玩家道具列表
            items = await self.item_service.get_player_items(player_id)
            
            # 遍历所有货币类型
            for currency_type, item_id in self.currency_item_mapping.items():
                amount = 0
                
                # 查找对应的货币道具
                for item in items:
                    if item.get("item_id") == item_id:
                        amount = item.get("quantity", 0)
                        break
                
                currencies[currency_type] = amount
            
            return currencies
            
        except Exception as e:
            logger.error(f"获取所有货币失败: {str(e)}")
            return {}
    
    async def transfer_currency(self, from_player: str, to_player: str, 
                              currency_type: str, amount: int) -> bool:
        """转移货币（玩家间转账）"""
        try:
            # 检查发送方货币是否足够
            if not await self.check_currency(from_player, currency_type, amount):
                logger.warning(f"转账失败：{from_player} 的 {currency_type} 不足")
                return False
            
            # 扣除发送方货币
            deduct_success = await self.deduct_currency(from_player, currency_type, amount)
            if not deduct_success:
                return False
            
            # 增加接收方货币
            add_success = await self.add_currency(to_player, currency_type, amount)
            if not add_success:
                # 回滚：退还发送方货币
                await self.add_currency(from_player, currency_type, amount)
                logger.error(f"转账失败：无法给 {to_player} 增加货币，已回滚")
                return False
            
            logger.info(f"货币转账成功: {from_player} -> {to_player}, {amount} {currency_type}")
            return True
            
        except Exception as e:
            logger.error(f"转移货币失败: {str(e)}")
            return False
    
    def get_currency_display_name(self, currency_type: str) -> str:
        """获取货币显示名称"""
        display_names = {
            "gold": "金币",
            "diamond": "钻石",
            "arena_coin": "竞技场币",
            "guild_coin": "公会币",
            "honor_point": "荣誉点",
            "exp": "经验点"
        }
        return display_names.get(currency_type, currency_type)
    
    def is_valid_currency_type(self, currency_type: str) -> bool:
        """检查是否是有效的货币类型"""
        return currency_type in self.currency_item_mapping
    
    async def get_currency_exchange_rate(self, from_currency: str, to_currency: str) -> Optional[float]:
        """获取货币兑换汇率（如果支持兑换）"""
        # 这里可以实现货币兑换逻辑
        # 例如：1钻石 = 100金币
        exchange_rates = {
            ("diamond", "gold"): 100.0,
            ("gold", "diamond"): 0.01,
        }
        
        return exchange_rates.get((from_currency, to_currency))
    
    async def exchange_currency(self, player_id: str, from_currency: str, 
                              to_currency: str, amount: int) -> bool:
        """货币兑换"""
        try:
            # 获取兑换汇率
            rate = await self.get_currency_exchange_rate(from_currency, to_currency)
            if rate is None:
                logger.error(f"不支持的货币兑换: {from_currency} -> {to_currency}")
                return False
            
            # 计算兑换后的数量
            exchanged_amount = int(amount * rate)
            if exchanged_amount <= 0:
                logger.error(f"兑换数量无效: {amount} * {rate} = {exchanged_amount}")
                return False
            
            # 检查原货币是否足够
            if not await self.check_currency(player_id, from_currency, amount):
                logger.warning(f"兑换失败：{from_currency} 不足")
                return False
            
            # 扣除原货币
            deduct_success = await self.deduct_currency(player_id, from_currency, amount)
            if not deduct_success:
                return False
            
            # 增加目标货币
            add_success = await self.add_currency(player_id, to_currency, exchanged_amount)
            if not add_success:
                # 回滚
                await self.add_currency(player_id, from_currency, amount)
                logger.error(f"兑换失败：无法增加 {to_currency}，已回滚")
                return False
            
            logger.info(f"货币兑换成功: {player_id}, {amount} {from_currency} -> {exchanged_amount} {to_currency}")
            return True
            
        except Exception as e:
            logger.error(f"货币兑换失败: {str(e)}")
            return False
