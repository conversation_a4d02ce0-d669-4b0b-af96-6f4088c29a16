﻿-- [Comment]
-- jnmo 
cardsPackData = class("cardsPackData")
cardsPackData.__index = cardsPackData
m_cardsPackData = nil
savename_cardsPackData = "savedata_cardsPackData"
function cardsPackData:ctor(defid)
    print("cardsPackData:ctor()")
    self.defid = defid
    self.cardgid = me.guid()
    self.appearTime = me.sysTime() / 1000
    -- 还有多久免费抽取
    self.freeTime = me.sysTime() / 1000
    -- 限制次数还剩多少
    self.limit_time = self:getDef().limit_time
    -- 当前次数是否半价
    self.bHalf = false
    -- 公开保底次数
    self.pulic_luck = nil
    self.pulic_luck_stars = { }
    -- 保底次数出五星
    self.LuckNum = nil
    -- 已经加入的卡包id
    self.joinsCardPackIds = { }
    -- 卡包星级期望
    self.cardStarExpect = { }
    -- 临时卡组
    self.cardgroups = { }
    --累计奖励次数
    self.gift_time = ec_int(0)
    self:resetLuckNum()
    self:initPublicLuckNum()
    --初始免费次数
    self.free_times = 0
    self:initCardGroups()
end
-- 判断是否是已经加入的卡包
function cardsPackData:isJoinedPack(cfg_id)
    for k, v in pairs(self.joinsCardPackIds) do
        if pack_id == v then
            return true
        end
    end
    return false
end
function cardsPackData:initCardGroups()
    self.cardgroups = { }
    for key, packdata in pairs(cfg[CfgType.CARD_EXPECT]) do
        if self:isJoinedPack(packdata.pack_id) or self:getDef().cfg_id == packdata.pack_id then
            local roledata = cfg[CfgType.CAREER][packdata.role_id]
            if self.cardgroups[roledata.star] == nil then
                self.cardgroups[roledata.star] = { }
            end
            -- 判断是否有条件进入
            if (packdata.conditions <= ec_int(user.vipExp) or CARD_LOCK == 1) then
                if self.cardgroups[roledata.star][packdata.role_id] == nil then
                    self.cardgroups[roledata.star][packdata.role_id] = packdata
                end
            end
        end
    end
    return self.cardgroups
end

-- 加入卡包到主卡包
function cardsPackData:joinToMainCardPack(defid)
    for key, var in pairs(self.joinsCardPackIds) do
        if var == defid then
            return
        end
    end
    table.insert(self.joinsCardPackIds, defid)
end
function cardsPackData:initPublicLuckNum()
    self.pulic_luck_stars = { }
    if self:getDef().pulic_luck then
        local tmp = me.split(self:getDef().pulic_luck, "|")
        if tmp then
            for key, var in pairs(me.split(tmp[2], ",")) do
                table.insert(self.pulic_luck_stars, tonumber(var))
            end
            if  (self.pulic_luck == nil or  self.pulic_luck  == ec_int(0))  then
                self.pulic_luck = ec_int(tonumber(tmp[1]))
            end
        end
    end
    self.cardStarExpect = { }
    if self:getDef().expact then
        local tmp = me.split(self:getDef().expact, ",")
        if tmp then
            for key, var in pairs(tmp) do
                local exps = me.split(var, ":")
                table.insert(self.cardStarExpect, exps)
            end
        end
    end
end
-- 获取随机抽取的星级
function cardsPackData:getRandStar()
    local expectSum = 0
    for key, var in pairs(self.cardStarExpect) do
        expectSum = expectSum + tonumber(var[2])
    end
    if expectSum > 0 then
        local r = me.getRandom(expectSum)
        local idx = 0
        for key, var in pairs(self.cardStarExpect) do
            idx = idx + tonumber(var[2])
            if r <= idx then
                return tonumber(var[1])
            end
        end
    end
    return nil
end
function cardsPackData.initDefaultCardPack(defid)
    local bhave = false
    for var = #user.cardsPacks, 1, -1 do
        if user.cardsPacks[var]:getDef().id == defid then
            bhave = true
        end
    end
    if bhave == false then
        cardsPackData.addCardsPackData(defid)
        cardsPackData.saveUserCardsPackDatas()
    end
end
function cardsPackData:resetLuckNum()
    if  self:getDef().guard then
        self.LuckNum = tonumber(self:getDef().guard)
    end
end
function cardsPackData:getDef()
    return cfg[CfgType.CARD_PACK][self.defid]
end
-- 当前是否可以免费招募 等于0 可以 大于0 是剩余多久 -1 是不能免费招募
function cardsPackData:ifree()
    --有免费次数
    if self.free_times > 0 then
        return 0
    end
    if self:getDef().free_time ~= -1  then
        return math.max(self:getDef().free_time - me.sysTime() / 1000 + self.freeTime, 0)
    else
        return -1
    end
end
-- 获取持续时间
function cardsPackData:iLastTime()
    if self:getDef().last_time ~= -1 then
        return math.max(self:getDef().last_time - me.sysTime() / 1000 + self.appearTime, 0)
    else
        return -1
    end
end
-- 获取卡包剩余抽卡次数
function cardsPackData:haveLimitNum()
    return self.limit_time
end
-- 获取配置卡包中 多星 兵种武将
function cardsPackData:fixcardsByStars(xstars)
    local pack = { }

    for key, var in pairs(xstars) do
        for k, packdata in pairs(self.cardgroups[tonumber(var)]) do
            if (packdata.conditions <= ec_int(user.vipExp) or CARD_LOCK == 1) then
                if cfg[CfgType.CAREER][packdata.role_id].star == tonumber(var) then
                    table.insert(pack, packdata)
                end
            end
        end
    end


    return pack
end
-- 是否达到限制 
function cardsPackData:bLimit()
    if self.limit_time and self.limit_time == 0 then
        return true
    end
    return false
end
-- 抽卡
function cardsPackData:getcard(packs)
    local expectSum = 0
    for key, var in pairs(packs) do
        expectSum = expectSum + var.expect
    end
    if expectSum > 0 then
        local r = me.getRandom(expectSum)
        local idx = 0
        for key, var in pairs(packs) do
            idx = idx + var.expect
            if r <= idx then
                return var
            end
        end
    end
    return nil
end
function cardsPackData:checkGift_time()
    if  self:showGift() then
        if ec_int(self.gift_time) < self:getDef().gift_time then
            self.gift_time = ec_add( self.gift_time , 1)
        end
    end
end
function cardsPackData:checkResetGiftTime(roleid)
    if  self:showGift() and self:getDef().reset_id then        
        if  roleid == self:getDef().reset_id and  ec_int(self.gift_time) < self:getDef().gift_time  then
            self.gift_time = ec_int(0)
            gameLogic:getInstance():saveGame()
        end
    end
end
function cardsPackData:getGift()
   if cfg[CfgType.VIP][user.vipLevel].step>=4 then
       if  self:showGift() and  ec_int(self.gift_time) >= self:getDef().gift_time  then
             self.gift_time = ec_int(0)
             addGroupGift("4:".. self:getDef().gift .."|1")
       end
   else 
       showTips("VIP5才可以领取该奖励")
   end
end
function cardsPackData:showGiftTimeText()
   return ec_int(self.gift_time).."/" .. self:getDef().gift_time
end
function cardsPackData:showGiftTimePercent()
   return ec_int(self.gift_time)*100/self:getDef().gift_time
end
function cardsPackData:showGiftBtn()
    return ec_int(self.gift_time)>= self:getDef().gift_time
end
function cardsPackData:showGift()
    
    if self:getDef().gift then
        local id = cfg[CfgType.ITEM][self:getDef().gift].useVar
        if self:getDef().reset_id  then
            if  id == self:getDef().reset_id then
                return self:getDef().gift_time ~= nil  and self:getDef().gift_time > 0 and self:getDef().gift ~= nil
            end
        else
            return self:getDef().gift_time ~= nil  and self:getDef().gift_time > 0 and self:getDef().gift ~= nil
        end
    end
    return false
end

function cardsPackData:dodrawcard(cLuck)
    if self.limit_time and self.limit_time == 0 then
        showTips("招募次数已用完")
        return nil
    end
    self:checkGift_time()
    if self.LuckNum and self.LuckNum == 0 and cLuck then
        print("隐秘保底，必出五星")
        local packs = self:fixcardsByStars( { 5 })
        local expectdata = self:getcard(packs)
        if expectdata then
            self:resetLuckNum()
            if self.limit_time and self.limit_time > 0 then
                self.limit_time = self.limit_time - 1
            end
            self:checkResetGiftTime(expectdata.role_id)
            return expectdata.role_id
        else
            self:dodrawcard(false)
        end
    else
        if self.LuckNum and self.LuckNum > 0 then
            print("隐秘保底 = " .. self.LuckNum)
            self.LuckNum = self.LuckNum - 1
        end
        -- 公开保底多少次后必出几星和几星
        if self.pulic_luck then
            if ec_int(self.pulic_luck) == 1 then
                print("保底必出4,5星")
                local packs = self:fixcardsByStars(self.pulic_luck_stars)
                self.pulic_luck = ec_red(self.pulic_luck, 1)
                local expectdata = self:getcard(packs)
                if expectdata then
                    self:initPublicLuckNum()
                    if cfg[CfgType.CAREER][tonumber(expectdata.role_id)].star == 5 then
                        self:resetLuckNum()
                    end
                    if self.limit_time and self.limit_time > 0 then
                        self.limit_time = self.limit_time - 1
                    end
                    self:checkResetGiftTime(expectdata.role_id)
                    return expectdata.role_id
                end
            else
                print("正常抽取")
                self.pulic_luck = ec_red(self.pulic_luck, 1)
                local star = self:getRandStar()
                local packs = self:fixcardsByStars( { star })
                local expectdata = self:getcard(packs)
                if expectdata then
                    if cfg[CfgType.CAREER][tonumber(expectdata.role_id)].star == 5 then
                        self:resetLuckNum()
                    end
                    if self.limit_time and self.limit_time > 0 then
                        self.limit_time = self.limit_time - 1
                    end
                    self:checkResetGiftTime(expectdata.role_id)
                    return expectdata.role_id
                end
            end
        else
            print("没有保底，正常抽取")
            local star = self:getRandStar()
            local packs = self:fixcardsByStars( { star })
            local expectdata = self:getcard(packs)
            if expectdata then
                if cfg[CfgType.CAREER][tonumber(expectdata.role_id)].star == 5 then
                    self:resetLuckNum()
                end
                if self.limit_time and self.limit_time > 0 then
                    self.limit_time = self.limit_time - 1
                end
                self:checkResetGiftTime(expectdata.role_id)
                return expectdata.role_id
            end
        end
    end
end
-- 抽取卡片
function cardsPackData:drawCard()
    if self:ifree() ~= 0 then
        if self.bHalf == false then
            if self:bLimit() == false then
                --优先判断招募道具
                if self:getDef().sitem_id and self:getDef().ec_sitem_num and getItemNum(self:getDef().sitem_id) >= ec_int(self:getDef().ec_sitem_num) then
                    redItem(self:getDef().sitem_id, ec_int(self:getDef().ec_sitem_num))
                    me.dispatchCustomEvent(GAMELAYER_RESUI)
                    return { self:dodrawcard(true) }
                else
                    if getItemNum(self:getDef().item_id) >= ec_int(self:getDef().ec_item_num) then
                        redItem(self:getDef().item_id, ec_int(self:getDef().ec_item_num))
                        me.dispatchCustomEvent(GAMELAYER_RESUI)
                        return { self:dodrawcard(true) }
                    else
                        local idata = itemData:create(self:getDef().item_id)
                        showTips(idata:getDef().name .. "不足")
                    end                   
                end 
            else
                showTips("次数已用完")
            end
        else
            if self:bLimit() == false then
                if getItemNum(self:getDef().item_id) >= ec_int(self:getDef().ec_item_num) / 2 then
                    redItem(self:getDef().item_id, ec_int(self:getDef().ec_item_num) / 2)
                    self.bHalf = false
                    me.dispatchCustomEvent(GAMELAYER_RESUI)
                    return { self:dodrawcard(true) }
                else
                    local idata = itemData:create(self:getDef().item_id)
                    showTips(idata:getDef().name .. "不足")
                end
            else
                showTips("次数已用完")
            end
        end
    else
        if self.free_times > 0 then
            self.free_times = self.free_times - 1
            return { self:dodrawcard(true) }
        else        
            if self:bLimit() == false then
                self.bHalf = true
                self.freeTime = me.sysTime() / 1000
                bombBridge.upLog("free", 1, 1)
                user.freeCost = user.freeCost + 1
                return { self:dodrawcard(true) }
            else
                showTips("次数已用完")
            end
        end
    end
    return nil
end
-- 连续多次抽取卡片
function cardsPackData:drawCardMul()
    --优先使用招募道具
    if self:bLimit() then
        showTips("招募次数已用完")
        return 
    end
    if self:getDef().bmany and self.limit_time and self.limit_time ~= -1 and  self.limit_time < self:getDef().bmany then
        showTips("招募次数不足")
        return 
    end
    if self:getDef().sitem_id and self:getDef().ec_sitem_num and getItemNum(self:getDef().sitem_id) >= ec_int(self:getDef().ec_sitem_num) * self:getDef().bmany  then
        redItem(self:getDef().sitem_id, ec_int(self:getDef().ec_sitem_num) * self:getDef().bmany )
        local ret = { }
        local num = self:getDef().bmany
        for var = 1, num do
            table.insert(ret, self:dodrawcard(true))
        end
        me.dispatchCustomEvent(GAMELAYER_RESUI)
        return ret
    else         
        if getItemNum(self:getDef().item_id) >= ec_int(self:getDef().ec_item_num) * self:getDef().bmany * 0.9 then
            redItem(self:getDef().item_id, ec_int(self:getDef().ec_item_num) * self:getDef().bmany * 0.9)
            local ret = { }
            local num = self:getDef().bmany
            for var = 1, num do
                table.insert(ret, self:dodrawcard(true))
            end
            me.dispatchCustomEvent(GAMELAYER_RESUI)
            return ret
        else
            showTips(cfg[CfgType.ITEM][self:getDef().item_id].name .. "不足")
        end
    end
    return nil
end
function cardsPackData:getSaveString()
    local msg = { }
    msg.defid = self.defid
    msg.cardgid = self.cardgid
    msg.appearTime = self.appearTime
    msg.freeTime = self.freeTime
    msg.limit_time = self.limit_time
    msg.bHalf = self.bHalf
    msg.LuckNum = self.LuckNum
    msg.pulic_luck = self.pulic_luck
    msg.pulic_luck_stars = self.pulic_luck_stars
    msg.joinsCardPackIds = self.joinsCardPackIds or { }
    msg.free_times = self.free_times or 0 
    msg.gift_time = self.gift_time or ec_int(0)
    return me.cjson.encode(msg)
end
function cardsPackData:loadSaveString(str)
    local msg = me.cjson.decode(str)
    self.defid = msg.defid
    self.cardgid = msg.cardgid
    self.appearTime = msg.appearTime
    self.freeTime = msg.freeTime
    self.limit_time = msg.limit_time
    self.bHalf = msg.bHalf
    self.LuckNum = msg.LuckNum
    self.pulic_luck = msg.pulic_luck
    self.pulic_luck_stars = msg.pulic_luck_stars
    self.joinsCardPackIds = msg.joinsCardPackIds or { }
    self.free_times = msg.free_times or 0
    self.gift_time = msg.gift_time or ec_int(0)
    if ( self.LuckNum ==nil or self.LuckNum == 0) and  self:getDef().guard then
        self.LuckNum = tonumber(self:getDef().guard)
    end
    self:initPublicLuckNum()
    self:initCardGroups()
end
function savecardsPackData(str)
    -- str = me.Helper:base64EncodeLua(str)
    me.SaveFile(bombBridge.getSavePath() .. user.dataId .. savename_cardsPackData, str)
end
function loadcardsPackData(send,call,errorcall)
    local path = bombBridge.getSavePath() .. user.dataId .. savename_cardsPackData
    local str = nil
    if cc.FileUtils:getInstance():isFileExist(path) then
        str = cc.FileUtils:getInstance():getStringFromFile(path)
        if send then       
            me.bmobFile(savename_cardsPackData,call or function (args)end,errorcall or function (args)end,str) 
        end
        str = me.Helper:es_decode(str)
    end
    return str
end
--每日重置限制次数
function cardsPackData.checkDayResetPack()
    for var = 1, #user.cardsPacks do
       local cdata  = user.cardsPacks[var]
       if cdata:getDef().reset_day and cdata:getDef().reset_day == 1 then
            cdata.limit_time = cdata:getDef().limit_time
       end
    end
end
-- 获取主卡包
function cardsPackData.getMainCardPack()
    for var = #user.cardsPacks, 1, -1 do
        if user.cardsPacks[var]:getDef().kindtype == "main" then
            return user.cardsPacks[var]
        end
    end
    return nil
end
-- 去掉过期的卡包
function cardsPackData.checkPackActive()
    --活动卡包加入
    if user.timePackData then
         if user.timePackData:isActive() then
             local pro = user.timePackData.params
             local bhave = false
             for var = 1, #user.cardsPacks do
                 local cdata  = user.cardsPacks[var]
                 if cdata:getDef().id == tonumber(pro) then
                      bhave = true
                 end
             end
             if bhave == false then
                    if tonumber( user.timePackData.st) ~= -1 and tonumber(pro) > 0 then
                        cardsPackData.addCardsPackDataNoAni(tonumber(pro),nil,user.timePackData.st)
                    end
             end
         end
    end
    for var = #user.cardsPacks, 1, -1 do
        --不重置的卡包去掉 
        if user.cardsPacks[var]:iLastTime() == 0 or ( user.cardsPacks[var]:getDef(). reset_day == 0 and  user.cardsPacks[var]:haveLimitNum() == 0) then
            if user.cardsPacks[var]:getDef().kindtype == "join" then
                cardsPackData.getMainCardPack():joinToMainCardPack(user.cardsPacks[var]:getDef().cfg_id)
            end
            table.remove(user.cardsPacks, var)
        end
    end
    cardsPackData.saveUserCardsPackDatas()
end

function cardsPackData.saveUserCardsPackDatas()
    local msg = { }
    for key, var in pairs(user.cardsPacks) do
        msg[key] = var:getSaveString()
    end
    local str = me.cjson.encode(msg)
    savecardsPackData(str)
    return str
end

function cardsPackData.loadUserCardsPackDatas(str)
    user.cardsPacks = { }
    if str == nil then
        str = loadcardsPackData()
    end
    if str ~= nil then
        local cardsPacks = me.cjson.decode(str)
        if cardsPacks then
            for key, var in pairs(cardsPacks) do
                local s = cardsPackData.new(1)
                s:loadSaveString(var)
                table.insert(user.cardsPacks, s)
            end
        end
    end
end
-- 加入新卡包 公共保底次数,初始免费次数
function cardsPackData.addCardsPackData(defid, p,freetimes)
    for key, var in pairs(user.cardsPacks) do
        if var:getDef().id == defid then
            return
        end
    end
    local s = cardsPackData.new(defid)
    if s.pulic_luck and p then
        s.pulic_luck = ec_int(p)
    end
    if s:getDef().free_times and s:getDef().free_times > 0  then
         s.free_times = s:getDef().free_times
    end
    if freetimes then
         s.free_times = freetimes
    end
    table.insert(user.cardsPacks, s)
    local global = me.createNode("Node_CardPackage.csb")
    local citem = cardPackItem:create(global, "cardPackageItem")
    getItemAni(s, citem)
end
--设置某卡包的保底次数1
function cardsPackData.setCardPackDataPublshNum(defid)
    for key, var in pairs(user.cardsPacks) do
        if var:getDef().id == defid then
            var.LuckNum = 1
            break
        end
    end 
end
function cardsPackData.addCardsPackDataNoAni(defid, p,appear)
    for key, var in pairs(user.cardsPacks) do
        if var:getDef().id == defid then
            return
        end
    end
    local s = cardsPackData.new(defid)
    if s.pulic_luck and p then
        s.pulic_luck = ec_int(p)
    end
    if appear then
        s.appearTime = appear
    end
    if s:getDef().free_times and s:getDef().free_times > 0  then
         s.free_times = s:getDef().free_times
    end
    table.insert(user.cardsPacks,1,s)
end