import requests
import json
from urllib.parse import quote
import logging
import time
from typing import List, Dict, Optional
import asyncio

logger = logging.getLogger(__name__)

class RabbitMQCleaner:
    def __init__(self, host: str, port: int, username: str, password: str, vhost: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.vhost = vhost
        self.api_base_url = f"http://{host}:{port}/api"
        self.session = self._create_session()
        self.default_exchanges = {
            "", "amq.direct", "amq.fanout", "amq.headers",
            "amq.match", "amq.rabbitmq.log", "amq.rabbitmq.trace", "amq.topic"
        }

    def _create_session(self) -> requests.Session:
        """创建带认证的 HTTP 会话"""
        session = requests.Session()
        session.auth = (self.username, self.password)
        session.headers.update({"Content-Type": "application/json"})
        return session

    async def get_queue_stats(self) -> Dict:
        """获取队列统计信息"""
        try:
            url = f"{self.api_base_url}/queues/{quote(self.vhost, safe='')}"
            response = self.session.get(url)
            response.raise_for_status()
            queues = response.json()
            
            stats = {
                "total_queues": len(queues),
                "total_messages": 0,
                "idle_queues": 0,
                "active_queues": 0,
                "queues_by_messages": []
            }
            
            for queue in queues:
                messages = queue.get("messages", 0)
                stats["total_messages"] += messages
                if messages == 0:
                    stats["idle_queues"] += 1
                else:
                    stats["active_queues"] += 1
                    stats["queues_by_messages"].append({
                        "name": queue["name"],
                        "messages": messages,
                        "consumers": queue.get("consumers", 0)
                    })
            
            stats["queues_by_messages"].sort(key=lambda x: x["messages"], reverse=True)
            return stats
        except Exception as e:
            logger.error(f"获取队列统计信息失败: {str(e)}")
            raise

    async def clean_idle_queues(self, idle_time: int = 3600) -> List[str]:
        """清理指定时间内未使用的队列"""
        try:
            url = f"{self.api_base_url}/queues/{quote(self.vhost, safe='')}"
            response = self.session.get(url)
            response.raise_for_status()
            queues = response.json()
            
            cleaned_queues = []
            for queue in queues:
                # 检查队列是否空闲
                if (queue.get("messages", 0) == 0 and
                    queue.get("messages_unacknowledged", 0) == 0 and
                    queue.get("consumers", 0) == 0):
                    # 检查最后使用时间
                    idle_since = queue.get("idle_since_timestamp")
                    if idle_since and (time.time() - idle_since/1000) > idle_time:
                        await self.delete_queue(queue["name"])
                        cleaned_queues.append(queue["name"])
            
            return cleaned_queues
        except Exception as e:
            logger.error(f"清理空闲队列失败: {str(e)}")
            raise

    async def delete_queue(self, queue_name: str) -> bool:
        """删除指定队列"""
        try:
            url = f"{self.api_base_url}/queues/{quote(self.vhost, safe='')}/{quote(queue_name, safe='')}"
            response = self.session.delete(url)
            if response.status_code == 204:
                logger.info(f"已删除队列: {queue_name}")
                return True
            else:
                logger.warning(f"删除队列 {queue_name} 失败: {response.text}")
                return False
        except Exception as e:
            logger.error(f"删除队列时发生错误: {str(e)}")
            return False

    async def purge_queue(self, queue_name: str) -> bool:
        """清空指定队列的消息"""
        try:
            url = f"{self.api_base_url}/queues/{quote(self.vhost, safe='')}/{quote(queue_name, safe='')}/contents"
            response = self.session.delete(url)
            if response.status_code == 204:
                logger.info(f"已清空队列消息: {queue_name}")
                return True
            else:
                logger.warning(f"清空队列 {queue_name} 消息失败: {response.text}")
                return False
        except Exception as e:
            logger.error(f"清空队列消息时发生错误: {str(e)}")
            return False

    async def close_stale_connections(self, idle_time: int = 1800) -> List[str]:
        """关闭空闲连接"""
        try:
            url = f"{self.api_base_url}/connections"
            response = self.session.get(url)
            response.raise_for_status()
            connections = response.json()
            
            closed_connections = []
            for conn in connections:
                # 跳过管理界面连接
                if "http" in conn["client_properties"].get("product", "").lower():
                    continue
                
                # 检查连接空闲时间
                idle_since = conn.get("idle_since")
                if idle_since and (time.time() - idle_since/1000) > idle_time:
                    if await self.close_connection(conn["name"]):
                        closed_connections.append(conn["name"])
            
            return closed_connections
        except Exception as e:
            logger.error(f"关闭空闲连接失败: {str(e)}")
            raise

    async def close_connection(self, connection_name: str) -> bool:
        """关闭指定连接"""
        try:
            url = f"{self.api_base_url}/connections/{quote(connection_name, safe='')}"
            response = self.session.delete(url)
            if response.status_code == 204:
                logger.info(f"已关闭连接: {connection_name}")
                return True
            else:
                logger.warning(f"关闭连接 {connection_name} 失败: {response.text}")
                return False
        except Exception as e:
            logger.error(f"关闭连接时发生错误: {str(e)}")
            return False

    async def cleanup(self, idle_queue_time: int = 3600, idle_connection_time: int = 1800):
        """执行完整的清理流程"""
        try:
            # 1. 获取当前状态
            stats_before = await self.get_queue_stats()
            logger.debug(f"清理前状态: {json.dumps(stats_before, indent=2)}")

            # 2. 清理空闲队列
            cleaned_queues = await self.clean_idle_queues(idle_queue_time)
            if cleaned_queues:
                logger.debug(f"已清理 {len(cleaned_queues)} 个空闲队列")

            # 3. 关闭空闲连接
            closed_connections = await self.close_stale_connections(idle_connection_time)
            if closed_connections:
                logger.debug(f"已关闭 {len(closed_connections)} 个空闲连接")

            # 4. 获取清理后状态
            stats_after = await self.get_queue_stats()
            logger.debug(f"清理后状态: {json.dumps(stats_after, indent=2)}")

            return {
                "stats_before": stats_before,
                "stats_after": stats_after,
                "cleaned_queues": cleaned_queues,
                "closed_connections": closed_connections
            }
        except Exception as e:
            logger.error(f"清理过程中发生错误: {str(e)}")
            raise
        finally:
            self.session.close() 