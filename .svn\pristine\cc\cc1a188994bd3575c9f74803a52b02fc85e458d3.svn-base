"""
邮件系统数据库管理器
处理邮件相关的数据库操作
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from mongodb_manager import MongoDBManager
from mail_models import (
    Mail, MailAttachment, MailType, MailStatus, AttachmentStatus,
    MailListItem, MailConstants
)

logger = logging.getLogger(__name__)


class MailDatabaseManager:
    """邮件数据库管理器"""
    
    def __init__(self):
        self.mails_collection = "mails"
        self.attachments_collection = "mail_attachments"
    
    async def _get_db(self):
        """获取数据库连接"""
        db_manager = await MongoDBManager.get_instance()
        return await db_manager.get_db()
    
    def generate_mail_id(self) -> str:
        """生成邮件ID"""
        return f"mail_{uuid.uuid4().hex[:16]}"
    
    def generate_attachment_id(self) -> str:
        """生成附件ID"""
        return f"attach_{uuid.uuid4().hex[:16]}"
    
    # ==================== 邮件基础操作 ====================
    
    async def create_mail(self, mail: Mail) -> bool:
        """创建邮件"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            mail_data = mail.to_dict()
            result = await collection.insert_one(mail_data)
            
            if result.inserted_id:
                logger.info(f"邮件创建成功: {mail.mail_id}")
                return True
            else:
                logger.error(f"邮件创建失败: {mail.mail_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建邮件时发生错误: {str(e)}")
            return False
    
    async def get_mail_by_id(self, mail_id: str) -> Optional[Mail]:
        """根据ID获取邮件"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            mail_doc = await collection.find_one({"mail_id": mail_id})
            if mail_doc:
                return Mail.from_dict(mail_doc)
            return None
            
        except Exception as e:
            logger.error(f"获取邮件时发生错误: {str(e)}")
            return None
    
    async def get_player_mails(self, player_id: str, page: int = 1, limit: int = 20, 
                              include_deleted: bool = False) -> List[MailListItem]:
        """获取玩家邮件列表"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            # 构建查询条件
            query = {"receiver_id": player_id}
            if not include_deleted:
                query["status"] = {"$ne": MailStatus.DELETED}
            
            # 计算跳过数量
            skip = (page - 1) * limit
            
            # 查询邮件列表
            cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(limit)
            mails = []
            
            async for mail_doc in cursor:
                mail = Mail.from_dict(mail_doc)
                mails.append(MailListItem.from_mail(mail))
            
            return mails
            
        except Exception as e:
            logger.error(f"获取玩家邮件列表时发生错误: {str(e)}")
            return []
    
    async def get_unread_count(self, player_id: str) -> int:
        """获取未读邮件数量"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            count = await collection.count_documents({
                "receiver_id": player_id,
                "status": MailStatus.UNREAD
            })
            
            return count
            
        except Exception as e:
            logger.error(f"获取未读邮件数量时发生错误: {str(e)}")
            return 0
    
    async def update_mail_status(self, mail_id: str, status: MailStatus, 
                                read_at: Optional[datetime] = None) -> bool:
        """更新邮件状态"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            update_data = {"status": status}
            if read_at:
                update_data["read_at"] = read_at
            if status == MailStatus.DELETED:
                update_data["deleted_at"] = datetime.now()
            
            result = await collection.update_one(
                {"mail_id": mail_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新邮件状态时发生错误: {str(e)}")
            return False
    
    async def update_attachment_status(self, mail_id: str, attachment_status: AttachmentStatus) -> bool:
        """更新附件状态"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            result = await collection.update_one(
                {"mail_id": mail_id},
                {"$set": {"attachment_status": attachment_status}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新附件状态时发生错误: {str(e)}")
            return False
    
    async def delete_mails(self, mail_ids: List[str]) -> int:
        """批量删除邮件（软删除）"""
        try:
            db = await self._get_db()
            collection = db[self.mails_collection]
            
            result = await collection.update_many(
                {"mail_id": {"$in": mail_ids}},
                {"$set": {
                    "status": MailStatus.DELETED,
                    "deleted_at": datetime.now()
                }}
            )
            
            return result.modified_count
            
        except Exception as e:
            logger.error(f"批量删除邮件时发生错误: {str(e)}")
            return 0
    
    # ==================== 附件操作 ====================
    
    async def create_attachments(self, attachments: List[MailAttachment]) -> bool:
        """创建邮件附件"""
        try:
            if not attachments:
                return True
                
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            attachment_docs = [attachment.to_dict() for attachment in attachments]
            result = await collection.insert_many(attachment_docs)
            
            if len(result.inserted_ids) == len(attachments):
                logger.info(f"附件创建成功: {len(attachments)} 个")
                return True
            else:
                logger.error(f"附件创建失败: 期望 {len(attachments)} 个，实际 {len(result.inserted_ids)} 个")
                return False
                
        except Exception as e:
            logger.error(f"创建邮件附件时发生错误: {str(e)}")
            return False
    
    async def get_mail_attachments(self, mail_id: str) -> List[MailAttachment]:
        """获取邮件附件"""
        try:
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            cursor = collection.find({"mail_id": mail_id})
            attachments = []
            
            async for attachment_doc in cursor:
                attachments.append(MailAttachment.from_dict(attachment_doc))
            
            return attachments
            
        except Exception as e:
            logger.error(f"获取邮件附件时发生错误: {str(e)}")
            return []
    
    async def claim_attachments(self, mail_id: str) -> bool:
        """领取邮件附件"""
        try:
            db = await self._get_db()
            collection = db[self.attachments_collection]
            
            result = await collection.update_many(
                {"mail_id": mail_id, "claimed": False},
                {"$set": {
                    "claimed": True,
                    "claimed_at": datetime.now()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"领取邮件附件时发生错误: {str(e)}")
            return False
    
    # ==================== 系统管理 ====================
    
    async def cleanup_expired_mails(self) -> int:
        """清理过期邮件"""
        try:
            db = await self._get_db()
            mails_collection = db[self.mails_collection]
            attachments_collection = db[self.attachments_collection]
            
            now = datetime.now()
            
            # 查找过期邮件
            expired_mails = []
            cursor = mails_collection.find({
                "expire_at": {"$lt": now},
                "status": {"$ne": MailStatus.DELETED}
            })
            
            async for mail_doc in cursor:
                expired_mails.append(mail_doc["mail_id"])
            
            if not expired_mails:
                return 0
            
            # 删除过期邮件
            mail_result = await mails_collection.delete_many({
                "mail_id": {"$in": expired_mails}
            })
            
            # 删除相关附件
            attachment_result = await attachments_collection.delete_many({
                "mail_id": {"$in": expired_mails}
            })
            
            logger.info(f"清理过期邮件: {mail_result.deleted_count} 封邮件，{attachment_result.deleted_count} 个附件")
            return mail_result.deleted_count
            
        except Exception as e:
            logger.error(f"清理过期邮件时发生错误: {str(e)}")
            return 0
    
    async def get_all_player_ids(self) -> List[str]:
        """获取所有玩家ID（用于广播邮件）"""
        try:
            # 这里需要根据实际的用户表来获取
            # 暂时返回空列表，实际实现时需要查询用户表
            db = await self._get_db()
            # 假设用户表名为 users
            collection = db.get("users", db["user_data"])  # 根据实际情况调整
            
            cursor = collection.find({}, {"id": 1})
            player_ids = []
            
            async for user_doc in cursor:
                if user_doc.get("id"):
                    player_ids.append(user_doc["id"])
            
            return player_ids
            
        except Exception as e:
            logger.error(f"获取所有玩家ID时发生错误: {str(e)}")
            return []
