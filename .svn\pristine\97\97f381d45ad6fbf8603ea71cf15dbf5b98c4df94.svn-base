import logging
import traceback
import json
from fastapi import WebSocket, HTTPException
from typing import Dict, Any, Optional
from fastapi.responses import JSONResponse
from models import ResponseModel

# 使用基本的日志配置，避免循环导入
logger = logging.getLogger(__name__)

async def handle_error(websocket: Optional[WebSocket] = None, 
                      message: str = "An error occurred", 
                      code: int = 500, 
                      exception: Optional[Exception] = None) -> Dict[str, Any]:
    """处理错误并返回标准错误响应"""
    error_data = {
        "success": False,
        "code": code,
        "message": message
    }
    
    if exception:
        logger.error(f"Error: {message}, Exception: {str(exception)}")
        logger.error(traceback.format_exc())
    else:
        logger.error(f"Error: {message}")
    
    if websocket:
        try:
            await websocket.send_json({
                "type": "error",
                "data": error_data
            })
        except Exception as e:
            logger.error(f"Failed to send error to websocket: {str(e)}")
    
    return error_data
def format_log_message(message: str, username: Optional[str] = None, 
                      worker_id: Optional[str] = None) -> str:
    """格式化日志消息"""
    prefix = []
    if username:
        prefix.append(f"User: {username}")
    if worker_id:
        prefix.append(f"Worker: {worker_id}")
    
    if prefix:
        return f"[{' | '.join(prefix)}] {message}"
    return message

class JSONEncoder(json.JSONEncoder):
    """扩展的JSON编码器，支持更多类型"""
    def default(self, obj):
        from datetime import datetime, date
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def to_json(data: Any) -> str:
    """将数据转换为JSON字符串"""
    return json.dumps(data, cls=JSONEncoder)

def from_json(json_str: str) -> Any:
    """将JSON字符串转换为数据"""
    return json.loads(json_str)

class HTTPError(HTTPException):
    """HTTP错误类"""
    def __init__(self, status_code: int = 500, detail: str = "Internal Server Error"):
        super().__init__(status_code=status_code, detail=detail)

async def handle_error_json(
    msg: str = "服务器错误",
    code: int = 500,
    log_level: str = "error",
    exception: Optional[Exception] = None
) -> JSONResponse:
    """统一处理错误，返回 JSONResponse，使用真实的 HTTP 状态码"""
    log_message = f"{msg}{f': {str(exception)}' if exception else ''}"
    if exception:
        logger.debug(f"堆栈: {traceback.format_exc()}")
    getattr(logger, log_level)(log_message)
    return JSONResponse(
        status_code=200,
        content=ResponseModel(
            success=False,
            code=code,
            message=log_message
        ).model_dump(),
        headers={"Content-Type": "application/json"}
    )