"""
商店系统HTTP API测试
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class ShopAPITester:
    """商店API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.player_id = "test_player"
    
    async def test_get_shop_list(self, session):
        """测试获取商店列表"""
        print("\n=== 测试获取商店列表 (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/list"
            params = {"player_id": self.player_id}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["success"]:
                        shops = data["data"]["shops"]
                        print(f"✅ 获取商店列表成功，共 {len(shops)} 个商店")
                        for shop in shops:
                            print(f"  - {shop['shop_name']} ({shop['shop_type']})")
                        return shops
                    else:
                        print(f"❌ API返回失败: {data}")
                        return []
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return []
    
    async def test_get_shop_items(self, session, shop_id: str):
        """测试获取商店商品"""
        print(f"\n=== 测试获取商店商品: {shop_id} (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/{shop_id}/items"
            params = {"player_id": self.player_id}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["success"]:
                        items = data["data"]["items"]
                        print(f"✅ 获取商店商品成功，共 {len(items)} 个商品")
                        for item in items:
                            print(f"  - {item['item_template_id']} x{item['item_quantity']}")
                            print(f"    价格: {item['final_price']} {item['currency_type']}")
                            print(f"    限购剩余: {item['limit_remaining']}")
                        return items
                    else:
                        print(f"❌ API返回失败: {data}")
                        return []
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return []
    
    async def test_get_item_detail(self, session, config_id: str):
        """测试获取商品详情"""
        print(f"\n=== 测试获取商品详情: {config_id} (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/item/{config_id}/detail"
            params = {"player_id": self.player_id}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["success"]:
                        item_data = data["data"]
                        print(f"✅ 获取商品详情成功")
                        print(f"  商品: {item_data['item_template_id']} x{item_data['item_quantity']}")
                        print(f"  原价: {item_data['price_info']['original_price']} {item_data['price_info']['currency_type']}")
                        print(f"  现价: {item_data['price_info']['final_price']} {item_data['price_info']['currency_type']}")
                        print(f"  可购买: {item_data['can_purchase']}")
                        return item_data
                    else:
                        print(f"❌ API返回失败: {data}")
                        return None
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return None
    
    async def test_preview_purchase(self, session, config_id: str, quantity: int = 1):
        """测试预览购买"""
        print(f"\n=== 测试预览购买: {config_id} x{quantity} (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/purchase/preview"
            params = {"player_id": self.player_id}
            data = {
                "config_id": config_id,
                "quantity": quantity
            }
            
            async with session.post(url, params=params, json=data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    if response_data["success"]:
                        preview = response_data["data"]
                        print(f"✅ 预览购买成功")
                        print(f"  可购买: {preview['can_purchase']}")
                        if preview["can_purchase"]:
                            print(f"  总价: {preview['final_price']} {preview['currency_type']}")
                            print(f"  折扣: {preview['discount_amount']}")
                        else:
                            print(f"  错误: {preview.get('error', '未知错误')}")
                        return preview
                    else:
                        print(f"❌ API返回失败: {response_data}")
                        return None
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return None
    
    async def test_purchase_item(self, session, config_id: str, quantity: int = 1):
        """测试购买商品"""
        print(f"\n=== 测试购买商品: {config_id} x{quantity} (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/purchase"
            params = {"player_id": self.player_id}
            data = {
                "config_id": config_id,
                "quantity": quantity,
                "metadata": {"test": True}
            }
            
            async with session.post(url, params=params, json=data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    if response_data["success"]:
                        purchase_data = response_data["data"]
                        print(f"✅ 购买成功")
                        print(f"  购买ID: {purchase_data['purchase_id']}")
                        print(f"  获得道具: {purchase_data['items']}")
                        return purchase_data
                    else:
                        print(f"❌ 购买失败: {response_data['error']}")
                        return None
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return None
    
    async def test_get_purchase_history(self, session):
        """测试获取购买历史"""
        print(f"\n=== 测试获取购买历史 (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/purchase/history"
            params = {"player_id": self.player_id, "limit": 10, "offset": 0}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["success"]:
                        purchases = data["data"]["purchases"]
                        print(f"✅ 获取购买历史成功，共 {len(purchases)} 条记录")
                        for purchase in purchases:
                            print(f"  - {purchase['purchase_time']}: {purchase['item_template_id']} x{purchase['quantity']}")
                        return purchases
                    else:
                        print(f"❌ API返回失败: {data}")
                        return []
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return []
    
    async def test_get_purchase_limits(self, session):
        """测试获取限购状态"""
        print(f"\n=== 测试获取限购状态 (HTTP API) ===")
        
        try:
            url = f"{self.base_url}/api/shop/purchase/limits"
            params = {"player_id": self.player_id}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["success"]:
                        limits = data["data"]
                        print(f"✅ 获取限购状态成功")
                        for limit_type, status in limits.items():
                            if isinstance(status, dict) and status.get("counters"):
                                print(f"  {limit_type}: {len(status['counters'])} 个商品有限购")
                        return limits
                    else:
                        print(f"❌ API返回失败: {data}")
                        return {}
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return {}
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {}
    
    async def test_admin_create_shop(self, session):
        """测试创建商店（管理接口）"""
        print(f"\n=== 测试创建商店 (管理接口) ===")
        
        try:
            url = f"{self.base_url}/api/shop/admin/shop"
            data = {
                "shop_name": "测试商店API",
                "shop_type": "normal",
                "description": "通过API创建的测试商店",
                "icon": "test_shop_api.png",
                "is_active": True,
                "access_conditions": {"player_level": 1},
                "sort_order": 999,
                "ui_config": {"theme": "test"}
            }
            
            async with session.post(url, json=data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    if response_data["success"]:
                        print(f"✅ 创建商店成功")
                        return True
                    else:
                        print(f"❌ 创建商店失败: {response_data['message']}")
                        return False
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return False
    
    async def run_full_test(self):
        """运行完整的HTTP API测试"""
        print("🚀 开始商店系统HTTP API测试")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            try:
                # 1. 测试管理接口 - 创建商店
                await self.test_admin_create_shop(session)
                
                # 2. 测试获取商店列表
                shops = await self.test_get_shop_list(session)
                if not shops:
                    print("❌ 没有可用商店，测试终止")
                    return
                
                # 3. 测试获取第一个商店的商品
                first_shop = shops[0]
                items = await self.test_get_shop_items(session, first_shop["shop_id"])
                if not items:
                    print("❌ 商店没有商品，跳过后续测试")
                else:
                    # 4. 测试获取第一个商品的详情
                    first_item = items[0]
                    detail = await self.test_get_item_detail(session, first_item["config_id"])
                    
                    if detail and detail["can_purchase"]:
                        # 5. 测试预览购买
                        preview = await self.test_preview_purchase(session, first_item["config_id"], 1)
                        
                        if preview and preview["can_purchase"]:
                            # 6. 测试实际购买（注意：这会消耗游戏资源）
                            print("\n⚠️  即将进行实际购买测试，这会消耗游戏资源")
                            print("如果不想进行实际购买，请在5秒内按Ctrl+C终止")
                            try:
                                await asyncio.sleep(5)
                                await self.test_purchase_item(session, first_item["config_id"], 1)
                            except KeyboardInterrupt:
                                print("\n⏹️  用户取消了购买测试")
                
                # 7. 测试获取购买历史
                await self.test_get_purchase_history(session)
                
                # 8. 测试获取限购状态
                await self.test_get_purchase_limits(session)
                
                print("\n✅ 商店系统HTTP API测试完成")
                
            except Exception as e:
                print(f"\n❌ 测试过程中发生错误: {str(e)}")
                logger.error(f"测试失败: {str(e)}")


async def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试器
    tester = ShopAPITester()
    
    # 运行测试
    await tester.run_full_test()


if __name__ == "__main__":
    print("商店系统HTTP API测试")
    print("确保游戏服务器正在运行在 localhost:8000")
    print("按Enter开始测试...")
    input()
    
    asyncio.run(main())
