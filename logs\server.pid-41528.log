2025-08-05 21:04:27,848 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,555 - models - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,589 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,948 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,962 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,974 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:28,989 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:29,002 - auth - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,627 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,819 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,877 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,916 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,949 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:33,950 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: a269b359)
2025-08-05 21:04:33,984 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,061 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,090 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,112 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,132 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,311 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,333 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,352 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,377 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,397 - game_server - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:34,398 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 21:04:34,400 - msgManager - INFO - Monster handlers registered
2025-08-05 21:04:34,400 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 21:04:34,403 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 21:04:34,421 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 21:04:34,422 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 21:04:34,422 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 6e565732)
2025-08-05 21:04:34,523 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 21:04:34,609 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 21:04:34,610 - game_server - INFO - 模板引擎初始化成功
2025-08-05 21:04:34,613 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:04:34,615 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 41528)
2025-08-05 21:04:34,615 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 21:04:34,968 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:04:34,970 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 21:04:37,165 - ConnectionManager - INFO - 后台任务已启动 (Worker 41528)
2025-08-05 21:04:37,166 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 41528)
2025-08-05 21:04:37,178 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:04:37,179 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:04:37,179 - game_server - INFO - 游戏配置加载完成 (Worker: 41528)
2025-08-05 21:04:37,180 - ConnectionManager - INFO - 连接状态 (Worker 41528): 活跃连接数=0, 用户数=0
2025-08-05 21:04:42,217 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 21:04:42,219 - ConnectionManager - INFO - Redis连接池状态 (Worker 41528): 使用中=2, 可用=0, 总计=2
2025-08-05 21:04:42,219 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 41528): 2/2 (100.0%)
2025-08-05 21:04:42,220 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41528): 连接中
2025-08-05 21:04:42,365 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 6e565732)
2025-08-05 21:04:42,366 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 6e565732)
2025-08-05 21:04:42,379 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:42,381 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 21:04:42,383 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 21:04:42,391 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:04:42,392 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 21:04:42,392 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:04:42,393 - ConnectionManager - INFO - Worker 41528 开始消费广播消息，消费者标签: ctag1.3a7125a2f29d445a85a41e6cd92a7b21
2025-08-05 21:04:42,567 - ConnectionManager - INFO - Worker 41528 开始消费个人消息，消费者标签: ctag1.14396dce770a42c7af95a6552b3a50e2
2025-08-05 21:04:42,774 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 41528)
2025-08-05 21:04:42,967 - distributed_lock - INFO - Worker 41528 获取锁超时: scheduler_initialization
2025-08-05 21:04:42,968 - game_server_scheduler_integration - INFO - Worker 41528 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 21:04:42,969 - game_server - INFO - 统一调度器初始化成功 (Worker: 41528)
2025-08-05 21:04:42,977 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:42,978 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 21:04:42,978 - game_server - INFO - 日志清理管理器已启动 (Worker: 41528)
2025-08-05 21:04:42,979 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:04:42,980 - game_server - INFO - Monster cooldown manager initialized (Worker: 41528)
2025-08-05 21:04:43,865 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 21:04:44,003 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:04:44,010 - game_server - INFO - 公会系统初始化成功 (Worker: 41528)
2025-08-05 21:04:44,013 - game_server - INFO - 邮件系统初始化成功 (Worker: 41528)
2025-08-05 21:04:44,016 - game_server - INFO - 商店系统初始化成功 (Worker: 41528)
2025-08-05 21:04:44,018 - game_server - INFO - 初始化完成 (Worker: 41528)
2025-08-05 21:04:45,114 - game_server - INFO - WebSocket 连接尝试，token: eyJhbGciOi..., worker: 41528
2025-08-05 21:04:45,206 - ConnectionManager - INFO - 已发布用户登录通知到Redis (用户: dsadjdj23, Worker: 41528)
2025-08-05 21:04:45,268 - CacheInvalidationManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:45,320 - CacheInvalidationManager - INFO - 缓存失效通知管理器初始化完成 (Worker 41528)
2025-08-05 21:04:45,698 - game_manager - INFO - 开始初始化游戏管理器 (Worker: 41528)
2025-08-05 21:04:45,699 - game_manager - INFO - 创建PlayerSessionManager...
2025-08-05 21:04:45,700 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 21:04:45,701 - game_manager - INFO - 创建GameNotificationManager...
2025-08-05 21:04:45,702 - game_manager - INFO - 获取UserCacheManager...
2025-08-05 21:04:45,702 - game_manager - INFO - 获取ItemCacheManager...
2025-08-05 21:04:45,743 - game_manager - INFO - 获取GuildServiceDistributed...
2025-08-05 21:04:45,743 - game_manager - INFO - 获取ConnectionManager...
2025-08-05 21:04:45,744 - game_manager - INFO - 游戏管理器初始化完成 (Worker: 41528)
2025-08-05 21:04:45,744 - game_manager - INFO - 处理玩家登录: dsadjdj23 (Worker: 41528)
2025-08-05 21:04:45,997 - player_session_manager - INFO - 创建玩家会话: dsadjdj23 (Worker: 41528)
2025-08-05 21:04:46,252 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 1754353521.6秒)
2025-08-05 21:04:46,419 - guild_cache_manager - INFO - GuildCacheManager实例已创建
2025-08-05 21:04:47,147 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 21:04:47,191 - mail_database_manager - INFO - 数据库中共有 12 个未过期的邮件模板
2025-08-05 21:04:47,233 - mail_database_manager - INFO - 用户 dsadjdj23 已处理的模板ID: {'template_46f1fcab221b4a38', 'template_e827e75c8beb4d5d', 'template_7fd5d6e6b55c4142', 'template_0acd29605c994701', 'template_6e47793807b84b13', 'template_b0a095478f034b26', 'template_575fc5088c474399', 'template_0738ee4b4a2d4983', 'template_c466e0ddaa8c44f2', 'template_14981f8399fe4572', 'template_62e4594c1bf04b4b', 'template_c999a774d9014253'}
2025-08-05 21:04:47,276 - mail_database_manager - INFO - 模板 template_6e47793807b84b13 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,277 - mail_database_manager - INFO - 模板 template_c466e0ddaa8c44f2 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,277 - mail_database_manager - INFO - 模板 template_c999a774d9014253 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,278 - mail_database_manager - INFO - 模板 template_575fc5088c474399 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,279 - mail_database_manager - INFO - 模板 template_0acd29605c994701 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,279 - mail_database_manager - INFO - 模板 template_14981f8399fe4572 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,280 - mail_database_manager - INFO - 模板 template_46f1fcab221b4a38 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,280 - mail_database_manager - INFO - 模板 template_7fd5d6e6b55c4142 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,280 - mail_database_manager - INFO - 模板 template_0738ee4b4a2d4983 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,281 - mail_database_manager - INFO - 模板 template_62e4594c1bf04b4b 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,281 - mail_database_manager - INFO - 模板 template_e827e75c8beb4d5d 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,281 - mail_database_manager - INFO - 模板 template_b0a095478f034b26 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:04:47,282 - mail_database_manager - INFO - 用户 dsadjdj23 有 0 个未处理的邮件模板
2025-08-05 21:04:47,282 - mail_service_distributed - INFO - 用户 dsadjdj23 登录时发现 0 个未处理的邮件模板
2025-08-05 21:04:47,282 - mail_cache_manager - INFO - MailCacheManager实例已创建
2025-08-05 21:04:47,547 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 21:04:47,636 - game_manager - INFO - 玩家登录处理完成: dsadjdj23
2025-08-05 21:04:47,637 - ConnectionManager - INFO - 用户 dsadjdj23 连接成功 (Token: eyJhbGciOi, Worker: 41528)
2025-08-05 21:04:57,136 - MessageIdempotencyManager - INFO - 日志系统初始化成功 (进程 ID: 41528)
2025-08-05 21:04:57,137 - MessageIdempotencyManager - INFO - 消息幂等性管理器初始化完成
2025-08-05 21:05:00,406 - ConnectionManager - INFO - 连接状态 (Worker 41528): 活跃连接数=1, 用户数=1
2025-08-05 21:05:03,514 - distributed_lock - INFO - Worker 41528 成功获取锁: purchase:lock:dsadjdj23:shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:05:04,213 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:05:04,945 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:05:05,689 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:05:07,189 - ConnectionManager - INFO - 连接状态 (Worker 41528): 活跃连接数=1, 用户数=1
2025-08-05 21:05:07,359 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 41528, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODU0ODV9.PD8d5NgH38J3pXq-P2VKBBlxIQHN87CnvZKXFCQHT64', 'last_active': '2025-08-05T21:04:45.320438'}
2025-08-05 21:05:07,361 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:05:07,361 - currency_service - INFO - 扣除货币成功: dsadjdj23 -80 gold
2025-08-05 21:05:07,362 - shop_purchase_service - INFO - item_data = {item_data}
2025-08-05 21:05:08,433 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 41528, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODU0ODV9.PD8d5NgH38J3pXq-P2VKBBlxIQHN87CnvZKXFCQHT64', 'last_active': '2025-08-05T21:04:45.320438'}
2025-08-05 21:05:08,434 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: add, 资产类型: ItemType.ITEM
2025-08-05 21:05:08,435 - shop_purchase_service - ERROR - 创建道具实例时发生错误: 'dict' object has no attribute 'success'
2025-08-05 21:05:08,435 - shop_purchase_service - WARNING - 开始回滚购买事务: purchase_10a4eff9a8c84362
2025-08-05 21:05:08,819 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 41528, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODU0ODV9.PD8d5NgH38J3pXq-P2VKBBlxIQHN87CnvZKXFCQHT64', 'last_active': '2025-08-05T21:04:45.320438'}
2025-08-05 21:05:08,820 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:05:08,820 - currency_service - INFO - 增加货币成功: dsadjdj23 +80 gold
2025-08-05 21:05:08,820 - shop_purchase_service - WARNING - 购买事务回滚完成: purchase_10a4eff9a8c84362
2025-08-05 21:05:08,863 - utils - ERROR - Error: 购买失败: 道具创建失败
2025-08-05 21:05:08,864 - base_handlers - ERROR - ShopPurchaseHandler 处理错误: 道具创建失败, 数据: {'config_id': 'shop_6f5c8d6413fe:82026:743b7a23', 'quantity': 1}
2025-08-05 21:05:08,866 - base_handlers - ERROR - NoneType: None

2025-08-05 21:05:30,674 - ConnectionManager - INFO - 连接状态 (Worker 41528): 活跃连接数=1, 用户数=1
2025-08-05 21:06:20,740 - ConnectionManager - INFO - 连接状态 (Worker 41528): 活跃连接数=1, 用户数=1
2025-08-05 21:06:20,741 - ConnectionManager - INFO - Redis连接池状态 (Worker 41528): 使用中=3, 可用=1, 总计=4
2025-08-05 21:06:20,742 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41528): 连接中
2025-08-05 21:06:22,241 - game_server - INFO - WebSocket 断开，用户: dsadjdj23, token: eyJhbGciOi..., 代码: 1012, 原因: , worker: 41528
2025-08-05 21:06:22,449 - ConnectionManager - INFO - 正在关闭WebSocket连接 (Token: eyJhbGciOi, 状态: WebSocketState.CONNECTED)
2025-08-05 21:06:22,449 - ConnectionManager - WARNING - 关闭WebSocket连接失败: Unexpected ASGI message 'websocket.close', after sending 'websocket.close' or response already completed.
2025-08-05 21:06:22,450 - ConnectionManager - INFO - 用户 dsadjdj23 断开连接 (Token: eyJhbGciOi, Worker: 41528)
2025-08-05 21:06:22,450 - game_manager - INFO - 处理玩家登出: dsadjdj23, 原因: disconnect (Worker: 41528)
2025-08-05 21:06:22,572 - player_session_manager - INFO - 移除玩家会话: dsadjdj23 (Worker: 41528)
2025-08-05 21:06:22,612 - game_manager - INFO - 玩家登出处理完成: dsadjdj23
2025-08-05 21:06:22,670 - game_server - INFO - 关闭服务器... (Worker: 41528)
2025-08-05 21:06:22,671 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 21:06:23,056 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:06:23,056 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 21:06:23,057 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 21:06:23,057 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 21:06:23,058 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 21:06:23,058 - game_server - INFO - 日志清理管理器已停止
2025-08-05 21:06:23,059 - game_server - INFO - 统一调度器已关闭
2025-08-05 21:06:23,060 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 41528)
2025-08-05 21:06:23,103 - game_server - INFO - 服务器资源已清理 (Worker: 41528)
