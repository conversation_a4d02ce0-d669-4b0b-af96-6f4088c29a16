from fastapi import <PERSON>TTPException, APIRouter, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, field_validator
from typing import Optional
from jose import JWTError, jwt
from datetime import datetime, timedelta
from bcrypt import hashpw, gensalt
import logging
import traceback
from models import ResponseModel
from config import config
from utils import handle_error_json
from logger_config import setup_logger
from UserCacheManager import UserCacheManager, UserData
from mongodb_manager import MongoDBManager
from redis_manager import RedisManager
# 初始化日志系统
logger = setup_logger(__name__)

# 创建路由器
router = APIRouter(tags=["认证"], prefix="")

# JWT 配置
jwt_config = config.get_jwt_config()
SECRET_KEY = jwt_config["secret_key"]
ALGORITHM = jwt_config["algorithm"]
ACCESS_TOKEN_EXPIRE_MINUTES = jwt_config["access_token_expire_minutes"]

# 数据模型
class UserCredentials(BaseModel):
    username: str
    password: str

    @field_validator("username")
    def validate_username(cls, v: str) -> str:
        if not v or len(v) < 3 or len(v) > 40:
            raise handle_error_json("用户名不能为空且长度在3到40个字符之间", 400)
        if not v.isalnum() or " " in v:
            raise handle_error_json("用户名只能包含字母和数字，且不能包含空格", 400)
        return v

    @field_validator("password")
    def validate_password(cls, v: str) -> str:
        if not v or len(v) < 6 or len(v) > 20:
            raise handle_error_json("密码不能为空且长度在6到20个字符之间", 400)
        if " " in v:
            raise handle_error_json("密码不能包含空格", 400)
        if not any(char.isdigit() for char in v) or not any(char.isalpha() for char in v):
            raise handle_error_json("密码必须包含字母和数字", 400)
        return v

class UserRegister(UserCredentials):
    pass

class UserLogin(UserCredentials):
    pass

# JWT 工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    try:
        if not isinstance(data, dict) or "sub" not in data:
            logger.error("创建 token 失败: 无效的 data 参数")
            raise handle_error_json("data 必须是包含 'sub' 字段的字典")
        to_encode = data.copy()
        expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
        to_encode.update({"exp": expire})
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    except Exception as e:
        logger.error(f"创建 token 失败: {str(e)}")
        raise

async def get_current_user(token: str) -> str:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            logger.error(f"Token 缺少 'sub' 字段，token: {token[:10]}...")
            raise handle_error_json("无效的 token: 缺少用户名", 401)
        logger.debug(f"Token 验证成功，用户: {username}")
        return username
    except JWTError as e:
        logger.error(f"JWT 验证失败，token: {token[:10]}...")
        raise handle_error_json(f"无效的 token: {str(e)}", 401)
    except Exception as e:
        logger.error(f"验证 token 失败，token: {token[:10]}...: {str(e)}")
        raise handle_error_json(f"服务器错误: {str(e)}", 500)

# 路由端点
@router.post("/register")
async def register_user_endpoint(user: UserRegister):
    return await register_user(user)
@router.post("/login")
async def login_user_endpoint(user: UserLogin, connection_manager=None):
    from ConnectionManager import ConnectionManager
    if connection_manager is None:
        connection_manager = ConnectionManager()
    return await login_user(user, connection_manager)

@router.post("/logout")
async def logout_user_endpoint(request: Request, connection_manager=None):
    from ConnectionManager import ConnectionManager
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise handle_error_json("缺少或无效的 Authorization 头", 401)
        token = auth_header.split(" ")[1]
        
        if connection_manager is None:
            connection_manager = ConnectionManager()
        return await logout_user(token, connection_manager)
    except HTTPException:
        raise
    except Exception as e:
        return await handle_error_json("用户登出失败", 500, exception=e)

# 用户管理
async def register_user(user: UserRegister):
    try:            
        UserCredentials(username=user.username, password=user.password)
        user_cache = await UserCacheManager.get_instance()
        existing_user = await user_cache.get_user_by_username(user.username)
        if existing_user:
            logger.warning(f"用户 {user.username} 已存在")
            return await handle_error_json("用户名已存在", 400)
        
        # 确保密码是字节类型
        password_bytes = user.password.encode('utf-8') if isinstance(user.password, str) else user.password
        
        # 生成密码哈希
        hashed_password = hashpw(password_bytes, gensalt())
        logger.debug(f"用户 {user.username} 注册，密码哈希: {hashed_password[:20]}...")
        user_data = UserData(
            id=user.username,
            password=hashed_password,
            created_at=datetime.now()
        )
        await user_cache.save_user(user_data)
        logger.info(f"用户 {user.username} 注册成功")
        return JSONResponse(
            status_code=200,
            content=ResponseModel(success=True, code=200, message="用户注册成功").model_dump(),
            headers={"Content-Type": "application/json"}
        )
    except HTTPException:
        raise
    except Exception as e:
        return await handle_error_json("用户注册失败", 500, exception=e)

async def login_user(user: UserLogin, manager) -> JSONResponse:
    try:
        UserCredentials(username=user.username, password=user.password)
        user_cache = await UserCacheManager.get_instance()
        if await user_cache.get_user_status(user.username) == "online":
            old_token = manager.user_tokens.get(user.username)
            if old_token:
                await manager.disconnect(old_token)
                logger.info(f"用户 {user.username} 旧会话已失效，token: {old_token[:10]}...")
        # 先从MongoDB获取用户数据，不使用缓存
        user_cache = await UserCacheManager.get_instance()
        db_user = await user_cache.get_user_by_username(user.username,True)
        if not db_user:
            logger.warning(f"用户 {user.username} 未找到")
            return await handle_error_json("用户未找到", 404)
        
        # 确保密码是字节类型
        password_bytes = user.password.encode('utf-8') if isinstance(user.password, str) else user.password
        
        # 确保存储的密码哈希也是字节类型
        stored_password = db_user.password
        if isinstance(stored_password, str):
            stored_password = stored_password.encode('utf-8')
        
        # 记录密码哈希信息用于调试
        logger.debug(f"验证用户 {user.username} 密码，存储哈希类型: {type(stored_password).__name__}, 长度: {len(stored_password)}, 前20字节: {stored_password[:20]}")
        
        # 使用try-except捕获可能的bcrypt错误
        try:
            # 验证密码
            password_match = hashpw(password_bytes, stored_password) == stored_password
            if not password_match:
                logger.warning(f"用户 {user.username} 登录失败: 密码错误")
                return await handle_error_json("密码错误", 401)
        except ValueError as ve:
            # 捕获bcrypt可能抛出的ValueError（如Invalid salt）
            logger.error(f"密码验证错误 (用户: {user.username}): {str(ve)}")
            return await handle_error_json(f"密码验证失败: {str(ve)}", 500)
        except Exception as e:
            # 捕获其他可能的错误
            logger.error(f"密码验证过程中发生未知错误 (用户: {user.username}): {str(e)}")
            return await handle_error_json("密码验证过程中发生错误", 500)
        
        token = create_access_token(data={"sub": user.username})
        logger.info(f"用户 {user.username} 登录成功，token: {token[:10]}...")
        return JSONResponse(
            status_code=200,
            content=ResponseModel(
                success=True,
                code=200,
                data={"access_token": token, "token_type": "bearer"}
            ).model_dump(),
            headers={"Content-Type": "application/json"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {str(traceback.format_exc())}")
        return await handle_error_json("用户登录失败", 500, exception=e)

async def logout_user(token: str, manager):
    try:
        username = await get_current_user(token)
        user_cache = await UserCacheManager.get_instance()
        if await user_cache.get_user_status(username) == "online":
            await manager.disconnect(token)
            logger.info(f"用户 {username} 登出成功")
            return JSONResponse(
                status_code=200,
                content=ResponseModel(success=True, code=200, message="登出成功").model_dump(),
                headers={"Content-Type": "application/json"}
            )
        return await handle_error_json("未登录", 401)
    except HTTPException:
        raise
    except Exception as e:
        return await handle_error_json("用户登出失败", 500, exception=e)