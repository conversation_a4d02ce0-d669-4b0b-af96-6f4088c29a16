/**
 * 商店管理系统 - API通信模块
 * 负责与后端API的所有通信，包括错误处理和日志记录
 */

class ShopAPI {
    constructor() {
        // 获取API配置
        const apiConfig = window.ShopAdminConfig ? window.ShopAdminConfig.getApiConfig() : this.getDefaultConfig();

        // API基础配置 - 对接现有的FastAPI后端
        this.baseURL = apiConfig.baseURL + '/api/shop';
        this.adminBaseURL = apiConfig.baseURL + '/api/shop/admin';
        this.timeout = apiConfig.timeout || 10000;

        // 请求拦截器配置
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        console.log('[ShopAPI] API配置:', apiConfig);
        console.log('[ShopAPI] Base URL:', this.baseURL);
        console.log('[ShopAPI] Admin URL:', this.adminBaseURL);
    }

    /**
     * 获取默认配置（当配置文件未加载时使用）
     * @returns {Object} 默认API配置
     */
    getDefaultConfig() {
        // 检查是否为文件协议或本地开发
        if (window.location.protocol === 'file:' ||
            window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1') {
            return {
                baseURL: 'http://localhost:8000',
                timeout: 10000
            };
        }

        // 生产环境使用相对路径
        return {
            baseURL: '',
            timeout: 10000
        };
    }

    /**
     * 通用HTTP请求方法
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应数据
     */
    async request(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            // 构建完整URL
            let fullURL;
            if (url.startsWith('http')) {
                fullURL = url;
            } else if (url.startsWith('/admin/')) {
                // 管理接口使用adminBaseURL
                fullURL = url.replace('/admin/', `${this.adminBaseURL}/`);
            } else {
                // 普通接口使用baseURL
                fullURL = `${this.baseURL}${url}`;
            }

            // 合并请求配置
            const config = {
                signal: controller.signal,
                headers: { ...this.defaultHeaders, ...options.headers },
                ...options
            };

            // 记录请求日志
            this.logRequest(config.method || 'GET', fullURL, options.body);

            // 发送请求
            const response = await fetch(fullURL, config);
            clearTimeout(timeoutId);

            // 处理响应
            const result = await this.handleResponse(response);
            
            // 记录成功日志
            this.logResponse('SUCCESS', fullURL, result);
            
            return result;

        } catch (error) {
            clearTimeout(timeoutId);
            
            // 记录错误日志
            this.logResponse('ERROR', url, null, error);
            
            // 处理不同类型的错误
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            
            if (error.message.includes('Failed to fetch')) {
                throw new Error('网络连接失败，请检查服务器状态');
            }
            
            throw error;
        }
    }

    /**
     * 处理HTTP响应
     * @param {Response} response - Fetch响应对象
     * @returns {Promise<Object>} 解析后的数据
     */
    async handleResponse(response) {
        let data;
        
        try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }
        } catch (error) {
            throw new Error('响应数据格式错误');
        }

        if (!response.ok) {
            const errorMessage = data?.message || data?.error || `HTTP ${response.status}: ${response.statusText}`;
            throw new Error(errorMessage);
        }

        // 统一响应格式处理
        if (typeof data === 'object' && data !== null) {
            return {
                success: data.success !== false,
                data: data.data || data,
                message: data.message || '操作成功',
                error_code: data.error_code || null
            };
        }

        return { success: true, data: data, message: '操作成功' };
    }

    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 响应数据
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullURL = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullURL, {
            method: 'GET'
        });
    }

    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} 响应数据
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} 响应数据
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @returns {Promise<Object>} 响应数据
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    // ==================== 商店相关API ====================

    /**
     * 获取商店列表 - 对接管理接口
     * @param {Object} filters - 筛选条件
     * @returns {Promise<Object>} 商店列表
     */
    async getShops(filters = {}) {
        try {
            console.log('[ShopAPI] 获取商店列表');

            // 使用新增的管理接口
            const response = await this.get('/admin/shops');

            // 转换响应格式以适配前端
            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data,
                    message: response.message || "获取商店列表成功"
                };
            }

            return response;

        } catch (error) {
            console.error('[ShopAPI] 获取商店列表失败:', error);

            // 如果API调用失败，返回空列表而不是错误
            return {
                success: true,
                data: [],
                message: "暂无商店数据"
            };
        }
    }

    /**
     * 获取单个商店详情 - 对接管理接口
     * @param {string} shopId - 商店ID
     * @returns {Promise<Object>} 商店详情
     */
    async getShop(shopId) {
        if (!shopId) {
            throw new Error('商店ID不能为空');
        }

        try {
            console.log('[ShopAPI] 获取商店详情:', shopId);

            // 使用新增的管理接口
            const response = await this.get(`/admin/shop/${shopId}`);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data,
                    message: response.message || "获取商店详情成功"
                };
            }

            return response;

        } catch (error) {
            console.error('[ShopAPI] 获取商店详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建商店 - 对接现有管理接口
     * @param {Object} shopData - 商店数据
     * @returns {Promise<Object>} 创建结果
     */
    async createShop(shopData) {
        this.validateShopData(shopData);

        // 转换为现有API期望的格式
        const apiData = {
            shop_name: shopData.shop_name,
            shop_type: shopData.shop_type,
            description: shopData.description || '',
            icon: shopData.icon || '',
            is_active: shopData.is_active !== false,
            access_conditions: shopData.access_conditions || {},
            refresh_config: shopData.refresh_config || {},
            sort_order: shopData.sort_order || 0,
            ui_config: shopData.ui_config || {}
        };

        console.log('[ShopAPI] 创建商店请求:', apiData);

        // 使用现有的创建商店接口
        return this.request('/admin/shop', {
            method: 'POST',
            body: JSON.stringify(apiData)
        });
    }

    /**
     * 更新商店 - 对接管理接口
     * @param {string} shopId - 商店ID
     * @param {Object} shopData - 商店数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateShop(shopId, shopData) {
        if (!shopId) {
            throw new Error('商店ID不能为空');
        }
        this.validateShopData(shopData);

        // 转换为现有API期望的格式
        const apiData = {
            shop_name: shopData.shop_name,
            shop_type: shopData.shop_type,
            description: shopData.description || '',
            icon: shopData.icon || '',
            is_active: shopData.is_active !== false,
            access_conditions: shopData.access_conditions || {},
            refresh_config: shopData.refresh_config || {},
            sort_order: shopData.sort_order || 0,
            ui_config: shopData.ui_config || {}
        };

        console.log('[ShopAPI] 更新商店请求:', shopId, apiData);

        // 使用新增的管理接口
        return this.request(`/admin/shop/${shopId}`, {
            method: 'PUT',
            body: JSON.stringify(apiData)
        });
    }

    /**
     * 删除商店 - 对接管理接口
     * @param {string} shopId - 商店ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteShop(shopId) {
        if (!shopId) {
            throw new Error('商店ID不能为空');
        }

        console.log('[ShopAPI] 删除商店:', shopId);

        // 使用新增的管理接口
        return this.request(`/admin/shop/${shopId}`, {
            method: 'DELETE'
        });
    }

    // ==================== 商品配置相关API ====================

    /**
     * 获取商店商品配置列表 - 对接现有API
     * @param {string} shopId - 商店ID
     * @returns {Promise<Object>} 商品配置列表
     */
    async getShopItems(shopId) {
        if (!shopId) {
            throw new Error('商店ID不能为空');
        }

        try {
            console.log('[ShopAPI] 获取商店商品配置:', shopId);

            // 使用现有的商店商品接口，添加管理员player_id
            const response = await this.get(`/${shopId}/items?player_id=admin`);

            // 转换响应格式以适配前端管理界面
            if (response.success && response.data) {
                // 将商品数据转换为配置格式
                const items = response.data.items || response.data;
                const configItems = items.map(item => {
                    console.log('[ShopAPI] 处理商品数据:', item);

                    // 正确处理价格配置
                    let priceConfig = item.price_config;
                    if (!priceConfig && item.original_price && item.currency_type) {
                        // 如果没有price_config但有价格信息，构建配置对象
                        priceConfig = {
                            currency_type: item.currency_type,
                            base_price: item.original_price
                        };
                        console.log('[ShopAPI] 从价格信息构建price_config:', priceConfig);
                    }

                    const configItem = {
                        config_id: item.config_id || `config_${item.item_template_id}`,
                        shop_id: shopId,
                        item_template_id: item.item_template_id,
                        item_quantity: item.item_quantity || 1,
                        item_quality: item.item_quality,
                        slot_id: item.slot_id,
                        price_config: priceConfig || { currency_type: "gold", base_price: 0 },
                        purchase_limit: item.purchase_limit || null,
                        availability: item.availability || {},
                        refresh_weight: item.refresh_weight || 100,
                        refresh_probability: item.refresh_probability || 1.0,
                        sort_order: item.sort_order || 0,
                        is_active: item.is_active !== false,
                        display_config: item.display_config || {},
                        created_at: item.created_at || new Date().toISOString(),
                        updated_at: item.updated_at || new Date().toISOString()
                    };

                    console.log('[ShopAPI] 转换后的配置项:', configItem);
                    return configItem;
                });

                return {
                    success: true,
                    data: configItems,
                    message: "获取商品配置列表成功"
                };
            }

            return response;

        } catch (error) {
            console.error('[ShopAPI] 获取商店商品配置失败:', error);

            // 如果API调用失败，返回空列表
            return {
                success: true,
                data: [],
                message: "暂无商品配置"
            };
        }
    }

    /**
     * 创建商品配置 - 对接现有管理接口
     * @param {string} shopId - 商店ID
     * @param {Object} itemData - 商品配置数据
     * @returns {Promise<Object>} 创建结果
     */
    async createShopItem(shopId, itemData) {
        if (!shopId) {
            throw new Error('商店ID不能为空');
        }
        this.validateItemData(itemData);

        // 转换为现有API期望的格式
        const apiData = {
            shop_id: shopId,
            item_template_id: itemData.item_template_id,
            item_quantity: itemData.item_quantity,
            item_quality: itemData.item_quality,
            slot_id: itemData.slot_id,
            price_config: itemData.price_config,
            purchase_limit: itemData.purchase_limit || {},
            availability: itemData.availability,
            refresh_weight: itemData.refresh_weight || 100,
            refresh_probability: itemData.refresh_probability || 1.0,
            sort_order: itemData.sort_order || 0,
            is_active: itemData.is_active !== false,
            display_config: itemData.display_config || {}
        };

        console.log('[ShopAPI] 创建商品配置请求:', apiData);

        // 使用现有的创建商品配置接口
        return this.request('/admin/item-config', {
            method: 'POST',
            body: JSON.stringify(apiData)
        });
    }

    /**
     * 更新商品配置 - 对接管理接口
     * @param {string} shopId - 商店ID
     * @param {string} configId - 配置ID
     * @param {Object} itemData - 商品配置数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateShopItem(shopId, configId, itemData) {
        if (!shopId || !configId) {
            throw new Error('商店ID和配置ID不能为空');
        }
        this.validateItemData(itemData);

        // 转换为现有API期望的格式
        const apiData = {
            item_template_id: itemData.item_template_id,
            item_quantity: itemData.item_quantity,
            item_quality: itemData.item_quality,
            slot_id: itemData.slot_id,
            price_config: itemData.price_config,
            purchase_limit: itemData.purchase_limit || {},
            availability: itemData.availability,
            refresh_weight: itemData.refresh_weight || 100,
            refresh_probability: itemData.refresh_probability || 1.0,
            sort_order: itemData.sort_order || 0,
            is_active: itemData.is_active !== false,
            display_config: itemData.display_config || {}
        };

        console.log('[ShopAPI] 更新商品配置请求:', configId, apiData);

        // 使用新增的管理接口
        return this.request(`/admin/item/${configId}`, {
            method: 'PUT',
            body: JSON.stringify(apiData)
        });
    }

    /**
     * 删除商品配置 - 对接管理接口
     * @param {string} shopId - 商店ID
     * @param {string} configId - 配置ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteShopItem(shopId, configId) {
        if (!shopId || !configId) {
            throw new Error('商店ID和配置ID不能为空');
        }

        console.log('[ShopAPI] 删除商品配置:', configId);

        // 使用新增的管理接口
        return this.request(`/admin/item/${configId}`, {
            method: 'DELETE'
        });
    }

    // ==================== 数据验证方法 ====================

    /**
     * 验证商店数据
     * @param {Object} shopData - 商店数据
     */
    validateShopData(shopData) {
        const required = ['shop_id', 'shop_name', 'shop_type'];
        const missing = required.filter(field => !shopData[field]);
        
        if (missing.length > 0) {
            throw new Error(`缺少必填字段: ${missing.join(', ')}`);
        }

        // 验证商店类型
        const validTypes = ['normal', 'guild', 'vip', 'event', 'arena'];
        if (!validTypes.includes(shopData.shop_type)) {
            throw new Error(`无效的商店类型: ${shopData.shop_type}`);
        }

        // 验证JSON字段
        this.validateJSONField(shopData.access_conditions, 'access_conditions');
        this.validateJSONField(shopData.refresh_config, 'refresh_config');
        this.validateJSONField(shopData.ui_config, 'ui_config');
    }

    /**
     * 验证商品配置数据
     * @param {Object} itemData - 商品配置数据
     */
    validateItemData(itemData) {
        const required = ['item_template_id', 'item_quantity'];
        const missing = required.filter(field => !itemData[field]);
        
        if (missing.length > 0) {
            throw new Error(`缺少必填字段: ${missing.join(', ')}`);
        }

        if (itemData.item_quantity <= 0) {
            throw new Error('商品数量必须大于0');
        }
    }

    /**
     * 验证JSON字段
     * @param {string} value - JSON字符串
     * @param {string} fieldName - 字段名
     */
    validateJSONField(value, fieldName) {
        if (value && typeof value === 'string') {
            try {
                JSON.parse(value);
            } catch (error) {
                throw new Error(`${fieldName} 不是有效的JSON格式`);
            }
        }
    }

    // ==================== 日志记录方法 ====================

    /**
     * 记录请求日志
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {*} data - 请求数据
     */
    logRequest(method, url, data) {
        console.log(`[API Request] ${method} ${url}`, data ? { data } : '');
    }

    /**
     * 记录响应日志
     * @param {string} status - 响应状态
     * @param {string} url - 请求URL
     * @param {*} data - 响应数据
     * @param {Error} error - 错误对象
     */
    logResponse(status, url, data, error) {
        if (status === 'SUCCESS') {
            console.log(`[API Response] SUCCESS ${url}`, { data });
        } else {
            console.error(`[API Response] ERROR ${url}`, { error: error.message });
        }
    }
}

// 创建全局API实例
window.shopAPI = new ShopAPI();
