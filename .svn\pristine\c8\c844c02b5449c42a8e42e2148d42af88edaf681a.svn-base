#!/usr/bin/env python
# -*- coding: utf-8 -*-

from enum import Enum, auto

class EventType(str, Enum):
    """
    系统事件类型枚举
    使用字符串值以便于调试和日志记录
    """
    # 用户事件
    USER_CONNECTED = "user.connected"          # 用户连接
    USER_DISCONNECTED = "user.disconnected"    # 用户断开连接
    USER_REGISTERED = "user.registered"        # 用户注册
    USER_LOGGED_IN = "user.logged_in"          # 用户登录
    USER_LOGGED_OUT = "user.logged_out"        # 用户登出
    
    # 资产事件
    ASSET_ADDED = "asset.added"                # 资产添加
    ASSET_UPDATED = "asset.updated"            # 资产更新
    ASSET_DELETED = "asset.deleted"            # 资产删除
    ASSET_BATCH_CHANGED = "asset.batch_changed" # 批量资产变更
    
    # 系统事件
    SYSTEM_STARTUP = "system.startup"          # 系统启动
    SYSTEM_SHUTDOWN = "system.shutdown"        # 系统关闭
    CONFIG_CHANGED = "system.config_changed"   # 配置变更
    
    # 消息事件
    MESSAGE_RECEIVED = "message.received"      # 接收消息
    MESSAGE_SENT = "message.sent"              # 发送消息
    
    # 错误事件
    ERROR_OCCURRED = "error.occurred"          # 发生错误
    
    # 心跳事件
    HEARTBEAT = "system.heartbeat"             # 心跳 