<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商店管理系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-container">
            <h1 class="header-title">商店管理系统</h1>
            <nav class="header-nav">
                <a href="index.html" class="nav-link active">商店管理</a>
                <a href="items.html" class="nav-link">商品管理</a>
            </nav>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 操作工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h2>商店列表</h2>
                <span class="shop-count">总计: <span id="shopCount">0</span> 个商店</span>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-primary" onclick="openShopForm()">
                    <i class="icon-plus"></i>
                    新增商店
                </button>
                <button class="btn btn-secondary" onclick="refreshShopList()">
                    <i class="icon-refresh"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-bar">
            <div class="filter-group">
                <label for="shopTypeFilter">商店类型:</label>
                <select id="shopTypeFilter" onchange="filterShops()">
                    <option value="">全部</option>
                    <option value="normal">普通商店</option>
                    <option value="guild">公会商店</option>
                    <option value="vip">VIP商店</option>
                    <option value="event">活动商店</option>
                    <option value="arena">竞技场商店</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="statusFilter">状态:</label>
                <select id="statusFilter" onchange="filterShops()">
                    <option value="">全部</option>
                    <option value="true">激活</option>
                    <option value="false">禁用</option>
                </select>
            </div>
            <div class="filter-group">
                <input type="text" id="searchInput" placeholder="搜索商店名称..." onkeyup="searchShops()">
            </div>
        </div>

        <!-- 商店列表 -->
        <div class="shop-list-container">
            <div id="shopList" class="shop-grid">
                <!-- 商店卡片将通过JavaScript动态生成 -->
            </div>
            
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state">
                <div class="spinner"></div>
                <p>加载中...</p>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-icon">🏪</div>
                <h3>暂无商店</h3>
                <p>点击"新增商店"按钮创建第一个商店</p>
                <button class="btn btn-primary" onclick="openShopForm()">新增商店</button>
            </div>
        </div>
    </main>

    <!-- 商店表单模态框 -->
    <div id="shopModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增商店</h3>
                <button class="modal-close" onclick="closeShopForm()" title="关闭弹窗 (按ESC键也可关闭)">&times;</button>
            </div>
            <div class="modal-body">
                <form id="shopForm" onsubmit="saveShop(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="shopId">商店ID *</label>
                            <input type="text" id="shopId" name="shop_id" required 
                                   placeholder="例: shop_001" pattern="[a-zA-Z0-9_]+" 
                                   title="只能包含字母、数字和下划线">
                        </div>
                        <div class="form-group">
                            <label for="shopName">商店名称 *</label>
                            <input type="text" id="shopName" name="shop_name" required 
                                   placeholder="例: 装备商店" maxlength="50">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="shopType">商店类型 *</label>
                            <select id="shopType" name="shop_type" required>
                                <option value="">请选择</option>
                                <option value="normal">普通商店</option>
                                <option value="guild">公会商店</option>
                                <option value="vip">VIP商店</option>
                                <option value="event">活动商店</option>
                                <option value="arena">竞技场商店</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="sortOrder">排序权重</label>
                            <input type="number" id="sortOrder" name="sort_order" 
                                   value="0" min="0" max="9999">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">商店描述</label>
                        <textarea id="description" name="description" 
                                  placeholder="商店的详细描述..." maxlength="200"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="icon">商店图标</label>
                        <input type="text" id="icon" name="icon" 
                               placeholder="图标路径或URL">
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isActive" name="is_active" checked>
                            <span class="checkmark"></span>
                            激活商店
                        </label>
                    </div>
                    
                    <!-- 高级配置 -->
                    <div class="form-section">
                        <h4 class="section-title">高级配置</h4>
                        <div class="form-group">
                            <label for="accessConditions">访问条件 (JSON)</label>
                            <textarea id="accessConditions" name="access_conditions" 
                                      placeholder='{"level": 10, "vip": 1}' rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="refreshConfig">刷新配置 (JSON)</label>
                            <textarea id="refreshConfig" name="refresh_config" 
                                      placeholder='{"interval": 3600, "auto_refresh": true}' rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="uiConfig">UI配置 (JSON)</label>
                            <textarea id="uiConfig" name="ui_config" 
                                      placeholder='{"theme": "default", "layout": "grid"}' rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="modal-hint">💡 提示：按ESC键可快速关闭弹窗</div>
                <div class="modal-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeShopForm()">取消</button>
                    <button type="submit" form="shopForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" onclick="closeDeleteModal()" title="关闭弹窗 (按ESC键也可关闭)">&times;</button>
            </div>
            <div class="modal-body">
                <p>确定要删除商店 "<span id="deleteShopName"></span>" 吗？</p>
                <p class="warning-text">此操作不可恢复，商店下的所有商品配置也将被删除。</p>
            </div>
            <div class="modal-footer">
                <div class="modal-hint">💡 提示：按ESC键可快速关闭弹窗</div>
                <div class="modal-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/shop-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
