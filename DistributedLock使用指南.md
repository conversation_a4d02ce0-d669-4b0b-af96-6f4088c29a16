# DistributedLock 使用指南

## 概述

优化后的 `DistributedLock` 类提供了自动管理 Redis 连接的分布式锁功能，无需外部传入 Redis 客户端，简化了使用方式并提高了灵活性。

## 主要特性

### 1. 自动连接管理
- 自动从配置文件读取 Redis 配置
- 支持连接池管理
- 自动重连和错误恢复
- 本地 Redis 作为备选方案

### 2. 增强的锁功能
- 支持阻塞和非阻塞获取锁
- 支持锁超时时间设置
- 支持锁超时时间延长
- 支持锁状态检查

### 3. 错误处理
- 完善的异常处理机制
- 连接错误自动重试
- 详细的日志记录
- 优雅的错误恢复

### 4. 上下文管理器支持
- 支持 `async with` 语法
- 自动获取和释放锁
- 异常安全保证

## 基本用法

### 1. 简单锁使用

```python
from distributed_lock import DistributedLock

async def simple_example():
    # 创建锁
    lock = DistributedLock("my_lock", ttl=30)
    
    # 获取锁
    acquired = await lock.acquire(blocking=True, timeout=10)
    if acquired:
        try:
            # 执行需要保护的代码
            print("执行受保护的代码...")
            await asyncio.sleep(5)
        finally:
            # 释放锁
            await lock.release()
    else:
        print("获取锁失败")
```

### 2. 使用上下文管理器

```python
async def context_manager_example():
    try:
        async with DistributedLock("my_lock", ttl=30) as lock:
            # 自动获取锁
            print("锁已获取，执行受保护的代码...")
            await asyncio.sleep(5)
            # 自动释放锁
    except TimeoutError:
        print("获取锁超时")
    except Exception as e:
        print(f"发生错误: {e}")
```

### 3. 非阻塞锁

```python
async def non_blocking_example():
    lock = DistributedLock("my_lock", ttl=30)
    
    # 非阻塞获取锁
    acquired = await lock.acquire(blocking=False)
    if acquired:
        try:
            print("立即获取锁成功")
            await asyncio.sleep(5)
        finally:
            await lock.release()
    else:
        print("锁被占用，无法获取")
```

## 高级用法

### 1. 锁超时时间延长

```python
async def extend_ttl_example():
    lock = DistributedLock("my_lock", ttl=10)
    
    if await lock.acquire():
        try:
            print("执行长时间操作...")
            await asyncio.sleep(5)
            
            # 延长锁超时时间
            if await lock.extend_ttl(20):
                print("锁超时时间已延长")
                await asyncio.sleep(10)
            else:
                print("延长锁超时时间失败")
        finally:
            await lock.release()
```

### 2. 锁状态检查

```python
async def check_status_example():
    lock = DistributedLock("my_lock", ttl=30)
    
    print(f"初始锁状态: {lock.is_acquired}")
    
    if await lock.acquire():
        print(f"获取锁后状态: {lock.is_acquired}")
        await lock.release()
        print(f"释放锁后状态: {lock.is_acquired}")
```

### 3. 并发锁测试

```python
async def concurrent_example():
    async def worker(worker_id: int):
        lock = DistributedLock("concurrent_lock", ttl=10)
        
        print(f"Worker {worker_id}: 尝试获取锁...")
        if await lock.acquire(blocking=True, timeout=15):
            print(f"Worker {worker_id}: 获取锁成功")
            await asyncio.sleep(3)  # 模拟工作
            await lock.release()
            print(f"Worker {worker_id}: 释放锁")
        else:
            print(f"Worker {worker_id}: 获取锁失败")
    
    # 创建多个并发任务
    tasks = [worker(i) for i in range(3)]
    await asyncio.gather(*tasks)
```

## 在武将系统中的应用

### 1. 武将操作锁

```python
class GeneralService:
    async def _acquire_general_lock(self, player_id: str, general_id: str = None):
        """获取武将操作锁"""
        if general_id:
            lock_key = f"general_lock:{player_id}:{general_id}"
        else:
            lock_key = f"general_lock:{player_id}"
        
        lock = DistributedLock(lock_key, self.lock_timeout)
        acquired = await lock.acquire(blocking=True, timeout=10)
        if not acquired:
            raise Exception(f"无法获取武将操作锁: {lock_key}")
        
        return lock
    
    async def draw_general(self, player_id: str, package_id: int = 1):
        """抽卡功能"""
        lock = None
        try:
            # 获取锁
            lock = await self._acquire_general_lock(player_id)
            
            # 执行抽卡逻辑
            # ... 抽卡代码 ...
            
        finally:
            if lock:
                await lock.release()
```

### 2. 使用上下文管理器

```python
async def draw_general_with_context(self, player_id: str, package_id: int = 1):
    """使用上下文管理器的抽卡功能"""
    lock_key = f"general_lock:{player_id}"
    
    try:
        async with DistributedLock(lock_key, ttl=30) as lock:
            # 执行抽卡逻辑
            # ... 抽卡代码 ...
            pass
    except TimeoutError:
        raise Exception("获取锁超时，请稍后重试")
    except Exception as e:
        raise Exception(f"抽卡失败: {str(e)}")
```

## 配置说明

### Redis 配置

在 `config/server.ini` 中配置 Redis 连接信息：

```ini
[Redis]
host = localhost
port = 6379
password = 
db = 0
max_connections = 100
socket_timeout = 5
socket_connect_timeout = 5
```

### 连接池配置

- `max_connections`: 连接池最大连接数
- `socket_timeout`: 操作超时时间
- `socket_connect_timeout`: 连接超时时间
- `health_check_interval`: 健康检查间隔

## 错误处理

### 1. 连接错误

```python
async def handle_connection_error():
    try:
        lock = DistributedLock("test_lock", ttl=30)
        acquired = await lock.acquire()
        if acquired:
            await lock.release()
    except Exception as e:
        if "无法连接到Redis服务器" in str(e):
            print("Redis连接失败，请检查配置")
        else:
            print(f"其他错误: {e}")
```

### 2. 锁超时

```python
async def handle_lock_timeout():
    try:
        async with DistributedLock("test_lock", ttl=5) as lock:
            await asyncio.sleep(10)  # 超过锁超时时间
    except TimeoutError:
        print("获取锁超时")
    except Exception as e:
        print(f"其他错误: {e}")
```

## 性能优化建议

### 1. 合理设置超时时间

```python
# 短时间操作
lock = DistributedLock("short_operation", ttl=10)

# 长时间操作
lock = DistributedLock("long_operation", ttl=60)
```

### 2. 使用非阻塞锁

```python
# 对于非关键操作，使用非阻塞锁
acquired = await lock.acquire(blocking=False)
if not acquired:
    # 处理锁被占用的情况
    pass
```

### 3. 及时释放锁

```python
# 使用 try-finally 确保锁被释放
lock = DistributedLock("my_lock", ttl=30)
try:
    if await lock.acquire():
        # 执行操作
        pass
finally:
    await lock.release()
```

## 监控和调试

### 1. 日志级别

```python
import logging

# 设置日志级别
logging.getLogger("distributed_lock").setLevel(logging.DEBUG)
```

### 2. 锁状态监控

```python
async def monitor_locks():
    lock = DistributedLock("monitor_lock", ttl=30)
    
    if await lock.acquire():
        print(f"锁状态: {lock.is_acquired}")
        print(f"锁键: {lock.key}")
        print(f"锁值: {lock.lock_value}")
        await lock.release()
```

### 3. 连接状态检查

```python
async def check_connection():
    try:
        # 尝试获取锁来测试连接
        lock = DistributedLock("connection_test", ttl=5)
        acquired = await lock.acquire(blocking=False)
        if acquired:
            await lock.release()
            print("Redis连接正常")
        else:
            print("Redis连接异常")
    except Exception as e:
        print(f"Redis连接失败: {e}")
```

## 最佳实践

### 1. 锁命名规范

```python
# 使用有意义的锁名
lock = DistributedLock("user:123:general:456", ttl=30)
lock = DistributedLock("system:maintenance", ttl=300)
lock = DistributedLock("cache:refresh", ttl=60)
```

### 2. 超时时间设置

```python
# 根据操作复杂度设置超时时间
SHORT_OPERATION_TTL = 10
MEDIUM_OPERATION_TTL = 30
LONG_OPERATION_TTL = 60

lock = DistributedLock("operation", ttl=SHORT_OPERATION_TTL)
```

### 3. 错误恢复

```python
async def robust_operation():
    max_retries = 3
    for attempt in range(max_retries):
        try:
            async with DistributedLock("robust_lock", ttl=30) as lock:
                # 执行操作
                pass
            break
        except TimeoutError:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(1)
```

## 总结

优化后的 `DistributedLock` 提供了更简单、更可靠的分布式锁功能：

1. **简化使用**: 无需外部传入 Redis 客户端
2. **自动管理**: 自动处理连接和错误恢复
3. **功能增强**: 支持更多锁操作和状态检查
4. **错误处理**: 完善的异常处理和日志记录
5. **性能优化**: 连接池和重试机制

通过这些改进，`DistributedLock` 更适合在生产环境中使用，提供了更好的可靠性和易用性。 