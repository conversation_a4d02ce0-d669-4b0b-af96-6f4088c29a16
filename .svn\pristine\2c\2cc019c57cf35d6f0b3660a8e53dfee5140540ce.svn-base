from typing import Dict, Any
from fastapi import WebSocket
import logging
import json
from datetime import datetime
import traceback
from game_database import Item, DatabaseManager
from ConnectionManager import ConnectionManager
import asyncio
from utils import handle_error
from enums import MessageId
import random
from logger_config import setup_logger
from models import MessageModel

# 初始化日志系统
logger = setup_logger(__name__)

class MessageHandler:
    """消息处理器基类"""
    def __init__(self):
        self.name = self.__class__.__name__
        
    async def validate(self, data: dict) -> bool:
        """验证消息数据"""
        return True
        
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理消息"""
        raise NotImplementedError
        
    async def error_handler(self, error: Exception, data: dict) -> dict:
        """处理错误"""
        error_msg = str(error)
        logger.error(f"{self.name} 处理错误: {error_msg}, 数据: {data}")
        logger.error(traceback.format_exc())
        return {
            "msgId": MessageId.ERROR,
            "data": {"error": error_msg}
        }

class ChatHandler(MessageHandler):
    async def validate(self, data: dict) -> bool:
        """验证聊天消息"""
        if not isinstance(data.get("content"), str):
            return False
        if len(data.get("content", "").strip()) == 0:
            return False
        return True

    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理聊天消息"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid chat message format")
                return self.error_handler(ValueError("Invalid chat message format"), data)

            message = MessageModel(msgId=MessageId.CHAT, data={
                    "sender": username,
                    "content": data.get("content", "").strip(),
                    "timestamp": datetime.now().isoformat()
            }).model_dump()
            
            # 使用改进的广播机制发送消息
            await connection_manager.broadcast(message)
            logger.debug(f"Chat message broadcasted: {message}")
            
            return message
        except Exception as e:
            logger.error(f"Error in ChatHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

class BroadcastHandler(MessageHandler):
    async def validate(self, data: dict) -> bool:
        """验证广播消息"""
        if not isinstance(data.get("message"), (dict, str)):
            return False
        return True

    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理广播消息"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid broadcast message format")
                return self.error_handler(ValueError("Invalid broadcast message format"), data)

            message = MessageModel(msgId=MessageId.BROADCAST_MESSAGE, data={
                    "sender": username,
                    "content": data.get("message"),
                    "timestamp": datetime.now().isoformat()
            }).model_dump()
            
            # 使用改进的广播机制发送消息
            await connection_manager.broadcast(message, priority=1)  # 广播消息优先级更高
            logger.debug(f"System broadcast sent: {message}")
            
            return message
        except Exception as e:
            logger.error(f"Error in BroadcastHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

class HeartbeatHandler(MessageHandler):
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理心跳消息"""
        try:
            await websocket.send_json(MessageModel(msgId=MessageId.HEARTBEAT, data={
                    "status": "ok",
                    "timestamp": datetime.now().isoformat()
                }).model_dump())
            return MessageModel(msgId=MessageId.HEARTBEAT, data={
                "status": "ok",
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        except Exception as e:
            logger.error(f"Error in HeartbeatHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

# 消息处理器映射
handlers: Dict[str, MessageHandler] = {
    "chat": ChatHandler(),
    "broadcast": BroadcastHandler(),
    "heartbeat": HeartbeatHandler()
}

async def handle_message(data: dict, websocket: WebSocket, 
                        username: str, token: str, 
                        connection_manager) -> dict:
    """处理接收到的消息"""
    try:
        message_type = data.get("type")
        if not message_type:
            await handle_error(websocket, "Message type not specified")
            return MessageModel(msgId=MessageId.ERROR, data={"error": "Message type not specified"}).model_dump()

        handler = handlers.get(message_type)
        if not handler:
            await handle_error(websocket, f"Unknown message type: {message_type}")
            return MessageModel(msgId=MessageId.ERROR, data={"error": f"Unknown message type: {message_type}"}).model_dump()

        return await handler.handle(data, websocket, username, token, connection_manager)
        
    except Exception as e:
        logger.error(f"Error handling message: {str(e)}")
        logger.error(traceback.format_exc())
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"Internal error while processing message: {str(e)}"}).model_dump()

async def heartbeat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理心跳消息，确保高可靠性"""
    try:
        # 心跳消息应当是最高优先级处理
        # 直接调用ConnectionManager的handle_heartbeat方法
        success = await manager.handle_heartbeat(websocket, token, data)
        
        if not success:
            # 如果心跳处理失败，尝试一次重试
            await asyncio.sleep(0.1)  # 短暂等待
            success = await manager.handle_heartbeat(websocket, token, data)
        
        # 返回简化的成功响应，减少网络负载
        return MessageModel(msgId=MessageId.HEARTBEAT, data={"ok": 1}).model_dump()
    except Exception as e:
        # 记录错误但不中断连接
        logger.error(f"心跳处理错误 (用户: {username}, Token: {token[:10]}): {str(e)}")
        # 不返回详细错误信息，减少攻击面
        return MessageModel(msgId=MessageId.HEARTBEAT, data={"ok": 0}).model_dump()

async def chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理公共聊天消息"""
    try:
        content = data.get("content")
        if not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.CHAT, data={"error": "内容不能为空"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "内容不能为空"}).model_dump()

        # 创建广播消息
        message = MessageModel(msgId=MessageId.CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 使用改进的广播机制
        await manager.broadcast(message)
        logger.info(f"用户 {username} 发送公共聊天消息: {content[:30]}...")
        return message
    except Exception as e:
        logger.error(f"处理聊天消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理聊天消息失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"处理聊天消息失败: {str(e)}"}).model_dump()

async def private_chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理私聊消息"""
    try:
        target = data.get("target")
        content = data.get("content")
        if not target or not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.PRIVATE_CHAT, data={"error": "目标或内容不能为空"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "目标或内容不能为空"}).model_dump()
        
        target_token = manager.user_tokens.get(target)
        message = MessageModel(msgId=MessageId.PRIVATE_CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        if target_token:
            # 发送给目标用户
            await manager.send_personal_message(message, target_token)
            # 发送给发送者自己
            await manager.send_personal_message(message, token)
            logger.info(f"用户 {username} 发送私聊消息给 {target}: {content[:30]}...")
            return message
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.PRIVATE_CHAT, data={"error": "目标用户不在线"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "目标用户不在线"}).model_dump()
    except Exception as e:
        logger.error(f"处理私聊消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理私聊消息失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"处理私聊消息失败: {str(e)}"}).model_dump()

async def group_chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理群聊消息"""
    try:
        targets = data.get("targets", [])
        content = data.get("content")
        if not targets or not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.GROUP_CHAT, data={"error": "目标或内容不能为空"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "目标或内容不能为空"}).model_dump()
        
        message = MessageModel(msgId=MessageId.GROUP_CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 发送给所有目标用户
        await manager.broadcast_to_users(message, targets)
        # 发送给发送者自己
        await manager.send_personal_message(message, token)
        logger.info(f"用户 {username} 发送群聊消息给 {len(targets)} 个用户: {content[:30]}...")
        return message
    except Exception as e:
        logger.error(f"处理群聊消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理群聊消息失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"处理群聊消息失败: {str(e)}"}).model_dump()

async def add_equip_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """添加装备"""
    defid = data.get("defid")
    attributes = data.get("attributes", {})
    if not isinstance(defid, int) or not isinstance(attributes, dict):
        await manager.send_personal_message(MessageModel(msgId=MessageId.ADD_EQUIP, data={"error": "定义 ID 或属性无效"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": "定义 ID 或属性无效"}).model_dump()
    try:
        item = Item(owner=username, defid=defid, attributes=attributes, type="equip")
        result = await item.save(manager.db_manager.db, manager.db_manager.redis_client)
        await manager.send_personal_message(MessageModel(msgId=MessageId.ADD_EQUIP, data={
                "message": f"Equip {defid} 添加成功，ID: {result.id}",
                "item": result.serialize()
            }).model_dump()    , token)
        return MessageModel(msgId=MessageId.ADD_EQUIP, data={
                "message": f"Equip {defid} 添加成功，ID: {result.id}",
                "item": result.serialize()
            }).model_dump()
    except Exception as e:
        logger.error(f"添加装备失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ADD_EQUIP, data={"error": f"添加装备失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"添加装备失败: {str(e)}"}).model_dump()

async def handle_add_item(connection_manager, websocket, data, user_id):
    """处理添加物品请求"""
    try:
        # 提取物品定义ID和数量
        item_def_id = data.get("def_id")
        quantity = data.get("quantity", 1)
        attributes = data.get("attributes", {})
        
        if not item_def_id:
            await websocket.send_json(MessageModel(msgId=MessageId.ERROR, data={"error": "缺少物品定义ID"}).model_dump())
            return
            
        # 添加超时保护和重试机制
        max_retries = 3
        retry_delay = 0.5  # 秒
        last_error = None
        
        # 创建数据库管理器实例
        db_manager = DatabaseManager()
        
        # 重试循环
        for attempt in range(max_retries):
            try:
                # 使用asyncio.wait_for添加超时保护
                item_id = await asyncio.wait_for(
                    db_manager.add_item_to_user(user_id, item_def_id, quantity, attributes),
                    timeout=5.0  # 5秒超时
                )
                
                # 成功响应
                await websocket.send_json(MessageModel(msgId=MessageId.ADD_ITEM, data={
                    "item_id": item_id,
                    "def_id": item_def_id,
                    "quantity": quantity,
                    "success": True
                }).model_dump())
                
                # 成功则返回
                return
                
            except asyncio.TimeoutError:
                last_error = "操作超时"
                logger.warning(f"添加物品操作超时 (尝试 {attempt+1}/{max_retries})，用户: {user_id}, defid: {item_def_id}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))  # 指数退避
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"添加物品操作失败 (尝试 {attempt+1}/{max_retries})，用户: {user_id}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
        
        # 如果所有重试都失败
        logger.error(f"添加物品操作超时，用户: {user_id}, defid: {item_def_id}")
        # 发送友好的错误消息
        await websocket.send_json(MessageModel(msgId=MessageId.ERROR, data={"error": "操作暂时无法完成，请稍后再试"}).model_dump())
        logger.error(f"添加物品失败，用户: {user_id}, 错误: {last_error}")
            
    except Exception as e:
        logger.error(f"添加物品时出错: {str(e)}")
        logger.error(traceback.format_exc())
        # 发送错误响应
        try:
            await websocket.send_json(MessageModel(msgId=MessageId.ERROR, data={"error": "服务器处理请求时出错"}).model_dump())
        except Exception as send_error:
            logger.error(f"发送错误响应失败: {str(send_error)}")
async def enter_game_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """进入游戏"""
    try:
        user = await manager.db_manager.get_user_by_username(username)
        if not user:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "用户不存在"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "用户不存在"}).model_dump()
        await manager.send_personal_message(MessageModel(msgId=MessageId.ROLE_INFO, data={"user": user.serialize(exclude_fields=["password"])}).model_dump(), token)
        items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client)
        await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items}).model_dump(), token)
        await manager.send_personal_message(MessageModel(msgId=MessageId.ENTER_GAME, data={}).model_dump(), token)
        return MessageModel(msgId=MessageId.ENTER_GAME, data={}).model_dump()
    except Exception as e:
        logger.error(f"进入游戏失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"进入游戏失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"进入游戏失败: {str(e)}"}).model_dump()  

async def handle_batch_add_items(connection_manager, websocket, data, user_id):
    """批量处理添加物品请求"""
    try:
        # 提取物品列表
        items = data.get("items", [])
        
        if not items or not isinstance(items, list):
            await websocket.send_json(MessageModel(msgId=MessageId.ERROR, data={"error": "缺少有效的物品列表"}).model_dump())
            return
            
        # 批量添加物品
        db_manager = DatabaseManager()
        results = []
        
        for item_data in items:
            item_def_id = item_data.get("def_id")
            quantity = item_data.get("quantity", 1)
            attributes = item_data.get("attributes", {})
            
            if not item_def_id:
                results.append({
                    "status": "error",
                    "message": "缺少物品定义ID",
                    "item_data": item_data
                })
                continue
                
            try:
                # 添加超时保护
                item_id = await asyncio.wait_for(
                    db_manager.add_item_to_user(user_id, item_def_id, quantity, attributes),
                    timeout=5.0
                )
                
                results.append({
                    "status": "success",
                    "item_id": item_id,
                    "def_id": item_def_id,
                    "quantity": quantity
                })
                
            except asyncio.TimeoutError:
                logger.error(f"批量添加物品操作超时，用户: {user_id}, defid: {item_def_id}")
                results.append({
                    "status": "error",
                    "message": "操作暂时无法完成，请稍后再试",
                    "error_code": "TIMEOUT",
                    "def_id": item_def_id
                })
                
            except Exception as e:
                logger.error(f"批量添加物品时单项出错: {str(e)}")
                results.append({
                    "status": "error",
                    "message": "处理该物品时出错",
                    "def_id": item_def_id
                })
        
        # 发送批量处理结果
        await websocket.send_json(MessageModel(msgId=MessageId.BATCH_ADD_ITEMS, data={"results": results}).model_dump())
            
    except Exception as e:
        logger.error(f"批量添加物品时出错: {str(e)}")
        # 发送错误响应
        await websocket.send_json({
            "type": "batch_add_items_response",
            "status": "error",
            "message": "服务器处理批量请求时出错"
        })

async def get_items_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """获取用户道具列表"""
    try:
        skip = data.get("skip", 0)
        limit = data.get("limit", 100)          
        items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client, skip, limit)        
        await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items, "skip": skip, "limit": limit}).model_dump(), token)
        return MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items, "skip": skip, "limit": limit}).model_dump()
    except ValueError as e:
        await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"error": str(e)}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": str(e)}).model_dump()
    except Exception as e:
        logger.error(f"获取道具列表失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"error": f"获取道具失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"获取道具失败: {str(e)}"}).model_dump()

async def delete_item_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """删除道具"""
    item_id = data.get("item_id")
    if not item_id:
        await manager.send_personal_message(MessageModel(msgId=MessageId.DELETE_ITEM, data={"error": "道具 ID 不能为空"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": "道具 ID 不能为空"}).model_dump()
    try:
        success = await Item.delete(username, item_id, manager.db_manager.db, manager.db_manager.redis_client)
        if success:
            await manager.send_personal_message(MessageModel(msgId=MessageId.DELETE_ITEM, data={"message": f"道具 {item_id} 删除成功"}).model_dump(), token)
            items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client)
            await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items}).model_dump(), token)
            return MessageModel(msgId=MessageId.DELETE_ITEM, data={"message": f"道具 {item_id} 删除成功"}).model_dump()
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.DELETE_ITEM, data={"error": f"道具 {item_id} 未找到"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": f"道具 {item_id} 未找到"}).model_dump()
    except Exception as e:
        logger.error(f"删除道具失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"删除失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"删除失败: {str(e)}"}).model_dump()

async def increase_item_quantity_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """增加道具数量"""
    defid = data.get("defid")
    amount = data.get("amount", 1)
    if not isinstance(defid, int) or not isinstance(amount, int) or amount <= 0:
        await manager.send_personal_message(MessageModel(msgId=MessageId.INCREASE_ITEM_QUANTITY, data={"error": "定义 ID 或数量无效"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": "定义 ID 或数量无效"}).model_dump()
    try:
        item = await Item.increase_quantity(username, defid, amount, manager.db_manager.db, manager.db_manager.redis_client)
        await manager.send_personal_message(MessageModel(msgId=MessageId.INCREASE_ITEM_QUANTITY, data={"message": f"道具 {defid} 数量增加 {amount}，当前数量: {item.quantity}", "item": item.serialize()}).model_dump(), token)
        items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client)
        await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items}).model_dump(), token)
        return MessageModel(msgId=MessageId.INCREASE_ITEM_QUANTITY, data={"message": f"道具 {defid} 数量增加 {amount}，当前数量: {item.quantity}", "item": item.serialize()}).model_dump()
    except ValueError as e:
        await manager.send_personal_message(MessageModel(msgId=MessageId.INCREASE_ITEM_QUANTITY, data={"error": str(e)}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": str(e)}).model_dump()
    except Exception as e:
        logger.error(f"增加道具数量失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"增加数量失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"增加数量失败: {str(e)}"}).model_dump()
# 角色信息
async def role_info_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """角色信息"""
    try:
        user = await manager.db_manager.get_user_by_username(username)
        await manager.send_personal_message(MessageModel(msgId=MessageId.ROLE_INFO, data={"user": user.serialize(exclude_fields=["password"])}).model_dump(), token)
        return MessageModel(msgId=MessageId.ROLE_INFO, data={"user": user.serialize()}).model_dump()
    except Exception as e:
        logger.error(f"获取角色信息失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"获取角色信息失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"获取角色信息失败: {str(e)}"}).model_dump()

# 设置昵称
async def set_nickname_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """设置昵称"""
    nickname = data.get("nickname")
    if not nickname:
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "昵称不能为空"}).model_dump(), token)
        return
    try:
        await manager.db_manager.update_user_fields(username, "nickname", nickname)
        await manager.send_personal_message(MessageModel(msgId=MessageId.SET_NICKNAME, data={"message": f"昵称设置成功: {nickname}"}).model_dump(), token) 
        return
    except Exception as e:
        logger.error(f"设置昵称失败，用户: {username}, 错误: {str(e)}")

async def decrease_item_quantity_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """减少道具数量"""
    defid = data.get("defid")
    amount = data.get("amount", 1)
    if not isinstance(defid, int) or not isinstance(amount, int) or amount <= 0:
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "定义 ID 或数量无效"}).model_dump(), token)
        return
    try:
        success = await Item.decrease_quantity(username, defid, amount, manager.db_manager.db, manager.db_manager.redis_client)
        if success:
            await manager.send_personal_message(MessageModel(msgId=MessageId.DECREASE_ITEM_QUANTITY, data={"message": f"道具 {defid} 数量减少 {amount}"}).model_dump(), token)
            items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client)
            await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items}).model_dump(), token)
            return MessageModel(msgId=MessageId.DECREASE_ITEM_QUANTITY, data={"message": f"道具 {defid} 数量减少 {amount}"}).model_dump()
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"道具 {defid} 未找到或数量不足"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": f"道具 {defid} 未找到或数量不足"}).model_dump()
    except ValueError as e:
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": str(e)}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": str(e)}).model_dump()
    except Exception as e:
        logger.error(f"减少道具数量失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"减少数量失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"减少数量失败: {str(e)}"}).model_dump()

async def broadcast_message_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """广播消息"""
    try:
        content = data.get("content")
        if not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "广播内容不能为空"}).model_dump(), token)
            return
        
        # 创建广播消息
        message = MessageModel(msgId=MessageId.BROADCAST_MESSAGE, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 使用改进的广播机制
        await manager.broadcast(message, priority=1)  # 广播消息使用更高优先级
        logger.info(f"用户 {username} 发送广播消息: {content[:30]}...")
        return message
    except Exception as e:
        logger.error(f"发送广播消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"广播失败: {str(e)}"}).model_dump(), token)
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"广播失败: {str(e)}"}).model_dump()

async def add_item_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理添加物品请求 - 兼容旧接口"""
    try:
        logger.info(f"收到添加物品请求，用户: {username}, 数据: {data}")
        # 提取物品信息
        defid = data.get("defid")
        quantity = data.get("quantity", 1)
        attributes = data.get("attributes", {})
        
        if not isinstance(defid, int):
            error_msg = "物品定义ID必须是整数"
            logger.warning(f"{error_msg}, 用户: {username}, 数据: {data}")
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": error_msg}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": error_msg}).model_dump()
        
        # 创建物品实例
        try:
            item = Item(
                defid=defid,
                quantity=quantity,
                attributes=attributes,
                owner=username,
                type="item",
                created_at=datetime.now()
            )
            
            # 保存物品
            result = await item.save(manager.db_manager.db, manager.db_manager.redis_client)
            logger.info(f"物品添加成功，用户: {username}, 物品ID: {result.id}")
            
            # 发送成功响应
            await manager.send_personal_message(MessageModel(msgId=MessageId.ADD_ITEM, data={"message": f"物品 {defid} 添加成功，ID: {result.id}", "item": result.serialize()}).model_dump(), token)
            
            # 刷新物品列表
            items = await Item.find_by_owner(username, manager.db_manager.db, manager.db_manager.redis_client)
            await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data={"items": items}).model_dump(), token)
            return MessageModel(msgId=MessageId.ADD_ITEM, data={"message": f"物品 {defid} 添加成功，ID: {result.id}", "item": result.serialize()}).model_dump()
        except Exception as e:
            logger.error(f"保存物品失败，用户: {username}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 发送错误响应
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"添加物品失败: {str(e)}"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": f"添加物品失败: {str(e)}"}).model_dump()
    except Exception as e:
        logger.error(f"添加物品处理器错误: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 尝试发送错误响应
        try:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"服务器内部错误: {str(e)}"}).model_dump(), token)
        except Exception as send_error:
            logger.error(f"发送错误响应失败: {str(send_error)}")
            
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"服务器内部错误: {str(e)}"}).model_dump()

async def batch_add_items_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """批量处理添加物品请求 - 兼容消息系统接口"""
    try:
        # 转换参数格式以适配handle_batch_add_items函数
        items = data.get("items", [])
        if not items:
            return MessageModel(msgId=MessageId.ERROR, data={"error": "缺少物品列表"}).model_dump()
            
        # 转换物品格式
        adapted_items = []
        for item in items:
            adapted_items.append({
                "def_id": item.get("defid"),
                "quantity": item.get("quantity", 1),
                "attributes": item.get("attributes", {})
            })
            
        # 构建适配的数据
        adapted_data = {
            "items": adapted_items
        }
        
        # 调用批量处理函数
        await handle_batch_add_items(manager, websocket, adapted_data, username)
        
        # 返回兼容消息系统的响应格式
        return MessageModel(msgId=MessageId.BATCH_ADD_ITEMS, data={"status": "success", "message": "批量物品添加请求已处理"}).model_dump()
    except Exception as e:
        logger.error(f"批量添加物品处理器错误: {str(e)}")
        logger.error(traceback.format_exc())
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"批量添加物品失败: {str(e)}"}).model_dump()