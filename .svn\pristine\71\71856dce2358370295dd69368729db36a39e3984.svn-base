"""
分布式武将服务
基于现有分布式架构实现服务器权威的武将管理系统
"""

import asyncio
import json
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from pymongo import UpdateOne
from bson import ObjectId
import traceback
from general_models import General, GeneralCreateRequest, GeneralUpdateRequest, FormationData, GeneralResponse
from enums import GeneralState, MessageId
from game_database import DatabaseManager
from distributed_lock import DistributedLock
from utils import generate_id, calculate_hash
from service_locator import ServiceLocator
logger = logging.getLogger(__name__)


class GeneralServiceDistributed:
    """分布式武将服务 - 基于现有分布式架构"""
    
    def __init__(self):
        self.db_manager =  None
        self.db = None
        self.cache = None
        
        # 锁超时时间
        self.lock_timeout = 30  # 30秒
        self.operation_timeout = 25  # 25秒
        
        # 集合名称
        self.generals_collection = "generals"
        self.formations_collection = "formations"
        self.templates_collection = "general_templates"
        self.draw_pools_collection = "general_draw_pools"
    def getCacheManager(self):
        db_manager = ServiceLocator.get("db_manager")
        if self.db_manager is None:
            self.db_manager = db_manager
        if self.db is None:
            self.db = db_manager
        if self.cache is None:
            self.cache = db_manager.general_cache
        return self.cache
    def getDbManager(self):
        return ServiceLocator.get("db_manager").db    
    async def _acquire_general_lock(self, player_id: str, general_id: str = None) -> DistributedLock:
        """获取武将操作锁"""
        if general_id:
            lock_key = f"general_lock:{player_id}:{general_id}"
        else:
            lock_key = f"general_lock:{player_id}"
        
        lock = DistributedLock(lock_key, self.lock_timeout)
        acquired = await lock.acquire(blocking=True, timeout=10)
        if not acquired:
            raise Exception(f"无法获取武将操作锁: {lock_key}")
        
        return lock
    
    async def _release_general_lock(self, lock: DistributedLock):
        """释放武将操作锁"""
        await lock.release()
    
    async def _get_general_template(self, template_id: int) -> Optional[Dict]:
        """获取武将模板"""
        try:
            template = await self.getDbManager()[self.templates_collection].find_one({"template_id": template_id})
            return template
        except Exception as e:
            logger.error(f"获取武将模板失败: {template_id}, 错误: {str(e)}")
            return None
    
    async def _create_general_from_template(self, player_id: str, template_id: int, name: str) -> Optional[General]:
        """从模板创建武将"""
        template = await self._get_general_template(template_id)
        if not template:
            return None
        
        # 创建武将实例
        general = General(
            player_id=player_id,
            template_id=template_id,
            name=name,
            avatar=template.get("avatar", "default"),
            general_type=template.get("general_type", "warrior"),
            rarity=template.get("rarity", 1),
            properties=template.get("base_properties", {}),
            skills=template.get("base_skills", [])
        )
        
        return general
    
    # ============= 基础CRUD操作 =============
    
    async def create_general(self, player_id: str, template_id: int, name: str) -> GeneralResponse:
        """创建武将"""
        lock = None
        try:
            # 获取锁
            lock = await self._acquire_general_lock(player_id)
            
            # 检查武将名称是否重复
            existing_generals = await self.get_player_generals(player_id)
            for general in existing_generals:
                if general.name == name:
                    return GeneralResponse(
                        success=False,
                        error="武将名称已存在"
                    )
            
            # 从模板创建武将
            general = await self._create_general_from_template(player_id, template_id, name)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将模板不存在"
                )
            
            # 保存到数据库
            general_dict = general.to_dict()
            result = await self.getDbManager()[self.generals_collection].insert_one(general_dict)
            
            if result.inserted_id:
                # 更新缓存
                await self.getCacheManager().add_general(general_dict)
                
                # 更新玩家武将列表
                await self.getCacheManager().invalidate_player_generals_cache(player_id)
                
                logger.info(f"创建武将成功: {player_id} -> {general.general_id}")
                return GeneralResponse(
                    success=True,
                    data=general_dict
                )
            else:
                return GeneralResponse(
                    success=False,
                    error="数据库插入失败"
                )
                
        except Exception as e:
            logger.error(f"创建武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"创建武将失败: {str(e)}"
            )
        finally:
            if lock:
                await self._release_general_lock(lock)
    
    async def get_general(self, player_id: str, general_id: str) -> Optional[General]:
        """获取武将详情"""
        try:
            # 先从缓存获取
            general_data = await self.getCacheManager().get_general_details(player_id, general_id)
            
            if general_data:
                return General.from_dict(general_data)
            
            # 缓存未命中，从数据库获取
            general_doc = await self.getDbManager()[self.generals_collection].find_one({
                "player_id": player_id,
                "general_id": general_id
            })
            
            if general_doc:
                general = General.from_dict(general_doc)
                # 更新缓存
                await self.getCacheManager().set_general_details(player_id, general_id, general_doc)
                return general
            
            return None
            
        except Exception as e:
            logger.error(f"获取武将失败: {player_id}:{general_id}, 错误: {str(e)}")
            return None
    
    async def get_player_generals(self, player_id: str) -> List[General]:
        """获取玩家武将列表"""
        try:
            # 先从缓存获取
            generals_data = await self.getCacheManager().get_player_generals(player_id)
            
            if generals_data:
                return [General.from_dict(general_data) for general_data in generals_data]
            
            # 缓存未命中，从数据库获取
            cursor = self.getDbManager()[self.generals_collection].find({"player_id": player_id})
            generals = []
            
            async for general_doc in cursor:
                generals.append(General.from_dict(general_doc))
            
            # 更新缓存
            if generals:
                generals_data = [general.to_dict() for general in generals]
                await self.getCacheManager().sync_generals_to_cache(player_id, generals_data)
            
            return generals
            
        except Exception as e:
            logger.error(f"获取玩家武将列表失败: {player_id}, 错误: {str(e)}")
            return []
    
    async def update_general(self, general: General) -> bool:
        """更新武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(general.player_id, general.general_id)
            
            # 更新更新时间
            general.updated_at = datetime.now()
            
            # 更新数据库
            result = await self.getDbManager()[self.generals_collection].update_one(
                {"player_id": general.player_id, "general_id": general.general_id},
                {"$set": general.to_dict()}
            )
            
            if result.modified_count > 0:
                # 更新缓存
                await self.getCacheManager().update_general(general.to_dict())
                # 使相关缓存失效
                await self.getCacheManager().invalidate_general_cache(general.player_id, general.general_id)
                
                logger.info(f"更新武将成功: {general.player_id}:{general.general_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新武将失败: {str(e)}")
            return False
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def delete_general(self, player_id: str, general_id: str) -> bool:
        """删除武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 检查武将是否存在
            general = await self.get_general(player_id, general_id)
            if not general:
                return False
            
            # 检查武将状态
            if general.state != GeneralState.IDLE:
                return False
            
            # 从数据库删除
            result = await self.getDbManager()[self.generals_collection].delete_one({
                "player_id": player_id,
                "general_id": general_id
            })
            
            if result.deleted_count > 0:
                # 更新缓存
                await self.getCacheManager().delete_general(general_id, player_id)
                
                logger.info(f"删除武将成功: {player_id}:{general_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除武将失败: {str(e)}")
            return False
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    # ============= 武将操作 =============
    
    async def draw_general(self, player_id: str, package_id: int = 1) -> GeneralResponse:
        """抽卡 - 使用现有配置系统"""
        lock = None
        try:
            # 获取锁
            lock = await self._acquire_general_lock(player_id)
            
            # 导入配置
            from config import config
            
            # 获取卡包配置
            package = config.get_card_package_by_id(package_id)
            if not package:
                return GeneralResponse(
                    success=False,
                    error=f"卡包不存在: {package_id}"
                )
            
            # 获取抽卡池
            draw_pool = config.get_draw_pool_by_package(package.get("cfg_id"))
            if not draw_pool:
                return GeneralResponse(
                    success=False,
                    error=f"卡池为空: {package_id}"
                )
            
            # 根据期望值抽取武将模板
            import random
            
            # 计算总期望值
            total_expect = sum(item.get("expect", 0) for item in draw_pool)
            if total_expect <= 0:
                return GeneralResponse(
                    success=False,
                    error="卡池配置错误"
                )
            
            # 随机抽取
            rand_value = random.randint(1, total_expect)
            current_expect = 0
            selected_item = None
            
            for item in draw_pool:
                current_expect += item.get("expect", 0)
                if rand_value <= current_expect:
                    selected_item = item
                    break
            
            if not selected_item:
                selected_item = draw_pool[0]  # 默认选择第一个
            
            # 获取武将模板
            template = selected_item.get("template")
            if not template:
                return GeneralResponse(
                    success=False,
                    error="武将模板不存在"
                )
            logger.info(f"template: {template}")
            # 创建武将

            
            # 从模板创建武将
            general = General(
                player_id=player_id,
                template_id=int(template.get("id")),
                name="武将",
                avatar=template.get("avatar", "default"),
                general_type=1,  # 默认类型
                rarity=template.get("star", 1),  # 根据星级确定稀有度
                level=1,
                star=template.get("star", 1),
                properties={
                    "attack": template.get("paramsid", 1) * 10,
                    "defense": template.get("paramsid", 1) * 5,
                    "hp": template.get("paramsid", 1) * 20,
                    "speed": template.get("paramsid", 1) * 2
                },
                skills=[],
                soldiers=[],
                equipment={},
                state=GeneralState.IDLE,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # 保存到数据库
            general_dict = general.to_dict()
            result = await self.getDbManager()[self.generals_collection].insert_one(general_dict)
            general_dict['_id'] = str(general_dict['_id'])
            if result.inserted_id:
                # 更新缓存
                await self.getCacheManager().add_general(general_dict)
                
                logger.info(f"抽卡成功: {player_id} -> {general.general_id}, 卡包: {package_id}")
                return GeneralResponse(
                    success=True,
                    data={
                        "general": general_dict,
                        "package_id": package_id,
                        "package_name": package.get("name", ""),
                        "rarity": general.rarity,
                        "star": general.star
                    }
                )
            else:
                return GeneralResponse(
                    success=False,
                    error="数据库插入失败"
                )
                
        except Exception as e:
            logger.error(f"抽卡失败: {str(traceback.format_exc())}")
            return GeneralResponse(
                success=False,
                error=f"抽卡失败: {str(e)}"
            )
        finally:
            if lock:
                await self._release_general_lock(lock)
    
    async def synthesis_general(self, player_id: str, general_ids: List[str]) -> GeneralResponse:
        """合成武将"""
        lock = None
        try:
            # 获取锁
            lock = await self._acquire_general_lock(player_id)
            
            if len(general_ids) < 2:
                return GeneralResponse(
                    success=False,
                    error="至少需要2个武将进行合成"
                )
            
            # 获取要合成的武将
            generals = []
            for general_id in general_ids:
                general = await self.get_general(player_id, general_id)
                if not general:
                    return GeneralResponse(
                        success=False,
                        error=f"武将不存在: {general_id}"
                    )
                if general.state != GeneralState.IDLE:
                    return GeneralResponse(
                        success=False,
                        error=f"武将状态不允许合成: {general_id}"
                    )
                generals.append(general)
            
            # 检查武将品质是否相同
            rarity = generals[0].rarity
            for general in generals:
                if general.rarity != rarity:
                    return GeneralResponse(
                        success=False,
                        error="只能合成相同品质的武将"
                    )
            
            # 计算合成结果
            # 这里可以根据具体规则实现合成逻辑
            # 简单示例：合成后品质提升一级
            rarity_order = [1,2,3,4]
            current_index = rarity_order.index(rarity)
            
            if current_index >= len(rarity_order) - 1:
                return GeneralResponse(
                    success=False,
                    error="最高品质武将无法继续合成"
                )
            
            new_rarity = rarity_order[current_index + 1]
            
            # 创建新武将（这里简化处理，实际应该根据合成规则创建）
            new_general = General(
                player_id=player_id,
                template_id=f"synthesis_{rarity}_{new_rarity}",
                name=f"合成武将_{generate_id()}",
                rarity=new_rarity,
                level=1,
                star=1
            )
            
            # 删除原武将
            for general_id in general_ids:
                await self.delete_general(player_id, general_id)
            
            # 保存新武将
            general_dict = new_general.to_dict()
            result = await self.getDbManager()[self.generals_collection].insert_one(general_dict)
            
            if result.inserted_id:
                # 更新缓存
                await self.getCacheManager().add_general(general_dict)
                
                logger.info(f"合成武将成功: {player_id} -> {new_general.general_id}")
                return GeneralResponse(
                    success=True,
                    data={
                        "new_general": general_dict,
                        "consumed_generals": general_ids,
                        "old_rarity": rarity,
                        "new_rarity": new_rarity
                    }
                )
            else:
                return GeneralResponse(
                    success=False,
                    error="数据库插入失败"
                )
                
        except Exception as e:
            logger.error(f"合成武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"合成武将失败: {str(e)}"
            )
        finally:
            if lock:
                await self._release_general_lock(lock)
    
    async def level_up_general(self, player_id: str, general_id: str, exp_amount: int) -> GeneralResponse:
        """武将升级"""
        lock = None
        try:
            # 获取锁
            lock = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 添加经验
            level_up_result = general.add_exp(exp_amount)
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"武将升级: {player_id}:{general_id}, 结果: {level_up_result}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "level_up_result": level_up_result
                }
            )
            
        except Exception as e:
            logger.error(f"武将升级失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"武将升级失败: {str(e)}"
            )
        finally:
            if lock:
                await self._release_general_lock(lock)
    
    async def star_up_general(self, player_id: str, general_id: str) -> GeneralResponse:
        """武将升星"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查是否可以升星
            if not general.can_star_up():
                return GeneralResponse(
                    success=False,
                    error="武将不满足升星条件"
                )
            
            # 升星
            general.star += 1
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"武将升星成功: {player_id}:{general_id} -> {general.star}星")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "new_star": general.star
                }
            )
            
        except Exception as e:
            logger.error(f"武将升星失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"武将升星失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def awaken_general(self, player_id: str, general_id: str) -> GeneralResponse:
        """武将觉醒"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查是否可以觉醒
            if not general.can_awaken():
                return GeneralResponse(
                    success=False,
                    error="武将不满足觉醒条件"
                )
            
            # 觉醒
            general.awaken_level += 1
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"武将觉醒成功: {player_id}:{general_id} -> {general.awaken_level}级觉醒")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "new_awaken_level": general.awaken_level
                }
            )
            
        except Exception as e:
            logger.error(f"武将觉醒失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"武将觉醒失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def train_general(self, player_id: str, general_id: str, training_type: str) -> GeneralResponse:
        """武将训练"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查武将状态
            if general.state != GeneralState.IDLE:
                return GeneralResponse(
                    success=False,
                    error="武将状态不允许训练"
                )
            
            # 开始训练
            general.state = GeneralState.TRAINING
            general.last_training = datetime.now()
            
            # 根据训练类型增加属性
            training_bonus = {
                "attack": {"attack": 10},
                "defense": {"defense": 10},
                "speed": {"speed": 5},
                "intelligence": {"intelligence": 5},
                "leadership": {"leadership": 5}
            }
            
            if training_type in training_bonus:
                bonus = training_bonus[training_type]
                for attr, value in bonus.items():
                    if hasattr(general.properties, attr):
                        current_value = getattr(general.properties, attr)
                        setattr(general.properties, attr, current_value + value)
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"武将训练开始: {player_id}:{general_id}, 类型: {training_type}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "training_type": training_type,
                    "training_start_time": general.last_training.isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"武将训练失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"武将训练失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    # ============= 装备管理 =============
    
    async def equip_general(self, player_id: str, general_id: str, equipment_type: str, item_id: str) -> GeneralResponse:
        """装备武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查装备类型
            if equipment_type not in ["weapon", "armor", "accessory"]:
                return GeneralResponse(
                    success=False,
                    error="无效的装备类型"
                )
            
            # 这里应该检查玩家是否拥有该装备
            # 简化处理，直接装备
            
            # 装备
            if equipment_type == "weapon":
                general.equip_weapon(item_id, f"武器_{item_id}", 0, 0)
            elif equipment_type == "armor":
                general.equip_armor(item_id, f"防具_{item_id}", 0, 0)
            elif equipment_type == "accessory":
                general.equip_accessory(item_id, f"饰品_{item_id}", 0, 0)
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"装备武将成功: {player_id}:{general_id}, 装备: {equipment_type}:{item_id}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "equipment_type": equipment_type,
                    "item_id": item_id
                }
            )
            
        except Exception as e:
            logger.error(f"装备武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"装备武将失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def unequip_general(self, player_id: str, general_id: str, equipment_type: str) -> GeneralResponse:
        """卸下装备"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查装备类型
            if equipment_type not in ["weapon", "armor", "accessory"]:
                return GeneralResponse(
                    success=False,
                    error="无效的装备类型"
                )
            
            # 卸下装备
            if equipment_type == "weapon":
                general.unequip_weapon()
            elif equipment_type == "armor":
                general.unequip_armor()
            elif equipment_type == "accessory":
                general.unequip_accessory()
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"卸下装备成功: {player_id}:{general_id}, 装备: {equipment_type}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "equipment_type": equipment_type
                }
            )
            
        except Exception as e:
            logger.error(f"卸下装备失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"卸下装备失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    # ============= 阵容管理 =============
    
    async def deploy_general(self, player_id: str, general_id: str, position: int) -> GeneralResponse:
        """上阵武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查武将状态
            if general.state != GeneralState.IDLE:
                return GeneralResponse(
                    success=False,
                    error="武将状态不允许上阵"
                )
            
            # 检查位置是否被占用
            formation = await self.get_formation(player_id)
            if formation and formation.get("generals"):
                for deployed_general in formation["generals"]:
                    if deployed_general.get("position") == position:
                        return GeneralResponse(
                            success=False,
                            error=f"位置 {position} 已被占用"
                        )
            
            # 上阵武将
            general.deploy(position)
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"上阵武将成功: {player_id}:{general_id} -> 位置{position}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "position": position
                }
            )
            
        except Exception as e:
            logger.error(f"上阵武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"上阵武将失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def retreat_general(self, player_id: str, general_id: str) -> GeneralResponse:
        """下阵武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 检查武将状态
            if general.state != GeneralState.DEPLOYED:
                return GeneralResponse(
                    success=False,
                    error="武将未上阵"
                )
            
            # 下阵武将
            general.retreat()
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"下阵武将成功: {player_id}:{general_id}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict()
                }
            )
            
        except Exception as e:
            logger.error(f"下阵武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"下阵武将失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def get_formation(self, player_id: str) -> Optional[Dict]:
        """获取阵容"""
        try:
            # 先从缓存获取
            formation_data = await self.cache.get_formation_data(player_id)
            if formation_data:
                return formation_data
            
            # 从数据库获取
            formation_doc = await self.getDbManager()[self.formations_collection].find_one({"player_id": player_id})
            
            if formation_doc:
                # 更新缓存
                await self.cache.set_formation_data(player_id, formation_doc)
                return formation_doc
            
            return None
            
        except Exception as e:
            logger.error(f"获取阵容失败: {player_id}, 错误: {str(e)}")
            return None
    
    async def save_formation(self, player_id: str, formation_data: Dict) -> GeneralResponse:
        """保存阵容"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id)
            
            # 验证阵容数据
            generals = formation_data.get("generals", [])
            if len(generals) > 5:  # 假设最多5个位置
                return GeneralResponse(
                    success=False,
                    error="阵容武将数量超出限制"
                )
            
            # 检查武将是否属于该玩家
            for general_info in generals:
                general_id = general_info.get("general_id")
                if general_id:
                    general = await self.get_general(player_id, general_id)
                    if not general:
                        return GeneralResponse(
                            success=False,
                            error=f"武将不存在: {general_id}"
                        )
            
            # 保存到数据库
            formation_doc = {
                "player_id": player_id,
                "formation_id": formation_data.get("formation_id", generate_id()),
                "formation_name": formation_data.get("formation_name", "默认阵容"),
                "generals": generals,
                "is_active": formation_data.get("is_active", True),
                "updated_at": datetime.now()
            }
            
            result = await self.getDbManager()[self.formations_collection].replace_one(
                {"player_id": player_id},
                formation_doc,
                upsert=True
            )
            
            if result.acknowledged:
                # 更新缓存
                await self.cache.set_formation_data(player_id, formation_doc)
                
                logger.info(f"保存阵容成功: {player_id}")
                return GeneralResponse(
                    success=True,
                    data=formation_doc
                )
            else:
                return GeneralResponse(
                    success=False,
                    error="数据库操作失败"
                )
                
        except Exception as e:
            logger.error(f"保存阵容失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"保存阵容失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    # ============= 状态管理 =============
    
    async def change_general_state(self, player_id: str, general_id: str, new_state: GeneralState) -> GeneralResponse:
        """改变武将状态"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 改变状态
            general.change_state(new_state)
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"改变武将状态成功: {player_id}:{general_id} -> {new_state.value}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict(),
                    "new_state": new_state.value
                }
            )
            
        except Exception as e:
            logger.error(f"改变武将状态失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"改变武将状态失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def lock_general(self, player_id: str, general_id: str) -> GeneralResponse:
        """锁定武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 锁定武将
            general.lock()
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"锁定武将成功: {player_id}:{general_id}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict()
                }
            )
            
        except Exception as e:
            logger.error(f"锁定武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"锁定武将失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id)
    
    async def unlock_general(self, player_id: str, general_id: str) -> GeneralResponse:
        """解锁武将"""
        lock_id = None
        try:
            # 获取锁
            lock_id = await self._acquire_general_lock(player_id, general_id)
            
            # 获取武将
            general = await self.get_general(player_id, general_id)
            if not general:
                return GeneralResponse(
                    success=False,
                    error="武将不存在"
                )
            
            # 解锁武将
            general.unlock()
            
            # 更新武将
            success = await self.update_general(general)
            if not success:
                return GeneralResponse(
                    success=False,
                    error="更新武将失败"
                )
            
            logger.info(f"解锁武将成功: {player_id}:{general_id}")
            return GeneralResponse(
                success=True,
                data={
                    "general": general.to_dict()
                }
            )
            
        except Exception as e:
            logger.error(f"解锁武将失败: {str(e)}")
            return GeneralResponse(
                success=False,
                error=f"解锁武将失败: {str(e)}"
            )
        finally:
            if lock_id:
                await self._release_general_lock(lock_id) 