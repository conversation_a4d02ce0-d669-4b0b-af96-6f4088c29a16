[{"id": 100, "name": "连技", "shortname": "连技", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 36, "duration": -1, "params": "100,2001", "desc": "释放技能时100%几率释放胡笳离愁"}, {"id": 101, "name": "下一次技能暴击", "shortname": "真劲", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 38, "duration": 5000, "params": "200.0", "desc": "下一次主动技能暴击 倍率。波动 + 100"}, {"id": 102, "name": "反弹伤害", "shortname": "反伤", "supose": 0, "effect": "buff_009", "show": 1, "debuff": 0, "dispel": 0, "kind": 35, "duration": 5000, "params": "10,100", "desc": "反弹X次Y%伤害"}, {"id": 103, "name": "间隔持续恢复威力血量", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 21, "duration": 6000, "params": "3000,100", "desc": "每隔S秒，回复Y威力的血量(受智力影响)"}, {"id": 104, "name": "间隔持续回血", "shortname": "回复", "supose": 0, "effect": "effect_006", "show": 1, "debuff": 0, "dispel": 0, "kind": 12, "duration": 5000, "params": "3000,5", "desc": "每隔3秒增加5%血量"}, {"id": 105, "name": "被攻击时几率转换百分比伤害为恢复血量", "shortname": "转换", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 7, "duration": -1, "params": "20,50", "desc": "受到时X%几率转换Y%伤害为恢复血量"}, {"id": 106, "name": "被攻击几率减伤", "shortname": "减伤", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 0, "kind": 8, "duration": -1, "params": "35,30", "desc": "受到伤害时X%几率减少Y%伤害"}, {"id": 107, "name": "被攻击几率回复血量", "shortname": "不屈", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 5, "duration": -1, "params": "20,50", "desc": "被攻击X%几率回复Y%血量"}, {"id": 108, "name": "缴械", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 31, "duration": 7000, "desc": "无法普攻"}, {"id": 109, "name": "计穷", "shortname": "计穷", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 33, "duration": 7000, "desc": "无法释放主动战法"}, {"id": 110, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "effect_009", "show": 1, "debuff": 2, "dispel": 1, "kind": 34, "duration": 7000, "desc": "敌我不分"}, {"id": 111, "name": "眩晕", "shortname": "眩晕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 1, "duration": 7000, "desc": "无法行动"}, {"id": 112, "name": "持续掉血1000-2000", "shortname": "DOT", "supose": 1, "show": 1, "debuff": 1, "dispel": 0, "kind": 1200, "duration": 7000, "params": "3000,180,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 113, "name": "缴械", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 2, "kind": 31, "duration": -1, "desc": "无法普攻"}, {"id": 114, "name": "计穷", "shortname": "计穷", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 2, "kind": 33, "duration": -1, "desc": "无法释放主动战法"}, {"id": 115, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "effect_009", "show": 1, "debuff": 2, "dispel": 0, "kind": 34, "duration": 7000, "desc": "敌我不分"}, {"id": 116, "name": "眩晕", "shortname": "眩晕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 1, "duration": 7000, "desc": "无法行动"}, {"id": 117, "name": "连击", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 60, "duration": 10000, "params": "60.0", "desc": "普攻后X%几率再次攻击"}, {"id": 118, "name": "规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 9, "duration": 10000, "params": "50,1", "desc": "X%几率免疫Y次伤害"}, {"id": 119, "name": "洞察", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": 5000, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 120, "name": "武力降低百分比", "shortname": "武力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3000, "duration": 5000, "params": "-10.0", "desc": "武力降低X%"}, {"id": 121, "name": "智力降低百分比", "shortname": "智力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3001, "duration": 5000, "params": "-10.0", "desc": "智力降低X%"}, {"id": 122, "name": "速度降低百分比", "shortname": "速度", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3002, "duration": 5000, "params": "-10.0", "desc": "速度降低X%"}, {"id": 123, "name": "体质降低百分比", "shortname": "体质", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3003, "duration": 5000, "params": "-10.0", "desc": "体质降低X%"}, {"id": 124, "name": "攻击降低百分比", "shortname": "武伤", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3004, "duration": 5000, "params": "-10.0", "desc": "武力伤害降低X%"}, {"id": 125, "name": "法术攻击降低百分比", "shortname": "策攻", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3005, "duration": 5000, "params": "-10.0", "desc": "法术伤害降低X%"}, {"id": 126, "name": "闭月", "shortname": "破甲", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3006, "duration": 8000, "params": "-50.0", "desc": "防御降低X%"}, {"id": 127, "name": "法术防御降低百分比", "shortname": "策防", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3007, "duration": 8000, "params": "-10.0", "desc": "法术防御降低X%"}, {"id": 128, "name": "受攻击降低百分比", "shortname": "受攻", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 1, "dispel": 0, "kind": 3008, "duration": 5000, "params": "-10.0", "desc": "受到武力伤害降低X%"}, {"id": 129, "name": "受法术攻击降低百分比", "shortname": "受策", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 1, "dispel": 0, "kind": 3009, "duration": 5000, "params": "-10.0", "desc": "受到法术伤害降低X%"}, {"id": 130, "name": "血量上限降低百分比", "shortname": "血量", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3010, "duration": 5000, "params": "-10.0", "desc": "血量上限降低X%"}, {"id": 131, "name": "武力降低", "shortname": "武力降低", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3011, "duration": 5000, "params": "-10.0", "desc": "武力降低X"}, {"id": 132, "name": "智力降低", "shortname": "智力降低", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3012, "duration": 5000, "params": "-10.0", "desc": "智力降低X"}, {"id": 133, "name": "速度降低", "shortname": "速度降低", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3013, "duration": 5000, "params": "-10.0", "desc": "速度降低X"}, {"id": 134, "name": "体质降低", "shortname": "体质降低", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3014, "duration": 5000, "params": "-10.0", "desc": "体质降低X%"}, {"id": 135, "name": "武力降低", "shortname": "武力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3011, "duration": 5000, "params": "-10.0", "desc": "武力降低X"}, {"id": 136, "name": "智力降低", "shortname": "智力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3012, "duration": 5000, "params": "-10.0", "desc": "智力降低X"}, {"id": 137, "name": "防御降低", "shortname": "防御降低", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3017, "duration": 5000, "params": "-10.0", "desc": "防御降低X"}, {"id": 138, "name": "法术防御降低", "shortname": "策防降低", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3018, "duration": 5000, "params": "-10.0", "desc": "法术防御降低X"}, {"id": 139, "name": "血量上限降低", "shortname": "血量降低", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3021, "duration": 5000, "params": "-10.0", "desc": "血量上限降低X"}, {"id": 140, "name": "武力提升百分比", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3100, "duration": 5000, "params": "10.0", "desc": "武力提升X%"}, {"id": 141, "name": "智力提升百分比", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3101, "duration": 5000, "params": "10.0", "desc": "智力提升X%"}, {"id": 142, "name": "速度提升百分比", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3102, "duration": 5000, "params": "10.0", "desc": "速度提升X%"}, {"id": 143, "name": "体质提升百分比", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3103, "duration": 15000, "params": "10.0", "desc": "体质提升X%"}, {"id": 144, "name": "攻击提升百分比", "shortname": "武伤", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3104, "duration": 5000, "params": "10.0", "desc": "武力伤害提升X%"}, {"id": 145, "name": "法术攻击提升百分比", "shortname": "策伤", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3105, "duration": 5000, "params": "10.0", "desc": "法术伤害提升X%"}, {"id": 146, "name": "防御提升百分比", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3106, "duration": 5000, "params": "10.0", "desc": "防御提升X%"}, {"id": 147, "name": "法术防御提升百分比", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3107, "duration": 5000, "params": "10.0", "desc": "法术防御提升X%"}, {"id": 148, "name": "受攻击提升百分比", "shortname": "受攻", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3108, "duration": 5000, "params": "10.0", "desc": "受到武力伤害提升X%"}, {"id": 149, "name": "受法术攻击提升百分比", "shortname": "受策", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3109, "duration": 5000, "params": "10.0", "desc": "受到法术伤害提升X%"}, {"id": 150, "name": "血量上限改变提升百分比", "shortname": "血量", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3110, "duration": 5000, "params": "10.0", "desc": "血量上限提升10%"}, {"id": 151, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 5000, "params": "10.0", "desc": "武力提升X"}, {"id": 152, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": 6000, "params": "3000.0", "desc": "智力提升X"}, {"id": 153, "name": "速度提升", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": 5000, "params": "10.0", "desc": "速度提升X"}, {"id": 154, "name": "体质提升", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3114, "duration": 5000, "params": "10.0", "desc": "体质提升X"}, {"id": 155, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 5000, "params": "10.0", "desc": "武力提升X"}, {"id": 156, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": 5000, "params": "10.0", "desc": "智力提升X"}, {"id": 157, "name": "防御提升", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3117, "duration": 5000, "params": "10.0", "desc": "防御提升X"}, {"id": 158, "name": "法术防御提升", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3118, "duration": 5000, "params": "10.0", "desc": "法术防御提升X"}, {"id": 159, "name": "血量上限提升", "shortname": "血量", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3121, "duration": 5000, "params": "10.0", "desc": "血量上限提升X"}, {"id": 160, "name": "每隔多久 攻击降低", "shortname": "攻击", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 100, "duration": 5000, "params": "3000,10,10"}, {"id": 161, "name": "每隔多久 武力减伤", "shortname": "防御", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 101, "duration": 5000, "params": "5000,-6,10"}, {"id": 162, "name": "每隔多久 法术攻击降低", "shortname": "策攻", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 102, "duration": 5000, "params": "3000,10,10"}, {"id": 163, "name": "每隔多久 法术防御降低", "shortname": "策防", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103, "duration": 5000, "params": "3000,10,10"}, {"id": 164, "name": "每隔多久 攻击提升", "shortname": "攻击", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 110, "duration": 5000, "params": "3000,10,10"}, {"id": 165, "name": "每隔多久 防御提升", "shortname": "防御", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 111, "duration": 5000, "params": "3000,10,10"}, {"id": 166, "name": "每隔多久 法术攻击提升", "shortname": "策攻", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 112, "duration": 5000, "params": "3000,10,10"}, {"id": 167, "name": "每隔多久 法术防御提升", "shortname": "策防", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 113, "duration": 5000, "params": "3000,10,10"}, {"id": 168, "name": "使用技能时几率减少该技能CD", "shortname": "减技", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 45, "duration": -1, "params": "50,50", "desc": "使用技能时50%几率 减少该技能50%CD"}, {"id": 169, "name": "普攻无视防御", "shortname": "白刃", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 50, "duration": -1, "desc": "普攻无视防御"}, {"id": 170, "name": "普攻时吸血", "shortname": "吸血", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 20, "duration": 6000, "params": "70.0", "desc": "攻击时吸血X%"}, {"id": 171, "name": "治疗效果减少", "shortname": "减疗", "supose": 0, "show": 1, "debuff": 1, "dispel": 1, "kind": 14, "duration": 5000, "params": "50.0", "desc": "回复效果减少X%"}, {"id": 172, "name": "治疗效果增加", "shortname": "增疗", "supose": 0, "show": 1, "debuff": 0, "dispel": 1, "kind": 15, "duration": 5000, "params": "50.0", "desc": "回复效果增加X%"}, {"id": 173, "name": "受伤害时 先减少护盾。 所受伤害百分比，耐久", "shortname": "护盾", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 1, "kind": 19, "duration": 5000, "params": "20,300,int,2", "desc": "获得Y+智力*U护盾,护盾持续期间减少X%伤害"}, {"id": 174, "name": "血量低于多少的时候,触发某BUFF（次数，血量，BUF）", "shortname": "狂暴", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 30, "duration": 20000, "params": "1,30,139", "desc": "30 当血量低于多少的时候,触发某BUFF（次数，血量，BUF）"}, {"id": 175, "name": "试图释放主动技能时受到伤害(受智力影响)", "shortname": "妖术", "supose": 0, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 70, "duration": 18000, "params": "200,4", "desc": "试图释放主动技能时受到X威力*层数伤害(受智力影响),最多Y层"}, {"id": 176, "name": "仁德", "shortname": "仁德", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 2, "duration": -1, "params": "50,120", "desc": "受到伤害时X%几率回复Y威力血量(受智力影响)"}, {"id": 177, "name": "被攻击时几率添加BUFF", "shortname": "减速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "50,XXX,0自己1对方", "desc": "受到伤害时50%几率添加XXBUFF"}, {"id": 178, "name": "攻击时几率添加BUFF", "shortname": "减速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 99, "duration": -1, "params": "50,XXX,0自己1对方", "desc": "攻击时50%几率添加XXBUFF"}, {"id": 179, "name": "攻击提升", "shortname": "攻击", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3104, "duration": -1, "params": "100.0", "desc": "武力伤害提升X%"}, {"id": 180, "name": "被攻击几率减伤", "shortname": "减伤", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 1, "kind": 8, "duration": 8000, "params": "100,30", "desc": "被攻击时减少Y%伤害"}, {"id": 181, "name": "红颜铁骑", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 60, "duration": -1, "params": "100.0", "desc": "普攻后再次攻击"}, {"id": 182, "name": "连击", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 60, "duration": -1, "params": "60.0", "desc": "普攻后X%几率再次攻击"}, {"id": 183, "name": "魏武强兵", "shortname": "魏武", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3100, "duration": -1, "params": "15.0", "desc": "武力提升X%"}, {"id": 184, "name": "魏武-智", "shortname": "魏武", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3101, "duration": -1, "params": "15.0", "desc": "智力提升X%"}, {"id": 185, "name": "魏武-体", "shortname": "魏武", "supose": 0, "effect": "buff_010", "show": 0, "debuff": 0, "dispel": 0, "kind": 3103, "duration": -1, "params": "15.0", "desc": "体质提升X%"}, {"id": 186, "name": "治疗效果减少", "shortname": "减疗", "supose": 0, "show": 1, "debuff": 1, "dispel": 1, "kind": 14, "duration": 10000, "params": "90.0", "desc": "回复效果减少90%"}, {"id": 187, "name": "计穷", "shortname": "计穷", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 33, "duration": 8000, "desc": "无法释放主动战法"}, {"id": 188, "name": "疾风连击", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 60, "duration": 20000, "params": "100.0", "desc": "普攻后X%几率再次攻击"}, {"id": 189, "name": "规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": 20000, "params": "100,2", "desc": "免疫Y次伤害"}, {"id": 190, "name": "燃烧", "shortname": "燃烧", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 1, "kind": 1000, "duration": 8000, "params": "7000,240,1", "desc": "进入燃烧状态，间隔7秒掉血Y威力血量(受智力影响),如果目标已经处于燃烧状态，立即受到Y威力伤害(受智力影响)"}, {"id": 191, "name": "反击", "shortname": "反击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 3, "duration": 20000, "params": "50.0", "desc": "被攻击时50%几率反击"}, {"id": 192, "name": "反击", "shortname": "反击", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 0, "dispel": 0, "kind": 3, "duration": -1, "params": "50.0", "desc": "被攻击时50%几率反击"}, {"id": 193, "name": "遗志", "shortname": "遗志", "supose": 0, "effect": "effect_006", "show": 0, "debuff": 0, "dispel": 0, "kind": 12, "duration": -1, "params": "5000,5", "desc": "每隔5秒增加Y%血量"}, {"id": 194, "name": "侵略如火", "shortname": "暴击", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 0, "dispel": 0, "kind": 4, "duration": -1, "params": "30,200", "desc": "主动技能造成伤害时,X%几率重击使伤害提升Y%"}, {"id": 195, "name": "巾帼战阵", "shortname": "伤害", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 0, "dispel": 0, "kind": 4, "duration": -1, "params": "100,30", "desc": "主动技能造成伤害时,重击使伤害提升Y%"}, {"id": 196, "name": "其徐如林-防", "shortname": "五胜", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 3106, "duration": 10000, "params": "20.0", "desc": "防御提升X%"}, {"id": 197, "name": "其徐如林-策", "shortname": "策防", "supose": 0, "effect": "buff_010", "show": 0, "debuff": 0, "dispel": 1, "kind": 3107, "duration": 10000, "params": "20.0", "desc": "法术防御提升X%"}, {"id": 198, "name": "其徐如林-血", "shortname": "血量", "supose": 0, "effect": "buff_010", "show": 0, "debuff": 0, "dispel": 1, "kind": 3121, "duration": 10000, "params": "20.0", "desc": "血量上限提升X"}, {"id": 199, "name": "其徐如林-避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": 10000, "params": "50,3", "desc": "受到伤害时X%几率免疫Y次伤害"}, {"id": 200, "name": "险途暗渡-虚弱", "shortname": "虚弱", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 3004, "duration": 7000, "params": "-40.0", "desc": "武力伤害降低X%"}, {"id": 201, "name": "迟智难酬", "shortname": "策攻", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 102, "duration": -1, "params": "5000,10,8", "desc": "每隔S秒法术伤害提升Y%，最多Z次"}, {"id": 202, "name": "霸王渡江", "shortname": "技能", "supose": 0, "effect": "buff_011", "show": 0, "debuff": 1, "dispel": 0, "kind": 6, "duration": -1, "params": "7000,2037", "desc": "每隔S秒Y"}, {"id": 203, "name": "破凰-火", "shortname": "火攻", "supose": 1, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 1010, "duration": 6000, "params": "2000,100,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 204, "name": "破凰-水", "shortname": "水攻", "supose": 1, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 1011, "duration": 6000, "params": "2000,150,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 205, "name": "破凰-妖", "shortname": "恐慌", "supose": 1, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 1012, "duration": 6000, "params": "2000,130,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 206, "name": "余音-增智", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 0, "debuff": 0, "dispel": 1, "kind": 3101, "duration": 8000, "params": "20.0", "desc": "智力提升X%"}, {"id": 207, "name": "余音-减智", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 3001, "duration": 8000, "params": "-20.0", "desc": "智力降低X%"}, {"id": 208, "name": "不动如山", "shortname": "防御", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 3106, "duration": -1, "params": "50.0", "desc": "防御提升X%"}, {"id": 209, "name": "不动如山", "shortname": "策防", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3107, "duration": -1, "params": "50.0", "desc": "法术防御提升X%"}, {"id": 210, "name": "不动如山", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": -1, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 211, "name": "鬼谋", "shortname": "鬼谋", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 10, "duration": 12000, "params": "3000,40,108:109:110:111", "desc": "每隔S秒有40%几率获得缴械，计穷，混乱，眩晕状态(每个状态单独判定)"}, {"id": 212, "name": "密谋定蜀-受物伤降低", "shortname": "减伤", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 1, "kind": 3008, "duration": 8000, "params": "-20,int,-100", "desc": "受到武力伤害降低X%(每100智力提升1%)"}, {"id": 213, "name": "密谋定蜀-受策伤降低", "shortname": "减伤", "supose": 0, "show": 1, "debuff": 0, "dispel": 1, "kind": 3009, "duration": 8000, "params": "-20,int,-100", "desc": "受到法术伤害降低X%(每100智力提升1%)"}, {"id": 214, "name": "密谋定蜀-受物伤提升", "shortname": "易伤", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3108, "duration": 8000, "params": "20,int,100", "desc": "受到武力伤害提升X%(每100智力提升1%)"}, {"id": 215, "name": "密谋定蜀-受策伤提升", "shortname": "易伤", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3109, "duration": 8000, "params": "20,int,100", "desc": "受到法术伤害提升X%(每100智力提升1%)"}, {"id": 216, "name": "风助火势", "shortname": "风势", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "params": "1000,200", "desc": "对[燃烧]状态的敌人造成伤害为Y%"}, {"id": 217, "name": "上将潘凤-眩晕", "shortname": "眩晕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 10, "duration": 3000, "params": "2000,100,111", "desc": "2秒后陷入眩晕状态"}, {"id": 218, "name": "火神-规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": 30000, "params": "50,5", "desc": "受到伤害时50%几率免疫5次伤害"}, {"id": 219, "name": "金匮要略-恢复", "shortname": "恢复", "supose": 0, "show": 1, "debuff": 0, "dispel": 1, "kind": 2, "duration": 24000, "params": "60,160", "desc": "受到伤害时X%几率回复Y威力血量(受智力影响)"}, {"id": 220, "name": "金匮要略-减伤", "shortname": "减伤", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 1, "kind": 8, "duration": 24000, "params": "100,20,int,120", "desc": "受到伤害时减少Y%伤害(每120智力提升1%)"}, {"id": 221, "name": "难知如阴-速", "shortname": "速度", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": 12000, "params": "2000.0", "desc": "速度提升2000"}, {"id": 222, "name": "疮痍累身", "shortname": "疮痍累身", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "50,XXX,0", "desc": "受到伤害时50%几率添加XXBUFF"}, {"id": 223, "name": "符毒", "shortname": "中毒", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 1004, "duration": 12000, "params": "4000,220,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 224, "name": "魏武-速", "shortname": "速度", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": 8000, "params": "500.0", "desc": "速度提升500"}, {"id": 225, "name": "魏武-攻", "shortname": "武力", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 8000, "params": "100.0", "desc": "武力提升X"}, {"id": 226, "name": "复仇业火-伤", "shortname": "受策", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3109, "duration": 10000, "params": "15.0", "desc": "受到法术伤害提升X%"}, {"id": 227, "name": "母仪-规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": -1, "params": "100,2", "desc": "免疫Y次伤害"}, {"id": 228, "name": "母仪-受攻击降低百分比", "shortname": "受攻", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3008, "duration": 40000, "params": "-18.0", "desc": "受到武力伤害降低X%"}, {"id": 229, "name": "母仪-受法术攻击降低百分比", "shortname": "受策", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3009, "duration": 40000, "params": "-18.0", "desc": "受到法术伤害降低X%"}, {"id": 230, "name": "治疗效果减少", "shortname": "减疗", "supose": 0, "show": 1, "debuff": 1, "dispel": 1, "kind": 14, "duration": -1, "params": "90.0", "desc": "回复效果减少90%"}, {"id": 231, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 34, "duration": 10000, "desc": "敌我不分"}, {"id": 232, "name": "12奇策", "shortname": "奇策", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 1, "dispel": 1, "kind": 10, "duration": 10000, "params": "3000,30,233,119", "desc": "每隔S秒有Y%几率获得规避,洞察状态(每个状态单独判定)"}, {"id": 233, "name": "规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": 10000, "params": "100,1", "desc": "免疫Y次伤害"}, {"id": 234, "name": "盲侯奋勇", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 3111, "duration": 500, "params": "500.0", "desc": "武力提升X"}, {"id": 235, "name": "攻击降低百分比", "shortname": "攻击", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 3004, "duration": 8000, "params": "-30.0", "desc": "武力伤害降低X%"}, {"id": 236, "name": "防御降低百分比", "shortname": "破甲", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 3006, "duration": 8000, "params": "-30.0", "desc": "防御降低X%"}, {"id": 237, "name": "虎豹督军", "shortname": "真劲", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 38, "duration": -1, "params": "200.0", "desc": "首次主动技能暴击"}, {"id": 238, "name": "受伤害时 先减少护盾。 所受伤害百分比，耐久", "shortname": "护盾", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 1, "kind": 19, "duration": 6000, "params": "20,200,int,2", "desc": "获得Y+智力*U护盾,护盾持续期间减少X%伤害"}, {"id": 239, "name": "反击", "shortname": "反击", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 0, "dispel": 0, "kind": 3, "duration": -1, "params": "30.0", "desc": "被攻击时X%几率反击"}, {"id": 240, "name": "缴械", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 2, "kind": 31, "duration": -1, "desc": "无法普攻"}, {"id": 241, "name": "法术攻击提升百分比", "shortname": "策伤", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3105, "duration": -1, "params": "20.0", "desc": "法术伤害提升X%"}, {"id": 242, "name": "攻击提升百分比", "shortname": "武伤", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 3104, "duration": 8000, "params": "30.0", "desc": "武力伤害提升X%"}, {"id": 243, "name": "法术攻击提升百分比", "shortname": "策伤", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 3105, "duration": 8000, "params": "30.0", "desc": "法术伤害提升X%"}, {"id": 244, "name": "受攻击降低百分比", "shortname": "受攻", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3008, "duration": -1, "params": "-30.0", "desc": "受到武力伤害降低X%"}, {"id": 245, "name": "受法术攻击降低百分比", "shortname": "受策", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3009, "duration": -1, "params": "-30.0", "desc": "受到法术伤害降低X%"}, {"id": 246, "name": "攻击提升百分比", "shortname": "攻击", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3104, "duration": -1, "params": "30.0", "desc": "攻击提升X%"}, {"id": 247, "name": "被攻击时几率添加BUFF", "shortname": "护主", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "30,248,0", "desc": "受到伤害时X%几率添加受到武力伤害降低70%的状态持续5秒"}, {"id": 248, "name": "受攻击降低百分比", "shortname": "受攻", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3008, "duration": 12000, "params": "-70.0", "desc": "受到武力伤害降低X%"}, {"id": 249, "name": "受法术攻击降低百分比", "shortname": "受策", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3009, "duration": 12000, "params": "-70.0", "desc": "受到法术伤害降低X%"}, {"id": 250, "name": "被攻击时几率添加BUFF", "shortname": "减速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "30,249,0", "desc": "受到伤害时X%几率添加受到法术伤害降低70%的状态持续5秒"}, {"id": 251, "name": "持续掉血1000-2000", "shortname": "溃逃", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 0, "kind": 1002, "duration": 7000, "params": "3000,180,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 252, "name": "血量上限降低百分比", "shortname": "血量", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 3010, "duration": 7000, "params": "-20.0", "desc": "血量上限降低20%"}, {"id": 253, "name": "武力降低百分比", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 3000, "duration": 9000, "params": "-30.0", "desc": "武力降低X%"}, {"id": 254, "name": "反弹伤害", "shortname": "反伤", "supose": 0, "effect": "buff_009", "show": 1, "debuff": 0, "dispel": 0, "kind": 35, "duration": -1, "params": "999,30", "desc": "受到伤害时反弹Y%伤害"}, {"id": 255, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 3111, "duration": 8000, "params": "50.0", "desc": "武力提升X"}, {"id": 256, "name": "血量上限降低百分比", "shortname": "血量", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 3010, "duration": 5000, "params": "-20.0", "desc": "血量上限降低20%"}, {"id": 257, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": 5000, "params": "300.0", "desc": "智力提升300"}, {"id": 258, "name": "洞察", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": -1, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 259, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": -1, "params": "50.0", "desc": "武力提升X"}, {"id": 260, "name": "智力提升百分比", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3101, "duration": -1, "params": "10.0", "desc": "智力提升X%"}, {"id": 261, "name": "被攻击几率减伤", "shortname": "减伤", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 0, "kind": 8, "duration": 40000, "params": "100,20", "desc": "被攻击时减少Y%伤害"}, {"id": 262, "name": "持续掉血1000-2000", "shortname": "溃逃", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 0, "kind": 1002, "duration": 6000, "params": "3000,120,0", "desc": "每间隔S秒掉血Y威力血量(受武力影响)"}, {"id": 263, "name": "灼烧", "shortname": "灼烧", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 0, "kind": 1001, "duration": 4000, "params": "3000,60,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 264, "name": "间隔持续恢复威力血量", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 21, "duration": 6000, "params": "3000,60", "desc": "每隔S秒，回复60威力的血量(受智力影响)"}, {"id": 265, "name": "防御提升", "shortname": "防御", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3117, "duration": -1, "params": "100.0", "desc": "防御提升X"}, {"id": 266, "name": "速度提升", "shortname": "速度", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": -1, "params": "100.0", "desc": "速度提升X"}, {"id": 267, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": -1, "params": "60.0", "desc": "武力提升X"}, {"id": 268, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": -1, "params": "60.0", "desc": "智力提升X"}, {"id": 269, "name": "血量上限提升", "shortname": "血量", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3121, "duration": -1, "params": "300.0", "desc": "血量上限提升X"}, {"id": 270, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": -1, "params": "10.0", "desc": "攻击提升X"}, {"id": 271, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": -1, "params": "10.0", "desc": "智力提升X"}, {"id": 272, "name": "防御提升", "shortname": "防御", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3117, "duration": -1, "params": "10.0", "desc": "防御提升X"}, {"id": 273, "name": "法术防御提升", "shortname": "策防", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3118, "duration": -1, "params": "10.0", "desc": "法术防御提升X"}, {"id": 274, "name": "血量上限提升", "shortname": "血量", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3121, "duration": -1, "params": "10.0", "desc": "血量上限提升X"}, {"id": 275, "name": "断金秘策", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 31, "duration": 22000, "desc": "无法普攻"}, {"id": 276, "name": "反计秘策", "shortname": "计穷", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 33, "duration": 22000, "desc": "无法释放主动战法"}, {"id": 277, "name": "避其锋芒-物理", "shortname": "受攻", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 0, "kind": 3008, "duration": 22000, "params": "-30,int,-100", "desc": "受到武力伤害降低X%(每Z智力提升1%)"}, {"id": 278, "name": "避其锋芒-法术", "shortname": "受策", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3009, "duration": 22000, "params": "-30,int,-100", "desc": "受到法术伤害降低X%(每Z智力提升1%)"}, {"id": 279, "name": "大赏三军-物理", "shortname": "攻击", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3104, "duration": 22000, "params": "30,int,100", "desc": "武力伤害提升X%(每100智力提升1%)"}, {"id": 280, "name": "大赏三军-法术", "shortname": "策攻", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3105, "duration": 22000, "params": "30,int,100", "desc": "法术伤害提升X%(每100智力提升1%)"}, {"id": 281, "name": "无心恋战-物理", "shortname": "攻击", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3004, "duration": 22000, "params": "-30,int,-100", "desc": "武力伤害降低X%(每100智力提升1%)"}, {"id": 282, "name": "无心恋战-法术", "shortname": "策攻", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3005, "duration": 22000, "params": "-30,int,-100", "desc": "法术伤害降低X%(每100智力提升1%)"}, {"id": 283, "name": "神兵-物理", "shortname": "受攻", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3108, "duration": 22000, "params": "20,int,100", "desc": "受到武力伤害提升X%(每100智力提升1%)"}, {"id": 284, "name": "神兵-法术", "shortname": "受策", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3109, "duration": 22000, "params": "20,int,100", "desc": "受到法术伤害提升X%(每100智力提升1%)"}, {"id": 285, "name": "空城计", "shortname": "空城", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 9, "duration": 22000, "params": "40,9999", "desc": "受到伤害时，X%几率免疫伤害"}, {"id": 286, "name": "先驱突击", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 60, "duration": 22000, "params": "100.0", "desc": "普攻后X%几率再次攻击"}, {"id": 287, "name": "不攻", "shortname": "缴械", "supose": 0, "show": 1, "debuff": 2, "dispel": 2, "kind": 31, "duration": -1, "desc": "无法普攻"}, {"id": 288, "name": "不攻", "shortname": "策攻", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 310500, "duration": -1, "params": "30.0", "desc": "法术伤害提升X%"}, {"id": 289, "name": "速战秘策", "shortname": "速度", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": 15000, "params": "2000.0", "desc": "速度提升X"}, {"id": 290, "name": "回天秘策", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 21, "duration": -1, "params": "7000,200", "desc": "每隔S秒，回复Y威力的血量(受智力影响)"}, {"id": 291, "name": "洞察", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": 24000, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 292, "name": "援军恢复", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 21, "duration": -1, "params": "10000,100", "desc": "每隔X秒，回复Y威力的血量(受智力影响)"}, {"id": 293, "name": "鼓舞秘策", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3100, "duration": 24000, "params": "10,int,100", "desc": "武力提升X%(每100智力提升1%)"}, {"id": 294, "name": "鼓舞秘策", "shortname": "智力", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3101, "duration": 24000, "params": "10,int,100", "desc": "智力提升X%(每100智力提升1%)"}, {"id": 295, "name": "鼓舞秘策", "shortname": "血量", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3110, "duration": 24000, "params": "10,int,100", "desc": "血量上限提升X%(每100智力提升1%)"}, {"id": 296, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 24000, "params": "50.0", "desc": "武力提升X"}, {"id": 297, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": 24000, "params": "50.0", "desc": "智力提升X"}, {"id": 298, "name": "防御提升", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 1, "debuff": 0, "dispel": 0, "kind": 3117, "duration": 24000, "params": "50.0", "desc": "防御提升X"}, {"id": 299, "name": "法术防御提升", "shortname": "策防", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3118, "duration": 24000, "params": "50.0", "desc": "法术防御提升X"}, {"id": 300, "name": "武力降低百分比", "shortname": "武力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 1, "kind": 3000, "duration": 24000, "params": "-30,pow,-200", "desc": "武力降低X%(每200武力提升1%)"}, {"id": 301, "name": "眩晕", "shortname": "眩晕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 1, "duration": 1000, "desc": "无法行动"}, {"id": 302, "name": "治疗效果减少", "shortname": "减疗", "supose": 0, "show": 1, "debuff": 1, "dispel": 1, "kind": 14, "duration": 10000, "params": "90.0", "desc": "回复效果减少90%"}, {"id": 303, "name": "兼弱-物理", "shortname": "受攻", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 0, "kind": 3108, "duration": 10000, "params": "20,pow,100", "desc": "受到武力伤害提升X%(每100武力提升1%)"}, {"id": 304, "name": "兼弱-法术", "shortname": "受策", "supose": 0, "show": 1, "debuff": 1, "dispel": 0, "kind": 3109, "duration": 10000, "params": "20,pow,100", "desc": "受到法术伤害提升X%(每100武力提升1%)"}, {"id": 305, "name": "缴械", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 31, "duration": 10000, "desc": "无法普攻"}, {"id": 306, "name": "计穷", "shortname": "计穷", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 33, "duration": 10000, "desc": "无法释放主动战法"}, {"id": 307, "name": "焚烧", "shortname": "灼烧", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 1, "kind": 1001, "duration": 4000, "params": "3000,120,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 308, "name": "风声溃逃", "shortname": "溃逃", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 1002, "duration": 12000, "params": "3000,140,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 309, "name": "恐慌", "shortname": "恐慌", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 1003, "duration": 12000, "params": "3000,70,1", "desc": "每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 310, "name": "养精蓄锐", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 1, "kind": 2101, "duration": 15000, "params": "3000,200", "desc": "每隔S秒，回复Y威力的血量(受智力影响)"}, {"id": 311, "name": "防御降低百分比", "shortname": "破甲", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3006, "duration": 10000, "params": "-60.0", "desc": "防御降低X%"}, {"id": 312, "name": "每隔多久 武力减伤", "shortname": "防御", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 101, "duration": -1, "params": "5000,-6,10", "desc": "受到武力伤害降低Y%,每隔S秒叠加一次，最多叠加Z次"}, {"id": 313, "name": "每隔多久 法术减伤", "shortname": "屏障", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 103, "duration": -1, "params": "5000,-6,10", "desc": "受到法术伤害降低Y%,每隔S秒叠加一次，最多叠加Z次"}, {"id": 314, "name": "深谋远虑", "shortname": "策攻", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 102, "duration": -1, "params": "5000,6,10", "desc": "每隔S秒法术伤害提升Y%,最多叠加Z次"}, {"id": 315, "name": "每隔多久 攻击提升（愈战愈勇）", "shortname": "攻击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 100, "duration": -1, "params": "5000,6,10", "desc": "每隔S秒武力伤害提升Y%,最多叠加Z次"}, {"id": 316, "name": "反击", "shortname": "反击", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 0, "dispel": 0, "kind": 3, "duration": -1, "params": "35.0", "desc": "被攻击时35%几率反击"}, {"id": 317, "name": "青囊", "shortname": "回复", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 2100, "duration": -1, "params": "7000,100", "desc": "每隔S秒，回复Y威力的血量(受智力影响)"}, {"id": 318, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "effect_009", "show": 1, "debuff": 2, "dispel": 1, "kind": 34, "duration": 12000, "desc": "敌我不分"}, {"id": 319, "name": "眩晕", "shortname": "眩晕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 1, "kind": 1, "duration": 8000, "desc": "无法行动"}, {"id": 320, "name": "缴械", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 31, "duration": 16000, "desc": "无法普攻"}, {"id": 321, "name": "试图释放主动技能时受到伤害(受智力影响)", "shortname": "妖术", "supose": 0, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 70, "duration": 22000, "params": "200,4", "desc": "试图释放主动技能时受到X威力*层数伤害(受智力影响),最多Y层"}, {"id": 322, "name": "闭月-智力降低", "shortname": "智力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3001, "duration": 8000, "params": "-30.0", "desc": "智力降低30%"}, {"id": 323, "name": "灼烧", "shortname": "灼烧", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 0, "kind": 1001, "duration": -1, "params": "6000,100,1", "desc": "进入灼烧状态，每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 324, "name": "冰冻", "shortname": "冰冻", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 0, "kind": 1003, "duration": -1, "params": "6000,100,1", "desc": "进入冰冻状态，每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 325, "name": "中毒", "shortname": "中毒", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 0, "kind": 1004, "duration": -1, "params": "6000,100,1", "desc": "进入中毒状态，每间隔S秒掉血Y威力血量(受智力影响)"}, {"id": 326, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 1, "kind": 3112, "duration": 10000, "params": "50.0", "desc": "智力提升X"}, {"id": 327, "name": "将倾-受物伤降低", "shortname": "减伤", "supose": 0, "effect": "buff_009", "show": 1, "debuff": 0, "dispel": 0, "kind": 3008, "duration": 12000, "params": "-40,def,-100", "desc": "受到武力伤害降低X%(每100防御提升1%)"}, {"id": 328, "name": "将倾-受策伤降低", "shortname": "减伤", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 3009, "duration": 12000, "params": "-40,def,-100", "desc": "受到法术伤害降低X%(每100防御提升1%)"}, {"id": 329, "name": "再起", "shortname": "回复", "supose": 0, "effect": "effect_006", "show": 1, "debuff": 0, "dispel": 0, "kind": 12, "duration": -1, "params": "30000,20", "desc": "每隔S秒增加Y%血量"}, {"id": 330, "name": "洞察", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": -1, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 331, "name": "仙术", "shortname": "仙术", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 10, "duration": -1, "params": "12000,40,108:109:110:111", "desc": "每隔S秒有40%几率获得缴械，计穷，混乱，眩晕状态(每个状态单独判定)"}, {"id": 332, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "effect_009", "show": 1, "debuff": 2, "dispel": 0, "kind": 34, "duration": -1, "desc": "敌我不分"}, {"id": 333, "name": " 攻击提升（天下无双）", "shortname": "无双", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 100, "duration": -1, "params": "20000,10,20", "desc": "每隔S秒攻击提升10%,最多叠加20次"}, {"id": 334, "name": "天下无双", "shortname": "无双", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 10, "duration": -1, "params": "20000,100,335", "desc": "每隔S秒获得规避10次伤害状态"}, {"id": 335, "name": "无双-规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": -1, "params": "100,10", "desc": "免疫Y次伤害"}, {"id": 336, "name": "试图释放主动技能时受到伤害(受智力影响)", "shortname": "妖术", "supose": 0, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 1, "kind": 70, "duration": 12000, "params": "500,4", "desc": "试图释放主动技能时受到X威力*层数伤害(受智力影响),最多Y层"}, {"id": 337, "name": "狂骨", "shortname": "狂骨", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 200, "duration": -1, "params": "75,75", "desc": "血量低于75%时候,普攻和技能伤害时吸血Y%"}, {"id": 338, "name": "血量低于多少的时候,触发某BUFF（次数，血量，BUF）", "shortname": "傲将", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 30, "duration": -1, "params": "99,50,337"}, {"id": 339, "name": "复活", "shortname": "重生", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 210, "duration": -1, "params": "1,100", "desc": "受到致死伤害时，复活并回复100%血量,限制1次。"}, {"id": 340, "name": "据守", "shortname": "物免", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 220, "duration": 10000, "desc": "免疫物理攻击"}, {"id": 341, "name": "据守", "shortname": "策免", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 230, "duration": 10000, "desc": "免疫法术攻击"}, {"id": 342, "name": "据守", "shortname": "物免", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 220, "duration": -1, "desc": "免疫物理攻击"}, {"id": 343, "name": "据守", "shortname": "策免", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 230, "duration": -1, "desc": "免疫法术攻击"}, {"id": 344, "name": "法术防御降低百分比", "shortname": "策防", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3007, "duration": -1, "params": "-35.0", "desc": "法术防御降低35%"}, {"id": 345, "name": "据守迎击", "shortname": "迎击", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 240, "duration": -1, "params": "30200.0", "desc": "受到普攻伤害时，有X%几率回复自身Y%威力血量，X%几率移除负面状态，X%获得1次规避状态"}, {"id": 346, "name": "规避", "shortname": "规避", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 1, "kind": 9, "duration": -1, "params": "100,1", "desc": "免疫Y次伤害"}, {"id": 347, "name": "深谋远虑", "shortname": "策攻", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 102, "duration": -1, "params": "10000,10,10", "desc": "每隔S秒法术伤害提升10%,最多叠加10次"}, {"id": 348, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 24000, "params": "300.0", "desc": "武力提升X"}, {"id": 349, "name": "速度提升", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3113, "duration": 24000, "params": "500.0", "desc": "速度提升500"}, {"id": 350, "name": "防御降低百分比", "shortname": "破甲", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 0, "kind": 3006, "duration": 24000, "params": "-50.0", "desc": "防御降低50%"}, {"id": 351, "name": " 攻击提升（天下无双）", "shortname": "无双", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 100, "duration": -1, "params": "1000,5,10000", "desc": "每隔S秒武力伤害提升5%,最多叠加10000次"}, {"id": 352, "name": "鬼吕布", "shortname": "鬼吕", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 10, "duration": -1, "params": "15000,100,119", "desc": "每隔S秒获得持续5秒的洞察状态"}, {"id": 353, "name": "天下无双", "shortname": "无双", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 102, "duration": -1, "params": "1000,5,10000", "desc": "每隔S秒法术伤害提升5%,最多叠加10000次"}, {"id": 354, "name": "胜利", "shortname": "胜利", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 250, "duration": -1, "params": "120000.0", "desc": "120秒后获得战斗胜利。"}, {"id": 355, "name": "无敌", "shortname": "无敌", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 260, "duration": -1, "desc": "免疫伤害"}, {"id": 356, "name": "疲惫", "shortname": "溃逃", "supose": 1, "effect": "buff_010", "show": 1, "debuff": 1, "dispel": 1, "kind": 1002, "duration": 16000, "params": "16000,600,1", "desc": "S秒后受到Y威力伤害(受智力影响)"}, {"id": 357, "name": "威慑", "shortname": "威慑", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 1, "kind": 3000, "duration": 8000, "params": "-10.0", "desc": "武力降低X%"}, {"id": 358, "name": "步兵加成", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 3103, "duration": -1, "params": "20.0", "desc": "体质+20%"}, {"id": 359, "name": "步兵加成", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 3103, "duration": -1, "params": "20.0", "desc": "体质+20%"}, {"id": 360, "name": "步兵加成", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 3103, "duration": -1, "params": "10.0", "desc": "体质+10%"}, {"id": 361, "name": "步兵加成", "shortname": "体质", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 3103, "duration": -1, "params": "5.0", "desc": "体质+5%"}, {"id": 362, "name": "骑兵加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93102, "duration": -1, "params": "20.0", "desc": "速度+20%"}, {"id": 363, "name": "骑兵加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93102, "duration": -1, "params": "20.0", "desc": "速度+20%"}, {"id": 364, "name": "骑兵加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93102, "duration": -1, "params": "10.0", "desc": "速度+10%"}, {"id": 365, "name": "骑兵加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93102, "duration": -1, "params": "5.0", "desc": "速度+5%"}, {"id": 366, "name": "弓兵加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 367, "name": "弓兵加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 368, "name": "弓兵加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93100, "duration": -1, "params": "5.0", "desc": "武力+5%"}, {"id": 369, "name": "弓兵加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93100, "duration": -1, "params": "2.5", "desc": "武力+2.5%"}, {"id": 370, "name": "弓兵加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 371, "name": "弓兵加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 372, "name": "弓兵加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93101, "duration": -1, "params": "5.0", "desc": "智力+5%"}, {"id": 373, "name": "弓兵加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 93101, "duration": -1, "params": "2.5", "desc": "智力+2.5%"}, {"id": 374, "name": "蜀国加成", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83106, "duration": -1, "params": "20.0", "desc": "防御+20%"}, {"id": 375, "name": "蜀国加成", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83106, "duration": -1, "params": "10.0", "desc": "防御+10%"}, {"id": 376, "name": "蜀国加成", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83106, "duration": -1, "params": "10.0", "desc": "防御+10%"}, {"id": 377, "name": "蜀国加成", "shortname": "防御", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83106, "duration": -1, "params": "5.0", "desc": "防御+5%"}, {"id": 378, "name": "蜀国加成", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83107, "duration": -1, "params": "10.0", "desc": "策防+10%"}, {"id": 379, "name": "蜀国加成", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83107, "duration": -1, "params": "10.0", "desc": "策防+10%"}, {"id": 380, "name": "蜀国加成", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83107, "duration": -1, "params": "5.0", "desc": "策防+5%"}, {"id": 381, "name": "蜀国加成", "shortname": "策防", "supose": 0, "effect": "buff_005", "show": 0, "debuff": 0, "dispel": 0, "kind": 83107, "duration": -1, "params": "2.5", "desc": "策防+2.5%"}, {"id": 382, "name": "魏国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 383, "name": "魏国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 384, "name": "魏国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83101, "duration": -1, "params": "5.0", "desc": "智力+5%"}, {"id": 385, "name": "魏国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83101, "duration": -1, "params": "2.5", "desc": "智力+2.5%"}, {"id": 386, "name": "魏国加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83102, "duration": -1, "params": "10.0", "desc": "速度+10%"}, {"id": 387, "name": "魏国加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83102, "duration": -1, "params": "10.0", "desc": "速度+10%"}, {"id": 388, "name": "魏国加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83102, "duration": -1, "params": "5.0", "desc": "速度+5%"}, {"id": 389, "name": "魏国加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83102, "duration": -1, "params": "2.5", "desc": "速度+2.5%"}, {"id": 390, "name": "吴国加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 391, "name": "吴国加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 392, "name": "吴国加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83100, "duration": -1, "params": "5.0", "desc": "武力+5%"}, {"id": 393, "name": "吴国加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 83100, "duration": -1, "params": "2.5", "desc": "武力+2.5%"}, {"id": 394, "name": "吴国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 395, "name": "吴国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73101, "duration": -1, "params": "10.0", "desc": "智力+10%"}, {"id": 396, "name": "吴国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73101, "duration": -1, "params": "5.0", "desc": "智力+5%"}, {"id": 397, "name": "吴国加成", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73101, "duration": -1, "params": "2.5", "desc": "智力+2.5%"}, {"id": 398, "name": "群雄加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73102, "duration": -1, "params": "10.0", "desc": "速度+10%"}, {"id": 399, "name": "群雄加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73102, "duration": -1, "params": "10.0", "desc": "速度+10%"}, {"id": 400, "name": "群雄加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73102, "duration": -1, "params": "5.0", "desc": "速度+5%"}, {"id": 401, "name": "群雄加成", "shortname": "速度", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73102, "duration": -1, "params": "2.5", "desc": "速度+2.5%"}, {"id": 402, "name": "群雄加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 403, "name": "群雄加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73100, "duration": -1, "params": "10.0", "desc": "武力+10%"}, {"id": 404, "name": "群雄加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73100, "duration": -1, "params": "5.0", "desc": "武力+5%"}, {"id": 405, "name": "群雄加成", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 0, "debuff": 0, "dispel": 0, "kind": 73100, "duration": -1, "params": "2.5", "desc": "武力+2.5%"}, {"id": 406, "name": "攻击时几率添加BUFF", "shortname": "孤胆", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 99, "duration": -1, "params": "20,407,0", "desc": "普通攻击时20%几率武力提升800，持续5秒"}, {"id": 407, "name": "武力提升", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": 5000, "params": "800.0", "desc": "武力提升800"}, {"id": 408, "name": "受伤害时 先减少护盾。 所受伤害百分比，耐久", "shortname": "护盾", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 19, "duration": -1, "params": "35,300,int,5", "desc": "获得Y+智力*U护盾,护盾持续期间减少X%伤害"}, {"id": 409, "name": "武力降低百分比指挥", "shortname": "武力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103000, "duration": 5000, "params": "-10.0", "desc": "武力降低X%"}, {"id": 410, "name": "智力降低百分比指挥", "shortname": "智力", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103001, "duration": 5000, "params": "-10.0", "desc": "智力降低X%"}, {"id": 411, "name": "速度降低百分比指挥", "shortname": "速度", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103002, "duration": 5000, "params": "-10.0", "desc": "速度降低X%"}, {"id": 412, "name": "体质降低百分比指挥", "shortname": "体质", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103003, "duration": 5000, "params": "-10.0", "desc": "体质降低X%"}, {"id": 413, "name": "攻击降低百分比指挥", "shortname": "攻击", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103004, "duration": 5000, "params": "-10.0", "desc": "武力伤害降低X%"}, {"id": 414, "name": "法术攻击降低百分比指挥", "shortname": "策攻", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103005, "duration": 5000, "params": "-10.0", "desc": "法术伤害降低X%"}, {"id": 415, "name": "武力降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103000, "duration": -1, "params": "-10.0", "desc": "武力降低X%"}, {"id": 416, "name": "智力降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103001, "duration": -1, "params": "-10.0", "desc": "智力降低X%"}, {"id": 417, "name": "体质降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103003, "duration": -1, "params": "-10.0", "desc": "体质降低X%"}, {"id": 418, "name": "伏击", "shortname": "伏击", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 0, "dispel": 0, "kind": 114, "duration": -1, "params": "114,14,50,111,1", "desc": "造成战法伤害时,如果敌方处于减疗状态,敌方将有50%几率陷入5秒眩晕状态"}, {"id": 419, "name": "速度降低百分比", "shortname": "迟缓", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 1, "kind": 3002, "duration": 8000, "params": "-50.0", "desc": "速度降低X%"}, {"id": 420, "name": "速度提升", "shortname": "乱舞", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 1, "kind": 3113, "duration": 8000, "params": "3000.0", "desc": "速度提升3000"}, {"id": 421, "name": "温候之勇", "shortname": "无双", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 0, "dispel": 0, "kind": 114, "duration": -1, "params": "114,115,40,420,0", "desc": "造成战法伤害时,如果敌方处于破胆状态,自己将有40%几率获得5秒乱舞速度提升3000"}, {"id": 422, "name": "疾风突击", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": -1, "params": "100,dex,10", "desc": "武力提升X(每100速度提升Z)"}, {"id": 423, "name": "破胆", "shortname": "破胆", "supose": 0, "show": 1, "debuff": 1, "dispel": 1, "kind": 115, "duration": 12000, "params": "30.0", "desc": "[破胆]受到普攻和追击战法伤害提升X%"}, {"id": 424, "name": "速度降低百分比", "shortname": "迟缓", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3002, "duration": 24000, "params": "-50.0", "desc": "速度降低X%"}, {"id": 425, "name": "胜利", "shortname": "胜利", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 0, "kind": 250, "duration": -1, "params": "300000.0", "desc": "300秒后获得战斗胜利。"}, {"id": 426, "name": "八门金锁", "shortname": "缴械", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 31, "duration": -1, "params": "45.0", "desc": "普攻时X%几率无法普攻"}, {"id": 427, "name": "仁德", "shortname": "仁德", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 2, "duration": -1, "params": "70,120,30,428", "desc": "受到伤害时X%几率回复Y威力血量(受智力影响),如果判定失败则30%附加减伤60%的状态，持续5秒"}, {"id": 428, "name": "被攻击几率减伤", "shortname": "减伤", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 0, "kind": 8, "duration": 5000, "params": "100,60", "desc": "被攻击时减少60%伤害"}, {"id": 429, "name": "武力降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103000, "duration": -1, "params": "-15.0", "desc": "武力降低15%"}, {"id": 430, "name": "智力降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103001, "duration": -1, "params": "-15.0", "desc": "智力降低15%"}, {"id": 431, "name": "体质降低百分比指挥", "shortname": "魏武", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 103003, "duration": -1, "params": "-15.0", "desc": "体质降低15%"}, {"id": 432, "name": "威慑", "shortname": "威慑", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 1, "kind": 3000, "duration": 10000, "params": "-25.0", "desc": "武力降低25%"}, {"id": 433, "name": "速度降低", "shortname": "减速", "supose": 0, "effect": "buff_006", "show": 1, "debuff": 1, "dispel": 0, "kind": 3013, "duration": 12000, "params": "-600,int,-10", "desc": "速度降低600(每100智力降低10)"}, {"id": 434, "name": "疾风突击", "shortname": "武力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3111, "duration": -1, "params": "100,dex,15", "desc": "武力提升X(每100速度提升Z)"}, {"id": 435, "name": "风助火势", "shortname": "风势", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "params": "1000,250", "desc": "攻击时，目标如果存在[燃烧]状态对其造成伤害提升150%"}, {"id": 436, "name": "虎步", "shortname": "虎步", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 10, "duration": -1, "params": "6000,100,437", "desc": "每隔S秒Y%几率获得真劲：下次主动战法伤害翻倍"}, {"id": 437, "name": "下一次技能暴击", "shortname": "真劲", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 38, "duration": 10000, "params": "200.0", "desc": "下一次主动技能暴击 倍率。波动 + 100"}, {"id": 438, "name": "难知如阴", "shortname": "谋定", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 1, "kind": 39, "duration": -1, "params": "1.0", "desc": "下一个主动技能发动率为100%，并且跳过施法时间"}, {"id": 439, "name": "头目", "shortname": "头目", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 999, "duration": -1, "params": "2000,4,337,338,339", "desc": "每间隔S秒，召唤Y个护卫"}, {"id": 440, "name": "伤害提升", "shortname": "伤害", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 4000, "duration": 22000, "params": "30", "desc": "伤害提升X%"}, {"id": 441, "name": "消除增益", "shortname": "消散", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 1, "dispel": 0, "kind": 51, "duration": 0, "params": "100", "desc": "X%几率消除增益状态"}, {"id": 442, "name": "净化减益", "shortname": "净化", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 52, "duration": 0, "params": "100", "desc": "X%几率消除减益状态"}, {"id": 443, "name": "免疫缴械", "shortname": "免疫", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 53, "duration": 10, "desc": "免疫缴械状态"}, {"id": 444, "name": "免疫计穷", "shortname": "免疫", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 54, "duration": 10, "desc": "免疫计穷状态"}, {"id": 445, "name": "百战", "shortname": "百战", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 55, "duration": 10, "params": "1,2,10", "desc": "释放主动战法时武力提升Y%,智力提升Y%(最多Z次)"}, {"id": 446, "name": "识破", "shortname": "识破", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 56, "duration": 5000, "params": "100.0", "desc": "X%几率免疫缴械，计穷，混乱，眩晕状态"}, {"id": 447, "name": "疯狂", "shortname": "疯狂", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 57, "duration": -1, "params": "1,10,10", "desc": "每击杀一个敌人，伤害提升Y%(最多Z次)"}, {"id": 448, "name": "坚毅", "shortname": "坚毅", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 58, "duration": -1, "params": "10,20", "desc": "受到暴击时，恢复X%威力血量(受智力影响),并且Y%几率获得洞察状态,持续5秒"}, {"id": 449, "name": "援护", "shortname": "援护", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 59, "duration": -1, "desc": "援护友军，帮助友军抵挡普通攻击"}, {"id": 450, "name": "灵动", "shortname": "灵动", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 10, "duration": -1, "params": "7000,100,346", "desc": "每隔S秒获得1次规避"}, {"id": 451, "name": "万箭秘策", "shortname": "万箭", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 61, "duration": -1, "params": "2132,2", "desc": "X技能冷却降低Y秒"}, {"id": 452, "name": "万箭齐发", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 6, "duration": -1, "params": "7000,2037", "desc": "每隔S秒Y"}, {"id": 453, "name": "诡道", "shortname": "诡道", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 63, "duration": -1, "params": "2004,227", "desc": "当张角释放黄天雷霆时,张角自己获得规避状态，可抵御2次伤害"}, {"id": 454, "name": "攻击提升", "shortname": "攻击", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 30104, "duration": -1, "params": "100.0", "desc": "武力伤害提升X%"}, {"id": 455, "name": "规避", "shortname": "极闪", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 9, "duration": -1, "params": "50,9999", "desc": "X%几率闪避伤害"}, {"id": 456, "name": "阶段", "shortname": "阶段", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 64, "duration": -1, "params": "30,1,538", "desc": "血量低于X%的时候触发特殊状态"}, {"id": 457, "name": "应机", "shortname": "应机", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 65, "duration": -1, "limit": 1017, "params": "20,1017", "desc": "当敌人受到治疗时，Y有X%的几率释放主战法"}, {"id": 458, "name": "穷追猛打", "shortname": "连击", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 60, "duration": 22000, "params": "100.0", "desc": "普攻后再次攻击"}, {"id": 459, "name": "绝代双姣", "shortname": "魅惑", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 2, "dispel": 0, "kind": 10, "duration": 3000, "params": "20000,100,111", "desc": "S秒后陷入混乱状态持续10秒"}, {"id": 460, "name": "混乱", "shortname": "混乱", "supose": 0, "effect": "effect_009", "show": 1, "debuff": 2, "dispel": 1, "kind": 34, "duration": 10000, "desc": "敌我不分"}, {"id": 461, "name": "被攻击时几率添加BUFF", "shortname": "始计", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "100,462,0,8000", "desc": "受到伤害时X%获得持续4秒的免疫缴械，眩晕，计穷，混乱状态,内置8秒冷却"}, {"id": 462, "name": "洞察", "shortname": "洞察", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": 4000, "desc": "免疫缴械，眩晕，计穷，混乱状态"}, {"id": 463, "name": "霸王渡江", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 0, "debuff": 1, "dispel": 0, "kind": 666, "duration": -1, "params": "7000,2037", "desc": "每隔S秒Y"}, {"id": 464, "name": "冢虎", "shortname": "冢虎", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 66, "duration": -1, "params": "1000,20", "desc": "对敌人造成伤害时，每高X智力,伤害提升Y%"}, {"id": 465, "name": "偃月", "shortname": "偃月", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 67, "duration": -1, "desc": "关羽的主战法青龙偃月范围变为敌方全体"}, {"id": 466, "name": "烈弓", "shortname": "烈弓", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 68, "duration": -1, "params": "30,50", "desc": "对血量低于X%的敌人造成伤害提升Y%"}, {"id": 467, "name": "鹰眼", "shortname": "鹰眼", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 69, "duration": -1, "params": "30,50", "desc": "对血量低于X%的敌人暴击率提升Y%"}, {"id": 468, "name": "虎豹", "shortname": "虎豹", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 164, "duration": -1, "params": "20", "desc": "我方阵营每有一个单位发动虎豹突袭，拥有该状态的单位伤害提升X%"}, {"id": 469, "name": "白耗", "shortname": "白耗", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 71, "duration": -1, "params": "100,4055", "desc": "我方白耗兵受到治疗时，立即发动一次螺旋突击，拥有该状态的所有单位防御提升X,"}, {"id": 470, "name": "被攻击时几率添加BUFF", "shortname": "反制", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 98, "duration": -1, "params": "30,187,1", "desc": "受到伤害时X%几率给敌人施加计穷"}, {"id": 471, "name": "飞将", "shortname": "飞将", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 72, "duration": -1, "params": "1000,10", "desc": "每X速度提高Y%伤害"}, {"id": 472, "name": "智力提升", "shortname": "隐忍", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3212, "duration": -1, "limit": 1047, "params": "5000,200,20", "desc": "W每隔S秒提升Y智力，最多提升Z次"}, {"id": 473, "name": "桃园", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 666, "duration": -1, "params": "7000,2037", "desc": "每隔S秒Y"}, {"id": 474, "name": "攻击时几率添加BUFF", "shortname": "凤仪", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 73, "duration": -1, "params": "40,152,0,10000", "desc": "释放主动主战法时X%几率获得智力提高3000，持续6秒(内置冷却10秒)"}, {"id": 475, "name": "法术攻击提升百分比", "shortname": "策伤", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 33105, "duration": 5000, "params": "10.0", "desc": "法术伤害提升X%"}, {"id": 476, "name": "曲顾", "shortname": "曲顾", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "limit": 1024, "params": "31,150", "desc": "W对[缴械]状态的敌人造成伤害为Y%"}, {"id": 477, "name": "百鸟", "shortname": "百鸟", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1014, "params": "230", "desc": "W主技能范围变为R"}, {"id": 478, "name": "凤雏", "shortname": "受攻", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 1, "kind": 4108, "duration": 22000, "params": "20,int,100", "desc": "受到武力伤害提升X%(每100智力提升1%)"}, {"id": 479, "name": "凤雏", "shortname": "受策", "supose": 0, "effect": "buff_015", "show": 1, "debuff": 1, "dispel": 1, "kind": 4109, "duration": 22000, "params": "20,int,100", "desc": "受到法术伤害提升X%(每100智力提升1%)"}, {"id": 480, "name": "被攻击时几率添加BUFF", "shortname": "霜甲", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 75, "duration": -1, "params": "15,4316", "desc": "受到伤害时X%几率Y"}, {"id": 481, "name": "元戎", "shortname": "元戎", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 76, "duration": -1, "params": "4322,4324", "desc": "我方阵营元戎弩兵发动X时，其他元戎弩兵也立即发动Y"}, {"id": 482, "name": "识破", "shortname": "识破", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 56, "duration": -1, "limit": 1026, "params": "50", "desc": "WX%几率免疫缴械，计穷，混乱，眩晕状态"}, {"id": 483, "name": "百出", "shortname": "百出", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 77, "duration": -1, "limit": 1023, "params": "5.0", "desc": "W主动主战法发动技能提升X%"}, {"id": 484, "name": "伤害提升", "shortname": "伤害", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 4000, "duration": -1, "limit": 1045, "params": "100", "desc": "W伤害提升X%"}, {"id": 485, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": -1, "limit": 1024, "params": "1000.0", "desc": "W智力提升X"}, {"id": 486, "name": "智力提升", "shortname": "智力", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3112, "duration": -1, "limit": 1053, "params": "1000.0", "desc": "W智力提升X"}, {"id": 487, "name": "忠良", "shortname": "忠良", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 78, "duration": -1, "limit": 1043, "params": "3.0", "desc": "W可持续增加性BUFF最大叠加次数增加X"}, {"id": 488, "name": "劫营", "shortname": "劫营", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 79, "duration": -1, "limit": 1027, "params": "80.0", "desc": "W锦帆奇袭重击伤害上限增加X%"}, {"id": 489, "name": "护主", "shortname": "护主", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 80, "duration": 7000, "params": "50.0", "desc": "援护友军，帮助友军分担X%伤害"}, {"id": 490, "name": "试图释放追击", "shortname": "妖术", "supose": 0, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 0, "kind": 81, "duration": -1, "params": "200.0", "desc": "试图释放追击技能时受到X威力伤害(受智力影响)"}, {"id": 491, "name": "免疫眩晕", "shortname": "清醒", "supose": 0, "effect": "buff_002", "show": 1, "debuff": 0, "dispel": 0, "kind": 82, "duration": -1, "params": "200.0", "desc": "免疫眩晕"}, {"id": 492, "name": "忠胆", "shortname": "忠胆", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 83, "duration": -1, "limit": 1014, "params": "100,4374", "desc": "W释放主技能时,X几率Y"}, {"id": 493, "name": "伤害提升", "shortname": "武", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 4000, "duration": -1, "limit": 1019, "params": "20", "desc": "W伤害提升X%"}, {"id": 494, "name": "武圣", "shortname": "圣", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 83, "duration": -1, "limit": 1019, "params": "50,0", "desc": "W释放主动主技能时,X几率再次释放主技能"}, {"id": 495, "name": "伤害提升", "shortname": "锐气", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 4000, "duration": -1, "limit": 1008, "params": "20", "desc": "W伤害提升X%"}, {"id": 496, "name": "穿弓", "shortname": "穿弓", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1042, "params": "220", "desc": "W主技能范围变为R"}, {"id": 497, "name": "咆哮", "shortname": "咆哮", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 84, "duration": -1, "limit": 1015, "params": "2", "desc": "W主技能施法时间减少X秒"}, {"id": 498, "name": "狼顾", "shortname": "狼顾", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 78, "duration": -1, "limit": 1047, "params": "5.0", "desc": "W可持续增加性BUFF最大叠加次数增加X"}, {"id": 499, "name": "胜机", "shortname": "胜机", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1154, "params": "110", "desc": "W主技能范围变为R"}, {"id": 500, "name": "枭雄", "shortname": "枭雄", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": -1, "limit": 1016, "desc": "W免疫缴械，眩晕，计穷，混乱状态"}, {"id": 501, "name": "据守", "shortname": "据守", "supose": 0, "effect": "buff_014", "show": 1, "debuff": 0, "dispel": 0, "kind": 8, "duration": -1, "limit": 1153, "params": "50,50", "desc": "W被攻击时X%几率减少Y%伤害"}, {"id": 502, "name": "王佐", "shortname": "王佐", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "limit": 1017, "params": "14,150", "desc": "W对[减疗]状态的敌人造成伤害为Y%"}, {"id": 503, "name": "血战", "shortname": "血战", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 85, "duration": -1, "params": "10,15", "desc": "血量百分比每少X%,伤害提升Y%"}, {"id": 504, "name": "仁义", "shortname": "仁义", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 86, "duration": -1, "limit": 1010, "params": "20.0", "desc": "W治疗效果提升X%"}, {"id": 505, "name": "相间", "shortname": "相间", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 87, "duration": -1, "params": "4406,34,200,110", "desc": "X命中的目标如果存在[混乱]状态，则造成伤害为Z%,否则为目标施加持续7秒的[混乱]状态"}, {"id": 506, "name": "神速", "shortname": "神速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 88, "duration": -1, "limit": 1000, "params": "dex,5", "desc": "W普攻时，附带X乘以Y真实伤害"}, {"id": 507, "name": "神速", "shortname": "神速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 88, "duration": -1, "limit": 1061, "params": "dex,5", "desc": "W普攻时，附带X乘以Y真实伤害"}, {"id": 508, "name": "无道", "shortname": "无道", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 48, "duration": -1, "limit": 1006, "desc": "W免疫缴械，眩晕，计穷，混乱状态"}, {"id": 509, "name": "附加技能", "shortname": "技能", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 89, "duration": -1, "limit": 1043, "desc": "W获得技能X"}, {"id": 510, "name": "疾风", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 666, "duration": -1, "limit": 1020, "params": "7000,2037", "desc": "W每隔S秒Y"}, {"id": 511, "name": "大赏三军-物理", "shortname": "攻击", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 3104, "duration": 10000, "params": "30,dex,100", "desc": "武力伤害提升X%(每100速度提升1%)"}, {"id": 512, "name": "龙胆", "shortname": "龙胆", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 90, "duration": -1, "limit": 1014, "desc": "W如果佩戴长坂救主，洞察持续整场，七进七出持续整场"}, {"id": 513, "name": "连环", "shortname": "链接", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 1, "dispel": 0, "kind": 91, "duration": -1, "params": "100", "desc": "附加链接状态:受到主动战法伤害时，其他有该状态的单位也将受到此伤害的X%伤害，并解除链接状态，链接状态下[规避]无效"}, {"id": 514, "name": "桃园", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 666, "duration": -1, "limit": 1052, "params": "7000,2037", "desc": "W每隔S秒Y"}, {"id": 515, "name": "观星", "shortname": "观星", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "limit": 1011, "params": "1,220", "desc": "W对[眩晕]状态的敌人造成伤害为Y%"}, {"id": 516, "name": "忠勇", "shortname": "忠勇", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "limit": 1019, "params": "33,180", "desc": "W对[计穷]状态的敌人造成伤害为Y%"}, {"id": 517, "name": "仁道", "shortname": "仁道", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 92, "duration": -1, "limit": 1010, "params": "1,1,20,2037", "desc": "W当X,Y时,获得1层仁道,当仁道达到Z层时U"}, {"id": 518, "name": "影舞", "shortname": "影舞", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 92, "duration": -1, "params": "1,3,3,4497,4000", "desc": "当X,Y时,获得1层影舞,当影舞达到Z层时U(内置冷却4秒)"}, {"id": 519, "name": "速度提升", "shortname": "速度", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 31113, "duration": -1, "params": "100.0", "desc": "速度提升X"}, {"id": 520, "name": "疾袭", "shortname": "疾袭", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1020, "params": "140", "desc": "W主技能范围变为R"}, {"id": 521, "name": "猛虎", "shortname": "猛虎", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 93, "duration": -1, "limit": 1026, "desc": "W洞察效果为100%免疫缴械，眩晕，计穷，混乱状态"}, {"id": 522, "name": "盛气", "shortname": "盛气", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 94, "duration": -1, "limit": 1019, "params": "2022,100,5", "desc": "W每释放X时，有Y%几率伤害提高Z%"}, {"id": 523, "name": "灵魂燃烧", "shortname": "紫怨", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 2, "kind": 95, "duration": -1, "params": "50", "desc": "目标在这期间所受伤害的X%在效果结束后以真实伤害结算。"}, {"id": 524, "name": "神速", "shortname": "神速", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 96, "duration": -1, "params": "3000,4503,3000", "desc": "每X速度使Y得冷却时间减少0.5秒，最多3秒"}, {"id": 525, "name": "鸩毒", "shortname": "鸩毒", "supose": 1, "effect": "buff_002", "show": 1, "debuff": 1, "dispel": 0, "kind": 1005, "duration": -1, "params": "6000,100,1,15", "desc": "进入无法驱散的鸩毒状态，每间隔S秒掉血Y威力血量(受智力影响)，最多叠加U层"}, {"id": 526, "name": "断魂", "shortname": "断魂", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 97, "duration": -1, "params": "10", "desc": "造成伤害时，有X%几率无视敌方防御"}, {"id": 527, "name": "虎威", "shortname": "虎威", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1048, "params": "240", "desc": "W主技能范围变为R"}, {"id": 528, "name": "虎威", "shortname": "虎威", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "limit": 1048, "params": "1,190", "desc": "W对[眩晕]状态的敌人造成伤害为Y%"}, {"id": 529, "name": "霸王", "shortname": "霸王", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1045, "params": "230", "desc": "W主技能范围变为R"}, {"id": 530, "name": "焱炎", "shortname": "焱炎", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 163, "duration": -1, "limit": 1053, "params": "150", "desc": "W对燃烧敌人引爆燃烧的伤害提升X%。"}, {"id": 531, "name": "奇谋", "shortname": "奇谋", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 150, "duration": -1, "limit": 1024, "params": "20", "desc": "W主技能无法被打断,并且伤害提升X%"}, {"id": 532, "name": "极火", "shortname": "极火", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 151, "duration": -1, "params": "50", "desc": "提升燃烧状态造成的基础伤害X%"}, {"id": 533, "name": "骁雄", "shortname": "骁雄", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 152, "duration": -1, "limit": 1006, "params": "1.5", "desc": "W提供相当于X%最大血量的攻击强度。"}, {"id": 534, "name": "踏阵", "shortname": "踏阵", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 153, "duration": -1, "limit": 1000, "params": "50", "desc": "W提升主技能基础威力X%"}, {"id": 535, "name": "孤兰", "shortname": "孤兰", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 154, "duration": -1, "limit": 1043, "params": "5", "desc": "W追击技能发动判定失败时，提升追击技能发动率5%，此效果发动追击技能时归0"}, {"id": 536, "name": "极炎", "shortname": "极炎", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 155, "duration": 0, "params": "1000,50,111,5000", "desc": "如果目标存在[燃烧]状态，则有Y%几率眩晕5秒。"}, {"id": 537, "name": "鬼才", "shortname": "鬼才", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1051, "params": "240", "desc": "W主技能范围变为R"}, {"id": 538, "name": "透骨", "shortname": "透骨", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 156, "duration": -1, "params": "15,300", "desc": "造成伤害时,有X%几率伤害提升Y%"}, {"id": 539, "name": "咒刃", "shortname": "咒刃", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 157, "duration": -1, "params": "500", "desc": "下一次普通攻击，伤害提升X%"}, {"id": 540, "name": "神速", "shortname": "神速", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 88, "duration": -1, "params": "dex,5", "desc": "W普攻时，附带X乘以Y真实伤害"}, {"id": 541, "name": "虎女", "shortname": "虎女", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1031, "params": "akl_p:30", "desc": "WX"}, {"id": 542, "name": "神威", "shortname": "神威", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1008, "params": "pkl_p:40", "desc": "WX"}, {"id": 543, "name": "燃烧", "shortname": "燃烧", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 1, "dispel": 0, "kind": 1000, "duration": 8000, "params": "7000,240,1", "desc": "进入燃烧状态，间隔7秒掉血Y威力血量(受智力影响),如果目标已经处于燃烧状态，立即受到Y威力伤害(受智力影响)"}, {"id": 544, "name": "桃园", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 6666, "duration": -1, "params": "7000,2037,16", "desc": "战斗开始Z秒后,每隔S秒Y"}, {"id": 545, "name": "巧变", "shortname": "巧变", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1033, "params": "230", "desc": "W主技能范围变为R"}, {"id": 546, "name": "被攻击时几率转换百分比伤害为恢复血量", "shortname": "转换", "supose": 0, "show": 1, "debuff": 0, "dispel": 0, "kind": 7, "duration": -1, "limit": 1033, "params": "20,30", "desc": "W受到时X%几率转换Y%伤害为恢复血量"}, {"id": 547, "name": "神女", "shortname": "神女", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1009, "params": "140", "desc": "W主技能范围变为R"}, {"id": 548, "name": "神女", "shortname": "神女", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1009, "params": "con:500", "desc": "WX"}, {"id": 549, "name": "明谏", "shortname": "明谏", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1070, "params": "440", "desc": "W主技能范围变为R"}, {"id": 550, "name": "明谏", "shortname": "明谏", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1070, "params": "askl:5", "desc": "WX"}, {"id": 551, "name": "机巧", "shortname": "机巧", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1013, "params": "mdmg_p:60,trtad:30", "desc": "WX"}, {"id": 552, "name": "卧龙", "shortname": "卧龙", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 159, "duration": -1, "limit": 1011, "params": "6,50", "desc": "W主技能发动判定失败时，提升主技能发动率X%，所有伤害提升Y%，此效果发动主技能时归0"}, {"id": 553, "name": "曲顾", "shortname": "曲顾", "supose": 0, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 11, "duration": -1, "params": "31,150", "desc": "W对[缴械]状态的敌人造成伤害为Y%"}, {"id": 554, "name": "桃园", "shortname": "技能", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 6666, "duration": -1, "limit": 1013, "params": "7000,2037,16", "desc": "W战斗开始Z秒后,每隔S秒Y"}, {"id": 555, "name": "光环", "shortname": "奇门", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 160, "duration": -1, "ring": 1, "params": "1", "desc": "[左慈存活时]主动技能冷却时间减少X"}, {"id": 556, "name": "光环", "shortname": "奇门", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 1, "dispel": 0, "kind": 161, "duration": -1, "ring": 1, "params": "1", "desc": "[左慈存活时]主动技能冷却时间增加X"}, {"id": 557, "name": "击瑕", "shortname": "击瑕", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 1, "dispel": 0, "kind": 162, "duration": -1, "params": "30", "desc": "每有1个减益状态受到伤害增加X%"}, {"id": 558, "name": "神兽", "shortname": "神兽", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 2588, "duration": -1, "params": "con_p:20,pow_p:10,int_p:10,dmg_p:10", "desc": "X"}, {"id": 559, "name": "闭月", "shortname": "闭月", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1002, "params": "240", "desc": "W主技能范围变为R"}, {"id": 560, "name": "豪族", "shortname": "豪族", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1003, "params": "140", "desc": "W主技能范围变为R"}, {"id": 561, "name": "驭雷", "shortname": "驭雷", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 163, "duration": -1, "limit": 1005, "params": "150", "desc": "W雷法技能伤害提升X%"}, {"id": 562, "name": "毒士", "shortname": "毒士", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 165, "duration": -1, "limit": 1007, "params": "2,2", "desc": "W妖术每次叠加X层,最大Y层"}, {"id": 563, "name": "才女", "shortname": "才女", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1001, "params": "140", "desc": "W主技能范围变为R"}, {"id": 564, "name": "天义", "shortname": "天义", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1034, "params": "210", "desc": "W主技能范围变为R"}, {"id": 565, "name": "天义", "shortname": "天义", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1034, "params": "pskl:5", "desc": "WX"}, {"id": 566, "name": "结姻", "shortname": "结姻", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 166, "duration": -1, "params": "20,8", "desc": "每有一个有益状态，伤害增加X%，最多Y个"}, {"id": 567, "name": "浴火", "shortname": "浴火", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 167, "duration": -1, "limit": 1052, "params": "568", "desc": "W主技能可以给我方施加一个智力*2的护盾,护盾持续期间受到伤害减少20%"}, {"id": 568, "name": "受伤害时 先减少护盾。 所受伤害百分比，耐久", "shortname": "护盾", "supose": 0, "effect": "buff_003", "show": 1, "debuff": 0, "dispel": 1, "kind": 19, "duration": 8000, "params": "20,300,int,2", "desc": "获得Y+智力*U护盾,护盾持续期间减少X%伤害"}, {"id": 569, "name": "附加技能", "shortname": "技能", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 89, "duration": -1, "limit": 1029, "params": "4649.0", "desc": "W获得技能X"}, {"id": 570, "name": "枭雄", "shortname": "枭雄", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 168, "duration": -1, "limit": 1016, "params": "6", "desc": "W主技能附加状态效果增加X%"}, {"id": 571, "name": "上将", "shortname": "上将", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 169, "duration": -1, "limit": 1054, "desc": "W主技能不再施加眩晕效果"}, {"id": 572, "name": "上将", "shortname": "上将", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1054, "params": "askl:5,pow:2000", "desc": "WX"}, {"id": 573, "name": "犄角", "shortname": "犄角", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 170, "duration": -1, "params": "15", "desc": "当所在行每有1个队友自身伤害提升X%"}, {"id": 574, "name": "都督", "shortname": "都督", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1053, "params": "mdmg_p:30", "desc": "WX"}, {"id": 575, "name": "都督", "shortname": "都督", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1024, "params": "mdmg_p:40", "desc": "WX"}, {"id": 576, "name": "飞焰", "shortname": "飞焰", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 84, "duration": -1, "limit": 1053, "params": "1", "desc": "W主技能施法时间减少X秒"}, {"id": 577, "name": "疾剑", "shortname": "疾剑", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 171, "duration": -1, "params": "0.6", "desc": "减少X秒普攻间隔"}, {"id": 578, "name": "九伐", "shortname": "九伐", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 172, "duration": -1, "params": "9,cae,50,2", "desc": "接下来的X次追击战法伤害提升Z%,如果击杀小兵则恢复U次九伐次数"}, {"id": 579, "name": "利刃", "shortname": "利刃", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 173, "duration": -1, "params": "20", "desc": "每装备1个追击类战法，对小兵的伤害提升X%"}, {"id": 580, "name": "殚谋", "shortname": "殚谋", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 174, "duration": -1, "params": "7000,10,100,int", "desc": "每隔S秒损失Y%血量，提升Z智力"}, {"id": 581, "name": "凤魄", "shortname": "凤魄", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 175, "duration": -1, "limit": 1012, "params": "30", "desc": "W发动连击时，有X%几率再次发动普通攻击，该次攻击不会触发连击和凤魄"}, {"id": 582, "name": "方天", "shortname": "方天", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 176, "duration": -1, "params": "30,4685", "desc": "发动战技或者追击时，如果自身有[乱舞]存在，X%几率Y"}, {"id": 583, "name": "铁骑", "shortname": "铁骑", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 177, "duration": -1, "limit": 1008, "params": "40,6", "desc": "W普通攻击不能暴击，他的普通攻击有X%几率造成(Y+额外暴击伤害/100)倍伤害"}, {"id": 584, "name": "天任", "shortname": "天任", "supose": 0, "effect": "buff_010", "show": 1, "debuff": 0, "dispel": 0, "kind": 178, "duration": -1, "params": "int,5,1184", "desc": "普攻时，附带ZX乘以Y真实伤害"}, {"id": 585, "name": "麒麟", "shortname": "麒麟", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 74, "duration": -1, "limit": 1184, "params": "140", "desc": "W主技能范围变为R"}, {"id": 586, "name": "免疫混乱", "shortname": "破妄", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 179, "duration": 10, "desc": "免疫混乱状态"}, {"id": 587, "name": "疾舞", "shortname": "疾舞", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 180, "duration": 10, "params": "dex,15000,pow,50", "desc": "当X大于Y时,Z提升X的U%"}, {"id": 588, "name": "火舞", "shortname": "火舞", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 181, "duration": -1, "limit": 1053, "params": "30,1", "desc": "W释放火系战法时，有X%几率减少当前技能冷却时间Y秒"}, {"id": 589, "name": "连营", "shortname": "连营", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 182, "duration": -1, "limit": 1053, "params": "20,3962", "desc": "W当有[燃烧]引爆时，获得1层[连营],当连营达到X层时消耗层数释放一次Y"}, {"id": 590, "name": "西陵", "shortname": "西陵", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 183, "duration": -1, "params": "200,10", "desc": "当敌人被引爆[燃烧]时，Y%几率立即对其造成X威力伤害(受智力影响)"}, {"id": 591, "name": "帼武", "shortname": "帼武", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 184, "duration": -1, "limit": 1061, "params": "pow,50,100", "desc": "W获得[乱舞]时,X增加Y,最多可以叠加Z次"}, {"id": 592, "name": "急救", "shortname": "急救", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 185, "duration": -1, "desc": "回天秘策的范围变为全体并且回复间隔减半"}, {"id": 593, "name": "仙术", "shortname": "仙术", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 186, "duration": -1, "desc": "仙人抚顶的范围变为全体"}, {"id": 594, "name": "灵策", "shortname": "灵策", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 187, "duration": -1, "limit": 1017, "params": "5,1017", "desc": "当己方单位受到治疗时,Y有X%几率释放主技能"}, {"id": 595, "name": "英魂", "shortname": "英魂", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 158, "duration": -1, "limit": 1026, "params": "con:1500", "desc": "WX"}, {"id": 596, "name": "结义", "shortname": "结义", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 188, "duration": -1, "limit": 1015, "params": "1010,1019,10", "desc": "X,Y释放主动战法时，W有Z%几率释放主技能"}, {"id": 597, "name": "济世", "shortname": "济世", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 189, "duration": -1, "params": "5000,6,10", "desc": "每隔S秒治疗加成提升Y%,最多叠加Z次"}, {"id": 598, "name": "八卦", "shortname": "八卦", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 190, "duration": -1, "params": "15.0", "desc": "受到伤害时，X%几率免疫伤害"}, {"id": 599, "name": "命运", "shortname": "命运", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 2588, "duration": 12000, "params": "pow:8000", "desc": "X"}, {"id": 600, "name": "彗星", "shortname": "彗星", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 191, "duration": -1, "params": "40000,0,15,4743", "desc": "Y时Z%几率U，内置冷却S秒"}, {"id": 601, "name": "疾速", "shortname": "疾速", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 1710, "duration": 15000, "params": "1", "desc": "减少X秒普攻间隔"}, {"id": 602, "name": "天堂", "shortname": "天堂", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 2589, "duration": 15000, "params": "m_att:4000", "desc": "X"}, {"id": 603, "name": "天堂", "shortname": "天堂", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 192, "duration": -1, "params": "40000,2,15,4744", "desc": "Y时Z%几率U，内置冷却S秒"}, {"id": 604, "name": "誓约", "shortname": "誓约", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 193, "duration": 20000, "params": "10.0", "desc": "减伤X%"}, {"id": 605, "name": "神光", "shortname": "神光", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 2590, "duration": 15000, "params": "cirt_hit:80", "desc": "X"}, {"id": 606, "name": "神光", "shortname": "神光", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 191, "duration": -1, "params": "40000,0,15,4748", "desc": "Y时Z%几率U，内置冷却S秒"}, {"id": 607, "name": "神御", "shortname": "神御", "supose": 0, "effect": "buff_012", "show": 1, "debuff": 0, "dispel": 0, "kind": 9, "duration": 15000, "params": "100,9999", "desc": "受到伤害时，X%几率免疫伤害"}, {"id": 608, "name": "龙魂", "shortname": "龙魂", "supose": 0, "effect": "buff_004", "show": 1, "debuff": 0, "dispel": 0, "kind": 194, "duration": -1, "params": "int,2,300,15", "desc": "Y时X增加Z，最多叠加U次"}, {"id": 609, "name": "燃烬", "shortname": "燃烬", "supose": 1, "effect": "buff_001", "show": 1, "debuff": 0, "dispel": 0, "kind": 195, "duration": -1, "params": "int,10", "desc": "当敌人被引爆[燃烧]时，额外附带Y倍X真实伤害"}]