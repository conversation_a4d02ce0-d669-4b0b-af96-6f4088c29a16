"""
邮件WebSocket消息处理器
处理邮件相关的WebSocket消息
"""

import logging
from typing import Dict, Any
from models import MessageModel
from enums import MessageId
from base_handlers import MessageHandler
from mail_service_distributed import MailServiceDistributed
from mail_models import (
    SendMailRequest, SystemMailRequest, BroadcastMailRequest,
    MailAttachmentData, AttachmentType
)

logger = logging.getLogger(__name__)


class MailHandlers(MessageHandler):
    """邮件消息处理器"""
    
    def __init__(self):
        self.mail_service = MailServiceDistributed()

    async def handle_get_mail_list(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取邮件列表消息"""
        try:
            page = data.get("page", 1)
            limit = data.get("limit", 20)
            
            # 调用服务
            result = await self.mail_service.get_mail_list(username, page, limit)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_LIST,
                    success=True,
                    data=result.data
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_LIST,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
                
        except Exception as e:
            logger.error(f"处理获取邮件列表消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_LIST,
                success=False,
                error="获取邮件列表时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_mail_detail(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取邮件详情消息"""
        try:
            mail_id = data.get("mail_id")
            if not mail_id:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_DETAIL,
                    success=False,
                    error="邮件ID不能为空"
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.mail_service.get_mail_detail(username, mail_id)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_DETAIL,
                    success=True,
                    data=result.data
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_DETAIL,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
                
        except Exception as e:
            logger.error(f"处理获取邮件详情消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_DETAIL,
                success=False,
                error="获取邮件详情时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_send_mail(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理发送邮件消息"""
        try:
            # 参数验证
            receiver_id = data.get("receiver_id")
            title = data.get("title")
            content = data.get("content")
            attachments_data = data.get("attachments", [])
            
            if not receiver_id or not title or not content:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_SEND,
                    success=False,
                    error="收件人、标题和内容不能为空"
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 构建附件列表
            attachments = []
            for attachment_data in attachments_data:
                attachment = MailAttachmentData(
                    attachment_type=AttachmentType(attachment_data.get("attachment_type", 1)),
                    item_id=attachment_data.get("item_id", ""),
                    item_name=attachment_data.get("item_name", ""),
                    quantity=attachment_data.get("quantity", 1),
                    quality=attachment_data.get("quality", 1),
                    extra_data=attachment_data.get("extra_data")
                )
                attachments.append(attachment)
            
            # 构建请求
            request = SendMailRequest(
                receiver_id=receiver_id,
                title=title,
                content=content,
                attachments=attachments if attachments else None
            )
            
            # 调用服务
            result = await self.mail_service.send_mail(username, request)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_SEND,
                    success=True,
                    data=result.data,
                    message=result.message
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_SEND,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
                
        except Exception as e:
            logger.error(f"处理发送邮件消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_SEND,
                success=False,
                error="发送邮件时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_mark_mail_read(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理标记邮件已读消息"""
        try:
            mail_id = data.get("mail_id")
            if not mail_id:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_READ,
                    success=False,
                    error="邮件ID不能为空"
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 调用服务
            result = await self.mail_service.mark_mail_read(username, mail_id)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_READ,
                    success=True,
                    message=result.message
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_READ,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
                
        except Exception as e:
            logger.error(f"处理标记邮件已读消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_READ,
                success=False,
                error="标记邮件已读时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_delete_mail(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理删除邮件消息"""
        try:
            mail_id = data.get("mail_id")
            mail_ids = data.get("mail_ids")  # 支持批量删除
            
            if mail_id:
                # 单个删除
                result = await self.mail_service.delete_mail(username, mail_id)
            elif mail_ids:
                # 批量删除
                result = await self.mail_service.batch_delete_mails(username, mail_ids)
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_DELETE,
                    success=False,
                    error="邮件ID不能为空"
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_DELETE,
                    success=True,
                    data=result.data,
                    message=result.message
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_DELETE,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
                
        except Exception as e:
            logger.error(f"处理删除邮件消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_DELETE,
                success=False,
                error="删除邮件时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_claim_attachments(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理领取附件消息"""
        try:
            mail_id = data.get("mail_id")
            claim_all = data.get("claim_all", False)  # 是否领取所有附件

            if claim_all:
                # 领取所有附件
                result = await self.mail_service.claim_all_attachments(username)
            elif mail_id:
                # 领取指定邮件的附件
                result = await self.mail_service.claim_attachments(username, mail_id)
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_CLAIM_ATTACHMENTS,
                    success=False,
                    error="邮件ID不能为空"
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response

            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_CLAIM_ATTACHMENTS,
                    success=True,
                    data=result.data,
                    message=result.message
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_CLAIM_ATTACHMENTS,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response

        except Exception as e:
            logger.error(f"处理领取附件消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_CLAIM_ATTACHMENTS,
                success=False,
                error="领取附件时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_get_unread_count(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取未读邮件数量消息"""
        try:
            # 调用服务
            result = await self.mail_service.get_unread_count(username)

            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_UNREAD_COUNT,
                    success=True,
                    data=result.data
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_UNREAD_COUNT,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response

        except Exception as e:
            logger.error(f"处理获取未读邮件数量消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_UNREAD_COUNT,
                success=False,
                error="获取未读邮件数量时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response

    async def handle_claim_all_attachments(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理一键领取所有附件消息"""
        try:
            # 调用服务
            result = await self.mail_service.claim_all_attachments(username)

            if result.success:
                response = MessageModel(
                    msgId=MessageId.MAIL_CLAIM_ALL_ATTACHMENTS,
                    success=True,
                    data=result.data,
                    message=result.message
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.MAIL_CLAIM_ALL_ATTACHMENTS,
                    success=False,
                    error=result.error
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response

        except Exception as e:
            logger.error(f"处理一键领取所有附件消息时发生错误: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.MAIL_CLAIM_ALL_ATTACHMENTS,
                success=False,
                error="一键领取所有附件时发生内部错误"
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response
