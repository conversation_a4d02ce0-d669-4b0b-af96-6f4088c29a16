# 商店管理系统 - 后端集成测试指南

## 📋 **集成状态**

### ✅ **已完成的集成**

#### **后端API接口**
- ✅ `POST /api/shop/admin/shop` - 创建商店
- ✅ `GET /api/shop/admin/shops` - 获取所有商店列表
- ✅ `GET /api/shop/admin/shop/{shop_id}` - 获取商店详情
- ✅ `PUT /api/shop/admin/shop/{shop_id}` - 更新商店
- ✅ `DELETE /api/shop/admin/shop/{shop_id}` - 删除商店
- ✅ `POST /api/shop/admin/item-config` - 创建商品配置
- ✅ `PUT /api/shop/admin/item/{config_id}` - 更新商品配置
- ✅ `DELETE /api/shop/admin/item/{config_id}` - 删除商品配置
- ✅ `GET /api/shop/{shop_id}/items?player_id=admin` - 获取商店商品配置

#### **前端功能**
- ✅ 商店列表展示
- ✅ 商店创建/编辑/删除
- ✅ 商品配置列表展示
- ✅ 商品配置创建/编辑/删除
- ✅ 响应式界面设计
- ✅ 错误处理和用户反馈

## 🧪 **测试步骤**

### **1. 启动后端服务**

确保您的FastAPI服务器正在运行，并且包含了新添加的管理接口。

### **2. 访问前端管理界面**

打开浏览器访问：`http://your-domain/admin/shopadmin/index.html`

### **3. 测试商店管理功能**

#### **3.1 创建商店**
1. 点击"新增商店"按钮
2. 填写商店信息：
   - 商店ID: `test_shop_001`
   - 商店名称: `测试商店`
   - 商店类型: `normal`
   - 描述: `这是一个测试商店`
   - 图标: `test_shop.png`
   - 排序权重: `1`
   - 激活状态: `勾选`
3. 点击"保存"
4. 检查是否成功创建并显示在列表中

#### **3.2 编辑商店**
1. 点击商店卡片的"编辑"按钮
2. 修改商店名称为: `测试商店（已修改）`
3. 点击"保存"
4. 检查修改是否生效

#### **3.3 查看商店详情**
1. 点击商店卡片的"商品管理"按钮
2. 应该跳转到商品管理页面

### **4. 测试商品配置管理功能**

#### **4.1 创建商品配置**
1. 在商品管理页面点击"新增商品"
2. 填写商品信息：
   - 道具模板ID: `10001`
   - 道具数量: `1`
   - 道具品质: `3`
   - 槽位ID: `1`
   - 价格配置: `{"currency": "gold", "amount": 100}`
   - 刷新权重: `100`
   - 出现概率: `1.0`
   - 排序权重: `1`
   - 激活状态: `勾选`
3. 点击"保存"
4. 检查是否成功创建并显示在列表中

#### **4.2 编辑商品配置**
1. 点击商品卡片的"编辑"按钮
2. 修改道具数量为: `5`
3. 点击"保存"
4. 检查修改是否生效

#### **4.3 删除商品配置**
1. 点击商品卡片的"删除"按钮
2. 确认删除
3. 检查商品是否从列表中移除

### **5. 测试错误处理**

#### **5.1 网络错误测试**
1. 停止后端服务
2. 尝试创建商店
3. 应该显示错误提示信息

#### **5.2 数据验证测试**
1. 尝试创建空名称的商店
2. 应该显示验证错误信息

## 🔧 **调试工具**

### **浏览器控制台**
打开浏览器开发者工具，查看Console标签页：
- API请求日志: `[ShopAPI] 请求详情`
- 管理器状态: `[ShopManager] 当前状态`
- 错误信息: 红色错误日志

### **网络请求监控**
在开发者工具的Network标签页中：
- 查看API请求和响应
- 检查请求参数和返回数据
- 确认HTTP状态码

### **语法测试页面**
访问 `test.html` 页面验证JavaScript语法：
```
http://your-domain/admin/shopadmin/test.html
```

## 🐛 **常见问题排查**

### **1. 商店列表为空**
- 检查后端API `/api/shop/admin/shops` 是否正常返回数据
- 检查浏览器控制台是否有错误信息
- 确认数据库中是否有商店数据

### **2. 创建商店失败**
- 检查后端API `/api/shop/admin/shop` 是否正常工作
- 检查请求数据格式是否正确
- 查看后端日志确认错误原因

### **3. 商品配置显示异常**
- 检查API `/api/shop/{shop_id}/items?player_id=admin` 返回数据
- 确认数据转换逻辑是否正确
- 检查商品配置的数据结构

### **4. 界面显示问题**
- 检查CSS文件是否正确加载
- 确认JavaScript文件没有语法错误
- 查看浏览器兼容性

## 📊 **性能测试**

### **响应时间测试**
- 商店列表加载: < 2秒
- 商店创建: < 3秒
- 商品配置加载: < 2秒
- 商品配置创建: < 3秒

### **并发测试**
- 多个用户同时访问管理界面
- 同时进行商店和商品配置操作
- 检查数据一致性

## 📝 **测试报告模板**

```
测试时间: ____年__月__日
测试人员: ________
浏览器版本: ________

功能测试结果:
[ ] 商店列表显示 - 通过/失败
[ ] 商店创建 - 通过/失败  
[ ] 商店编辑 - 通过/失败
[ ] 商店删除 - 通过/失败
[ ] 商品配置列表 - 通过/失败
[ ] 商品配置创建 - 通过/失败
[ ] 商品配置编辑 - 通过/失败
[ ] 商品配置删除 - 通过/失败

发现的问题:
1. ________________
2. ________________
3. ________________

建议改进:
1. ________________
2. ________________
3. ________________
```

## 🚀 **部署检查清单**

- [ ] 后端API服务正常运行
- [ ] 所有管理接口已添加并测试
- [ ] 前端文件正确部署
- [ ] 数据库连接正常
- [ ] 缓存服务正常
- [ ] 日志记录正常
- [ ] 错误处理完善
- [ ] 用户权限验证
- [ ] 性能测试通过
- [ ] 安全测试通过

---

**注意**: 这是一个完整的前后端集成系统，请确保在生产环境中进行充分的测试和验证。
