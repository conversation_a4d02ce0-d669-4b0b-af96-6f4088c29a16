#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为现有的商品配置添加item_type字段
"""

import asyncio
import logging
from datetime import datetime
from mongodb_manager import MongoDBManager
from config import config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def migrate_item_type():
    """为现有的商品配置添加item_type字段"""
    try:
        # 获取数据库连接
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        # 获取集合配置
        collections = config.get_database_collections()
        collection_name = collections["shop_item_configs"]
        collection = db[collection_name]
        
        logger.info(f"开始迁移集合: {collection_name}")
        
        # 查找所有没有item_type字段的记录
        query = {"item_type": {"$exists": False}}
        cursor = collection.find(query)
        
        update_count = 0
        total_count = await collection.count_documents(query)
        
        logger.info(f"找到 {total_count} 条需要迁移的记录")
        
        if total_count == 0:
            logger.info("没有需要迁移的记录")
            return
        
        # 批量更新记录
        bulk_operations = []
        
        async for doc in cursor:
            config_id = doc.get('config_id', 'unknown')
            
            # 为每个记录添加默认的item_type字段
            update_op = {
                "updateOne": {
                    "filter": {"_id": doc["_id"]},
                    "update": {
                        "$set": {
                            "item_type": "item",  # 默认为普通道具
                            "updated_at": datetime.now().isoformat()
                        }
                    }
                }
            }
            
            bulk_operations.append(update_op)
            
            # 每100条记录执行一次批量操作
            if len(bulk_operations) >= 100:
                result = await collection.bulk_write(bulk_operations)
                update_count += result.modified_count
                logger.info(f"已更新 {update_count}/{total_count} 条记录")
                bulk_operations = []
        
        # 处理剩余的操作
        if bulk_operations:
            result = await collection.bulk_write(bulk_operations)
            update_count += result.modified_count
        
        logger.info(f"迁移完成！总共更新了 {update_count} 条记录")
        
        # 验证迁移结果
        remaining_count = await collection.count_documents({"item_type": {"$exists": False}})
        if remaining_count == 0:
            logger.info("✅ 迁移验证成功：所有记录都已包含item_type字段")
        else:
            logger.warning(f"⚠️ 仍有 {remaining_count} 条记录缺少item_type字段")
        
        # 显示各种道具类型的统计
        pipeline = [
            {"$group": {"_id": "$item_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info("道具类型统计:")
        async for stat in collection.aggregate(pipeline):
            item_type = stat["_id"]
            count = stat["count"]
            logger.info(f"  {item_type}: {count} 条记录")
            
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {str(e)}")
        raise

async def rollback_migration():
    """回滚迁移：移除item_type字段"""
    try:
        # 获取数据库连接
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        # 获取集合配置
        collections = config.get_database_collections()
        collection_name = collections["shop_item_configs"]
        collection = db[collection_name]
        
        logger.info(f"开始回滚集合: {collection_name}")
        
        # 移除所有记录的item_type字段
        result = await collection.update_many(
            {"item_type": {"$exists": True}},
            {"$unset": {"item_type": ""}}
        )
        
        logger.info(f"回滚完成！移除了 {result.modified_count} 条记录的item_type字段")
        
    except Exception as e:
        logger.error(f"回滚过程中发生错误: {str(e)}")
        raise

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        logger.info("执行回滚操作...")
        await rollback_migration()
    else:
        logger.info("执行迁移操作...")
        await migrate_item_type()

if __name__ == "__main__":
    asyncio.run(main())
