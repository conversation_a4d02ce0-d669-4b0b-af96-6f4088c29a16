import time
import os
from fastapi import WebSocket
from fastapi.websockets import WebSocketState
from typing import Dict, List, Set, Optional, Union
import logging
import asyncio
import aio_pika
from ItemCacheManager import ItemCacheManager
import json
from datetime import datetime
from config import config
from utils import handle_error, format_cache_key
from collections import defaultdict
from rabbitmq_cleaner import RabbitMQCleaner
import traceback
from logger_config import setup_logger
from enums import MessageId, ItemType
from models import MessageModel
from redis import exceptions as redis_exceptions  # 导入Redis异常处理

# 自定义JSON编码器，处理特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理特殊类型"""
    def default(self, obj):
        try:
            from bson import ObjectId
        except ImportError:
            ObjectId = None

        if ObjectId and isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, bytes):
            return obj.decode('utf-8')
        return super().default(obj)
from UserCacheManager import UserCacheManager
from redis_manager import RedisManager

# 初始化日志系统
logger = setup_logger(__name__)

class ConnectionManager:
    def __init__(self):
        """初始化连接管理器"""
        self.active_connections: Dict[str, Dict[str, Union[WebSocket, str]]] = {}  # token -> {"websocket": WebSocket, "username": str}
        self.user_tokens: Dict[str, str] = {}  # username -> token
        self.last_heartbeat: Dict[str, float] = {}  # token -> timestamp
        self.heartbeat_counters: Dict[str, int] = {}  # token -> int (心跳计数器)
        self.tasks = set()
        self.worker_id = str(os.getpid())
        # RabbitMQ相关
        self.rabbit_connection = None
        self.rabbit_channel = None
        self.broadcast_exchange = None
        self.personal_queue = None
        self.broadcast_queue = None
        
        # 配置参数
        self.heartbeat_timeout_seconds = 120
        self.broadcast_batch_size = 50
        self.broadcast_batch_timeout = 0.05
        
        # 锁和状态
        self.broadcast_lock = asyncio.Lock()
        self.cleaner = None
        self.last_cleanup = 0
        self._closed = False
        self.redis_client = None
        # 启动连接检查任务
        asyncio.create_task(self.check_connections())
        # 启动任务清理任务
        asyncio.create_task(self.cleanup_completed_tasks())

        logger.debug(f"ConnectionManager 初始化完成，worker_id: {self.worker_id}")
    async def _get_redis_client(self):
        """获取Redis客户端"""
        if not self.redis_client:
            self.redis_client = await RedisManager.get_instance()
            self.redis_client = await self.redis_client.get_redis()
        return self.redis_client
    async def initialize(self):
        """初始化连接管理器"""
        retry_count = 0
        max_retries = 3
        retry_delay = 2  # 秒
        
        while retry_count < max_retries:
            try:
                # 获取RabbitMQ配置
                rabbit_config = config.get_rabbitmq_config()
                
                # 记录RabbitMQ配置信息
                logger.info(f"RabbitMQ配置: host={rabbit_config['host']}, port={rabbit_config['port']}, virtual_host={rabbit_config['virtual_host']}")
                
                # 尝试连接RabbitMQ
                try:
                    # 创建RabbitMQ连接
                    self.rabbit_connection = await aio_pika.connect_robust(
                        host=rabbit_config["host"],
                        port=rabbit_config["port"],
                        login=rabbit_config["username"],
                        password=rabbit_config["password"],
                        virtualhost=rabbit_config["virtual_host"],
                        timeout=5,  # 5秒连接超时
                        heartbeat=30  # 设置心跳间隔为30秒
                    )
                    logger.info(f"成功连接到RabbitMQ服务器: {rabbit_config['host']}:{rabbit_config['port']}")
                except Exception as e:
                    logger.error(f"无法连接到RabbitMQ服务器 {rabbit_config['host']}:{rabbit_config['port']}: {str(e)}")
                    logger.warning("尝试连接到本地RabbitMQ服务器...")
                    
                    try:
                        # 尝试连接本地RabbitMQ
                        self.rabbit_connection = await aio_pika.connect_robust(
                            host="localhost",
                            port=5672,
                            login="guest",
                            password="guest",
                            virtualhost="/",
                            timeout=5,  # 5秒连接超时
                            heartbeat=30  # 设置心跳间隔为30秒
                        )
                        logger.info("成功连接到本地RabbitMQ服务器")
                    except Exception as local_e:
                        logger.error(f"连接本地RabbitMQ也失败了: {str(local_e)}")
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.info(f"将在 {retry_delay} 秒后重试 RabbitMQ 连接 (尝试 {retry_count}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2  # 指数退避
                            continue
                        else:
                            logger.warning("RabbitMQ连接失败，将以降级模式运行（仅支持单worker）")
                            break
                
                # 如果连接成功，进行后续初始化
                if self.rabbit_connection:
                    # 创建通道
                    self.rabbit_channel = await self.rabbit_connection.channel()
                    await self.rabbit_channel.set_qos(prefetch_count=200)  # 增加预取数量，提高吞吐量
                    
                    # 声明死信交换机
                    dlx_exchange = await self.rabbit_channel.declare_exchange(
                        "dlx",
                        aio_pika.ExchangeType.DIRECT,
                        durable=True
                    )
                    
                    # 声明死信队列
                    dead_letter_queue = await self.rabbit_channel.declare_queue(
                        "dead_letter_queue",
                        durable=True
                    )
                    await dead_letter_queue.bind(dlx_exchange)
                    
                    # 声明广播交换机
                    self.broadcast_exchange = await self.rabbit_channel.declare_exchange(
                        "broadcast_exchange",
                        aio_pika.ExchangeType.FANOUT,
                        durable=True
                    )
                    
                    # 为当前worker创建唯一的广播队列
                    self.broadcast_queue = await self.rabbit_channel.declare_queue(
                        f"broadcast_queue_{self.worker_id}",
                        durable=True,
                        arguments={
                            'x-dead-letter-exchange': 'dlx',
                            'x-max-length': 50000,  # 增加最大长度
                            'x-max-priority': 4,
                            'x-message-ttl': 60000  # 消息存活时间为60秒
                        }
                    )
                    
                    # 将广播队列绑定到广播交换机
                    await self.broadcast_queue.bind(self.broadcast_exchange)
                    
                    # 声明个人消息队列
                    self.personal_queue = await self.rabbit_channel.declare_queue(
                        f"personal_queue_{self.worker_id}",
                        durable=True,
                        arguments={
                            'x-dead-letter-exchange': 'dlx',
                            'x-max-length': 50000,  # 增加最大长度
                            'x-max-priority': 4,
                            'x-message-ttl': 60000  # 消息存活时间为60秒
                        }
                    )
                    
                    # 初始化RabbitMQ清理器
                    try:
                        self.cleaner = RabbitMQCleaner(
                            host=rabbit_config["host"],
                            port=rabbit_config.get("management_port", 15672),
                            username=rabbit_config["username"],
                            password=rabbit_config["password"],
                            vhost=rabbit_config["virtual_host"]
                        )
                    except Exception as e:
                        logger.error(f"RabbitMQ清理器初始化失败: {str(e)}")
                        logger.warning("自动清理功能将不可用")
                        self.cleaner = None
                    
                    # 启动后台任务
                    self._start_background_tasks()
                    
                    logger.info(f"ConnectionManager 初始化完成 (Worker {self.worker_id})")
                    return
                
                # 如果到达这里，说明重试后仍然失败
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"将在 {retry_delay} 秒后重试连接 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error(f"重试 {max_retries} 次后仍无法初始化 ConnectionManager，将以降级模式运行")
                    
            except Exception as e:
                logger.error(f"ConnectionManager 初始化过程中发生未处理异常: {str(e)}")
                logger.error("异常详情:", exc_info=True)
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"将在 {retry_delay} 秒后重试连接 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    logger.error(f"重试 {max_retries} 次后仍无法初始化 ConnectionManager")

    def _start_background_tasks(self):
        """启动后台任务"""
        # 处理广播消息
        task = asyncio.create_task(self.process_broadcast_queue())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        # 处理个人消息
        task = asyncio.create_task(self.process_personal_queue())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        # 监控连接状态
        task = asyncio.create_task(self.monitor_connections())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        # 自动清理
        task = asyncio.create_task(self.auto_cleanup())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        # 添加Redis订阅任务，用于多进程协调
        task = asyncio.create_task(self.subscribe_redis_events())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        # 添加连接池监控任务
        task = asyncio.create_task(self.monitor_connection_pools())
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        
        logger.info(f"后台任务已启动 (Worker {self.worker_id})")

    async def connect(self, websocket: WebSocket, token: str, username: str):
        """处理新的WebSocket连接，处理重复登录情况"""
        try:
            # 接受WebSocket连接
            await websocket.accept()
            
            # 检查是否已经存在连接
            if username in self.user_tokens:
                old_token = self.user_tokens[username]
                if old_token != token and old_token in self.active_connections:
                    # 发送通知给旧连接
                    try:
                        old_websocket = self.active_connections[old_token]["websocket"]
                        if old_websocket and old_websocket.application_state == WebSocketState.CONNECTED:
                            await old_websocket.send_json({
                                "msgId": 0,  # 系统消息
                                "data": {
                                    "message": "您的账号已在其他地方登录",
                                    "timestamp": datetime.now().isoformat(),
                                    "code": "account_login_elsewhere"
                                }
                            })
                    except Exception as e:
                        logger.warning(f"发送重复登录通知到旧连接失败: {str(e)}")
                    
                    # 断开旧连接
                    await self.disconnect(old_token)
                    logger.info(f"断开用户 {username} 的旧连接 (Token: {old_token[:10]})")
            
            # 使用Redis广播通知其他进程
            try:
                redis_manager = await RedisManager.get_instance()
                redis = await redis_manager.get_redis()
                # 检查Redis是否可用
                if redis:
                    # 发布重复登录通知到Redis
                    await redis.publish(
                        "user_login_channel",
                        json.dumps({
                            "action": "user_login",
                            "username": username,
                            "token": token,
                            "worker_id": self.worker_id,
                            "timestamp": datetime.now().isoformat()
                        }, cls=CustomJSONEncoder)
                    )
                    logger.info(f"已发布用户登录通知到Redis (用户: {username}, Worker: {self.worker_id})")
            except Exception as e:
                logger.error(f"发布登录通知到Redis失败: {str(e)}")
            user_cache = await UserCacheManager.get_instance()
            await user_cache.register_user_connection(username, token)   
            # 存储连接信息
            self.active_connections[token] = {"websocket": websocket, "username": username,'last_heartbeat':time.time()}
            self.user_tokens[username] = token
            self.last_heartbeat[token] = asyncio.get_event_loop().time()  # 使用与心跳处理相同的计时方法
            user_cache = await UserCacheManager.get_instance()
            user = await user_cache.get_user_by_username(username)
            # 获取用户信息
            
            if user:
                nickname = user.nickname
            else:
                nickname = None
            if nickname is not None:
                # 使用GameManager处理登录
                try:
                    from game_manager import GameManager
                    game_manager = await GameManager.get_instance()
                    await game_manager.on_player_login(username, token, websocket)
                except Exception as e:
                    logger.error(f"GameManager登录处理失败，用户: {username}, 错误: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
            else:
                await websocket.send_json(MessageModel(msgId=MessageId.CREATE_ROLE, data={}).model_dump())
            logger.info(f"用户 {username} 连接成功 (Token: {token[:10]}, Worker: {self.worker_id})")
        except Exception as e:
            logger.error(f"处理连接失败: {str(e)}")
            if token in self.active_connections:
                await self.disconnect(token)
            raise
    async def disconnect(self, token: str):
        """断开WebSocket连接"""
        try:
            # 获取连接信息（先获取再移除）
            conn_data = self.active_connections.get(token)
            if not conn_data:
                logger.warning(f"尝试断开不存在的连接 (Token: {token[:10]})")
                return
            
            username = conn_data.get("username")
            websocket = conn_data.get("websocket")
            user_cache = await UserCacheManager.get_instance()
            await user_cache.remove_user_connection(username=username)
            # 移除连接信息
            self.active_connections.pop(token, None)
            self.last_heartbeat.pop(token, None)

            # 移除用户令牌映射
            if username and username in self.user_tokens and self.user_tokens[username] == token:
                self.user_tokens.pop(username, None)
            
            # 确保WebSocket连接关闭
            if websocket:
                try:
                    # 检查连接是否已关闭
                    if websocket.application_state != WebSocketState.DISCONNECTED:
                        logger.info(f"正在关闭WebSocket连接 (Token: {token[:10]}, 状态: {websocket.application_state})")
                        await websocket.close(code=1000, reason="连接已被服务器关闭")
                    else:
                        logger.info(f"WebSocket连接已经处于断开状态 (Token: {token[:10]})")
                except Exception as e:
                    logger.warning(f"关闭WebSocket连接失败: {str(e)}")
            
            if username:
                logger.info(f"用户 {username} 断开连接 (Token: {token[:10]}, Worker: {self.worker_id})")

                # 通知GameManager处理登出
                try:
                    from service_locator import ServiceLocator
                    game_manager = ServiceLocator.get("game_manager")
                    if game_manager:
                        await game_manager.on_player_logout(username, "disconnect")
                except Exception as e:
                    logger.warning(f"GameManager登出处理失败: {username}, 错误: {str(e)}")
        except Exception as e:
            logger.error(f"断开连接失败 (Token: {token[:10] if token else 'None'}): {str(e)}")
            logger.error(traceback.format_exc())

    async def broadcast(self, message: dict, priority: int = 0):
        """广播消息到所有连接"""
        try:
            message_body = json.dumps(message, cls=CustomJSONEncoder).encode()
            # 通过广播交换机发送消息
            await self.broadcast_exchange.publish(
                aio_pika.Message(
                    body=message_body,
                    priority=priority,
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT
                ),
                routing_key=""  # FANOUT类型不需要routing key
            )
            logger.debug(f"广播消息已发送: {message}")
        except Exception as e:
            logger.error(f"广播消息失败: {str(e)}")
            raise

    async def send_personal_message(self, message: dict, token: str, priority: int = 0):
        """发送个人消息"""
        try:
            # 如果是当前worker的连接，直接发送
            if token in self.active_connections:
                websocket = self.active_connections[token]["websocket"]
                await self._send_to_websocket(websocket, message, token)
                return
            
            # 否则发送到消息队列
            message_body = json.dumps([token, message], cls=CustomJSONEncoder).encode()
            await self.rabbit_channel.default_exchange.publish(
                aio_pika.Message(
                    body=message_body,
                    priority=priority,
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT
                ),
                routing_key=f"personal_queue_{self.worker_id}"
            )
        except Exception as e:
            logger.error(f"发送个人消息失败: {str(e)}")
            raise
    async def send_personal_message_to_user(self, message: dict, username: str, priority: int = 0):
        """发送个人消息给指定用户"""
        try:
            # 首先检查用户是否在当前worker的活跃连接中
            if username in self.user_tokens:
                token = self.user_tokens[username]
                if token in self.active_connections:
                    await self.send_personal_message(message, token, priority)
                    return True
            
            # 如果不在当前worker，则从Redis获取连接信息
            cache_keys = config.get_cache_keys()
            try:
                # 添加重试逻辑
                retry_count = 0
                max_retries = 3
                while retry_count < max_retries:
                    try:
                        redis_client = await self._get_redis_client()
                        connection_info = await redis_client.get(
                            format_cache_key(
                                cache_keys.get("user_connection", "game:v2:users:{username}:connection"), 
                                username=username
                            )
                        )
                        break
                    except redis_exceptions.RedisError as e:
                        retry_count += 1
                        if retry_count >= max_retries:
                            logger.error(f"获取用户 {username} 连接信息失败，已达最大重试次数: {str(e)}")
                            return False
                        await asyncio.sleep(0.5 * retry_count)  # 指数退避
                
                if connection_info:
                    # 有连接信息，说明用户在线
                    try:
                        info = json.loads(connection_info)
                        logger.info(f"用户 {username} 连接信息: {info}")
                        worker_id = info.get("worker_id")
                        token = info.get("token")
                        
                        if not token:
                            logger.warning(f"用户 {username} 连接信息中缺少token")
                            return False
                            
                        await self.send_personal_message(message, token, priority)
                        return True
                    except json.JSONDecodeError as e:
                        logger.error(f"解析用户 {username} 连接信息失败: {str(e)}")
                        return False
                else:
                    logger.warning(f"用户 {username} 不在线，无法发送消息")
                    # 可以在这里实现离线消息存储逻辑
                    # await self.store_offline_message(username, message)
                    return False
            except Exception as e:
                logger.error(f"获取用户 {username} Redis连接信息失败: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"发送个人消息给用户失败，用户: {username}, 错误: {str(e)}")
            return False
    async def broadcast_to_users(self, message: dict, usernames: List[str], priority: int = 0):
        """广播消息给指定用户列表"""
        results = {"success": [], "failed": []}
        try:
            for username in usernames:
                success = await self.send_personal_message_to_user(message, username, priority)
                if success:
                    results["success"].append(username)
                else:
                    results["failed"].append(username)
            
            if results["failed"]:
                logger.warning(f"广播消息部分失败: 成功 {len(results['success'])} 个用户，失败 {len(results['failed'])} 个用户")
            
            return results
        except Exception as e:
            logger.error(f"广播消息给用户列表失败: {str(e)}")
            # 不抛出异常，而是返回失败结果
            return {"success": results["success"], "failed": results["failed"] + [u for u in usernames if u not in results["success"] and u not in results["failed"]]}

    async def process_broadcast_queue(self):
        """处理广播消息队列"""
        while not self._closed:
            try:
                if not self.broadcast_queue or not self.rabbit_channel:
                    logger.warning("广播队列或RabbitMQ通道未初始化，10秒后重试")
                    await asyncio.sleep(10)
                    continue
                
                async def on_message(message: aio_pika.IncomingMessage):
                    async with message.process():
                        try:
                            body = json.loads(message.body.decode())
                            logger.info(f"收到广播消息: {body}")
                            
                            async with self.broadcast_lock:
                                # 批量发送消息
                                sent_count = 0
                                error_count = 0
                                active_connections = list(self.active_connections.items())
                                
                                # 使用并发任务处理大量连接
                                if len(active_connections) > 50:
                                    # 分批处理
                                    batch_size = min(50, max(10, len(active_connections) // 4))
                                    tasks = []
                                    
                                    for i in range(0, len(active_connections), batch_size):
                                        batch = active_connections[i:i+batch_size]
                                        tasks.append(self._process_broadcast_batch(batch, body))
                                    
                                    results = await asyncio.gather(*tasks, return_exceptions=True)
                                    
                                    # 统计结果
                                    for batch_result in results:
                                        if isinstance(batch_result, tuple):
                                            sent, errors = batch_result
                                            sent_count += sent
                                            error_count += errors
                                        else:
                                            error_count += 1
                                else:
                                    # 少量连接直接顺序处理
                                    for token, conn_data in active_connections:
                                        if conn_data["websocket"] and conn_data["websocket"].application_state == WebSocketState.CONNECTED:
                                            try:
                                                await self._send_to_websocket(conn_data["websocket"], body, token)
                                                sent_count += 1
                                            except Exception as e:
                                                error_count += 1
                                                logger.error(f"发送消息失败，token: {token[:10]}..., 错误: {str(e)}")
                            
                            logger.info(f"广播消息已发送给 {sent_count} 个用户，失败 {error_count} 个")
                        except json.JSONDecodeError as e:
                            logger.error(f"广播消息格式错误: {str(e)}")
                        except Exception as e:
                            logger.error(f"处理广播消息时发生错误: {str(e)}")
                
                # 开始消费，并保存消费者标签
                consumer_tag = await self.broadcast_queue.consume(on_message)
                logger.info(f"Worker {self.worker_id} 开始消费广播消息，消费者标签: {consumer_tag}")
                
                try:
                    # 等待直到被取消
                    while not self._closed and self.rabbit_connection and not self.rabbit_connection.is_closed:
                        await asyncio.sleep(1)
                    
                finally:
                    # 确保取消消费者，避免资源泄漏
                    try:
                        if not self._closed and self.rabbit_connection and not self.rabbit_connection.is_closed:
                            await self.broadcast_queue.cancel(consumer_tag)
                            logger.info(f"已取消广播队列消费者: {consumer_tag}")
                    except Exception as cancel_err:
                        logger.warning(f"取消广播队列消费者失败: {str(cancel_err)}")
                
                # 如果连接关闭但管理器未关闭，尝试重连
                if not self._closed and (not self.rabbit_connection or self.rabbit_connection.is_closed):
                    logger.error("RabbitMQ连接已关闭，尝试重新连接")
                    await self._reconnect_rabbitmq()
                
            except Exception as e:
                if not self._closed:
                    logger.error(f"广播消息处理失败: {str(e)}")
                    logger.error("将在10秒后重试...")
                    await asyncio.sleep(10)

    async def _process_broadcast_batch(self, connections_batch, message):
        """处理一批广播连接"""
        sent_count = 0
        error_count = 0
        for token, conn_data in connections_batch:
            if conn_data["websocket"] and conn_data["websocket"].application_state == WebSocketState.CONNECTED:
                try:
                    if await self._send_to_websocket(conn_data["websocket"], message, token):
                        sent_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    error_count += 1
                    logger.error(f"批处理发送消息失败，token: {token[:10]}..., 错误: {str(e)}")
        return sent_count, error_count

    async def process_personal_queue(self):
        """处理个人消息队列"""
        while not self._closed:
            try:
                if not self.personal_queue or not self.rabbit_channel:
                    logger.warning("个人队列或RabbitMQ通道未初始化，10秒后重试")
                    await asyncio.sleep(10)
                    continue
                
                async def on_message(message: aio_pika.IncomingMessage):
                    async with message.process():
                        try:
                            body = json.loads(message.body.decode())
                            logger.debug(f"收到个人消息: {body}")
                            
                            if "token" not in body:
                                logger.error("个人消息缺少token字段")
                                return
                            
                            token = body["token"]
                            data = body["data"]
                            
                            if token in self.active_connections:
                                websocket = self.active_connections[token]["websocket"]
                                if websocket.application_state == WebSocketState.CONNECTED:
                                    try:
                                        await self._send_to_websocket(websocket, data, token)
                                        logger.debug(f"个人消息已发送给 token: {token[:10]}...")
                                    except Exception as e:
                                        logger.error(f"发送个人消息失败，token: {token[:10]}..., 错误: {str(e)}")
                                else:
                                    logger.warning(f"WebSocket连接状态异常，token: {token[:10]}..., 状态: {websocket.application_state}")
                            else:
                                logger.warning(f"找不到匹配的连接，token: {token[:10]}...")
                                
                        except json.JSONDecodeError as e:
                            logger.error(f"个人消息格式错误: {str(e)}")
                        except Exception as e:
                            logger.error(f"处理个人消息时发生错误: {str(e)}")
                
                # 开始消费，并保存消费者标签
                consumer_tag = await self.personal_queue.consume(on_message)
                logger.info(f"Worker {self.worker_id} 开始消费个人消息，消费者标签: {consumer_tag}")
                
                try:
                    # 等待直到被取消
                    while not self._closed and self.rabbit_connection and not self.rabbit_connection.is_closed:
                        await asyncio.sleep(1)
                    
                finally:
                    # 确保取消消费者，避免资源泄漏
                    try:
                        if not self._closed and self.rabbit_connection and not self.rabbit_connection.is_closed:
                            await self.personal_queue.cancel(consumer_tag)
                            logger.info(f"已取消个人队列消费者: {consumer_tag}")
                    except Exception as cancel_err:
                        logger.warning(f"取消个人队列消费者失败: {str(cancel_err)}")
                
                # 如果连接关闭但管理器未关闭，尝试重连
                if not self._closed and (not self.rabbit_connection or self.rabbit_connection.is_closed):
                    logger.error("RabbitMQ连接已关闭，尝试重新连接")
                    await self._reconnect_rabbitmq()
                
            except Exception as e:
                if not self._closed:
                    logger.error(f"个人消息处理失败: {str(e)}")
                    logger.error("将在10秒后重试...")
                    await asyncio.sleep(10)

    async def _send_to_websocket(self, websocket: WebSocket, message: dict, token: str):
        """发送消息到WebSocket连接"""
        try:
            if websocket.application_state == WebSocketState.CONNECTED:
                try:
                    # 使用自定义编码器序列化消息
                    message_text = json.dumps(message, cls=CustomJSONEncoder, ensure_ascii=False)
                    await asyncio.wait_for(
                        websocket.send_text(message_text),
                        timeout=1.0  # 降低超时时间，避免阻塞
                    )
                    return True
                except asyncio.TimeoutError:
                    logger.warning(f"发送消息超时 (Token: {token[:10]})")
                    # 不立即断开连接，而是标记为需检查
                    if token in self.last_heartbeat:
                        self.last_heartbeat[token] = asyncio.get_event_loop().time() - 90  # 设置为接近超时时间
                    return False
            else:
                logger.warning(f"WebSocket连接状态异常 (Token: {token[:10]}, 状态: {websocket.application_state})")
                await self.disconnect(token)
                return False
        except Exception as e:
            logger.error(f"发送消息失败: {str(e)}")
            await self.disconnect(token)
            return False

    async def _reconnect_rabbitmq(self):
        """RabbitMQ重连"""
        if self._closed:
            logger.debug("ConnectionManager已关闭，不再尝试重连")
            return False
        
        retry_count = 0
        max_retries = 5
        retry_delay = 1  # 秒
        
        while retry_count < max_retries:
            try:
                logger.info(f"尝试重新连接到RabbitMQ (尝试 {retry_count + 1}/{max_retries})...")
                
                # 首先关闭现有连接
                try:
                    if self.rabbit_connection:
                        await self.rabbit_connection.close()
                except Exception as e:
                    logger.warning(f"关闭旧连接时出错: {str(e)}")
                
                # 获取RabbitMQ配置
                rabbit_config = config.get_rabbitmq_config()
                
                # 重新连接
                self.rabbit_connection = await aio_pika.connect_robust(
                    host=rabbit_config["host"],
                    port=rabbit_config["port"],
                    login=rabbit_config["username"],
                    password=rabbit_config["password"],
                    virtualhost=rabbit_config["virtual_host"],
                    timeout=5,
                    heartbeat=30
                )
                
                # 重新创建通道和队列
                self.rabbit_channel = await self.rabbit_connection.channel()
                await self.rabbit_channel.set_qos(prefetch_count=200)
                
                # 重新声明交换机和队列
                self.broadcast_exchange = await self.rabbit_channel.declare_exchange(
                    "broadcast_exchange",
                    aio_pika.ExchangeType.FANOUT,
                    durable=True
                )
                
                self.broadcast_queue = await self.rabbit_channel.declare_queue(
                    f"broadcast_queue_{self.worker_id}",
                    durable=True,
                    arguments={
                        'x-dead-letter-exchange': 'dlx',
                        'x-max-length': 50000,
                        'x-max-priority': 4,
                        'x-message-ttl': 60000
                    }
                )
                
                await self.broadcast_queue.bind(self.broadcast_exchange)
                
                self.personal_queue = await self.rabbit_channel.declare_queue(
                    f"personal_queue_{self.worker_id}",
                    durable=True,
                    arguments={
                        'x-dead-letter-exchange': 'dlx',
                        'x-max-length': 50000,
                        'x-max-priority': 4,
                        'x-message-ttl': 60000
                    }
                )
                
                # 重启消息处理任务
                task = asyncio.create_task(self.process_broadcast_queue())
                self.tasks.add(task)
                task.add_done_callback(self.tasks.discard)
                
                task = asyncio.create_task(self.process_personal_queue())
                self.tasks.add(task)
                task.add_done_callback(self.tasks.discard)
                
                logger.info("RabbitMQ重连成功")
                return True
            except Exception as e:
                logger.error(f"RabbitMQ重连失败: {str(e)}")
                retry_count += 1
                retry_delay *= 2  # 指数退避
                if retry_count < max_retries:
                    logger.info(f"将在 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
        
        logger.error(f"多次重连尝试后仍无法连接到RabbitMQ，将以降级模式运行")
        return False

    async def monitor_connections(self):
        """监控连接状态"""
        while not self._closed:
            try:
                current_time = asyncio.get_event_loop().time()
                # 检查心跳超时
                for token, last_time in list(self.last_heartbeat.items()):
                    if current_time - last_time > self.heartbeat_timeout_seconds:
                        logger.warning(f"连接心跳超时 (Token: {token[:10]}, 超时时间: {current_time - last_time:.1f}秒)")
                        await self.disconnect(token)
                
                # 记录当前状态
                logger.info(
                    f"连接状态 (Worker {self.worker_id}): "
                    f"活跃连接数={len(self.active_connections)}, "
                    f"用户数={len(self.user_tokens)}"
                )
                
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                if not self._closed:
                    logger.error(f"监控连接状态失败: {str(e)}")
                    await asyncio.sleep(5)

    async def auto_cleanup(self):
        """自动清理过期的队列和交换机"""
        while not self._closed:
            try:
                current_time = time.time()
                if current_time - self.last_cleanup > 3600:  # 每小时清理一次
                    if self.cleaner:
                        await self.cleaner.cleanup(idle_queue_time=3600, idle_connection_time=1800)
                        self.last_cleanup = current_time
                        logger.info("自动清理队列和连接完成")
                await asyncio.sleep(60)
            except Exception as e:
                if not self._closed:
                    logger.error(f"自动清理失败: {str(e)}")
                    await asyncio.sleep(60)

    async def cleanup_completed_tasks(self):
        """定期清理已完成的任务，防止内存泄漏"""
        while not self._closed:
            try:
                # 清理已完成的任务
                completed_tasks = [task for task in self.tasks if task.done()]
                for task in completed_tasks:
                    self.tasks.discard(task)  # 使用discard避免KeyError

                if completed_tasks:
                    logger.debug(f"清理了 {len(completed_tasks)} 个已完成的任务，剩余任务数: {len(self.tasks)}")

                # 每30秒清理一次
                await asyncio.sleep(30)
            except Exception as e:
                if not self._closed:
                    logger.error(f"清理已完成任务失败: {str(e)}")
                    await asyncio.sleep(5)

    async def cleanup(self):
        """清理资源"""
        try:
            self._closed = True

            # 断开所有连接
            for token in list(self.active_connections.keys()):
                await self.disconnect(token)

            # 取消所有任务
            for task in self.tasks:
                if not task.done():
                    task.cancel()

            # 清理所有任务
            self.tasks.clear()

            # 关闭RabbitMQ连接
            if self.rabbit_connection and not self.rabbit_connection.is_closed:
                await self.rabbit_connection.close()

            logger.info(f"ConnectionManager资源清理完成 (Worker {self.worker_id})")
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")

    async def handle_heartbeat(self, websocket, token, data=None):
        """处理心跳请求，优化响应频率和可靠性"""
        try:
            # 更新连接的最后心跳时间
            if token in self.active_connections:
                current_time = time.time()
                self.active_connections[token]["last_heartbeat"] = current_time
                self.last_heartbeat[token] = current_time  # 确保两处心跳时间同步更新
                
                # 获取心跳计数器，如果不存在则初始化为0
                heartbeat_counter = self.active_connections[token].get("heartbeat_counter", 0)
                heartbeat_counter += 1
                self.active_connections[token]["heartbeat_counter"] = heartbeat_counter
                
                # 优化Redis操作：批量更新策略
                # 只有在以下情况才更新Redis:
                # 1. 每10次心跳更新一次
                # 2. 首次心跳
                # 3. 距离上次更新超过30秒
                username = self.active_connections[token].get("username")
                last_redis_update = self.active_connections[token].get("last_redis_update", 0)
                
                should_update_redis = (
                    heartbeat_counter % 10 == 0 or  # 每10次心跳
                    heartbeat_counter == 1 or       # 首次心跳
                    current_time - last_redis_update > 30  # 超过30秒未更新
                )
                
                if should_update_redis and username:
                    try:
                        # 使用pipeline批量执行Redis操作
                        redis_client = await self._get_redis_client()
                        pipe = redis_client.pipeline()
                        
                        user_cache_manager = await UserCacheManager.get_instance()
                        # 1. 更新用户连接TTL (使用更长的过期时间，减少更新频率)
                        await user_cache_manager.update_user_connection_ttl(username, ttl=3600)
                        
                        # 2. 记录最后更新时间
                        self.active_connections[token]["last_redis_update"] = current_time
                        
                        # 3. 更新进程间共享的心跳状态 (使用哈希结构提高效率)
                        worker_key = f"heartbeat:worker:{self.worker_id}"
                        pipe.hset(worker_key, token, int(current_time))
                        pipe.expire(worker_key, 3600)
                        
                        # 执行pipeline
                        await pipe.execute()
                    except Exception as e:
                        # 记录错误但不影响心跳响应
                        logger.warning(f"更新Redis心跳状态失败: {str(e)}")
                
                # 心跳响应策略：降低响应频率
                # 每3次心跳才发送一次完整响应，其他时候发送简化响应
                try:
                    if heartbeat_counter % 3 == 0:
                        # 完整响应
                        await websocket.send_json(MessageModel(
                            msgId=MessageId.HEARTBEAT,
                            data={
                                "ts": int(current_time),
                                "counter": heartbeat_counter,
                                "status": "ok"
                            }
                        ).model_dump())
                    else:
                        # 简化响应 - 仅发送最小数据
                        await websocket.send_json({"msgId": MessageId.HEARTBEAT, "data": {"ts": int(current_time)}})
                    return True
                except Exception as e:
                    # 如果发送失败，记录但不终止连接，让重试机制处理
                    logger.warning(f"发送心跳响应失败 (Token: {token[:10]}): {str(e)}")
                    return False
            return False
        except Exception as e:
            logger.error(f"处理心跳请求时出错 (Token: {token[:10] if token else 'None'}): {str(e)}")
            return False

    async def subscribe_redis_events(self):
        """订阅Redis事件，处理多进程间的用户登录通知"""
        redis_manager = await RedisManager.get_instance()
        redis = await redis_manager.get_redis()
        if not redis:
            logger.warning("Redis客户端不可用，无法订阅多进程事件")
            return
        
        retry_count = 0
        max_retries = 10  # 允许更多重试，因为这是关键功能
        retry_delay = 1
            
        while not self._closed and retry_count < max_retries:
            try:
                # 创建Redis发布/订阅连接
                pubsub = redis.pubsub()
                await pubsub.subscribe("user_login_channel")
                logger.info(f"已订阅Redis用户登录通道 (Worker {self.worker_id})")
                
                # 重置重试计数器，因为成功订阅
                retry_count = 0
                
                # 持续监听消息
                while not self._closed:
                    try:
                        message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                        if message and message["type"] == "message":
                            try:
                                data = json.loads(message["data"])
                                
                                # 处理用户登录事件
                                if data.get("action") == "user_login":
                                    username = data.get("username")
                                    new_token = data.get("token")
                                    source_worker = data.get("worker_id")
                                    
                                    # 只处理来自其他worker的消息
                                    if source_worker != self.worker_id and username in self.user_tokens:
                                        old_token = self.user_tokens[username]
                                        if old_token in self.active_connections:
                                            logger.info(f"收到其他进程的用户登录通知，断开本地连接 (用户: {username}, Worker: {self.worker_id})")
                                            
                                            # 通知客户端被踢下线
                                            try:
                                                old_websocket = self.active_connections[old_token]["websocket"]
                                                if old_websocket and old_websocket.application_state == WebSocketState.CONNECTED:
                                                    await old_websocket.send_json(MessageModel(
                                                        msgId=MessageId.ERROR,  # 系统消息
                                                        data={
                                                            "error": "您的账号已在其他设备登录",
                                                            "timestamp": datetime.now().isoformat()
                                                        }
                                                    ).model_dump())
                                            except Exception as e:
                                                logger.error(f"发送踢下线通知失败: {str(e)}")
                                            
                                            # 断开连接
                                            await self.disconnect(old_token)
                            except json.JSONDecodeError as e:
                                logger.error(f"解析Redis消息失败: {str(e)}")
                        
                        # 避免CPU占用过高
                        await asyncio.sleep(0.1)
                        
                    except redis_exceptions.RedisError as e:
                        if not self._closed:
                            logger.error(f"Redis获取消息出错: {str(e)}")
                            # 如果是连接错误，跳出内循环以便重新订阅
                            if isinstance(e, (redis_exceptions.ConnectionError, redis_exceptions.TimeoutError)):
                                logger.warning("Redis连接错误，将重新订阅")
                                break
                            await asyncio.sleep(1)
                    except Exception as e:
                        if not self._closed:
                            logger.error(f"处理Redis消息时出错: {str(e)}")
                            await asyncio.sleep(1)
                
                # 如果内循环因为错误而退出，尝试关闭pubsub
                try:
                    await pubsub.close()
                except Exception:
                    pass
                    
            except redis_exceptions.RedisError as e:
                retry_count += 1
                logger.error(f"Redis订阅出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
                if retry_count < max_retries:
                    # 使用指数退避策略
                    sleep_time = retry_delay * (2 ** (retry_count - 1))
                    logger.info(f"将在 {sleep_time} 秒后重试订阅")
                    await asyncio.sleep(sleep_time)
                else:
                    logger.error("达到最大重试次数，放弃Redis事件订阅")
            except Exception as e:
                retry_count += 1
                logger.error(f"Redis订阅出现未知错误 (尝试 {retry_count}/{max_retries}): {str(e)}")
                logger.error(traceback.format_exc())
                if retry_count < max_retries:
                    await asyncio.sleep(retry_delay * (2 ** (retry_count - 1)))
                else:
                    logger.error("达到最大重试次数，放弃Redis事件订阅")
        
        if not self._closed:
            logger.warning("Redis事件订阅任务结束，但ConnectionManager仍在运行，这可能导致多worker模式下的同步问题")

    async def handle_heartbeat_timeout(self, token: str, last_heartbeat_time: float, current_time: float, is_vip: bool = False) -> bool:
        """统一处理心跳超时逻辑
        
        Args:
            token: 用户token
            last_heartbeat_time: 上次心跳时间
            current_time: 当前时间
            is_vip: 是否为VIP用户
            
        Returns:
            bool: 是否已处理超时（断开连接）
        """
        try:
            # 计算超时时间
            default_timeout = self.heartbeat_timeout_seconds
            
            # VIP用户可以有更长的超时时间
            heartbeat_timeout = default_timeout * 2 if is_vip else default_timeout
            
            # 根据连接历史调整超时时间
            if token in self.active_connections:
                heartbeat_counter = self.active_connections[token].get("heartbeat_counter", 0)
                if heartbeat_counter > 100:  # 长期稳定连接
                    heartbeat_timeout *= 1.5  # 增加50%的容忍度
            
            time_since_last_heartbeat = current_time - last_heartbeat_time
            
            # 检查是否超时
            if time_since_last_heartbeat > heartbeat_timeout:
                # 超时前先尝试发送ping
                try:
                    if token in self.active_connections:
                        websocket = self.active_connections[token]["websocket"]
                        if websocket and websocket.application_state == WebSocketState.CONNECTED:
                            # 发送ping消息
                            try:
                                await websocket.send_json({"msgId": MessageId.PING, "data": {}})
                                # 给予短暂宽限期
                                self.last_heartbeat[token] = current_time - heartbeat_timeout * 0.5
                                logger.info(f"连接心跳超时，已发送ping (Token: {token[:10]}, 超时: {time_since_last_heartbeat:.1f}秒)")
                                return False  # 未断开连接，给予宽限期
                            except Exception:
                                # ping失败，断开连接
                                logger.warning(f"连接心跳超时且ping失败 (Token: {token[:10]}, 超时: {time_since_last_heartbeat:.1f}秒)")
                                await self.disconnect(token)
                                return True  # 已断开连接
                        else:
                            # WebSocket已不在连接状态
                            await self.disconnect(token)
                            return True  # 已断开连接
                except Exception as e:
                    logger.error(f"检查超时连接失败: {str(e)}")
                    await self.disconnect(token)
                    return True  # 已断开连接
            
            return False  # 未超时，未断开连接
        except Exception as e:
            logger.error(f"处理心跳超时失败 (Token: {token[:10]}): {str(e)}")
            try:
                await self.disconnect(token)
            except Exception:
                pass
            return True  # 异常情况下，假设已断开连接

    async def check_connections(self):
        """定期检查连接状态，优化心跳超时处理"""
        while True:
            try:
                now = time.time()
                active_connections = 0
                active_users = 0
                
                # 复制连接字典，避免在迭代过程中修改
                connections = list(self.active_connections.items())
                redis_client = await self._get_redis_client()
                # 每10分钟从Redis加载其他进程的连接信息
                if int(now) % 600 < 10:
                    try:
                        # 获取所有worker的心跳数据
                        retry_count = 0
                        max_retries = 3
                        while retry_count < max_retries:
                            try:
                                all_worker_keys = await redis_client.keys("heartbeat:worker:*")
                                break
                            except redis_exceptions.RedisError as e:
                                retry_count += 1
                                if retry_count >= max_retries:
                                    logger.error(f"获取worker心跳键失败，已达最大重试次数: {str(e)}")
                                    all_worker_keys = []
                                    break
                                await asyncio.sleep(0.5 * retry_count)
                        
                        if all_worker_keys:
                            for worker_key in all_worker_keys:
                                worker_id = worker_key.split(':')[-1]
                                # 跳过自己的worker
                                if worker_id == self.worker_id:
                                    continue
                                
                                try:
                                    # 获取该worker的所有token心跳时间
                                    heartbeats = await redis_client.hgetall(worker_key)
                                    if heartbeats:
                                        for token_bytes, timestamp_bytes in heartbeats.items():
                                            try:
                                                token = token_bytes.decode('utf-8') if isinstance(token_bytes, bytes) else token_bytes
                                                timestamp = int(timestamp_bytes) if isinstance(timestamp_bytes, bytes) else int(timestamp_bytes)
                                                
                                                # 如果是本地也有的token，检查是否需要断开本地连接
                                                if token in self.active_connections:
                                                    local_timestamp = self.last_heartbeat.get(token, 0)
                                                    # 如果其他进程的心跳时间更新，说明用户已在其他进程连接
                                                    if timestamp > local_timestamp + 10:  # 允许10秒误差
                                                        logger.info(f"检测到token在其他进程活跃度更高，断开本地连接: {token[:10]}")
                                                        await self.disconnect(token)
                                            except Exception as e:
                                                logger.warning(f"处理其他进程心跳数据出错: {str(e)}")
                                except Exception as e:
                                    logger.warning(f"获取worker {worker_id} 的心跳数据失败: {str(e)}")
                    except Exception as e:
                        logger.warning(f"从Redis加载其他进程连接信息失败: {str(e)}")
                
                # 检查当前连接状态
                for token, conn_data in connections:
                    if conn_data["websocket"] is not None:
                        active_connections += 1
                        
                        # 获取上次心跳时间
                        last_heartbeat = self.last_heartbeat.get(token, 0)
                        
                        # 检查用户是否为VIP
                        username = conn_data.get("username")
                        is_vip = False  # 这里可以添加VIP检查逻辑
                        
                        # 使用统一的心跳超时处理逻辑
                        timeout_handled = await self.handle_heartbeat_timeout(token, last_heartbeat, now, is_vip)
                        
                        if not timeout_handled:
                            active_users += 1
                
                # 每30秒记录一次连接状态
                if int(now) % 30 == 0:
                    logger.info(f"连接状态 (Worker {self.worker_id}): 活跃连接数={active_users}, 用户数={len(self.user_tokens)}")
                
                # 自适应检查间隔：连接数多时检查频率降低，减轻系统负担
                check_interval = min(max(1, active_connections / 1000), 10)  # 1-10秒范围
                await asyncio.sleep(check_interval)
            except Exception as e:
                logger.error(f"检查连接状态时出错: {str(e)}")
                await asyncio.sleep(5)

    async def monitor_connection_pools(self):
        """监控连接池状态"""
        while not self._closed:
            try:
                # 监控Redis连接池
                redis_client = await self._get_redis_client()
                if redis_client:
                    try:
                        if hasattr(redis_client, 'connection_pool'):
                            pool = redis_client.connection_pool
                            in_use = len(pool._in_use_connections) if hasattr(pool, '_in_use_connections') else 0
                            available = len(pool._available_connections) if hasattr(pool, '_available_connections') else 0
                            
                            # 记录连接池状态
                            logger.info(f"Redis连接池状态 (Worker {self.worker_id}): 使用中={in_use}, 可用={available}, 总计={in_use+available}")
                            
                            # 如果使用率过高，发出警告
                            if in_use > 0 and in_use > 0.9 * (in_use + available):
                                logger.warning(f"Redis连接池使用率过高 (Worker {self.worker_id}): {in_use}/{in_use+available} ({in_use/(in_use+available)*100:.1f}%)")
                    except Exception as e:
                        logger.warning(f"监控Redis连接池失败 (Worker {self.worker_id}): {str(e)}")
                
                # 监控RabbitMQ连接状态
                if self.rabbit_connection:
                    try:
                        is_closed = self.rabbit_connection.is_closed
                        logger.info(f"RabbitMQ连接状态 (Worker {self.worker_id}): {'已关闭' if is_closed else '连接中'}")
                        
                        if is_closed and not self._closed:
                            logger.warning(f"RabbitMQ连接已关闭，尝试重连 (Worker {self.worker_id})")
                            await self._reconnect_rabbitmq()
                    except Exception as e:
                        logger.warning(f"监控RabbitMQ连接失败 (Worker {self.worker_id}): {str(e)}")
                
                # 每分钟检查一次
                await asyncio.sleep(60)
            except Exception as e:
                logger.error(f"监控连接池失败 (Worker {self.worker_id}): {str(e)}")
                await asyncio.sleep(60)