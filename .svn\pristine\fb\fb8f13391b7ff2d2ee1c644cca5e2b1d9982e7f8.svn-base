"""
商店购买事务服务
"""

import uuid
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from distributed_lock import DistributedLock
from shop_event_broadcaster import get_shop_event_broadcaster
from shop_database_manager import ShopDatabaseManager
from shop_cache_service import ShopCacheService
from shop_limit_service import ShopLimitService
from shop_discount_service import ShopDiscountService
from shop_models import (
    PurchaseRequest, PurchaseResult, ValidationResult, 
    ShopPurchase, ShopItemConfig, PriceInfo
)

logger = logging.getLogger(__name__)


class ShopPurchaseService:
    """商店购买事务服务"""
    
    def __init__(self):
        self.db_manager = ShopDatabaseManager()
        self.cache_service = ShopCacheService()
        self.limit_service = ShopLimitService()
        self.discount_service = ShopDiscountService()
        
        # 外部服务接口 (需要注入)
        self.currency_service = None  # 货币服务
        self.player_service = None    # 玩家服务

    def set_external_services(self, currency_service, player_service):
        """设置外部服务"""
        self.currency_service = currency_service
        self.player_service = player_service
    
    # ==================== 购买主流程 ====================
    
    async def purchase_item(self, request: PurchaseRequest) -> PurchaseResult:
        """购买商品 - 主入口"""
        purchase_lock_key = f"purchase:lock:{request.player_id}:{request.config_id}"
        
        try:
            # 使用分布式锁防止重复购买
            async with DistributedLock(purchase_lock_key, 30):
                return await self._execute_purchase_transaction(request)
                
        except Exception as e:
            logger.error(f"购买商品时发生错误: {str(e)}")
            return PurchaseResult(
                success=False,
                error=f"购买失败: {str(e)}"
            )
    
    async def _execute_purchase_transaction(self, request: PurchaseRequest) -> PurchaseResult:
        """执行购买事务"""
        transaction = PurchaseTransaction(request, self)
        return await transaction.execute()
    
    # ==================== 购买验证 ====================
    
    async def validate_purchase(self, request: PurchaseRequest) -> ValidationResult:
        """验证购买请求"""
        try:
            # 1. 验证商品配置
            config = await self.db_manager.get_item_config(request.config_id)
            if not config:
                return ValidationResult(success=False, error="商品不存在")
            
            if not config.is_active:
                return ValidationResult(success=False, error="商品已下架")
            
            # 2. 验证商品可用性
            availability_check = await self._check_item_availability(config, request.player_id)
            if not availability_check["available"]:
                return ValidationResult(success=False, error=availability_check["reason"])
            
            # 3. 验证购买数量
            if request.quantity <= 0:
                return ValidationResult(success=False, error="购买数量必须大于0")
            
            # 4. 验证限购
            limit_check = await self.limit_service.check_purchase_limit(
                request.player_id, request.config_id, request.quantity
            )
            if not limit_check.can_purchase:
                return ValidationResult(
                    success=False, 
                    error=f"超出限购数量，剩余可购买: {limit_check.remaining}"
                )
            
            # 5. 验证货币
            price_info = await self.discount_service.calculate_final_price(
                request.player_id, request.config_id, request.quantity
            )
            
            if self.currency_service:
                currency_check = await self.currency_service.check_currency(
                    request.player_id, price_info.currency_type, price_info.final_price
                )
                if not currency_check:
                    return ValidationResult(success=False, error="货币不足")
            
            return ValidationResult(
                success=True,
                details={
                    "config": config,
                    "price_info": price_info,
                    "limit_check": limit_check
                }
            )
            
        except Exception as e:
            logger.error(f"验证购买请求时发生错误: {str(e)}")
            return ValidationResult(success=False, error=f"验证失败: {str(e)}")
    
    async def _check_item_availability(self, config: ShopItemConfig, player_id: str) -> Dict[str, Any]:
        """检查商品可用性"""
        try:
            now = datetime.now()
            
            # 1. 检查时间限制
            availability = config.availability
            if availability.get("start_time"):
                start_time = datetime.fromisoformat(availability["start_time"])
                if now < start_time:
                    return {"available": False, "reason": "商品尚未开始销售"}
            
            if availability.get("end_time"):
                end_time = datetime.fromisoformat(availability["end_time"])
                if now > end_time:
                    return {"available": False, "reason": "商品销售已结束"}
            
            # 2. 检查玩家条件
            conditions = availability.get("conditions", {})
            
            if conditions.get("player_level") and self.player_service:
                player_level = await self.player_service.get_player_level(player_id)
                if player_level < conditions["player_level"]:
                    return {"available": False, "reason": f"需要玩家等级 {conditions['player_level']}"}
            
            if conditions.get("vip_level") and self.player_service:
                vip_level = await self.player_service.get_vip_level(player_id)
                if vip_level < conditions["vip_level"]:
                    return {"available": False, "reason": f"需要VIP等级 {conditions['vip_level']}"}
            
            return {"available": True}
            
        except Exception as e:
            logger.error(f"检查商品可用性时发生错误: {str(e)}")
            return {"available": False, "reason": "检查失败"}
    
    # ==================== 价格计算 ====================
    
    async def calculate_price(self, player_id: str, config_id: str, quantity: int) -> PriceInfo:
        """计算商品价格"""
        return await self.discount_service.calculate_final_price(player_id, config_id, quantity)
    
    # ==================== 购买历史 ====================
    
    async def get_purchase_history(self, player_id: str, limit: int = 50, offset: int = 0) -> List[ShopPurchase]:
        """获取购买历史"""
        return await self.db_manager.get_player_purchases(player_id, limit, offset)


class PurchaseTransaction:
    """购买事务 - 保证原子性"""
    
    def __init__(self, request: PurchaseRequest, service: ShopPurchaseService):
        self.request = request
        self.service = service
        self.purchase_id = f"purchase_{uuid.uuid4().hex[:16]}"
        self.rollback_actions: List[Callable] = []
        
        # 事务数据
        self.config: Optional[ShopItemConfig] = None
        self.price_info: Optional[PriceInfo] = None
        self.item_instances: List[str] = []
    
    async def execute(self) -> PurchaseResult:
        """执行购买事务"""
        try:
            # 1. 验证购买条件
            validation = await self.service.validate_purchase(self.request)
            if not validation.success:
                return PurchaseResult(success=False, error=validation.error)
            
            self.config = validation.details["config"]
            self.price_info = validation.details["price_info"]
            
            # 2. 扣除货币
            currency_result = await self._deduct_currency()
            if not currency_result:
                return PurchaseResult(success=False, error="货币扣除失败")
            
            # 3. 创建道具实例
            items_result = await self._create_item_instances()
            if not items_result:
                await self._rollback()
                return PurchaseResult(success=False, error="道具创建失败")
            
            
            # 5. 更新限购计数
            await self._update_purchase_limits()
            
            # 6. 记录购买日志
            await self._record_purchase()

            # 7. 广播购买成功事件
            await self._broadcast_purchase_success()

            logger.info(f"购买成功: {self.purchase_id}, 玩家: {self.request.player_id}, 商品: {self.request.config_id}")

            return PurchaseResult(
                success=True,
                purchase_id=self.purchase_id,
                items=self.item_instances,
                data={
                    "original_price": self.price_info.original_price,
                    "final_price": self.price_info.final_price,
                    "currency_type": self.price_info.currency_type,
                    "discounts_applied": self.price_info.discounts_applied
                }
            )
            
        except Exception as e:
            await self._rollback()
            logger.error(f"购买事务执行失败: {str(e)}")
            return PurchaseResult(success=False, error=f"购买失败: {str(e)}")
    
    async def _deduct_currency(self) -> bool:
        """扣除货币"""
        try:
            if not self.service.currency_service:
                logger.warning("货币服务未配置，跳过货币扣除")
                return True
            
            success = await self.service.currency_service.deduct_currency(
                self.request.player_id,
                self.price_info.currency_type,
                self.price_info.final_price
            )
            
            if success:
                # 添加回滚操作
                self.rollback_actions.append(
                    lambda: self.service.currency_service.add_currency(
                        self.request.player_id,
                        self.price_info.currency_type,
                        self.price_info.final_price
                    )
                )
            
            return success
            
        except Exception as e:
            logger.error(f"扣除货币时发生错误: {str(e)}")
            return False
    
    async def _create_item_instances(self) -> bool:
        """创建道具实例"""
        try:
            # 解析道具模板ID为defid（假设格式为数字或可转换为数字）
            try:
                defid = int(self.config.item_template_id)
            except ValueError:
                # 如果不是数字，使用hash值
                logger.erro("item_template_id 错误")
                return False

            # 创建道具实例
            from UserCacheManager import UserCacheManager
            user_cache = await UserCacheManager.get_instance()
            item_data = {
                "defid": defid,
                "type": self.config.item_type,
                "quantity": self.request.quantity or 1,
                "source": "shop_purchase",
            }
            username = self.request.player_id

            result = await user_cache.add_user_asset(username, self.config.item_type, item_data,False,True)
            
            return result.success

        except Exception as e:
            logger.error(f"创建道具实例时发生错误: {str(e)}")
            return False
        
    async def _update_purchase_limits(self):
        """更新限购计数"""
        try:
            await self.service.limit_service.update_purchase_count(
                self.request.player_id,
                self.request.config_id,
                self.request.quantity
            )
            
        except Exception as e:
            logger.error(f"更新限购计数时发生错误: {str(e)}")
    
    async def _record_purchase(self):
        """记录购买日志"""
        try:
            purchase = ShopPurchase(
                purchase_id=self.purchase_id,
                player_id=self.request.player_id,
                shop_id=self.config.shop_id,
                config_id=self.request.config_id,
                item_template_id=self.config.item_template_id,
                item_quantity=self.request.quantity,
                item_instances=self.item_instances,
                currency_type=self.price_info.currency_type,
                original_price=self.price_info.original_price,
                final_price=self.price_info.final_price,
                discount_applied=self.price_info.discounts_applied,
                limit_context=None,  # 可以添加限购上下文信息
                purchase_time=datetime.now(),
                metadata=self.request.metadata or {},
                created_at=datetime.now()
            )
            
            await self.service.db_manager.create_purchase(purchase)
            
        except Exception as e:
            logger.error(f"记录购买日志时发生错误: {str(e)}")

    async def _broadcast_purchase_success(self):
        """广播购买成功事件"""
        try:
            broadcaster = get_shop_event_broadcaster()

            purchase_data = {
                "purchase_id": self.purchase_id,
                "item_template_id": self.config.item_template_id,
                "quantity": self.request.quantity,
                "final_price": self.price_info.final_price,
                "currency_type": self.price_info.currency_type,
                "shop_id": self.config.shop_id
            }

            await broadcaster.broadcast_purchase_success(self.request.player_id, purchase_data)

            # 通知货币变化
            currency_changes = {
                self.price_info.currency_type: -self.price_info.final_price
            }
            await broadcaster.notify_currency_change(self.request.player_id, currency_changes)

        except Exception as e:
            logger.error(f"广播购买成功事件时发生错误: {str(e)}")

    async def _rollback(self):
        """回滚操作"""
        logger.warning(f"开始回滚购买事务: {self.purchase_id}")

        for action in reversed(self.rollback_actions):
            try:
                await action()
            except Exception as e:
                logger.error(f"回滚操作失败: {str(e)}")

        logger.warning(f"购买事务回滚完成: {self.purchase_id}")
