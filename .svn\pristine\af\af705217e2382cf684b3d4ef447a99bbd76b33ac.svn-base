"""
简化的调度器监控API
避免复杂依赖，提供基本的调度器状态查询
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
import traceback
from logger_config import setup_logger

logger = setup_logger(__name__)

# 创建路由器
scheduler_router = APIRouter(prefix="/api/scheduler", tags=["调度器管理"])

@scheduler_router.get("/ping")
async def ping():
    """简单的ping接口"""
    return {"status": "ok", "message": "pong"}

@scheduler_router.get("/test")
async def test_scheduler_api():
    """测试调度器API是否可用"""
    return {"message": "调度器API正常工作", "status": "ok"}

@scheduler_router.get("/health")
async def get_health_check():
    """获取基本健康检查信息"""
    try:
        # 基本健康检查，不依赖复杂的调度器逻辑
        from service_locator import ServiceLocator
        
        health_info = {
            "status": "ok",
            "message": "基本健康检查",
            "services": {}
        }
        
        # 检查基本服务
        services_to_check = [
            ("redis_manager", "Redis管理器"),
            ("mongodb_manager", "MongoDB管理器"),
            ("conn_manager", "连接管理器"),
            ("monster_cooldown_manager", "怪物冷却管理器")
        ]
        
        for service_key, service_name in services_to_check:
            service = ServiceLocator.get(service_key)
            health_info["services"][service_key] = {
                "name": service_name,
                "available": service is not None,
                "type": str(type(service).__name__) if service else None
            }
        
        return health_info
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "error_details": traceback.format_exc()
        }

@scheduler_router.get("/status")
async def get_scheduler_status():
    """获取调度器状态"""
    try:
        from service_locator import ServiceLocator
        
        status_info = {
            "api_status": "ok",
            "message": "调度器状态查询",
            "services": {},
            "scheduler_info": {}
        }
        
        # 检查调度器相关服务
        scheduler_manager = ServiceLocator.get("scheduler_manager")
        task_queue = ServiceLocator.get("task_queue")
        
        status_info["scheduler_info"] = {
            "scheduler_manager_available": scheduler_manager is not None,
            "task_queue_available": task_queue is not None
        }
        
        # 尝试获取统一调度器状态
        try:
            from game_server_scheduler_integration import get_game_server_scheduler
            import asyncio
            
            scheduler_integration = await get_game_server_scheduler()
            if scheduler_integration:
                status_info["unified_scheduler"] = {
                    "available": True,
                    "is_running": scheduler_integration.is_running(),
                    "is_initialized": scheduler_integration.is_initialized
                }
            else:
                status_info["unified_scheduler"] = {
                    "available": False,
                    "message": "统一调度器实例不存在"
                }
                
        except Exception as e:
            status_info["unified_scheduler"] = {
                "available": False,
                "error": str(e)
            }
        
        return status_info
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取调度器状态失败: {str(e)}",
            "error_details": traceback.format_exc()
        }

@scheduler_router.get("/services")
async def get_services_info():
    """获取所有注册服务信息"""
    try:
        from service_locator import ServiceLocator
        
        # 获取所有已注册的服务
        services_info = {
            "total_services": 0,
            "services": {},
            "message": "服务信息查询成功"
        }
        
        # 常见的服务列表
        common_services = [
            "redis_manager",
            "mongodb_manager", 
            "conn_manager",
            "monster_cooldown_manager",
            "scheduler_manager",
            "task_queue",
            "db_manager"
        ]
        
        for service_key in common_services:
            service = ServiceLocator.get(service_key)
            if service:
                services_info["services"][service_key] = {
                    "available": True,
                    "type": str(type(service).__name__),
                    "module": str(type(service).__module__)
                }
                services_info["total_services"] += 1
            else:
                services_info["services"][service_key] = {
                    "available": False,
                    "type": None,
                    "module": None
                }
        
        return services_info
        
    except Exception as e:
        logger.error(f"获取服务信息失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取服务信息失败: {str(e)}",
            "error_details": traceback.format_exc()
        }

@scheduler_router.get("/debug")
async def get_debug_info():
    """获取调试信息"""
    try:
        import os
        import sys
        from datetime import datetime
        
        debug_info = {
            "timestamp": datetime.now().isoformat(),
            "process_id": os.getpid(),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "environment": {
                "PYTHONPATH": os.environ.get("PYTHONPATH", "未设置"),
            },
            "modules": {
                "fastapi_available": True,
                "service_locator_available": False,
                "scheduler_files_exist": {}
            }
        }
        
        # 检查关键模块
        try:
            import service_locator
            debug_info["modules"]["service_locator_available"] = True
        except ImportError as e:
            debug_info["modules"]["service_locator_error"] = str(e)
        
        # 检查调度器文件是否存在
        scheduler_files = [
            "unified_scheduler_manager.py",
            "scheduler_health_checks.py", 
            "scheduler_tasks_unified.py",
            "game_server_scheduler_integration.py"
        ]
        
        for filename in scheduler_files:
            debug_info["modules"]["scheduler_files_exist"][filename] = os.path.exists(filename)
        
        return debug_info
        
    except Exception as e:
        logger.error(f"获取调试信息失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取调试信息失败: {str(e)}",
            "error_details": traceback.format_exc()
        }
