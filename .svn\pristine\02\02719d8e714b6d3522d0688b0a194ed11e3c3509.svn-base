from CacheKeyBuilder import CacheKeyBuilder
from redis_manager import RedisManager
from typing import Optional, Dict, List
import os
import datetime
from redis import exceptions as redis_exceptions  # 显式导入Redis异常
from logger_config import setup_logger
from CacheManager import CacheManager
# 初始化日志系统
logger = setup_logger(__name__)

class GeneralCacheManager:
    _instance = None
    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            redis_manager = await RedisManager.get_instance()
            redis_client = await redis_manager.get_redis()
            cache_manager = CacheManager(redis_client)
            cls._instance = cls(cache_manager)
        return cls._instance
    def __init__(self, cache_manager):
        self.cache = cache_manager
    
    async def get_player_generals(self, player_id: str) -> List[Dict]:
        """获取玩家武将列表缓存"""
        key = CacheKeyBuilder.generals_by_player(player_id)
        generals = await self.cache.get(key) or []
        return generals if isinstance(generals, list) else []
    
    async def set_player_generals(self, player_id: str, generals: List[Dict], ttl: int = 3600):
        """设置玩家武将列表缓存"""
        key = CacheKeyBuilder.generals_by_player(player_id)
        return await self.cache.set(key, generals, ttl)
    
    async def get_general_details(self, player_id: str, general_id: str) -> Optional[Dict]:
        """获取武将详情缓存"""
        key = CacheKeyBuilder.general_details(player_id, general_id)
        return await self.cache.get(key)
    
    async def set_general_details(self, player_id: str, general_id: str, general_data: Dict, ttl: int = 86400):
        """设置武将详情缓存"""
        key = CacheKeyBuilder.general_details(player_id, general_id)
        return await self.cache.set(key, general_data, ttl)
    
    async def get_general_properties(self, player_id: str, general_id: str) -> Optional[Dict]:
        """获取武将属性计算结果缓存"""
        key = CacheKeyBuilder.general_properties(player_id, general_id)
        return await self.cache.get(key)
    
    async def set_general_properties(self, player_id: str, general_id: str, properties: Dict, ttl: int = 600):
        """设置武将属性计算结果缓存（10分钟过期）"""
        key = CacheKeyBuilder.general_properties(player_id, general_id)
        return await self.cache.set(key, properties, ttl)
    
    async def get_general_fight_power(self, player_id: str, general_id: str) -> Optional[int]:
        """获取武将战斗力缓存"""
        key = CacheKeyBuilder.general_fight_power(player_id, general_id)
        return await self.cache.get(key)
    
    async def set_general_fight_power(self, player_id: str, general_id: str, fight_power: int, ttl: int = 600):
        """设置武将战斗力缓存（10分钟过期）"""
        key = CacheKeyBuilder.general_fight_power(player_id, general_id)
        return await self.cache.set(key, fight_power, ttl)
    
    async def get_formation_data(self, player_id: str) -> Optional[Dict]:
        """获取玩家阵容缓存"""
        key = CacheKeyBuilder.formation_data(player_id)
        return await self.cache.get(key)
    
    async def set_formation_data(self, player_id: str, formation_data: Dict, ttl: int = 3600):
        """设置玩家阵容缓存"""
        key = CacheKeyBuilder.formation_data(player_id)
        return await self.cache.set(key, formation_data, ttl)
    
    async def add_general(self, general: Dict, ttl: int = 3600):
        """添加武将并更新相关缓存"""
        if not general or not isinstance(general, dict):
            return False
        
        player_id = general.get("player_id")
        general_id = general.get("general_id")
        
        if not player_id or not general_id:
            return False
        
        # 1. 缓存武将详情
        await self.set_general_details(player_id, general_id, general, ttl=86400)
        
        # 2. 更新玩家武将列表
        generals = await self.get_player_generals(player_id)
        
        # 检查是否已存在
        found = False
        for i, existing_general in enumerate(generals):
            if existing_general.get("general_id") == general_id:
                generals[i] = general
                found = True
                break
        
        if not found:
            generals.append(general)
        
        # 更新缓存
        return await self.set_player_generals(player_id, generals, ttl)
    
    async def update_general(self, general: Dict, ttl: int = 3600):
        """更新武将并刷新相关缓存"""
        # 逻辑与add_general相同，但更注重确保武将已存在
        return await self.add_general(general, ttl)
    
    async def delete_general(self, general_id: str, player_id: str):
        """删除武将并更新相关缓存"""
        if not general_id or not player_id:
            return False
        
        # 1. 删除武将详情缓存
        detail_key = CacheKeyBuilder.general_details(player_id, general_id)
        await self.cache.delete(detail_key)
        
        # 2. 删除武将属性缓存
        properties_key = CacheKeyBuilder.general_properties(player_id, general_id)
        await self.cache.delete(properties_key)
        
        # 3. 删除武将战斗力缓存
        fight_power_key = CacheKeyBuilder.general_fight_power(player_id, general_id)
        await self.cache.delete(fight_power_key)
        
        # 4. 更新玩家武将列表
        generals = await self.get_player_generals(player_id)
        
        # 过滤掉要删除的武将
        new_generals = [general for general in generals if general.get("general_id") != general_id]
        
        if len(new_generals) == len(generals):
            # 未删除任何武将
            return False
        
        # 更新缓存
        return await self.set_player_generals(player_id, new_generals)
    
    async def invalidate_general_cache(self, player_id: str, general_id: str):
        """使武将相关缓存失效"""
        # 删除武将详情缓存
        detail_key = CacheKeyBuilder.general_details(player_id, general_id)
        await self.cache.delete(detail_key)
        
        # 删除武将属性缓存
        properties_key = CacheKeyBuilder.general_properties(player_id, general_id)
        await self.cache.delete(properties_key)
        
        # 删除武将战斗力缓存
        fight_power_key = CacheKeyBuilder.general_fight_power(player_id, general_id)
        await self.cache.delete(fight_power_key)
        
        # 删除玩家武将列表缓存（因为武将数据已变更）
        generals_key = CacheKeyBuilder.generals_by_player(player_id)
        await self.cache.delete(generals_key)
    
    async def invalidate_player_generals_cache(self, player_id: str):
        """使玩家所有武将缓存失效"""
        generals_key = CacheKeyBuilder.generals_by_player(player_id)
        await self.cache.delete(generals_key)
    
    async def sync_generals_to_cache(self, player_id: str, generals: List[Dict], ttl: int = 3600):
        """将武将列表同步到缓存"""
        if not generals:
            return True
        
        # 1. 更新玩家武将列表
        await self.set_player_generals(player_id, generals, ttl)
        
        # 2. 更新每个武将的详情缓存
        for general in generals:
            general_id = general.get("general_id")
            if general_id:
                await self.set_general_details(player_id, general_id, general, ttl=86400)
        
        return True
    
    async def batch_get_generals(self, player_id: str, general_ids: List[str]) -> Dict[str, Dict]:
        """批量获取武将详情"""
        if not general_ids:
            return {}
        
        keys = [CacheKeyBuilder.general_details(player_id, general_id) for general_id in general_ids]
        results = await self.cache.multi_get(keys)
        
        # 转换结果格式
        general_details = {}
        for general_id in general_ids:
            key = CacheKeyBuilder.general_details(player_id, general_id)
            if key in results and results[key]:
                general_details[general_id] = results[key]
        
        return general_details
    
    async def batch_update_generals(self, player_id: str, generals: List[Dict], ttl: int = 3600):
        """批量更新武将缓存"""
        if not generals:
            return True
        
        # 1. 批量更新武将详情
        general_details = {}
        for general in generals:
            general_id = general.get("general_id")
            if general_id:
                general_details[CacheKeyBuilder.general_details(player_id, general_id)] = general
        
        await self.cache.multi_set(general_details, ttl=86400)
        
        # 2. 更新玩家武将列表
        await self.set_player_generals(player_id, generals, ttl)
        
        return True
