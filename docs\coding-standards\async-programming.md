# 异步编程规范

## 📋 概述

本文档定义了游戏服务器项目中异步编程的最佳实践，确保高性能、可维护和安全的异步代码。

## 🎯 核心原则

1. **非阻塞优先** - 避免在异步函数中使用阻塞操作
2. **资源管理** - 正确管理异步资源的生命周期
3. **错误处理** - 完善的异步错误处理机制
4. **性能优化** - 合理使用并发和批处理

## 🔄 异步函数定义

### 基础异步函数
```python
import asyncio
from typing import Optional, List, Dict, Any
import aiohttp
import aioredis

# ✅ 正确：清晰的异步函数定义
async def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """异步获取用户数据"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"/api/users/{user_id}") as response:
                if response.status == 200:
                    return await response.json()
                return None
    except aiohttp.ClientError as e:
        logger.error(f"获取用户数据失败: {e}")
        return None

async def update_player_stats(
    player_id: int,
    stats: Dict[str, int]
) -> bool:
    """异步更新玩家统计数据"""
    try:
        # 使用异步数据库操作
        await db_manager.update_player_stats(player_id, stats)
        
        # 更新缓存
        await redis_client.hset(
            f"player:{player_id}:stats",
            mapping=stats
        )
        
        # 通知其他服务
        await event_bus.publish("player_stats_updated", {
            "player_id": player_id,
            "stats": stats
        })
        
        return True
    except Exception as e:
        logger.error(f"更新玩家统计失败: {e}")
        return False

# ❌ 错误：在异步函数中使用阻塞操作
async def bad_get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    # 错误：使用同步的requests库
    import requests
    response = requests.get(f"/api/users/{user_id}")  # 阻塞操作
    return response.json() if response.status_code == 200 else None
```

### 异步上下文管理器
```python
import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

class DatabaseConnection:
    """异步数据库连接"""
    
    async def connect(self) -> None:
        """建立连接"""
        self.connection = await asyncpg.connect(DATABASE_URL)
    
    async def disconnect(self) -> None:
        """关闭连接"""
        if hasattr(self, 'connection'):
            await self.connection.close()
    
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()

# ✅ 正确：使用异步上下文管理器
async def process_user_transaction(user_id: int, amount: int) -> bool:
    """处理用户交易"""
    async with DatabaseConnection() as db:
        async with db.connection.transaction():
            # 检查余额
            balance = await db.get_user_balance(user_id)
            if balance < amount:
                return False
            
            # 扣除余额
            await db.update_user_balance(user_id, balance - amount)
            
            # 记录交易
            await db.create_transaction_record(user_id, amount)
            
            return True

@asynccontextmanager
async def game_session_context(
    player_id: int
) -> AsyncGenerator[GameSession, None]:
    """游戏会话上下文管理器"""
    session = await GameSession.create(player_id)
    try:
        await session.initialize()
        yield session
    finally:
        await session.cleanup()

# 使用自定义异步上下文管理器
async def handle_player_action(player_id: int, action: str) -> bool:
    """处理玩家动作"""
    async with game_session_context(player_id) as session:
        return await session.process_action(action)
```

## 🔀 并发控制

### 任务并发执行
```python
import asyncio
from typing import List, Tuple

# ✅ 正确：使用asyncio.gather进行并发
async def get_multiple_users(user_ids: List[int]) -> List[Optional[User]]:
    """并发获取多个用户信息"""
    tasks = [get_user_data(user_id) for user_id in user_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    users = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"获取用户 {user_ids[i]} 失败: {result}")
            users.append(None)
        else:
            users.append(User.from_dict(result) if result else None)
    
    return users

# ✅ 正确：使用asyncio.as_completed处理完成顺序
async def process_player_actions_as_completed(
    actions: List[PlayerAction]
) -> List[ActionResult]:
    """按完成顺序处理玩家动作"""
    tasks = [process_action(action) for action in actions]
    results = []
    
    for coro in asyncio.as_completed(tasks):
        try:
            result = await coro
            results.append(result)
            logger.info(f"动作处理完成: {result.action_id}")
        except Exception as e:
            logger.error(f"动作处理失败: {e}")
            results.append(ActionResult.failed(str(e)))
    
    return results

# ✅ 正确：限制并发数量
async def batch_update_players_with_limit(
    player_updates: List[PlayerUpdate],
    max_concurrent: int = 10
) -> List[bool]:
    """限制并发数量的批量更新"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def update_with_semaphore(update: PlayerUpdate) -> bool:
        async with semaphore:
            return await update_player(update)
    
    tasks = [update_with_semaphore(update) for update in player_updates]
    return await asyncio.gather(*tasks, return_exceptions=False)
```

### 超时控制
```python
import asyncio
from typing import Optional

# ✅ 正确：使用asyncio.wait_for设置超时
async def get_user_with_timeout(
    user_id: int,
    timeout: float = 5.0
) -> Optional[User]:
    """带超时的用户获取"""
    try:
        user_data = await asyncio.wait_for(
            get_user_data(user_id),
            timeout=timeout
        )
        return User.from_dict(user_data) if user_data else None
    except asyncio.TimeoutError:
        logger.warning(f"获取用户 {user_id} 超时")
        return None
    except Exception as e:
        logger.error(f"获取用户 {user_id} 失败: {e}")
        return None

# ✅ 正确：批量操作的超时控制
async def batch_process_with_timeout(
    items: List[Any],
    process_func: Callable,
    timeout_per_item: float = 2.0,
    total_timeout: float = 30.0
) -> List[Any]:
    """批量处理，支持单项和总体超时"""
    async def process_item_with_timeout(item):
        return await asyncio.wait_for(
            process_func(item),
            timeout=timeout_per_item
        )
    
    tasks = [process_item_with_timeout(item) for item in items]
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=total_timeout
        )
        return results
    except asyncio.TimeoutError:
        logger.error("批量处理总体超时")
        return [None] * len(items)
```

## 🔄 事件循环管理

### 事件循环最佳实践
```python
import asyncio
import signal
from typing import Set

class GameServer:
    """游戏服务器主类"""
    
    def __init__(self):
        self.running = False
        self.background_tasks: Set[asyncio.Task] = set()
    
    async def start(self) -> None:
        """启动服务器"""
        self.running = True
        
        # 启动后台任务
        await self._start_background_tasks()
        
        # 设置信号处理
        self._setup_signal_handlers()
        
        logger.info("游戏服务器启动完成")
    
    async def stop(self) -> None:
        """停止服务器"""
        self.running = False
        
        # 取消所有后台任务
        await self._cancel_background_tasks()
        
        logger.info("游戏服务器已停止")
    
    async def _start_background_tasks(self) -> None:
        """启动后台任务"""
        # 创建后台任务
        tasks = [
            asyncio.create_task(self._heartbeat_task()),
            asyncio.create_task(self._cleanup_task()),
            asyncio.create_task(self._stats_collection_task()),
        ]
        
        # 添加到任务集合
        for task in tasks:
            self.background_tasks.add(task)
            # 任务完成时从集合中移除
            task.add_done_callback(self.background_tasks.discard)
    
    async def _cancel_background_tasks(self) -> None:
        """取消后台任务"""
        if not self.background_tasks:
            return
        
        # 取消所有任务
        for task in self.background_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(
            *self.background_tasks,
            return_exceptions=True
        )
        
        self.background_tasks.clear()
    
    async def _heartbeat_task(self) -> None:
        """心跳任务"""
        while self.running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(30)  # 30秒心跳间隔
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳任务错误: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler():
            logger.info("收到停止信号，开始优雅关闭...")
            asyncio.create_task(self.stop())
        
        # 注册信号处理器
        for sig in (signal.SIGTERM, signal.SIGINT):
            signal.signal(sig, lambda s, f: signal_handler())

# ✅ 正确：主函数的事件循环管理
async def main():
    """主函数"""
    server = GameServer()
    
    try:
        await server.start()
        
        # 保持运行直到收到停止信号
        while server.running:
            await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    
    finally:
        await server.stop()

if __name__ == "__main__":
    # 使用asyncio.run启动
    asyncio.run(main())
```

## ⚠️ 错误处理

### 异步错误处理模式
```python
import asyncio
from typing import Union, Tuple
from enum import Enum

class ErrorType(Enum):
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"

class AsyncResult:
    """异步操作结果封装"""
    
    def __init__(
        self,
        success: bool,
        data: Any = None,
        error: Optional[str] = None,
        error_type: Optional[ErrorType] = None
    ):
        self.success = success
        self.data = data
        self.error = error
        self.error_type = error_type
    
    @classmethod
    def ok(cls, data: Any = None) -> "AsyncResult":
        return cls(success=True, data=data)
    
    @classmethod
    def error(
        cls,
        error: str,
        error_type: ErrorType = ErrorType.NETWORK_ERROR
    ) -> "AsyncResult":
        return cls(success=False, error=error, error_type=error_type)

# ✅ 正确：结构化的异步错误处理
async def safe_get_user_data(user_id: int) -> AsyncResult:
    """安全的用户数据获取"""
    try:
        # 参数验证
        if user_id <= 0:
            return AsyncResult.error(
                "无效的用户ID",
                ErrorType.VALIDATION_ERROR
            )
        
        # 异步操作
        user_data = await asyncio.wait_for(
            db_manager.get_user(user_id),
            timeout=5.0
        )
        
        if not user_data:
            return AsyncResult.error(
                "用户不存在",
                ErrorType.DATABASE_ERROR
            )
        
        return AsyncResult.ok(user_data)
    
    except asyncio.TimeoutError:
        return AsyncResult.error(
            "数据库查询超时",
            ErrorType.TIMEOUT_ERROR
        )
    
    except DatabaseConnectionError as e:
        return AsyncResult.error(
            f"数据库连接失败: {e}",
            ErrorType.DATABASE_ERROR
        )
    
    except Exception as e:
        logger.exception(f"获取用户数据时发生未知错误: {e}")
        return AsyncResult.error(
            "内部服务器错误",
            ErrorType.NETWORK_ERROR
        )

# ✅ 正确：异步重试机制
async def retry_async_operation(
    operation: Callable[[], Awaitable[T]],
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0
) -> AsyncResult:
    """异步操作重试机制"""
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            result = await operation()
            return AsyncResult.ok(result)
        
        except Exception as e:
            last_exception = e
            
            if attempt < max_retries:
                wait_time = delay * (backoff_factor ** attempt)
                logger.warning(
                    f"操作失败，{wait_time:.1f}秒后重试 "
                    f"(尝试 {attempt + 1}/{max_retries + 1}): {e}"
                )
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"操作最终失败，已重试 {max_retries} 次: {e}")
    
    return AsyncResult.error(
        f"操作失败: {last_exception}",
        ErrorType.NETWORK_ERROR
    )
```

## 🚀 性能优化

### 异步批处理
```python
import asyncio
from collections import defaultdict
from typing import List, Dict

class BatchProcessor:
    """异步批处理器"""
    
    def __init__(
        self,
        batch_size: int = 100,
        flush_interval: float = 1.0
    ):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self._batches: Dict[str, List] = defaultdict(list)
        self._flush_task: Optional[asyncio.Task] = None
        self._processors: Dict[str, Callable] = {}
    
    def register_processor(
        self,
        batch_type: str,
        processor: Callable[[List], Awaitable[None]]
    ) -> None:
        """注册批处理器"""
        self._processors[batch_type] = processor
    
    async def add_item(self, batch_type: str, item: Any) -> None:
        """添加项目到批处理队列"""
        self._batches[batch_type].append(item)
        
        # 检查是否需要立即处理
        if len(self._batches[batch_type]) >= self.batch_size:
            await self._flush_batch(batch_type)
        
        # 启动定时刷新任务
        if not self._flush_task or self._flush_task.done():
            self._flush_task = asyncio.create_task(
                self._periodic_flush()
            )
    
    async def _flush_batch(self, batch_type: str) -> None:
        """刷新指定类型的批处理"""
        if not self._batches[batch_type]:
            return
        
        items = self._batches[batch_type].copy()
        self._batches[batch_type].clear()
        
        processor = self._processors.get(batch_type)
        if processor:
            try:
                await processor(items)
            except Exception as e:
                logger.error(f"批处理失败 {batch_type}: {e}")
    
    async def _periodic_flush(self) -> None:
        """定期刷新所有批处理"""
        await asyncio.sleep(self.flush_interval)
        
        for batch_type in list(self._batches.keys()):
            await self._flush_batch(batch_type)

# 使用批处理器
batch_processor = BatchProcessor(batch_size=50, flush_interval=2.0)

# 注册处理器
async def process_user_updates(updates: List[Dict]) -> None:
    """批量处理用户更新"""
    await db_manager.batch_update_users(updates)

batch_processor.register_processor("user_updates", process_user_updates)

# 添加更新到批处理队列
async def update_user_stats(user_id: int, stats: Dict) -> None:
    """更新用户统计（批处理）"""
    await batch_processor.add_item("user_updates", {
        "user_id": user_id,
        "stats": stats,
        "timestamp": time.time()
    })
```

## ✅ 检查清单

### 异步代码审查清单
- [ ] 所有I/O操作都使用异步版本
- [ ] 正确使用async/await关键字
- [ ] 异步函数有适当的类型注解
- [ ] 使用异步上下文管理器管理资源
- [ ] 实现了适当的超时控制
- [ ] 错误处理覆盖所有异常情况
- [ ] 避免了阻塞操作
- [ ] 合理使用并发控制
- [ ] 后台任务有正确的生命周期管理
- [ ] 事件循环管理符合最佳实践

---

**注意**: 异步编程需要特别注意资源管理和错误处理，确保系统的稳定性和性能。
