"""
武将WebSocket消息处理器
基于现有WebSocket架构实现武将相关的消息处理
"""

import json
import logging
from typing import Dict, Any, Optional, List
from models import MessageModel
from enums import MessageId
from general_service_distributed import GeneralServiceDistributed
from websocket_handlers import MessageHandler
import traceback
from card_pack_manager_distributed import CardPackManagerDistributed
logger = logging.getLogger(__name__)


class GeneralHandlersDistributed(MessageHandler):
    """武将消息处理器 - 基于现有WebSocket架构"""
    
    def __init__(self):
        super().__init__()
        self.general_service = GeneralServiceDistributed()
    
    async def handle_get_generals(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取武将列表消息"""
        try:
            # 参数验证
            player_id = username  # 使用username作为player_id
            
            # 获取武将列表
            generals = await self.general_service.get_player_generals(player_id)
            
            # 转换为字典格式
            generals_data = [general.to_dict() for general in generals]
            
            # 发送响应
            response = MessageModel(
                msgId=MessageId.GET_GENERALS,
                data={"data": generals_data}
            ).model_dump()
            
            await connection_manager.send_personal_message(response, token)
            
            logger.info(f"获取武将列表成功: {player_id}, 数量: {len(generals_data)}")
            return response
            
        except Exception as e:
            logger.error(f"获取武将列表失败: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"获取武将列表失败: {str(e)}"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response
    
    async def handle_draw_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理抽卡消息"""
        try:
            # 参数验证
            player_id = username  # 使用username作为player_id
            package_id = data.get("package_id", 1)  # 默认使用卡包1
            cardManager = CardPackManagerDistributed(player_id)
            await cardManager.load()
            result = await cardManager.draw_from_pack(package_id)
            all_packs = cardManager.get_all_packs()
            all_packs_data = [pack.to_dict() for pack in all_packs]
            if result:
                response = MessageModel(
                    msgId=MessageId.DRAW_GENERAL,
                    data={"result": result.data, "all_packs": all_packs_data}
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "抽卡失败"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
        except Exception as e:
            logger.error(f"抽卡失败: {str(traceback.format_exc())}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"抽卡失败: {str(e)}"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response
    async def handle_synthesis_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理合成武将消息"""
        try:
            # 参数验证
            player_id = username  # 使用username作为player_id
            general_ids = data.get("general_ids", [])
            
            if not general_ids or len(general_ids) < 2:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "至少需要2个武将进行合成"}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
            # 合成武将
            result = await self.general_service.synthesis_general(player_id, general_ids)
            
            if result.success:
                response = MessageModel(
                    msgId=MessageId.SYNTHESIS_GENERAL,
                    data={"data": result.data}
                ).model_dump()
                await connection_manager.send_personal_message(response, token)
                logger.info(f"合成武将成功: {player_id}, 消耗武将: {general_ids}")
                return response
            else:
                error_response = MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": result.error}
                ).model_dump()
                await connection_manager.send_personal_message(error_response, token)
                return error_response
            
        except Exception as e:
            logger.error(f"合成武将失败: {str(e)}")
            error_response = MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"合成武将失败: {str(e)}"}
            ).model_dump()
            await connection_manager.send_personal_message(error_response, token)
            return error_response
    
    # 为其他方法创建存根实现
    async def handle_level_up_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将升级消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_star_up_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将升星消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()  
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_awaken_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将觉醒消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_train_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将训练消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_deploy_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将部署消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_retreat_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将撤退消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        )
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_get_formation(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取阵型消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_save_formation(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理保存阵型消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_equip_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将装备消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()      
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_unequip_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理武将卸下装备消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_change_general_state(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理改变武将状态消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_lock_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理锁定武将消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    
    async def handle_unlock_general(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理解锁武将消息"""
        error_response = MessageModel(
            msgId=MessageId.ERROR,
            data={"error": "功能暂未实现"}
        ).model_dump()
        await connection_manager.send_personal_message(error_response, token)
        return error_response
    async def handle_get_card_packs(self, websocket, username: str, token: str, data: dict, connection_manager) -> Dict[str, Any]:
        """处理获取卡包列表消息"""
        cardManager = CardPackManagerDistributed(username)
        await cardManager.load()
        all_packs = cardManager.get_all_packs()
        all_packs_data = [pack.to_dict() for pack in all_packs]
        response = MessageModel(
            msgId=MessageId.GET_CARD_PACKS,
            data={"all_packs": all_packs_data}
        ).model_dump()
        await connection_manager.send_personal_message(response, token)
        return response
    # ============= 消息路由 =============
    
    async def handle_message(self, websocket, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理武将相关消息"""
        try:
            message_id = message.get("message_id")
            
            # 根据消息ID路由到对应的处理方法
            handlers = {
                MessageId.GET_GENERALS: self.handle_get_generals,
                MessageId.DRAW_GENERAL: self.handle_draw_general,
                MessageId.SYNTHESIS_GENERAL: self.handle_synthesis_general,
                MessageId.LEVEL_UP_GENERAL: self.handle_level_up_general,
                MessageId.STAR_UP_GENERAL: self.handle_star_up_general,
                MessageId.AWAKEN_GENERAL: self.handle_awaken_general,
                MessageId.TRAIN_GENERAL: self.handle_train_general,
                MessageId.DEPLOY_GENERAL: self.handle_deploy_general,
                MessageId.RETREAT_GENERAL: self.handle_retreat_general,
                MessageId.GET_FORMATION: self.handle_get_formation,
                MessageId.SAVE_FORMATION: self.handle_save_formation,
                MessageId.EQUIP_GENERAL: self.handle_equip_general,
                MessageId.UNEQUIP_GENERAL: self.handle_unequip_general,
                MessageId.CHANGE_GENERAL_STATE: self.handle_change_general_state,
                MessageId.LOCK_GENERAL: self.handle_lock_general,
                MessageId.UNLOCK_GENERAL: self.handle_unlock_general,
            }
            
            handler = handlers.get(message_id)
            if handler:
                return await handler(websocket, message)
            else:
                return MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"未知的消息ID: {message_id}"}
                ).model_dump()      
                
        except Exception as e:
            logger.error(f"处理武将消息失败: {str(e)}")
            return MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"处理武将消息失败: {str(e)}"}
            ).model_dump() 