# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8002
SERVER_WORKERS=2
SERVER_DEBUG=false

# MongoDB配置
MONGODB_HOST=mongodb
MONGODB_PORT=27017
MONGODB_USERNAME=game_user
MONGODB_PASSWORD=game_password
MONGODB_DATABASE=game_server

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=300
REDIS_SOCKET_TIMEOUT=5.0
REDIS_SOCKET_CONNECT_TIMEOUT=5.0

# RabbitMQ配置
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=gameadmin
RABBITMQ_PASSWORD=gamepassword
RABBITMQ_VHOST=/
RABBITMQ_EXCHANGE=game_exchange
RABBITMQ_QUEUE=game_queue

# JWT配置
JWT_SECRET_KEY=your_secret_key_at_least_32_characters_long
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/server.log
LOG_BACKUP_COUNT=5
LOG_MAX_BYTES=10485760 