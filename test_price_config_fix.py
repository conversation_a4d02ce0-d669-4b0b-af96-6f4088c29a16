#!/usr/bin/env python3
"""
测试价格配置修复
验证商品价格计算和格式兼容性
"""

import asyncio
import logging
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_price_config_formats():
    """测试不同价格配置格式的兼容性"""
    print("=" * 60)
    print("🧪 测试价格配置格式兼容性")
    print("=" * 60)
    
    try:
        from shop_discount_service import ShopDiscountService
        from shop_database_manager import ShopDatabaseManager
        from shop_models import ShopItemConfig
        
        # 初始化服务
        db_manager = ShopDatabaseManager()
        discount_service = ShopDiscountService(db_manager)
        
        # 测试不同的价格配置格式
        test_configs = [
            {
                "name": "标准后端格式",
                "price_config": {
                    "currency_type": "gold",
                    "base_price": 100
                }
            },
            {
                "name": "前端格式",
                "price_config": {
                    "currency": "diamond",
                    "amount": 200
                }
            },
            {
                "name": "混合格式",
                "price_config": {
                    "currency_type": "arena_coin",
                    "amount": 150
                }
            },
            {
                "name": "空配置",
                "price_config": {}
            },
            {
                "name": "None配置",
                "price_config": None
            }
        ]
        
        for i, test_case in enumerate(test_configs):
            print(f"\n{i+1}. 测试 {test_case['name']}")
            print(f"   价格配置: {test_case['price_config']}")
            
            # 创建测试商品配置
            config = ShopItemConfig(
                config_id=f"test_config_{i}",
                shop_id="test_shop",
                slot_id=None,
                item_template_id="test_item",
                item_quantity=1,
                item_quality=None,
                price_config=test_case['price_config'],
                purchase_limit=None,
                availability={},
                refresh_weight=100,
                refresh_probability=1.0,
                is_active=True,
                sort_order=0,
                display_config={},
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            try:
                # 测试价格计算
                price_info = await discount_service.calculate_final_price("test_player", config.config_id, 1)
                print(f"   ✅ 价格计算成功:")
                print(f"      原价: {price_info.original_price}")
                print(f"      最终价格: {price_info.final_price}")
                print(f"      货币类型: {price_info.currency_type}")
                
            except Exception as e:
                print(f"   ❌ 价格计算失败: {str(e)}")
        
        print("\n✅ 价格配置格式兼容性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_item_creation_and_retrieval():
    """测试商品创建和获取"""
    print("\n" + "=" * 60)
    print("🧪 测试商品创建和获取")
    print("=" * 60)
    
    try:
        from shop_service import ShopService
        from shop_models import ShopItemConfig
        
        shop_service = ShopService()
        
        # 测试创建商品配置
        test_item_data = {
            "shop_id": "test_shop_price",
            "item_template_id": "test_item_82001",
            "item_quantity": 1,
            "item_quality": None,
            "slot_id": None,
            "price_config": {
                "currency": "gold",  # 前端格式
                "amount": 500
            },
            "purchase_limit": None,
            "availability": {},
            "refresh_weight": 100,
            "refresh_probability": 1.0,
            "is_active": True,
            "sort_order": 1,
            "display_config": {
                "name": "测试商品",
                "description": "用于测试价格配置的商品"
            }
        }
        
        print("1. 创建测试商品配置...")
        success = await shop_service.create_item_config(test_item_data)
        if success:
            print("   ✅ 商品配置创建成功")
        else:
            print("   ❌ 商品配置创建失败")
            return False
        
        # 生成配置ID
        config_id = shop_service.db_manager.generate_config_id(
            test_item_data["shop_id"], 
            test_item_data["item_template_id"]
        )
        
        print(f"2. 获取商品详情 (config_id: {config_id})...")
        item_detail = await shop_service.get_item_detail(config_id, "test_player")
        
        if item_detail:
            print("   ✅ 商品详情获取成功:")
            print(f"      配置ID: {item_detail['config'].config_id}")
            print(f"      价格信息: {item_detail['price_info']}")
            print(f"      可购买: {item_detail['can_purchase']}")
        else:
            print("   ❌ 商品详情获取失败")
            return False
        
        print("3. 清理测试数据...")
        await shop_service.db_manager.delete_item_config(config_id)
        print("   ✅ 测试数据清理完成")
        
        print("\n✅ 商品创建和获取测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_shop_items_api():
    """测试商店商品API"""
    print("\n" + "=" * 60)
    print("🧪 测试商店商品API")
    print("=" * 60)
    
    try:
        from shop_service import ShopService
        
        shop_service = ShopService()
        
        # 测试获取商店商品列表
        test_shop_id = "shop_8697f73416d6"  # 从日志中看到的商店ID
        
        print(f"1. 获取商店商品列表 (shop_id: {test_shop_id})...")
        items = await shop_service.get_shop_items(test_shop_id, "admin")
        
        if items:
            print(f"   ✅ 获取到 {len(items)} 个商品:")
            for item in items[:3]:  # 只显示前3个
                print(f"      - {item.get('config', {}).get('config_id', 'Unknown')}")
                print(f"        价格: {item.get('price_info', {})}")
        else:
            print("   ⚠️ 没有获取到商品（可能是正常的）")
        
        print("\n✅ 商店商品API测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始价格配置修复测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试价格配置格式兼容性
    print("📊 测试价格配置格式兼容性...")
    format_result = await test_price_config_formats()
    results.append(("价格配置格式测试", format_result))
    
    # 2. 测试商品创建和获取
    print("\n🛍️ 测试商品创建和获取...")
    creation_result = await test_item_creation_and_retrieval()
    results.append(("商品创建获取测试", creation_result))
    
    # 3. 测试商店商品API
    print("\n🏪 测试商店商品API...")
    api_result = await test_shop_items_api()
    results.append(("商店商品API测试", api_result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！价格配置修复成功！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行失败: {str(e)}")
        sys.exit(1)
