#!/usr/bin/env python3
"""
测试商店删除功能修复
验证缓存服务错误处理和Redis连接问题
"""

import asyncio
import logging
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_shop_cache_service():
    """测试商店缓存服务的错误处理"""
    print("=" * 60)
    print("🧪 测试商店缓存服务错误处理")
    print("=" * 60)
    
    try:
        from shop_cache_service import ShopCacheService
        from shop_models import Shop
        
        cache_service = ShopCacheService()
        
        # 创建测试商店
        test_shop = Shop(
            shop_id="test_shop_delete",
            shop_name="测试删除商店",
            shop_type="normal",
            description="测试删除功能的商店",
            icon="test.png",
            is_active=True,
            access_conditions={},
            refresh_config={},
            sort_order=1,
            ui_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        print("1. 测试缓存商店配置（Redis可能不可用）...")
        success = await cache_service.cache_shop_config(test_shop)
        print(f"   缓存结果: {'成功' if success else '失败（预期，Redis不可用）'}")
        
        print("2. 测试获取缓存配置...")
        cached_shop = await cache_service.get_cached_shop_config("test_shop_delete")
        print(f"   获取结果: {'找到缓存' if cached_shop else '未找到缓存（预期）'}")
        
        print("3. 测试清除商店配置缓存...")
        success = await cache_service.invalidate_shop_config("test_shop_delete")
        print(f"   清除结果: {'成功' if success else '失败'}")
        
        print("4. 测试清除商店商品缓存...")
        success = await cache_service.invalidate_shop_items("test_shop_delete")
        print(f"   清除结果: {'成功' if success else '失败'}")
        
        print("5. 测试清除所有商店缓存...")
        success = await cache_service.clear_shop_cache("test_shop_delete")
        print(f"   清除结果: {'成功' if success else '失败'}")
        
        print("✅ 缓存服务错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 缓存服务测试失败: {str(e)}")
        return False


async def test_shop_service_delete():
    """测试商店服务删除功能"""
    print("\n" + "=" * 60)
    print("🧪 测试商店服务删除功能")
    print("=" * 60)
    
    try:
        from shop_service import ShopService
        from shop_models import Shop
        
        shop_service = ShopService()
        
        # 创建测试商店
        test_shop = Shop(
            shop_id="test_shop_service_delete",
            shop_name="测试服务删除商店",
            shop_type="normal",
            description="测试服务删除功能的商店",
            icon="test.png",
            is_active=True,
            access_conditions={},
            refresh_config={},
            sort_order=1,
            ui_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        print("1. 创建测试商店...")
        success = await shop_service.db_manager.create_shop(test_shop)
        if success:
            print("   ✅ 商店创建成功")
        else:
            print("   ❌ 商店创建失败")
            return False
        
        print("2. 验证商店存在...")
        shop = await shop_service.get_shop("test_shop_service_delete")
        if shop:
            print("   ✅ 商店存在")
        else:
            print("   ❌ 商店不存在")
            return False
        
        print("3. 测试删除商店（包含缓存错误处理）...")
        success = await shop_service.delete_shop("test_shop_service_delete")
        if success:
            print("   ✅ 商店删除成功")
        else:
            print("   ❌ 商店删除失败")
            return False
        
        print("4. 验证商店已删除...")
        shop = await shop_service.get_shop("test_shop_service_delete")
        if shop is None:
            print("   ✅ 商店已成功删除")
        else:
            print("   ❌ 商店删除验证失败")
            return False
        
        print("✅ 商店服务删除功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 商店服务删除测试失败: {str(e)}")
        return False


async def test_redis_connection():
    """测试Redis连接状态"""
    print("\n" + "=" * 60)
    print("🧪 测试Redis连接状态")
    print("=" * 60)
    
    try:
        from shop_redis_manager import get_shop_redis_manager
        
        print("1. 获取Redis管理器...")
        redis_manager = await get_shop_redis_manager()
        
        print("2. 测试Redis连接...")
        is_connected = await redis_manager.ping()
        if is_connected:
            print("   ✅ Redis连接正常")
        else:
            print("   ⚠️ Redis连接失败（这是预期的，如果Redis服务器不可用）")
        
        print("3. 尝试获取Redis客户端...")
        try:
            redis_client = await redis_manager.get_redis()
            print("   ✅ Redis客户端获取成功")
        except Exception as e:
            print(f"   ⚠️ Redis客户端获取失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始商店删除功能修复测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试Redis连接
    print("📡 测试Redis连接状态...")
    redis_result = await test_redis_connection()
    results.append(("Redis连接测试", redis_result))
    
    # 2. 测试缓存服务
    print("\n🗄️ 测试缓存服务错误处理...")
    cache_result = await test_shop_cache_service()
    results.append(("缓存服务测试", cache_result))
    
    # 3. 测试商店服务删除
    print("\n🏪 测试商店服务删除功能...")
    service_result = await test_shop_service_delete()
    results.append(("商店删除测试", service_result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！商店删除功能修复成功！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行失败: {str(e)}")
        sys.exit(1)
