# 统一调度器架构使用指南

## 📋 概述

统一调度器架构解决了原有调度器系统的问题，提供了完整的服务依赖管理、健康检查和监控功能。

## 🏗️ 架构特性

### ✅ **已解决的问题**
- **服务依赖缺失**: 自动初始化和验证所有依赖服务
- **静默失败**: 完整的错误处理和监控
- **重复初始化**: 统一的初始化流程
- **健康检查缺失**: 实时服务健康监控

### 🚀 **新增功能**
- **服务健康检查**: 实时监控所有依赖服务状态
- **任务执行指标**: 详细的任务执行统计和性能监控
- **HTTP监控API**: 提供完整的调度器管理接口
- **多模式支持**: 支持独立和集成两种运行模式

## 🔧 使用方式

### **方式1: 独立调度器模式**

```bash
# 启动独立调度器
python scheduler_tasks_unified.py
```

**特点**:
- 完全独立的进程
- 自动初始化所有依赖服务
- 适合专门的调度器服务器

### **方式2: 集成模式（推荐）**

```bash
# 启动游戏服务器（自动包含调度器）
python main.py
```

**特点**:
- 集成到游戏服务器中
- 共享服务实例，资源利用率高
- 适合生产环境

## 📊 监控和管理

### **HTTP API接口**

#### 1. **获取调度器状态**
```http
GET /api/scheduler/status
```

响应示例:
```json
{
  "is_running": true,
  "mode": "integrated",
  "total_tasks": 4,
  "active_tasks": 4,
  "service_health": [
    {
      "service_name": "Redis管理器",
      "service_key": "redis_manager",
      "is_healthy": true
    }
  ],
  "task_stats": [
    {
      "task_name": "monster_cooldown_notify",
      "execution_count": 120,
      "failure_count": 0,
      "success_rate": 100.0,
      "last_execution_time": 0.05,
      "avg_execution_time": 0.048
    }
  ]
}
```

#### 2. **获取任务列表**
```http
GET /api/scheduler/tasks
```

#### 3. **控制任务执行**
```http
POST /api/scheduler/tasks/control
Content-Type: application/json

{
  "task_name": "monster_cooldown_notify",
  "action": "pause"  // "pause", "resume", "trigger"
}
```

#### 4. **健康检查**
```http
GET /api/scheduler/health
```

#### 5. **获取执行指标**
```http
GET /api/scheduler/metrics
```

#### 6. **重启调度器**
```http
POST /api/scheduler/restart
```

## 🔍 服务健康检查

### **自动检查的服务**
- **Redis管理器**: 执行ping命令验证连接
- **MongoDB管理器**: 执行ping命令验证连接
- **连接管理器**: 检查初始化状态和连接能力
- **怪物冷却管理器**: 测试Redis操作能力

### **健康检查频率**
- 默认每60秒检查一次
- 可通过配置调整检查间隔
- 不健康的服务会记录警告日志

## 📈 任务执行指标

### **收集的指标**
- **执行次数**: 任务总执行次数
- **失败次数**: 任务执行失败次数
- **成功率**: 任务执行成功率百分比
- **执行时间**: 最近执行时间和平均执行时间

### **指标用途**
- 性能监控和优化
- 故障诊断和分析
- 容量规划和调优

## 🛠️ 配置和定制

### **添加新任务**

1. **在`scheduler_tasks_unified.py`中添加任务方法**:
```python
@with_metrics("my_custom_task")
async def my_custom_task(self):
    """自定义任务"""
    logger.info("[定时任务] 自定义任务执行")
    # 任务逻辑
```

2. **注册任务定义**:
```python
self.scheduler_manager.register_task(
    TaskDefinition(
        name="my_custom_task",
        func=self.my_custom_task,
        trigger="interval",
        trigger_args={"minutes": 5},
        dependencies=["redis_manager"],
        lock_key="lock:scheduled:my_custom_task",
        lock_ttl=60,
        enabled=True
    )
)
```

### **添加新服务依赖**

```python
self.scheduler_manager.register_service_dependency(
    ServiceDependency(
        name="我的服务",
        service_key="my_service",
        required=True,
        health_check=my_health_check_function,
        initialization=my_initialization_function
    )
)
```

## 🚨 故障排查

### **常见问题**

#### 1. **服务初始化失败**
```
错误: 服务初始化失败: Redis连接失败
解决: 检查Redis服务状态和配置
```

#### 2. **任务执行失败**
```
错误: 任务依赖验证失败
解决: 检查依赖服务的健康状态
```

#### 3. **分布式锁获取失败**
```
错误: Failed to acquire lock
解决: 检查Redis连接，清理残留锁
```

### **调试命令**

```bash
# 检查服务健康状态
curl http://localhost:8000/api/scheduler/health

# 查看任务执行指标
curl http://localhost:8000/api/scheduler/metrics

# 手动触发任务
curl -X POST http://localhost:8000/api/scheduler/tasks/control \
  -H "Content-Type: application/json" \
  -d '{"task_name": "monster_cooldown_notify", "action": "trigger"}'
```

## 📝 迁移指南

### **从旧调度器迁移**

1. **停止使用旧的`scheduler_tasks.py`**
2. **使用新的统一调度器**:
   - 独立模式: `python scheduler_tasks_unified.py`
   - 集成模式: 游戏服务器自动启用

3. **验证迁移**:
   - 检查所有任务是否正常执行
   - 验证服务健康状态
   - 监控任务执行指标

### **兼容性说明**
- 保留了原有的任务处理器接口
- 支持渐进式迁移
- 可以同时运行新旧系统进行对比

## 🎯 最佳实践

1. **生产环境使用集成模式**
2. **定期检查健康状态和指标**
3. **为关键任务设置适当的锁TTL**
4. **监控任务执行频率和性能**
5. **及时处理不健康的服务**

## 📊 性能优化建议

1. **调整健康检查间隔**: 根据系统负载调整检查频率
2. **优化任务执行频率**: 避免过于频繁的任务执行
3. **监控资源使用**: 关注CPU、内存和网络使用情况
4. **分析执行指标**: 根据指标优化任务逻辑
