"""
公会系统配置
定义公会系统的各种配置参数
"""

from typing import Dict, Any
from guild_models import GuildPosition


class GuildConfig:
    """公会系统配置类"""
    
    # 公会基础配置
    GUILD_SETTINGS = {
        # 公会名称限制
        "min_guild_name_length": 2,
        "max_guild_name_length": 20,
        "max_description_length": 200,
        
        # 公会数量限制
        "max_guilds_per_server": 1000,
        "default_max_members": 30,
        
        # 创建限制
        "create_cooldown_hours": 24,  # 创建公会冷却时间（小时）
        "min_player_level_to_create": 10,  # 创建公会最低等级
        
        # 申请限制
        "max_applications_per_player": 5,  # 每个玩家最多同时申请的公会数
        "application_expire_days": 7,  # 申请过期天数
        
        # 成员限制
        "max_members_by_level": {
            1: 30,   # 1级公会最多30人
            2: 35,   # 2级公会最多35人
            3: 40,   # 3级公会最多40人
            4: 45,   # 4级公会最多45人
            5: 50,   # 5级公会最多50人
            6: 55,   # 6级公会最多55人
            7: 60,   # 7级公会最多60人
            8: 65,   # 8级公会最多65人
            9: 70,   # 9级公会最多70人
            10: 80   # 10级公会最多80人
        }
    }
    
    # 职位配置
    POSITION_SETTINGS = {
        # 各职位的最大数量
        "max_positions": {
            GuildPosition.LEADER: 1,        # 会长只能有1个
            GuildPosition.VICE_LEADER: 2,   # 副会长最多2个
            GuildPosition.ELDER: 5,         # 长老最多5个
            GuildPosition.MEMBER: 999       # 普通成员无限制
        },
        
        # 职位名称显示
        "position_names": {
            GuildPosition.LEADER: "会长",
            GuildPosition.VICE_LEADER: "副会长", 
            GuildPosition.ELDER: "长老",
            GuildPosition.MEMBER: "成员"
        },
        
        # 职位颜色（用于UI显示）
        "position_colors": {
            GuildPosition.LEADER: "#FF6B35",      # 橙红色
            GuildPosition.VICE_LEADER: "#9B59B6", # 紫色
            GuildPosition.ELDER: "#3498DB",       # 蓝色
            GuildPosition.MEMBER: "#95A5A6"       # 灰色
        }
    }
    
    # 公会等级配置
    LEVEL_SETTINGS = {
        # 升级所需经验
        "level_exp_requirements": {
            1: 0,      # 1级不需要经验
            2: 1000,   # 升到2级需要1000经验
            3: 2500,   # 升到3级需要2500经验
            4: 5000,   # 升到4级需要5000经验
            5: 8000,   # 升到5级需要8000经验
            6: 12000,  # 升到6级需要12000经验
            7: 17000,  # 升到7级需要17000经验
            8: 23000,  # 升到8级需要23000经验
            9: 30000,  # 升到9级需要30000经验
            10: 40000  # 升到10级需要40000经验
        },
        
        # 等级奖励
        "level_rewards": {
            2: {"max_members": 35, "features": ["公会公告"]},
            3: {"max_members": 40, "features": ["公会商店"]},
            4: {"max_members": 45, "features": ["公会技能"]},
            5: {"max_members": 50, "features": ["公会战"]},
            6: {"max_members": 55, "features": ["公会副本"]},
            7: {"max_members": 60, "features": ["公会领地"]},
            8: {"max_members": 65, "features": ["高级公会技能"]},
            9: {"max_members": 70, "features": ["公会联盟"]},
            10: {"max_members": 80, "features": ["传奇公会特权"]}
        }
    }
    
    # 贡献度配置
    CONTRIBUTION_SETTINGS = {
        # 贡献度获得方式
        "contribution_sources": {
            "daily_login": 10,          # 每日登录
            "complete_task": 20,        # 完成任务
            "donate_gold": 1,           # 捐献金币（1金币=1贡献）
            "donate_item": 50,          # 捐献道具
            "guild_war_win": 100,       # 公会战胜利
            "guild_war_participate": 30 # 参与公会战
        },
        
        # 贡献度排行榜
        "leaderboard_settings": {
            "weekly_reset": True,       # 每周重置周贡献
            "monthly_rewards": True,    # 月度奖励
            "top_contributors": 10      # 显示前10名贡献者
        }
    }
    
    # 缓存配置
    CACHE_SETTINGS = {
        "guild_info_ttl": 3600,         # 公会信息缓存1小时
        "member_list_ttl": 1800,        # 成员列表缓存30分钟
        "member_info_ttl": 1800,        # 成员信息缓存30分钟
        "player_guild_ttl": 3600,       # 玩家公会信息缓存1小时
        "applications_ttl": 600,        # 申请列表缓存10分钟
        "online_status_ttl": 300,       # 在线状态缓存5分钟
        "search_results_ttl": 300       # 搜索结果缓存5分钟
    }
    
    # 分布式锁配置
    LOCK_SETTINGS = {
        "guild_operation_ttl": 30,      # 公会操作锁30秒
        "member_operation_ttl": 30,     # 成员操作锁30秒
        "application_process_ttl": 60,  # 申请处理锁60秒
        "guild_creation_ttl": 60        # 公会创建锁60秒
    }
    
    # 通知配置
    NOTIFICATION_SETTINGS = {
        # 自动通知事件
        "auto_notifications": {
            "member_join": True,        # 成员加入通知
            "member_leave": True,       # 成员离开通知
            "position_change": True,    # 职位变更通知
            "level_up": True,           # 公会升级通知
            "application_received": True # 收到申请通知
        },
        
        # 通知保留时间
        "notification_retention_days": 30
    }
    
    @classmethod
    def get_max_members_for_level(cls, level: int) -> int:
        """获取指定等级公会的最大成员数"""
        return cls.GUILD_SETTINGS["max_members_by_level"].get(level, 30)
    
    @classmethod
    def get_exp_requirement_for_level(cls, level: int) -> int:
        """获取升级到指定等级所需的经验"""
        return cls.LEVEL_SETTINGS["level_exp_requirements"].get(level, 0)
    
    @classmethod
    def get_position_name(cls, position: GuildPosition) -> str:
        """获取职位的中文名称"""
        return cls.POSITION_SETTINGS["position_names"].get(position, "未知")
    
    @classmethod
    def get_position_color(cls, position: GuildPosition) -> str:
        """获取职位的颜色"""
        return cls.POSITION_SETTINGS["position_colors"].get(position, "#000000")
    
    @classmethod
    def get_max_position_count(cls, position: GuildPosition) -> int:
        """获取指定职位的最大数量"""
        return cls.POSITION_SETTINGS["max_positions"].get(position, 999)
    
    @classmethod
    def validate_guild_name(cls, name: str) -> tuple[bool, str]:
        """验证公会名称"""
        if not name:
            return False, "公会名称不能为空"
        
        if len(name) < cls.GUILD_SETTINGS["min_guild_name_length"]:
            return False, f"公会名称至少需要{cls.GUILD_SETTINGS['min_guild_name_length']}个字符"
        
        if len(name) > cls.GUILD_SETTINGS["max_guild_name_length"]:
            return False, f"公会名称不能超过{cls.GUILD_SETTINGS['max_guild_name_length']}个字符"
        
        # 检查是否包含非法字符
        forbidden_chars = ['<', '>', '&', '"', "'", '\\', '/', '|', '*', '?', ':']
        for char in forbidden_chars:
            if char in name:
                return False, f"公会名称不能包含特殊字符: {char}"
        
        return True, ""
    
    @classmethod
    def validate_guild_description(cls, description: str) -> tuple[bool, str]:
        """验证公会简介"""
        if len(description) > cls.GUILD_SETTINGS["max_description_length"]:
            return False, f"公会简介不能超过{cls.GUILD_SETTINGS['max_description_length']}个字符"
        
        return True, ""
    
    @classmethod
    def can_create_guild(cls, player_level: int) -> tuple[bool, str]:
        """检查玩家是否可以创建公会"""
        min_level = cls.GUILD_SETTINGS["min_player_level_to_create"]
        if player_level < min_level:
            return False, f"创建公会需要达到{min_level}级"
        
        return True, ""
    
    @classmethod
    def get_contribution_for_action(cls, action: str) -> int:
        """获取指定行为的贡献度"""
        return cls.CONTRIBUTION_SETTINGS["contribution_sources"].get(action, 0)


# 全局配置实例
guild_config = GuildConfig()
