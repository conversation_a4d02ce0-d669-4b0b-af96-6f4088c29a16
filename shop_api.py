"""
商店系统API接口
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from shop_service import ShopService
from shop_models import LimitType, ShopType, DiscountType, ScopeType

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/shop", tags=["商店系统"])

# 全局商店服务实例
shop_service = ShopService()


# ==================== 请求模型 ====================

class PurchaseRequestModel(BaseModel):
    """购买请求模型"""
    config_id: str = Field(..., description="商品配置ID")
    quantity: int = Field(..., gt=0, description="购买数量")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class ShopCreateModel(BaseModel):
    """创建商店请求模型"""
    shop_name: str = Field(..., description="商店名称")
    shop_type: str = Field(..., description="商店类型")
    description: str = Field("", description="商店描述")
    icon: str = Field("", description="商店图标")
    is_active: bool = Field(True, description="是否激活")
    access_conditions: Dict[str, Any] = Field(default_factory=dict, description="访问条件")
    refresh_config: Optional[Dict[str, Any]] = Field(None, description="刷新配置")
    sort_order: int = Field(0, description="排序权重")
    ui_config: Dict[str, Any] = Field(default_factory=dict, description="UI配置")


class ItemConfigCreateModel(BaseModel):
    """创建商品配置请求模型"""
    shop_id: str = Field(..., description="商店ID")
    slot_id: Optional[int] = Field(None, description="商品槽位ID")
    item_template_id: str = Field(..., description="道具模板ID")
    item_type: str = Field(..., description="道具类型")
    item_quantity: int = Field(1, gt=0, description="道具数量")
    item_quality: Optional[int] = Field(None, description="道具品质")
    price_config: Dict[str, Any] = Field(..., description="价格配置")
    purchase_limit: Optional[Dict[str, Any]] = Field(None, description="限购配置")
    availability: Dict[str, Any] = Field(default_factory=dict, description="可用性配置")
    refresh_weight: int = Field(1, description="刷新权重")
    refresh_probability: float = Field(1.0, description="出现概率")
    is_active: bool = Field(True, description="是否激活")
    sort_order: int = Field(0, description="排序")
    display_config: Dict[str, Any] = Field(default_factory=dict, description="显示配置")


class ShopUpdateModel(BaseModel):
    """更新商店请求模型"""
    shop_name: Optional[str] = Field(None, description="商店名称")
    shop_type: Optional[str] = Field(None, description="商店类型")
    description: Optional[str] = Field(None, description="商店描述")
    icon: Optional[str] = Field(None, description="商店图标")
    is_active: Optional[bool] = Field(None, description="是否激活")
    access_conditions: Optional[Dict[str, Any]] = Field(None, description="访问条件")
    refresh_config: Optional[Dict[str, Any]] = Field(None, description="刷新配置")
    sort_order: Optional[int] = Field(None, description="排序权重")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI配置")


class ItemConfigUpdateModel(BaseModel):
    """更新商品配置请求模型"""
    slot_id: Optional[int] = Field(None, description="商品槽位ID")
    item_template_id: Optional[str] = Field(None, description="道具模板ID")
    item_type: Optional[str] = Field(None, description="道具类型")
    item_quantity: Optional[int] = Field(None, gt=0, description="道具数量")
    item_quality: Optional[int] = Field(None, description="道具品质")
    price_config: Optional[Dict[str, Any]] = Field(None, description="价格配置")
    purchase_limit: Optional[Dict[str, Any]] = Field(None, description="限购配置")
    availability: Optional[Dict[str, Any]] = Field(None, description="可用性配置")
    refresh_weight: Optional[int] = Field(None, description="刷新权重")
    refresh_probability: Optional[float] = Field(None, description="出现概率")
    sort_order: Optional[int] = Field(None, description="排序权重")
    is_active: Optional[bool] = Field(None, description="是否激活")
    display_config: Optional[Dict[str, Any]] = Field(None, description="显示配置")


class DiscountCreateModel(BaseModel):
    """创建折扣请求模型"""
    discount_name: str = Field(..., description="折扣名称")
    scope_type: str = Field(..., description="适用范围类型")
    scope_values: List[str] = Field(default_factory=list, description="适用范围值")
    discount_rule: Dict[str, Any] = Field(..., description="折扣规则")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="使用条件")
    priority: int = Field(0, description="优先级")
    mutex_groups: List[str] = Field(default_factory=list, description="互斥组")
    stackable: bool = Field(False, description="是否可叠加")
    is_active: bool = Field(True, description="是否激活")


# ==================== 依赖注入 ====================

async def get_player_id(player_id: str = Query(..., description="玩家ID")) -> str:
    """获取玩家ID（这里简化处理，实际应该从JWT token中获取）"""
    if not player_id:
        raise HTTPException(status_code=400, detail="玩家ID不能为空")
    return player_id


# ==================== 玩家接口 ====================

@router.get("/list", summary="获取商店列表")
async def get_shop_list(player_id: str = Depends(get_player_id)):
    """获取玩家可访问的商店列表"""
    try:
        shops = await shop_service.get_available_shops(player_id)
        
        return {
            "success": True,
            "data": {
                "shops": [
                    {
                        "shop_id": shop.shop_id,
                        "shop_name": shop.shop_name,
                        "shop_type": shop.shop_type,
                        "description": shop.description,
                        "icon": shop.icon,
                        "sort_order": shop.sort_order,
                        "ui_config": shop.ui_config
                    }
                    for shop in shops
                ],
                "total": len(shops)
            }
        }
        
    except Exception as e:
        logger.error(f"获取商店列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取商店列表失败")


@router.get("/{shop_id}/items", summary="获取商店商品")
async def get_shop_items(shop_id: str, player_id: str = Depends(get_player_id)):
    """获取商店商品列表"""
    try:
        # 检查商店是否存在
        shop = await shop_service.get_shop(shop_id)
        if not shop:
            raise HTTPException(status_code=404, detail="商店不存在")
        
        # 获取商品列表
        items = await shop_service.get_shop_items(shop_id, player_id)
        
        # 为每个商品计算价格和限购信息
        item_details = []
        for item in items:
            detail = await shop_service.get_item_detail(item.config_id, player_id)
            if detail:
                item_details.append({
                    "config_id": item.config_id,
                    "item_template_id": item.item_template_id,
                    "item_quantity": item.item_quantity,
                    "item_quality": item.item_quality,
                    "price_config": item.price_config,  # 添加原始价格配置
                    "original_price": detail["price_info"].original_price,
                    "final_price": detail["price_info"].final_price,
                    "currency_type": detail["price_info"].currency_type,
                    "discount_amount": detail["price_info"].discount_amount,
                    "discounts_applied": detail["price_info"].discounts_applied,
                    "can_purchase": detail["can_purchase"],
                    "limit_remaining": detail["limit_status"].remaining,
                    "limit_type": detail["limit_status"].limit_type,
                    "sort_order": item.sort_order,
                    "display_config": item.display_config,
                    "purchase_limit": item.purchase_limit,  # 添加限购配置
                    "availability": item.availability,  # 添加可用性配置
                    "refresh_weight": item.refresh_weight,  # 添加刷新权重
                    "refresh_probability": item.refresh_probability,  # 添加刷新概率
                    "is_active": item.is_active  # 添加激活状态
                })
        
        # 确保返回的数据是JSON安全的
        from shop_models import sanitize_for_json

        response_data = {
            "success": True,
            "data": {
                "shop_id": shop_id,
                "shop_name": shop.shop_name,
                "items": item_details,
                "total": len(item_details)
            }
        }

        return sanitize_for_json(response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商店商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取商店商品失败")


@router.get("/item/{config_id}/detail", summary="获取商品详情")
async def get_item_detail(config_id: str, player_id: str = Depends(get_player_id)):
    """获取商品详情"""
    try:
        detail = await shop_service.get_item_detail(config_id, player_id)
        if not detail:
            raise HTTPException(status_code=404, detail="商品不存在或无权访问")
        
        config = detail["config"]
        price_info = detail["price_info"]
        limit_status = detail["limit_status"]
        
        return {
            "success": True,
            "data": {
                "config_id": config.config_id,
                "shop_id": config.shop_id,
                "item_template_id": config.item_template_id,
                "item_quantity": config.item_quantity,
                "item_quality": config.item_quality,
                "price_info": {
                    "original_price": price_info.original_price,
                    "final_price": price_info.final_price,
                    "currency_type": price_info.currency_type,
                    "discount_amount": price_info.discount_amount,
                    "discounts_applied": price_info.discounts_applied
                },
                "limit_status": {
                    "can_purchase": limit_status.can_purchase,
                    "remaining": limit_status.remaining,
                    "current_count": limit_status.current_count,
                    "limit_count": limit_status.limit_count,
                    "limit_type": limit_status.limit_type
                },
                "can_purchase": detail["can_purchase"],
                "availability": config.availability,
                "display_config": config.display_config
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商品详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取商品详情失败")


@router.post("/purchase", summary="购买商品")
async def purchase_item(
    request: PurchaseRequestModel,
    player_id: str = Depends(get_player_id)
):
    """购买商品"""
    try:
        result = await shop_service.purchase_item(
            player_id=player_id,
            config_id=request.config_id,
            quantity=request.quantity,
            metadata=request.metadata
        )
        
        if result.success:
            return {
                "success": True,
                "data": {
                    "purchase_id": result.purchase_id,
                    "items": result.items,
                    "purchase_info": result.data
                },
                "message": "购买成功"
            }
        else:
            return {
                "success": False,
                "error": result.error,
                "message": "购买失败"
            }
        
    except Exception as e:
        logger.error(f"购买商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail="购买商品失败")


@router.post("/purchase/preview", summary="预览购买")
async def preview_purchase(
    request: PurchaseRequestModel,
    player_id: str = Depends(get_player_id)
):
    """预览购买 - 计算价格和检查条件"""
    try:
        preview = await shop_service.preview_purchase(
            player_id=player_id,
            config_id=request.config_id,
            quantity=request.quantity
        )
        
        return {
            "success": True,
            "data": preview
        }
        
    except Exception as e:
        logger.error(f"预览购买失败: {str(e)}")
        raise HTTPException(status_code=500, detail="预览购买失败")


@router.get("/purchase/history", summary="购买历史")
async def get_purchase_history(
    player_id: str = Depends(get_player_id),
    limit: int = Query(50, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取购买历史"""
    try:
        history = await shop_service.get_purchase_history(player_id, limit, offset)
        
        return {
            "success": True,
            "data": {
                "purchases": history,
                "total": len(history),
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"获取购买历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取购买历史失败")


@router.get("/purchase/limits", summary="限购状态")
async def get_purchase_limits(player_id: str = Depends(get_player_id)):
    """获取当前限购状态"""
    try:
        limits = await shop_service.get_player_limits(player_id)
        
        return {
            "success": True,
            "data": limits
        }
        
    except Exception as e:
        logger.error(f"获取限购状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取限购状态失败")


# ==================== 管理接口 ====================

@router.post("/admin/shop", summary="创建商店", tags=["管理接口"])
async def create_shop(request: ShopCreateModel):
    """创建商店"""
    try:
        success = await shop_service.create_shop(request.dict())
        
        if success:
            return {
                "success": True,
                "message": "商店创建成功"
            }
        else:
            return {
                "success": False,
                "message": "商店创建失败"
            }
        
    except Exception as e:
        logger.error(f"创建商店失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建商店失败")


@router.post("/admin/item-config", summary="创建商品配置", tags=["管理接口"])
async def create_item_config(request: ItemConfigCreateModel):
    """创建商品配置"""
    try:
        success = await shop_service.create_item_config(request.dict())
        
        if success:
            return {
                "success": True,
                "message": "商品配置创建成功"
            }
        else:
            return {
                "success": False,
                "message": "商品配置创建失败"
            }
        
    except Exception as e:
        logger.error(f"创建商品配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建商品配置失败")


@router.post("/admin/discount", summary="创建折扣", tags=["管理接口"])
async def create_discount(request: DiscountCreateModel):
    """创建折扣"""
    try:
        success = await shop_service.create_discount(request.dict())
        
        if success:
            return {
                "success": True,
                "message": "折扣创建成功"
            }
        else:
            return {
                "success": False,
                "message": "折扣创建失败"
            }
        
    except Exception as e:
        logger.error(f"创建折扣失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建折扣失败")


@router.post("/admin/limits/reset", summary="重置限购", tags=["管理接口"])
async def reset_limits(limit_type: str = Query(..., description="限购类型")):
    """重置限购"""
    try:
        if limit_type not in [LimitType.DAILY, LimitType.WEEKLY, LimitType.MONTHLY]:
            raise HTTPException(status_code=400, detail="无效的限购类型")
        
        success = await shop_service.reset_limits(limit_type)
        
        if success:
            return {
                "success": True,
                "message": f"{limit_type} 限购重置成功"
            }
        else:
            return {
                "success": False,
                "message": f"{limit_type} 限购重置失败"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置限购失败: {str(e)}")
        raise HTTPException(status_code=500, detail="重置限购失败")


@router.get("/admin/statistics/{shop_id}", summary="商店统计", tags=["管理接口"])
async def get_shop_statistics(
    shop_id: str,
    days: int = Query(7, ge=1, le=30, description="统计天数")
):
    """获取商店统计信息"""
    try:
        stats = await shop_service.get_shop_statistics(shop_id, days)

        return {
            "success": True,
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取商店统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取商店统计失败")


# ==================== 新增管理接口 ====================

@router.get("/admin/shops", summary="获取所有商店列表", tags=["管理接口"])
async def get_all_shops():
    """获取所有商店列表 - 管理接口"""
    try:
        logger.info("[ShopAPI] 获取所有商店列表")

        # 获取所有商店（包括未激活的）
        shops = await shop_service.get_all_shops()

        return {
            "success": True,
            "data": shops,
            "message": f"成功获取 {len(shops)} 个商店"
        }

    except Exception as e:
        logger.error(f"[ShopAPI] 获取商店列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商店列表失败: {str(e)}")


@router.get("/admin/shop/{shop_id}", summary="获取商店详情", tags=["管理接口"])
async def get_shop_detail(shop_id: str):
    """获取商店详情 - 管理接口"""
    try:
        logger.info(f"[ShopAPI] 获取商店详情: {shop_id}")

        shop = await shop_service.get_shop_detail(shop_id)

        if not shop:
            raise HTTPException(status_code=404, detail=f"商店不存在: {shop_id}")

        return {
            "success": True,
            "data": shop,
            "message": "获取商店详情成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ShopAPI] 获取商店详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商店详情失败: {str(e)}")


@router.put("/admin/shop/{shop_id}", summary="更新商店", tags=["管理接口"])
async def update_shop(shop_id: str, request: ShopUpdateModel):
    """更新商店 - 管理接口"""
    try:
        logger.info(f"[ShopAPI] 更新商店: {shop_id}")

        # 执行更新
        success = await shop_service.update_shop(shop_id, request.dict(exclude_unset=True))

        if success:
            logger.info(f"[ShopAPI] 商店更新成功: {shop_id}")
            return {"success": True, "message": "商店更新成功"}
        else:
            raise HTTPException(status_code=400, detail="商店更新失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ShopAPI] 更新商店失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新商店失败: {str(e)}")


@router.delete("/admin/shop/{shop_id}", summary="删除商店", tags=["管理接口"])
async def delete_shop(shop_id: str):
    """删除商店 - 管理接口"""
    try:
        logger.info(f"[ShopAPI] 删除商店: {shop_id}")

        # 执行删除
        success = await shop_service.delete_shop(shop_id)

        if success:
            logger.info(f"[ShopAPI] 商店删除成功: {shop_id}")
            return {"success": True, "message": "商店删除成功"}
        else:
            raise HTTPException(status_code=400, detail="商店删除失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ShopAPI] 删除商店失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除商店失败: {str(e)}")


@router.put("/admin/item/{config_id}", summary="更新商品配置", tags=["管理接口"])
async def update_item_config(config_id: str, request: ItemConfigUpdateModel):
    """更新商品配置 - 管理接口"""
    try:
        logger.info(f"[ShopAPI] 更新商品配置: {config_id}")

        # 执行更新
        success = await shop_service.update_item_config(config_id, request.dict(exclude_unset=True))

        if success:
            logger.info(f"[ShopAPI] 商品配置更新成功: {config_id}")
            return {"success": True, "message": "商品配置更新成功"}
        else:
            raise HTTPException(status_code=400, detail="商品配置更新失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ShopAPI] 更新商品配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新商品配置失败: {str(e)}")


@router.delete("/admin/item/{config_id}", summary="删除商品配置", tags=["管理接口"])
async def delete_item_config(config_id: str):
    """删除商品配置 - 管理接口"""
    try:
        logger.info(f"[ShopAPI] 删除商品配置: {config_id}")

        # 执行删除
        success = await shop_service.delete_item_config(config_id)

        if success:
            logger.info(f"[ShopAPI] 商品配置删除成功: {config_id}")
            return {"success": True, "message": "商品配置删除成功"}
        else:
            raise HTTPException(status_code=400, detail="商品配置删除失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ShopAPI] 删除商品配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除商品配置失败: {str(e)}")
