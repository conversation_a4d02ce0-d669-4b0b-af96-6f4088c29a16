"""
测试邮件系统修复
"""

import asyncio
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mail_models():
    """测试邮件模型的to_dict方法"""
    print("📧 测试邮件模型...")
    
    try:
        from mail_models import Mail, MailListItem, MailAttachment, MailTemplate, UserTemplateProcessed
        from mail_models import MailStatus, AttachmentStatus, AttachmentType
        
        # 测试Mail模型
        mail = Mail(
            mail_id="test_mail_001",
            sender_id="system",
            sender_name="系统",
            receiver_id="test_player",
            title="测试邮件",
            content="这是一封测试邮件",
            status=MailStatus.UNREAD,
            has_attachments=False,
            attachment_status=AttachmentStatus.NONE,
            created_at=datetime.now()
        )
        
        mail_dict = mail.to_dict()
        print(f"✅ Mail.to_dict() 成功: created_at={mail_dict['created_at']}")
        
        # 测试MailListItem模型
        mail_item = MailListItem.from_mail(mail)
        item_dict = mail_item.to_dict()
        print(f"✅ MailListItem.to_dict() 成功: created_at={item_dict['created_at']}")
        
        # 测试MailAttachment模型
        attachment = MailAttachment(
            attachment_id="att_001",
            mail_id="test_mail_001",
            attachment_type=AttachmentType.ITEM,
            item_id="item_001",
            item_name="测试道具",
            quantity=1,
            claimed_at=datetime.now()
        )
        
        att_dict = attachment.to_dict()
        print(f"✅ MailAttachment.to_dict() 成功: claimed_at={att_dict['claimed_at']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 邮件模型测试失败: {str(e)}")
        return False


async def test_mail_service():
    """测试邮件服务"""
    print("\n📮 测试邮件服务...")
    
    try:
        from mail_service_distributed import MailServiceDistributed
        
        mail_service = MailServiceDistributed()
        
        # 测试获取邮件列表
        response = await mail_service.get_mail_list("test_player", page=1, limit=10)
        
        if response.success:
            print(f"✅ 获取邮件列表成功: {len(response.data.get('mails', []))} 封邮件")
        else:
            print(f"⚠️  获取邮件列表返回空结果: {response.error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 邮件服务测试失败: {str(e)}")
        return False


async def test_string_datetime_conversion():
    """测试字符串日期时间转换"""
    print("\n🕒 测试日期时间转换...")
    
    try:
        from mail_models import Mail, MailStatus, AttachmentStatus
        
        # 模拟从数据库获取的数据（日期字段为字符串）
        mail_data = {
            "mail_id": "test_mail_002",
            "sender_id": "system",
            "sender_name": "系统",
            "receiver_id": "test_player",
            "title": "字符串日期测试",
            "content": "测试字符串日期转换",
            "status": MailStatus.UNREAD,
            "has_attachments": False,
            "attachment_status": AttachmentStatus.NONE,
            "created_at": "2025-08-03T10:30:00"  # 字符串格式的日期
        }
        
        # 测试from_dict方法
        mail = Mail.from_dict(mail_data)
        print(f"✅ Mail.from_dict() 成功: created_at类型={type(mail.created_at)}")
        
        # 测试to_dict方法
        mail_dict = mail.to_dict()
        print(f"✅ Mail.to_dict() 成功: created_at={mail_dict['created_at']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日期时间转换测试失败: {str(e)}")
        return False


async def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    try:
        from mail_models import MailListItem, MailStatus, AttachmentStatus
        
        # 测试None值
        mail_item = MailListItem(
            mail_id="test_edge",
            sender_name="测试",
            title="边界测试",
            status=MailStatus.UNREAD,
            has_attachments=False,
            attachment_status=AttachmentStatus.NONE,
            created_at=None  # None值
        )
        
        item_dict = mail_item.to_dict()
        print(f"✅ None值处理成功: created_at={item_dict['created_at']}")
        
        # 测试非标准类型
        mail_item.created_at = "2025-08-03"  # 字符串
        item_dict = mail_item.to_dict()
        print(f"✅ 字符串值处理成功: created_at={item_dict['created_at']}")
        
        # 测试数字类型
        mail_item.created_at = 1691000000  # 时间戳
        item_dict = mail_item.to_dict()
        print(f"✅ 数字值处理成功: created_at={item_dict['created_at']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {str(e)}")
        return False


async def test_database_integration():
    """测试数据库集成"""
    print("\n🗄️ 测试数据库集成...")
    
    try:
        from mail_database_manager import MailDatabaseManager
        
        db_manager = MailDatabaseManager()
        
        # 测试获取玩家邮件列表
        mails = await db_manager.get_player_mails("test_player", page=1, limit=5)
        print(f"✅ 数据库获取邮件成功: {len(mails)} 封邮件")
        
        # 测试每个邮件的to_dict方法
        for mail in mails:
            mail_dict = mail.to_dict()
            print(f"  - 邮件 {mail.mail_id}: created_at={mail_dict['created_at']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🧪 邮件系统修复验证测试")
    print("=" * 50)
    
    tests = [
        ("邮件模型测试", test_mail_models),
        ("字符串日期转换测试", test_string_datetime_conversion),
        ("边界情况测试", test_edge_cases),
        ("邮件服务测试", test_mail_service),
        ("数据库集成测试", test_database_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            if await test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少通过3个测试
        print("🎉 邮件系统修复验证成功！")
        print("\n✅ 修复内容:")
        print("   - Mail.to_dict() 方法安全处理各种日期类型")
        print("   - MailListItem.to_dict() 方法防止字符串调用isoformat()")
        print("   - MailAttachment.to_dict() 方法安全转换日期")
        print("   - MailTemplate.to_dict() 方法安全转换日期")
        print("   - UserTemplateProcessed.to_dict() 方法安全转换日期")
    else:
        print("⚠️  部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
