"""
测试商店系统修复
"""

import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_currency_service():
    """测试货币服务"""
    print("💰 测试货币服务...")
    
    try:
        from currency_service import CurrencyService
        
        currency_service = CurrencyService()
        
        # 测试获取货币数量
        amount = await currency_service.get_currency_amount("test_player", "gold")
        print(f"✅ 获取货币数量成功: {amount}")
        
        # 测试检查货币
        can_afford = await currency_service.check_currency("test_player", "gold", 100)
        print(f"✅ 检查货币成功: {can_afford}")
        
        return True
        
    except Exception as e:
        print(f"❌ 货币服务测试失败: {str(e)}")
        return False


async def test_player_service():
    """测试玩家服务"""
    print("\n👤 测试玩家服务...")
    
    try:
        from player_service import PlayerService
        
        player_service = PlayerService()
        
        # 测试获取玩家信息
        player_info = await player_service.get_player_info("test_player")
        print(f"✅ 获取玩家信息成功: level={player_info.get('level', 1)}")
        
        # 测试获取玩家等级
        level = await player_service.get_player_level("test_player")
        print(f"✅ 获取玩家等级成功: {level}")
        
        return True
        
    except Exception as e:
        print(f"❌ 玩家服务测试失败: {str(e)}")
        return False


async def test_shop_service():
    """测试商店服务"""
    print("\n🏪 测试商店服务...")
    
    try:
        from shop_service import ShopService
        from currency_service import CurrencyService
        from player_service import PlayerService
        
        # 创建服务
        shop_service = ShopService()
        currency_service = CurrencyService()
        player_service = PlayerService()
        
        # 设置外部服务依赖
        shop_service.set_external_services(currency_service, None, player_service)
        
        # 测试获取商店列表
        shops = await shop_service.get_available_shops("test_player")
        print(f"✅ 获取商店列表成功: {len(shops)} 个商店")
        
        return True
        
    except Exception as e:
        print(f"❌ 商店服务测试失败: {str(e)}")
        return False


async def test_imports():
    """测试模块导入"""
    print("\n📦 测试模块导入...")
    
    try:
        # 测试商店相关模块
        from shop_service import ShopService
        print("✅ shop_service 导入成功")
        
        from currency_service import CurrencyService
        print("✅ currency_service 导入成功")
        
        from player_service import PlayerService
        print("✅ player_service 导入成功")
        
        from shop_websocket_handlers import register_shop_handlers
        print("✅ shop_websocket_handlers 导入成功")
        
        from shop_api import router
        print("✅ shop_api 导入成功")
        
        # 测试现有的道具相关模块
        from ItemCacheManager import ItemCacheManager, Item
        print("✅ ItemCacheManager 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入测试失败: {str(e)}")
        return False


async def test_database_connections():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    try:
        from mongodb_manager import MongoDBManager
        
        # 测试MongoDB连接
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        if db:
            print("✅ MongoDB连接成功")
        else:
            print("❌ MongoDB连接失败")
            return False
        
        # 测试Redis连接
        try:
            from shop_redis_manager import get_shop_redis
            redis = await get_shop_redis()
            await redis.ping()
            print("✅ Redis连接成功")
        except Exception as e:
            print(f"⚠️  Redis连接测试失败: {str(e)}")
            # Redis失败不影响整体测试
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        return False


async def test_game_server_integration():
    """测试游戏服务器集成"""
    print("\n🎮 测试游戏服务器集成...")
    
    try:
        # 模拟游戏服务器的商店系统初始化
        from shop_service import ShopService
        from shop_websocket_handlers import set_shop_external_services
        from shop_event_broadcaster import set_shop_event_broadcaster_connection_manager
        from currency_service import CurrencyService
        from player_service import PlayerService
        
        # 创建商店服务
        shop_service = ShopService()
        
        # 创建外部服务依赖
        currency_service = CurrencyService()
        player_service = PlayerService()
        
        # 设置外部服务依赖
        shop_service.set_external_services(currency_service, None, player_service)
        set_shop_external_services(currency_service, None, player_service)
        
        print("✅ 商店系统集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 游戏服务器集成测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🧪 商店系统修复验证测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("数据库连接测试", test_database_connections),
        ("货币服务测试", test_currency_service),
        ("玩家服务测试", test_player_service),
        ("商店服务测试", test_shop_service),
        ("游戏服务器集成测试", test_game_server_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            if await test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！商店系统修复成功")
        print("\n✅ 现在可以启动游戏服务器:")
        print("   python game_server.py")
    else:
        print("⚠️  部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
