from pydantic import BaseModel, ConfigDict
from typing import Dict, List, Optional, Any, Union, Tuple
import redis.asyncio as redis
from redis import exceptions as redis_exceptions  # 显式导入Redis异常
import motor.motor_asyncio
from bson import ObjectId
from datetime import datetime
import json
import uuid
import logging
import traceback
import asyncio
import aio_pika
import os
import contextlib
from abc import ABC, abstractmethod
from models import ResponseModel
from config import config
from utils import handle_error
from enums import ItemType
from logger_config import setup_logger
# 初始化日志系统
logger = setup_logger(__name__)
from service_locator import ServiceLocator
# 设置其他库的日志级别
logging.getLogger("pymongo").setLevel(logging.INFO)
logging.getLogger("pymongo.topology").setLevel(logging.WARNING)

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        try:
            if isinstance(obj, ObjectId):
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, bytes):
                return obj.decode('utf-8')
            return super().default(obj)
        except Exception as e:
            logger.error(f"JSON 编码失败: {str(e)}，对象: {obj}")
            return str(obj)

# ============= 缓存管理系统 =============

class CacheKeyBuilder:
    """缓存键构建器，确保键命名一致性"""
    
    @staticmethod
    def build(pattern: str, **kwargs) -> str:
        """从模式和参数构建缓存键"""
        try:
            key = pattern
            for k, v in kwargs.items():
                key = key.replace(f"{{{k}}}", str(v))
            return key
        except Exception as e:
            logger.warning(f"构建缓存键失败: {str(e)}, 模式: {pattern}, 参数: {kwargs}")
            # 返回原始模式而不是None，确保在错误情况下仍有返回值
            return pattern
    
    @staticmethod
    def user_data(username: str) -> str:
        """用户数据缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_data", "game:v2:users:{username}"), username=username)
    
    @staticmethod
    def user_status(username: str) -> str:
        """用户状态缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_status", "game:v2:users:{username}:status"), username=username)
    
    @staticmethod
    def user_connection(username: str) -> str:
        """用户连接信息缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_connection", "game:v2:users:{username}:connection"), username=username)
    
    @staticmethod
    def token_to_user(token: str) -> str:
        """Token到用户的映射缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("token_to_user", "game:v2:token_to_user:{token}"), token=token)
    
    @staticmethod
    def items_by_owner(owner: str, item_type: str = None) -> str:
        """用户物品列表缓存键"""
        return CacheKeyBuilder.build(config.get_item_cache_key(item_type), username=owner, owner=owner)
    
    @staticmethod
    def item_details(owner: str, item_id: str) -> str:
        """物品详情缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("item_data", "game:v2:items:{owner}:{item_id}"), owner=owner, item_id=item_id)


class CacheTransaction:
    """缓存事务类，用于管理Redis管道操作"""
    
    def __init__(self, pipeline):
        self.pipeline = pipeline
    
    async def get(self, key: str):
        """获取值但不立即执行"""
        self.pipeline.get(key)
    
    async def set(self, key: str, value, ttl: int = 3600):
        """设置值但不立即执行"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value, cls=CustomJSONEncoder)
        self.pipeline.set(key, value, ex=ttl)
    
    async def delete(self, key: str):
        """删除值但不立即执行"""
        self.pipeline.delete(key)


class CacheManager:
    """缓存管理核心类"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    # 基本缓存操作
    async def get(self, key: str, default=None):
        """获取缓存，返回反序列化后的值"""
        try:
            if not self.redis:
                return default
            
            data = await self.redis.get(key)
            if not data:
                return default
            
            try:
                return json.loads(data.decode('utf-8'))
            except json.JSONDecodeError:
                # 可能是非JSON值，如简单字符串
                return data.decode('utf-8')
        except redis_exceptions.RedisError as e:
            logger.warning(f"获取缓存失败: {key}, 错误: {str(e)}")
            return default
        except Exception as e:
            logger.error(f"处理缓存数据失败: {key}, 错误: {str(e)}")
            return default
    
    async def set(self, key: str, value, ttl: int = 3600):
        """设置缓存，自动处理序列化"""
        try:
            if not self.redis:
                return False
            
            # 处理复杂数据类型
            if isinstance(value, (dict, list)):
                serialized = json.dumps(value, cls=CustomJSONEncoder)
            else:
                serialized = value
            
            await self.redis.set(key, serialized, ex=ttl)
            return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"设置缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def delete(self, key: str):
        """删除缓存"""
        try:
            if not self.redis:
                return False
            
            await self.redis.delete(key)
            return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"删除缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if not self.redis:
                return False
            
            return await self.redis.exists(key) > 0
        except redis_exceptions.RedisError as e:
            logger.warning(f"检查缓存是否存在失败: {key}, 错误: {str(e)}")
            return False
    
    # 批量操作
    async def multi_get(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取多个键的缓存"""
        result = {}
        if not keys or not self.redis:
            return result
        
        try:
            # Redis的mget命令
            pipeline = self.redis.pipeline()
            for key in keys:
                pipeline.get(key)
            
            values = await pipeline.execute()
            
            for i, key in enumerate(keys):
                if values[i]:
                    try:
                        result[key] = json.loads(values[i].decode('utf-8'))
                    except json.JSONDecodeError:
                        result[key] = values[i].decode('utf-8')
            
            return result
        except redis_exceptions.RedisError as e:
            logger.warning(f"批量获取缓存失败: {keys}, 错误: {str(e)}")
            return result
    
    async def multi_set(self, key_values: Dict[str, Any], ttl: int = 3600):
        """批量设置多个键的缓存"""
        if not key_values or not self.redis:
            return False
        
        try:
            pipeline = self.redis.pipeline()
            
            for key, value in key_values.items():
                # 处理复杂数据类型
                if isinstance(value, (dict, list)):
                    serialized = json.dumps(value, cls=CustomJSONEncoder)
                else:
                    serialized = value
                
                pipeline.set(key, serialized, ex=ttl)
            
            await pipeline.execute()
            return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"批量设置缓存失败, 错误: {str(e)}")
            return False
    
    async def multi_delete(self, keys: List[str]):
        """批量删除多个键的缓存"""
        if not keys or not self.redis:
            return False
        
        try:
            await self.redis.delete(*keys)
            return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"批量删除缓存失败: {keys}, 错误: {str(e)}")
            return False
    
    # 列表操作
    async def list_append(self, key: str, value, ttl: int = 3600):
        """向列表缓存添加元素"""
        try:
            if not self.redis:
                return False
            
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                current_list = []
            
            # 添加元素
            current_list.append(value)
            
            # 更新缓存
            return await self.set(key, current_list, ttl)
        except Exception as e:
            logger.warning(f"向列表缓存添加元素失败: {key}, 错误: {str(e)}")
            return False
    
    async def list_update(self, key: str, identifier_field: str, identifier_value: Any, new_value: Any, ttl: int = 3600):
        """更新列表缓存中的特定元素"""
        try:
            if not self.redis:
                return False
            
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                return False
            
            # 查找并更新元素
            found = False
            for i, item in enumerate(current_list):
                if isinstance(item, dict) and item.get(identifier_field) == identifier_value:
                    if isinstance(new_value, dict) and isinstance(current_list[i], dict):
                        # 更新字典
                        current_list[i].update(new_value)
                    else:
                        # 替换元素
                        current_list[i] = new_value
                    found = True
                    break
            
            if not found:
                # 如果未找到匹配元素，则添加新元素
                if isinstance(new_value, dict):
                    new_value[identifier_field] = identifier_value
                    current_list.append(new_value)
                else:
                    # 无法添加非字典元素
                    return False
            
            # 更新缓存
            return await self.set(key, current_list, ttl)
        except Exception as e:
            logger.warning(f"更新列表缓存中的元素失败: {key}, 错误: {str(e)}")
            return False
    
    async def list_remove(self, key: str, identifier_field: str, identifier_value: Any, ttl: int = 3600):
        """从列表缓存中移除特定元素"""
        try:
            if not self.redis:
                return False
            
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                return False
            
            # 查找并移除元素
            new_list = [
                item for item in current_list 
                if not (isinstance(item, dict) and item.get(identifier_field) == identifier_value)
            ]
            
            if len(new_list) == len(current_list):
                # 未移除任何元素
                return False
            
            # 更新缓存
            return await self.set(key, new_list, ttl)
        except Exception as e:
            logger.warning(f"从列表缓存中移除元素失败: {key}, 错误: {str(e)}")
            return False
    
    # 事务支持
    @contextlib.asynccontextmanager
    async def transaction(self):
        """提供缓存事务支持"""
        if not self.redis:
            # 如果Redis不可用，提供空事务
            class DummyTransaction:
                async def get(self, *args, **kwargs): pass
                async def set(self, *args, **kwargs): pass
                async def delete(self, *args, **kwargs): pass
            
            yield DummyTransaction()
            return
        
        pipeline = self.redis.pipeline()
        try:
            yield CacheTransaction(pipeline)
            await pipeline.execute()
        except Exception as e:
            logger.error(f"缓存事务执行失败: {str(e)}")
            raise


class UserCacheManager:
    """用户相关缓存管理"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
    
    async def get_user_data(self, username: str) -> Optional[Dict]:
        """获取用户数据缓存"""
        key = CacheKeyBuilder.user_data(username)
        return await self.cache.get(key)
    
    async def set_user_data(self, username: str, user_data: Dict, ttl: int = 3600):
        """缓存用户数据"""
        key = CacheKeyBuilder.user_data(username)
        return await self.cache.set(key, user_data, ttl)
    
    async def invalidate_user_data(self, username: str):
        """使用户数据缓存失效"""
        key = CacheKeyBuilder.user_data(username)
        return await self.cache.delete(key)
    
    async def update_user_field(self, username: str, field: str, value: Any, ttl: int = 3600):
        """更新用户特定字段并刷新缓存"""
        # 获取用户数据
        user_data = await self.get_user_data(username)
        if not user_data:
            return False
        
        # 支持嵌套字段路径 (如 "profile.avatar")
        if "." in field:
            parts = field.split(".")
            target = user_data
            for part in parts[:-1]:
                if part not in target:
                    target[part] = {}
                target = target[part]
            target[parts[-1]] = value
        else:
            user_data[field] = value
        
        # 更新缓存
        return await self.set_user_data(username, user_data, ttl)
    
    async def get_user_status(self, username: str) -> str:
        """获取用户状态"""
        key = CacheKeyBuilder.user_status(username)
        status = await self.cache.get(key)
        return status or "offline"
    
    async def set_user_status(self, username: str, status: str, ttl: int = 3600):
        """设置用户状态"""
        key = CacheKeyBuilder.user_status(username)
        return await self.cache.set(key, status, ttl)
    
    async def register_connection(self, username: str, token: str, worker_id: int = None, ttl: int = 3600):
        """注册用户连接信息"""
        worker_id = worker_id or os.getpid()
        connection_info = {
            "worker_id": worker_id,
            "token": token,
            "last_active": datetime.now().isoformat()
        }
        
        # 设置用户连接信息
        user_conn_key = CacheKeyBuilder.user_connection(username)
        token_key = CacheKeyBuilder.token_to_user(token)
        
        # 使用事务确保两个键同时设置
        async with self.cache.transaction() as tx:
            await tx.set(user_conn_key, connection_info, ttl)
            await tx.set(token_key, username, ttl)
        
        # 同时更新用户状态
        await self.set_user_status(username, "online", ttl)
        return True
    
    async def remove_connection(self, username: str = None, token: str = None):
        """移除用户连接信息"""
        # 确保至少提供了一个参数
        if username is None and token is None:
            return False
        
        # 如果只提供了token，先查找对应的username
        if username is None and token is not None:
            token_key = CacheKeyBuilder.token_to_user(token)
            username = await self.cache.get(token_key)
            if not username:
                return False
        
        # 如果提供了username，查找对应的token
        token_to_delete = None
        if token is None and username is not None:
            user_conn_key = CacheKeyBuilder.user_connection(username)
            conn_info = await self.cache.get(user_conn_key)
            if conn_info and isinstance(conn_info, dict):
                token_to_delete = conn_info.get("token")
        else:
            token_to_delete = token
        
        # 删除连接信息
        keys_to_delete = []
        if username:
            keys_to_delete.append(CacheKeyBuilder.user_connection(username))
        
        if token_to_delete:
            keys_to_delete.append(CacheKeyBuilder.token_to_user(token_to_delete))
        
        if keys_to_delete:
            await self.cache.multi_delete(keys_to_delete)
            
            # 更新用户状态为离线
            if username:
                await self.set_user_status(username, "offline")
            
            return True
        return False
    
    async def update_connection_ttl(self, username: str, ttl: int = 3600):
        """更新连接TTL"""
        # 获取连接信息
        user_conn_key = CacheKeyBuilder.user_connection(username)
        conn_info = await self.cache.get(user_conn_key)
        
        if not conn_info or not isinstance(conn_info, dict):
            return False
        
        token = conn_info.get("token")
        if not token:
            return False
        
        # 更新两个键的TTL
        token_key = CacheKeyBuilder.token_to_user(token)
        
        # 使用Redis的EXPIRE命令
        try:
            if self.cache.redis:
                await self.cache.redis.expire(user_conn_key, ttl)
                await self.cache.redis.expire(token_key, ttl)
                
                # 同时更新用户状态的TTL
                status_key = CacheKeyBuilder.user_status(username)
                await self.cache.redis.expire(status_key, ttl)
                
                return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"更新连接TTL失败: {str(e)}")
        
        return False


class ItemCacheManager:
    """物品相关缓存管理"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
    
    async def get_user_items(self, username: str, item_type: str = None) -> List[Dict]:
        """获取用户物品列表缓存"""
        key = CacheKeyBuilder.items_by_owner(username, item_type)
        items = await self.cache.get(key) or []
        return items if isinstance(items, list) else []
    
    async def set_user_items(self, username: str, items: List[Dict], item_type: str = None, ttl: int = 3600):
        """设置用户物品列表缓存"""
        key = CacheKeyBuilder.items_by_owner(username, item_type)
        return await self.cache.set(key, items, ttl)
    
    async def get_item_details(self, owner: str, item_id: str) -> Optional[Dict]:
        """获取物品详情缓存"""
        key = CacheKeyBuilder.item_details(owner, item_id)
        return await self.cache.get(key)
    
    async def set_item_details(self, owner: str, item_id: str, item_data: Dict, ttl: int = 86400):
        """设置物品详情缓存"""
        key = CacheKeyBuilder.item_details(owner, item_id)
        return await self.cache.set(key, item_data, ttl)
    
    async def add_item(self, item: Dict, ttl: int = 3600):
        """添加物品并更新相关缓存"""
        if not item or not isinstance(item, dict):
            return False
        
        owner = item.get("owner")
        item_id = item.get("id")
        item_type = item.get("type")
        
        if not owner or not item_id:
            return False
        
        # 1. 缓存物品详情
        await self.set_item_details(owner, item_id, item, ttl=86400)
        
        # 2. 更新用户物品列表
        items = await self.get_user_items(owner, item_type)
        
        # 检查是否已存在
        found = False
        for i, existing_item in enumerate(items):
            if existing_item.get("id") == item_id:
                items[i] = item
                found = True
                break
        
        if not found:
            items.append(item)
        
        # 更新缓存
        return await self.set_user_items(owner, items, item_type, ttl)
    
    async def update_item(self, item: Dict, ttl: int = 3600):
        """更新物品并刷新相关缓存"""
        # 逻辑与add_item相同，但更注重确保物品已存在
        return await self.add_item(item, ttl)
    
    async def delete_item(self, item_id: str, owner: str, item_type: str = None):
        """删除物品并更新相关缓存"""
        if not item_id or not owner:
            return False
        
        # 1. 删除物品详情缓存
        detail_key = CacheKeyBuilder.item_details(owner, item_id)
        await self.cache.delete(detail_key)
        
        # 2. 更新用户物品列表
        items = await self.get_user_items(owner, item_type)
        
        # 过滤掉要删除的物品
        new_items = [item for item in items if item.get("id") != item_id]
        
        if len(new_items) == len(items):
            # 未删除任何物品
            return False
        
        # 更新缓存
        return await self.set_user_items(owner, new_items, item_type)
    
    async def sync_items_to_cache(self, owner: str, items: List[Dict], item_type: str = None, ttl: int = 3600):
        """将物品列表同步到缓存"""
        if not items:
            return True
        
        # 1. 更新用户物品列表
        await self.set_user_items(owner, items, item_type, ttl)
        
        # 2. 更新每个物品的详情缓存
        for item in items:
            item_id = item.get("id")
            if item_id:
                await self.set_item_details(owner, item_id, item, ttl=86400)
        
        return True

# ============= 原有基础类 =============

class BaseModelORM(BaseModel, ABC):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def serialize(self, exclude_fields=None) -> dict:
        """
        序列化模型数据，支持排除指定字段
        
        Args:
            exclude_fields: 要排除的字段列表，如 ['password', 'token']
            
        Returns:
            dict: 序列化后的字典
        """
        try:
            # 构建排除字段集合
            exclude_set = set()
            if exclude_fields:
                if isinstance(exclude_fields, (list, tuple, set)):
                    exclude_set = set(exclude_fields)
                else:
                    exclude_set = {exclude_fields}
            
            # 获取模型数据 - 修改为包含所有字段，不论是否显式设置
            data = self.dict(exclude_unset=False)
            
            # 手动排除字段和None值
            for field, value in list(data.items()):
                if field in exclude_set or value is None:
                    del data[field]
            
            # 处理特殊类型
            if "_id" in data:
                data["_id"] = str(data["_id"])
            if "created_at" in data and isinstance(data["created_at"], datetime):
                data["created_at"] = data["created_at"].isoformat()
            
            # 如果密码字段没有被排除，则确保它是字符串形式
            if "password" in data and isinstance(data["password"], bytes):
                data["password"] = data["password"].decode('utf-8')
                
            return data
        except Exception as e:
            logger.error(f"序列化失败: {str(e)}, 排除字段: {exclude_fields}")
            return {}

    @classmethod
    def deserialize(cls, data: dict) -> 'BaseModelORM':
        try:
            clean_data = {k: v for k, v in data.items() if k != "_id"}
            if "created_at" in clean_data and isinstance(clean_data["created_at"], str):
                clean_data["created_at"] = datetime.fromisoformat(clean_data["created_at"])
            if "password" in clean_data and isinstance(clean_data["password"], str):
                clean_data["password"] = clean_data["password"].encode('utf-8')
            return cls(**clean_data)
        except Exception as e:
            logger.error(f"反序列化失败: {str(e)}，数据: {data}")
            raise

class UserData(BaseModelORM):
    id: str
    password: Union[str, bytes]  # 修改为可以接受字符串或字节类型
    created_at: Optional[datetime] = None # 创建时间    
    profile: Optional[Dict[str, Any]] = None # 角色信息
    nickname: Optional[str] = None # 角色昵称
    
    @property
    def username(self) -> str:
        return self.id

    @classmethod
    async def validate_field(cls, field_path: str, value: Any, field_config: Dict[str, Any]) -> Tuple[bool, str]:
        try:
            field_spec = field_config.get("fields", {}).get(field_path)
            if not field_spec:
                logger.debug(f"字段 {field_path} 未在配置文件中定义，允许更新")
                return True, ""

            type_map = {"int": int, "str": str, "float": float, "list": list, "dict": dict}
            expected_type = type_map.get(field_spec.get("type"))
            if not expected_type:
                logger.warning(f"字段 {field_path} 配置无效类型: {field_spec.get('type')}")
                return True, ""

            if not isinstance(value, expected_type):
                return False, f"字段 {field_path} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"

            if "range" in field_spec and isinstance(value, (int, float)):
                min_val, max_val = field_spec["range"]
                if not (min_val <= value <= max_val):
                    return False, f"字段 {field_path} 值超出范围，期望 {min_val} 到 {max_val}，实际 {value}"
            elif "values" in field_spec:
                if value not in field_spec["values"]:
                    return False, f"字段 {field_path} 值无效，期望 {field_spec['values']}，实际 {value}"
            elif "element_type" in field_spec and isinstance(value, list):
                element_type = type_map.get(field_spec["element_type"])
                if not element_type or not all(isinstance(item, element_type) for item in value):
                    return False, f"字段 {field_path} 列表元素类型错误，期望 {field_spec['element_type']}"
            return True, ""
        except Exception as e:
            return False, f"字段验证失败: {str(e)}"

    @classmethod
    async def initialize_nested_field(cls, username: str, field_path: str, db, field_config: Dict[str, Any]) -> bool:
        max_retries = 3
        for attempt in range(max_retries):
            try:
                parts = field_path.split(".")
                if len(parts) <= 1:
                    return True
                parent_path = ".".join(parts[:-1])
                update = {parent_path: {}}
                for i in range(len(parts) - 1):
                    current_path = ".".join(parts[:i + 1])
                    update[current_path] = update.get(current_path, {})
                field_spec = field_config.get("fields", {}).get(field_path)
                if field_spec and "default" in field_spec:
                    update[field_path] = field_spec["default"]
                result = await db.users.update_one(
                    {"id": username, parent_path: {"$exists": False}},
                    {"$set": update}
                )
                if result.modified_count > 0:
                    logger.info(f"初始化用户 {username} 的嵌套字段 {parent_path} 成功")
                return True
            except Exception as e:
                logger.error(f"初始化嵌套字段 {field_path} 失败，用户 {username}，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                else:
                    return False

    @classmethod
    async def update_field(cls, username: str, updates: Dict[str, Any], db, redis_client, field_config: Dict[str, Any], caller: str = "unknown") -> Tuple[bool, str]:
        try:
            if not updates or not isinstance(updates, dict):
                return False, "更新数据为空或格式错误"
            set_updates = {}
            for field_path, value in updates.items():
                is_valid, error_msg = await cls.validate_field(field_path, value, field_config)
                if not is_valid:
                    return False, error_msg
                if "." in field_path:
                    if not await cls.initialize_nested_field(username, field_path, db, field_config):
                        return False, f"初始化嵌套字段 {field_path} 失败"
                set_updates[field_path] = value
            result = await db.users.update_one(
                {"id": username},
                {"$set": set_updates}
            )
            if result.matched_count == 0:
                return False, f"用户 {username} 不存在"
            if result.modified_count == 0:
                return True, "字段未发生变化"
            user = await db.users.find_one({"id": username})
            if user:
                await cache_user_data(redis_client, username, user)
                logger.info(f"[{caller}] 批量更新用户 {username} 的字段 {list(updates.keys())} 成功")
                return True, ""
            return False, "更新缓存失败"
        except Exception as e:
            logger.error(f"更新用户字段失败，用户: {username}, 错误: {str(e)}")
            return False, f"服务器错误: {str(e)}"

class Item(BaseModelORM):
    defid: int
    attributes: Dict[str, Any] = {}
    id: Optional[str] = None
    owner: Optional[str] = None
    type: Optional[ItemType] = ItemType.ITEM
    quantity: Optional[int] = None
    created_at: Optional[datetime] = None

    async def save(self, db, redis_client) -> 'Item':
        try:
            if not self.id:
                self.id = str(uuid.uuid4())
            if self.type == ItemType.ITEM or self.type == ItemType.RUNE:
                existing_item = await db.items.find_one(
                    {"defid": self.defid, "owner": self.owner, "type": self.type}
                )
                if existing_item:
                    new_quantity = existing_item.get("quantity", 1) + (self.quantity or 1)
                    update_result = await db.items.update_one(
                        {"id": existing_item["id"]},
                        {"$set": {"quantity": new_quantity, "updated_at": datetime.now()}}
                    )
                    if update_result.modified_count > 0:
                        self.id = existing_item["id"]
                        self.quantity = new_quantity
                        await self._sync_to_redis(db, redis_client)
                        return self
            self.quantity = self.quantity or 1
            self.created_at = self.created_at or datetime.now()
            data = self.serialize()
            data["updated_at"] = datetime.now()
            result = await db.items.insert_one(data)
            self._id = str(result.inserted_id)
            await self._sync_to_redis(db, redis_client)
            return self
        except Exception as e:
            raise       
    async def _sync_to_redis(self, db, redis_client):
        """同步物品数据到Redis缓存"""
        try:
            if redis_client is None:
                return
            
            # 获取DatabaseManager实例来使用缓存管理器
            db_manager = DatabaseManager()
            if db_manager.item_cache is None:
                # 回退到传统方式
                # 获取Redis缓存键
                cache_keys = config.get_cache_keys()
                items_key = format_cache_key(
                    config.get_item_cache_key(self.type),
                    username=self.owner,
                    owner=self.owner
                )
                
                # 获取单个物品缓存键
                item_key = format_cache_key(
                    cache_keys.get("item_data", "game:v2:items:{owner}:{item_id}"),
                    owner=self.owner,
                    item_id=self.id
                )
                
                # 序列化物品数据
                item_data = self.serialize()
                
                # 更新Redis缓存
                try:
                    async with redis_client.pipeline() as pipe:
                        # 1. 更新单个物品缓存
                        pipe.set(item_key, json.dumps(item_data, cls=CustomJSONEncoder), ex=86400)
                        
                        # 2. 更新物品列表缓存
                        # 检查是否存在物品列表缓存
                        items_data = await redis_client.get(items_key)
                        if items_data:
                            items = json.loads(items_data.decode('utf-8'))
                            
                            # 更新或添加物品
                            found = False
                            for i, item in enumerate(items):
                                if item.get("id") == self.id:
                                    items[i] = item_data
                                    found = True
                                    break
                            
                            if not found:
                                items.append(item_data)
                            
                            # 更新缓存
                            pipe.set(items_key, json.dumps(items, cls=CustomJSONEncoder), ex=3600)
                        else:
                            # 如果不存在缓存，创建新的
                            pipe.set(items_key, json.dumps([item_data], cls=CustomJSONEncoder), ex=3600)
                        
                        await pipe.execute()
                        logger.debug(f"传统方式同步物品 {self.id} 数据到Redis成功")
                except redis_exceptions.RedisError as e:
                    logger.warning(f"同步物品到Redis缓存失败: {str(e)}")
            else:
                # 使用缓存管理器
                # 序列化物品数据
                item_data = self.serialize()
                
                # 使用物品缓存管理器更新缓存
                await db_manager.item_cache.update_item(item_data)
                logger.debug(f"使用缓存管理器同步物品 {self.id} 数据到Redis成功")
        except Exception as e:
            logger.error(f"同步物品数据到Redis失败: {str(e)}")
    @classmethod
    async def delete(cls, owner: str, item_id: str, db, redis_client) -> bool:
        try:
            # 先获取物品信息，用于后续通知
            item_data = await db.items.find_one({"id": item_id, "owner": owner})
            if not item_data:
                return False
                
            result = await db.items.delete_one({"id": item_id, "owner": owner})
            if result.deleted_count == 0:
                return False
                
            # 使用缓存管理器更新缓存（如果可用）
            db_manager = DatabaseManager()
            if db_manager.item_cache is not None:
                # 使用ItemCacheManager删除缓存
                await db_manager.item_cache.delete_item(
                    item_id=item_id, 
                    owner=owner, 
                    item_type=item_data.get("type", ItemType.ITEM)
                )
                logger.debug(f"使用缓存管理器删除物品 {item_id} 的缓存")
            else:
                # 回退到传统方式
                user = await db.users.find_one({"id": owner})
                if user and redis_client:
                    cache_keys = config.get_cache_keys()
                    async with redis_client.pipeline() as pipe:
                        # 1. 删除单个物品缓存
                        item_key = format_cache_key(
                            cache_keys.get("item_data", "game:v2:items:{owner}:{item_id}"),
                            owner=owner,
                            item_id=item_id
                        )
                        pipe.delete(item_key)
                        
                        # 2. 更新用户数据缓存
                        user_key = format_cache_key(
                            cache_keys.get("user_data", "game:v2:users:{username}"),
                            username=owner
                        )
                        pipe.set(
                            user_key,
                            json.dumps(user, cls=CustomJSONEncoder),
                            ex=3600
                        )
                        
                        # 3. 更新物品列表缓存
                        items_key = format_cache_key(
                            config.get_item_cache_key(item_data.get("type", ItemType.ITEM)),
                            username=owner,
                            owner=owner
                        )
                        
                        # 获取当前物品列表
                        items_data = await redis_client.get(items_key)
                        if items_data:
                            try:
                                items = json.loads(items_data.decode('utf-8'))
                                if isinstance(items, list):
                                    # 过滤掉要删除的物品
                                    items = [item for item in items if item.get("id") != item_id]
                                    # 更新缓存
                                    pipe.set(items_key, json.dumps(items, cls=CustomJSONEncoder), ex=3600)
                            except Exception as e:
                                logger.warning(f"更新物品列表缓存失败: {str(e)}，但不影响正常流程")
                        
                        await pipe.execute()
                    
            # 发送资产删除通知
            try:
                notification_asset = {
                    "id": item_id,
                    "defid": item_data.get("defid"),
                    "type": item_data.get("type", ItemType.ITEM)  # 使用物品的实际类型
                }
                
                await db_manager.notify_asset_change(
                    username=owner,
                    operation_type="delete",
                    asset_type=item_data.get("type", ItemType.ITEM),
                    assets_data=notification_asset
                )
            except Exception as e:
                logger.warning(f"发送资产删除通知失败: {str(e)}，但不影响正常流程")
                
            return True
        except Exception as e:
            raise

    @classmethod
    async def modify_quantity(cls, owner: str, defid: int, amount: int, db, redis_client, increase: bool = True) -> Tuple[Optional['Item'], bool]:
        try:
            if amount <= 0:
                raise handle_error("数量变化必须大于 0")
            existing_item = await db.items.find_one({"defid": defid, "owner": owner, "type": ItemType.ITEM})
            if not existing_item:
                if not increase:
                    return None, False
                instance = cls(owner=owner, defid=defid, type=ItemType.ITEM, quantity=amount)
                saved_item = await instance.save(db, redis_client)
                
                # 发送添加物品通知
                try:
                    db_manager = DatabaseManager()
                    notification_asset = {
                        "id": saved_item.id,
                        "defid": saved_item.defid,
                        "quantity": saved_item.quantity,
                    }
                    if saved_item.attributes:
                        notification_asset["attributes"] = saved_item.attributes
                        
                    await db_manager.notify_asset_change(
                        username=owner,
                        operation_type="add",
                        asset_type=ItemType.ITEM,
                        assets_data=notification_asset
                    )
                except Exception as e:
                    logger.warning(f"发送物品添加通知失败: {str(e)}，但不影响正常流程")
                
                return saved_item, True
                
            current_quantity = existing_item.get("quantity", 1)
            new_quantity = current_quantity + amount if increase else current_quantity - amount
            
            if new_quantity <= 0:
                success = await cls.delete(owner, existing_item["id"], db, redis_client)
                
                # 发送删除物品通知
                if success:
                    try:
                        db_manager = DatabaseManager()
                        notification_asset = {
                            "id": existing_item["id"],
                            "defid": existing_item["defid"]
                        }
                        
                        await db_manager.notify_asset_change(
                            username=owner,
                            operation_type="delete",
                            asset_type=ItemType.ITEM,
                            assets_data=notification_asset
                        )
                    except Exception as e:
                        logger.warning(f"发送物品删除通知失败: {str(e)}，但不影响正常流程")
                
                return None, success
            else:
                # 更新物品数量
                await db.items.update_one(
                    {"id": existing_item["id"]},
                    {"$set": {"quantity": new_quantity, "updated_at": datetime.now()}}
                )
                
                # 更新物品实例
                existing_item["quantity"] = new_quantity
                existing_item["updated_at"] = datetime.now()
                
                # 创建物品实例
                item = cls.deserialize(existing_item)
                
                # 同步到Redis缓存
                await item._sync_to_redis(db, redis_client)
                
                # 发送更新通知
                try:
                    db_manager = DatabaseManager()
                    notification_asset = {
                        "id": item.id,
                        "defid": item.defid,
                        "quantity": item.quantity
                    }
                    if item.attributes:
                        notification_asset["attributes"] = item.attributes
                        
                    await db_manager.notify_asset_change(
                        username=owner,
                        operation_type="update",
                        asset_type=ItemType.ITEM,
                        assets_data=notification_asset
                    )
                except Exception as e:
                    logger.warning(f"发送物品更新通知失败: {str(e)}，但不影响正常流程")
                
                return item, True
        except Exception as e:
            raise

    @classmethod
    async def increase_quantity(cls, owner: str, defid: int, amount: int, db, redis_client) -> Optional['Item']:
        item, success = await cls.modify_quantity(owner, defid, amount, db, redis_client, increase=True)
        return item if success else None

    @classmethod
    async def decrease_quantity(cls, owner: str, defid: int, amount: int, db, redis_client) -> bool:
        item, success = await cls.modify_quantity(owner, defid, amount, db, redis_client, increase=False)
        return success

class DatabaseManager:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            logger.debug("创建DatabaseManager单例实例")
            cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not DatabaseManager._initialized:
            logger.debug("初始化DatabaseManager单例")
            self.redis_client = None
            self.client = None
            self.db = None
            self._closed = False
            self.USERS_COLLECTION = "users"
            # 缓存管理器初始化为None
            self.cache_manager = None
            self.user_cache = None
            self.item_cache = None
            DatabaseManager._initialized = True
        else:
            logger.debug("复用现有的DatabaseManager单例")
    async def connect_with_retry(self, client_class, connection_args, max_retries=5, delay=2):
        retries = 0
        while retries < max_retries:
            try:
                if client_class == redis.Redis:
                    client = redis.Redis(
                        connection_pool=self.redis_pool,
                        socket_timeout=connection_args["socket_timeout"],
                        socket_connect_timeout=connection_args["socket_connect_timeout"],
                        retry_on_timeout=True
                    )
                    await client.ping()
                else:
                    client = client_class(**connection_args)
                    await client.admin.command('ping')
                return client
            except Exception as e:
                retries += 1
                logger.error(f"连接失败，重试 {retries}/{max_retries}: {str(e)}")
                if retries == max_retries:
                    raise Exception(f"无法连接数据库: {str(e)}")
                await asyncio.sleep(delay * (2 ** retries))

    async def setup_indexes(self):
        try:
            await self.db.users.create_index([("id", 1)], unique=True)
            await self.db.items.create_index([("owner", 1)])
            await self.db.items.create_index([("defid", 1), ("owner", 1), ("type", 1)])
            await self.db.items.create_index([("id", 1)], unique=True)
        except Exception as e:
            logger.warning(f"创建索引失败: {str(e)}")

    async def startup(self):
        """初始化数据库连接"""
        worker_id = os.getpid()
        logger.info(f"Worker {worker_id}: 开始初始化数据库连接 (DatabaseManager id: {id(self)})")
        
        retry_count = 0
        max_retries = 3
        retry_delay = 2  # 秒
        
        while retry_count < max_retries:
            try:
                # 获取数据库配置
                mongo_config = config.get_mongodb_config()
                redis_config = config.get_redis_config()
                
                # 初始化Redis连接
                if self.redis_client is None:
                    try:
                        redis_host = redis_config['host']
                        redis_port = redis_config['port']
                        redis_password = redis_config.get('password')
                        redis_db = redis_config.get('db', 0)
                        logger.info(f"Worker {worker_id}: 正在连接到Redis: {redis_host}:{redis_port}")
                        self.redis_client = redis.Redis(
                            host=redis_host,
                            port=redis_port,
                            password=redis_password,
                            db=redis_db,
                            encoding="utf-8",
                            decode_responses=False,
                            socket_timeout=3,
                            socket_connect_timeout=3,
                            socket_keepalive=True,
                            health_check_interval=15,
                            max_connections=300
                        )
                        # 验证连接可用性
                        await self.redis_client.ping()
                        logger.info(f"Worker {worker_id}: Redis连接成功: {redis_host}:{redis_port}")
                        
                        # 初始化缓存管理器
                        self.cache_manager = CacheManager(self.redis_client)
                        self.user_cache = UserCacheManager(self.cache_manager)
                        self.item_cache = ItemCacheManager(self.cache_manager)
                        logger.info(f"Worker {worker_id}: 缓存管理器初始化成功")
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Redis连接失败: {str(e)}")
                        # 回退到本地Redis
                        if redis_host != 'localhost' and redis_host != '127.0.0.1':
                            try:
                                logger.warning(f"Worker {worker_id}: 尝试连接本地Redis")
                                self.redis_client = redis.Redis(
                                    host='localhost',
                                    port=6379,
                                    encoding="utf-8",
                                    decode_responses=False,
                                    socket_timeout=3,
                                    socket_connect_timeout=3,
                                    socket_keepalive=True,
                                    health_check_interval=15,
                                    max_connections=300
                                )
                                await self.redis_client.ping()
                                logger.info(f"Worker {worker_id}: 成功连接到本地Redis")
                            except Exception as local_e:
                                logger.error(f"Worker {worker_id}: 连接本地Redis也失败了: {str(local_e)}")
                                self.redis_client = None

                # 初始化MongoDB连接
                if self.db is None:
                    try:
                        mongo_uri = f"mongodb://{mongo_config['host']}:{mongo_config['port']}"
                        
                        # 如果配置了用户名和密码，添加认证信息
                        if mongo_config.get('username') and mongo_config.get('password'):
                            mongo_uri = f"mongodb://{mongo_config['username']}:{mongo_config['password']}@{mongo_config['host']}:{mongo_config['port']}"
                        
                        logger.info(f"Worker {worker_id}: 正在连接到MongoDB: {mongo_config['host']}:{mongo_config['port']}")
                        self.client = motor.motor_asyncio.AsyncIOMotorClient(
                            mongo_uri,
                            serverSelectionTimeoutMS=3000,  # 降低服务器选择超时
                            connectTimeoutMS=3000,  # 降低连接超时
                            socketTimeoutMS=3000,  # 降低socket超时
                            maxPoolSize=50,  # 增加连接池大小
                            minPoolSize=10,  # 设置最小池大小，保持连接温暖
                            maxIdleTimeMS=60000,  # 设置最大空闲时间
                            waitQueueTimeoutMS=3000,  # 等待队列超时
                            retryWrites=True  # 启用写入重试
                        )
                        # 验证连接可用性
                        await self.client.server_info()
                        self.db = self.client[mongo_config['database']]
                        logger.info(f"Worker {worker_id}: MongoDB连接成功: {mongo_config['host']}:{mongo_config['port']}")
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: MongoDB连接失败: {str(e)}")
                        # 回退到本地MongoDB
                        if mongo_config['host'] != 'localhost' and mongo_config['host'] != '127.0.0.1':
                            try:
                                logger.warning(f"Worker {worker_id}: 尝试连接本地MongoDB")
                                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                                    "mongodb://localhost:27017",
                                    serverSelectionTimeoutMS=3000,
                                    connectTimeoutMS=3000,
                                    socketTimeoutMS=3000,
                                    maxPoolSize=50,
                                    minPoolSize=10,
                                    maxIdleTimeMS=60000,
                                    waitQueueTimeoutMS=3000,
                                    retryWrites=True
                                )
                                await self.client.server_info()
                                self.db = self.client["gamedb"]
                                logger.info(f"Worker {worker_id}: 成功连接到本地MongoDB")
                            except Exception as local_e:
                                logger.error(f"Worker {worker_id}: 连接本地MongoDB也失败了: {str(local_e)}")
                                self.db = None
                
                # 检查所有必需的连接是否都已建立
                if self.db is not None:
                    if self.redis_client is not None:
                        logger.info(f"Worker {worker_id}: 数据库初始化完成，所有连接正常")
                    else:
                        logger.warning(f"Worker {worker_id}: 数据库部分初始化，Redis连接不可用")
                    return
                
                # 如果到达这里，说明有连接未建立，需要重试
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Worker {worker_id}: 将在 {retry_delay} 秒后重试连接数据库 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error(f"Worker {worker_id}: 数据库连接重试 {max_retries} 次后仍失败")
                    
            except Exception as e:
                logger.error(f"Worker {worker_id}: 数据库初始化出现未处理异常: {str(e)}")
                logger.error(traceback.format_exc())
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Worker {worker_id}: 将在 {retry_delay} 秒后重试连接数据库 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    logger.error(f"Worker {worker_id}: 数据库连接重试 {max_retries} 次后仍失败")
                    break

    async def shutdown(self):
        """关闭数据库连接"""
        worker_id = os.getpid()
        
        if self._closed:
            logger.debug(f"Worker {worker_id}: DatabaseManager已关闭，无需再次关闭")
            return
        
        try:
            if self.redis_client is not None:
                try:
                    logger.info(f"Worker {worker_id}: 关闭Redis连接...")
                    await self.redis_client.close()
                    logger.info(f"Worker {worker_id}: Redis连接已关闭")
                except Exception as e:
                    logger.error(f"Worker {worker_id}: 关闭Redis连接失败: {str(e)}")

            if self.client is not None:
                try:
                    logger.info(f"Worker {worker_id}: 关闭MongoDB连接...")
                    self.client.close()
                    logger.info(f"Worker {worker_id}: MongoDB连接已关闭")
                except Exception as e:
                    logger.error(f"Worker {worker_id}: 关闭MongoDB连接失败: {str(e)}")
            
            # 标记为已关闭
            self._closed = True
            logger.info(f"Worker {worker_id}: 所有数据库连接已关闭")
        except Exception as e:
            logger.error(f"Worker {worker_id}: 数据库关闭过程中发生异常: {str(e)}")
            logger.error(traceback.format_exc())
    # 初始化用户 保证只在注册的时候调用
    async def save_user(self, user: UserData):
        try:
            if self.db is None:
                logger.error("MongoDB连接未初始化，无法保存用户数据")
                raise Exception("数据库连接未初始化")
                
            data = user.serialize()

            # 检查用户是否已存在
            existing_user = await self.db.users.find_one({"id": user.id})
            if existing_user:
                logger.warning(f"用户 {user.id} 已存在，更新用户数据")
                result = await self.db.users.update_one(
                    {"id": user.id},
                    {"$set": data}
                )
                user._id = str(existing_user.get("_id"))
            else:
                # 创建新用户
                result = await self.db.users.insert_one(data)
                user._id = str(result.inserted_id)
                
            # 更新Redis缓存
            if self.redis_client is not None:
                await cache_user_data(self.redis_client, user.id, data)
                
            # 初始化用户 - 不发送通知
            await self.add_user_asset(user.id, ItemType.ITEM, [{"defid": 82001, "quantity": 1000, "attributes": {}},
                                                               {"defid": 82002, "quantity": 10, "attributes": {}},
                                                               {"defid": 82003, "quantity": 100, "attributes": {}},
                                                               {"defid": 82004, "quantity": 1000, "attributes": {}},
                                                               {"defid": 82005, "quantity": 10000, "attributes": {}}],
                                      batch=True, notify=False)
            logger.info(f"用户 {user.id} 数据保存成功")
            return user
        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise Exception(f"保存用户数据失败: {str(e)}")

    async def get_user_by_username(self, username, mongo_only=False):
        worker_id = os.getpid()
        try:
            if self.db is None:
                logger.error(f"Worker {worker_id}: MongoDB连接未初始化，无法获取用户 {username} 数据")
                return None
            
            # 先尝试从Redis获取 - 使用用户缓存管理器
            if self.user_cache is not None and not mongo_only:
                try:
                    user_data = await self.user_cache.get_user_data(username)
                    if user_data:
                        logger.debug(f"Worker {worker_id}: 从缓存获取用户: {username}")
                        return UserData(**user_data)
                except Exception as redis_err:
                    logger.warning(f"Worker {worker_id}: 从Redis获取用户缓存失败: {str(redis_err)}")
            else:
                logger.debug(f"Worker {worker_id}: 缓存管理器未初始化或指定只从MongoDB获取，用户: {username}")
            
            # 从MongoDB获取
            try:
                collection = self.db[self.USERS_COLLECTION]
                user_doc = await collection.find_one({"id": username})
                
                if user_doc:
                    logger.debug(f"Worker {worker_id}: 从MongoDB获取用户: {username}")
                    user = UserData(**user_doc)
                    # 更新缓存
                    if self.user_cache is not None:
                        await self.user_cache.set_user_data(username, user_doc)
                    return user
                else:
                    logger.warning(f"Worker {worker_id}: 用户不存在: {username}")
                    return None
            except Exception as mongo_err:
                logger.error(f"Worker {worker_id}: 从MongoDB获取用户数据失败，用户: {username}，错误: {str(mongo_err)}")
                return None
        except Exception as e:
            logger.error(f"Worker {worker_id}: 获取用户数据失败，用户: {username}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    async def update_user_fields(self, username: str, field_path: Optional[str] = None, value: Optional[Any] = None, updates: Optional[Dict[str, Any]] = None, caller: str = "unknown") -> Tuple[bool, str]:
        try:
            if field_path and updates:
                return False, "不能同时提供 field_path 和 updates"
            if field_path:
                if value is None:
                    return False, "单一字段更新需要提供值"
                updates = {field_path: value}
            elif updates:
                if not isinstance(updates, dict) or not updates:
                    return False, "更新数据为空或格式错误"
            else:
                return False, "必须提供字段路径或更新数据"
            return await UserData.update_field(username, updates, self.db, self.redis_client, {}, caller)
        except Exception as e:
            logger.error(f"更新用户字段失败，用户: {username}, 错误: {str(e)}")
            return False, f"服务器错误: {str(e)}"
    async def add_user_asset(self, 
                           username: str, 
                           asset_type: str, 
                           assets_data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                           batch: bool = False,
                           notify: bool = True) -> Dict[str, Any]:
        """统一的资产添加方法，支持物品和装备，支持单个和批量
        
        Args:
            username (str): 用户ID
            asset_type (str): 资产类型，'item'或'equipment'
            assets_data: 单个资产数据或资产列表
                单个格式: {
                    "defid": int,            # 定义ID
                    "quantity": int,         # 数量 (仅物品类型)
                    "level": int,            # 等级 (仅装备类型)
                    "attributes": dict       # 属性 (可选)
                }
                列表格式: [单个格式, 单个格式, ...]
            batch (bool): 是否为批量操作，为False时assets_data应为单个资产数据，为True时应为列表
            notify (bool): 是否发送资产变更通知，默认为True
            
        Returns:
            Dict: 包含操作结果的字典
        """
        worker_id = os.getpid()
        
        # 验证资产类型
        if asset_type not in [ItemType.ITEM, ItemType.EQUIPMENT, ItemType.RUNE]:
            return {
                "success": False,
                "message": f"不支持的资产类型: {asset_type}，支持的类型: {ItemType.ITEM}, {ItemType.EQUIPMENT}, {ItemType.RUNE}"
            }
        

        # 统一资产数据格式
        assets_list = []
        if batch:
            if not isinstance(assets_data, list):
                return {
                    "success": False,
                    "message": "批量模式下，assets_data应为列表"
                }
            assets_list = assets_data
        else:
            if not isinstance(assets_data, dict):
                return {
                    "success": False,
                    "message": "单个资产模式下，assets_data应为字典"
                }
            assets_list = [assets_data]
        
        # 处理结果
        result_assets = []
        failed_assets = []
        
        try:
            now = datetime.now()
            asset_docs = []
            asset_ids = []
            
            # 准备所有资产数据
            for asset_data in assets_list:
                try:
                    defid = asset_data.get("defid")
                    if not defid:
                        failed_assets.append({"defid": defid, "error": "缺少定义ID"})
                        continue
                    if not config.is_valid_id(asset_type,defid):
                        failed_assets.append({"defid": defid, "error": f"不支持的资产ID: {defid}"})
                        continue
                    asset_id = str(uuid.uuid4())
                    attributes = asset_data.get("attributes", {})
                    
                    # 创建基础资产数据
                    asset = {
                        "id": asset_id,
                        "defid": defid,
                        "owner": username,
                        "type": asset_type,
                        "attributes": attributes,
                        "created_at": now,
                        "updated_at": now
                    }                    
                    # 根据资产类型添加特定字段
                    if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                        quantity = asset_data.get("quantity", 1)
                        if quantity <= 0:
                            failed_assets.append({"defid": defid, "error": "物品数量必须大于0"})
                            continue
                        asset["quantity"] = quantity
                        
                    elif asset_type == ItemType.EQUIPMENT:
                        level = asset_data.get("level", 1)
                        if level <= 0:
                            failed_assets.append({"defid": defid, "error": "装备等级必须大于0"})
                            continue
                        asset["level"] = level
                    
                    asset_docs.append(asset)
                    asset_ids.append(asset_id)
                    
                    # 添加到结果
                    result_asset = {
                        "id": asset_id,
                        "defid": defid
                    }
                    if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                        result_asset["quantity"] = asset.get("quantity", 1)
                    elif asset_type == ItemType.EQUIPMENT:
                        result_asset["level"] = asset.get("level", 1)
                    
                    result_assets.append(result_asset)
                    
                except Exception as e:
                    failed_assets.append({
                        "defid": asset_data.get("defid", "未知"),
                        "error": str(e)
                    })
            
            # 如果没有有效资产，直接返回
            if not asset_docs:
                message = config.get_item_type_name(asset_type) + "添加失败"
                return {
                    "success": True,
                    "message": message,
                    "added_assets": [],
                    "failed_assets": failed_assets
                }
            
            # 合并相同defid的物品，提高效率
            if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                merged_assets = {}
                for asset in asset_docs:
                    defid = asset["defid"]
                    if defid in merged_assets:
                        merged_assets[defid]["quantity"] += asset["quantity"]
                        # 合并属性
                        if asset.get("attributes"):
                            merged_assets[defid]["attributes"].update(asset["attributes"])
                    else:
                        merged_assets[defid] = asset
                
                asset_docs = list(merged_assets.values())
                # 更新asset_ids，确保与asset_docs同步
                asset_ids = [asset["id"] for asset in asset_docs]
            
            # MongoDB操作
            changed_assets = []
            user = None
            if self.db is not None:
                try:
                    # 检查MongoDB是否支持事务
                    is_replica_set = False
                    try:
                        # 检查是否为副本集
                        server_info = await self.db.client.admin.command('isMaster')
                        is_replica_set = server_info.get('setName') is not None or server_info.get('msg') == 'isdbgrid'
                    except Exception as e:
                        logger.warning(f"Worker {worker_id}: 检查MongoDB事务支持失败: {str(e)}")
                        is_replica_set = False
                    
                    # 如果是副本集，使用事务
                    if is_replica_set:
                        logger.debug(f"Worker {worker_id}: MongoDB支持事务，使用事务模式")
                        async with await self.db.client.start_session() as session:
                            async with session.start_transaction():
                                changed_assets = await self._process_assets_with_transaction(
                                    username, asset_type, asset_docs, asset_ids, now, session
                                )
                                # 获取更新后的用户数据，用于更新Redis缓存
                                user = await self.db.users.find_one({"id": username}, session=session)
                    else:
                        # 不支持事务，使用普通操作
                        logger.debug(f"Worker {worker_id}: MongoDB不支持事务，使用非事务模式")
                        changed_assets, user = await self._process_assets_without_transaction(
                            username, asset_type, asset_docs, asset_ids, now
                        )
                except Exception as e:
                    logger.error(f"Worker {worker_id}: MongoDB操作失败: {str(e)}")
                    raise
            
            # 更新Redis缓存
            if self.redis_client is not None and user:
                try:
                    # 优先使用缓存管理器
                    if self.item_cache is not None:
                        # 使用ItemCacheManager更新缓存
                        logger.debug(f"Worker {worker_id}: 使用缓存管理器更新缓存")
                        
                        # 1. 更新用户数据缓存
                        if self.user_cache:
                            await self.user_cache.set_user_data(username, user)
                        
                        # 2. 同步所有物品到缓存
                        items_query = {"owner": username, "type": asset_type}
                        items_cursor = self.db.items.find(items_query)
                        items_list = []
                        async for item in items_cursor:
                            item_copy = process_db_item(item)
                            items_list.append(item_copy)
                        
                        # 使用缓存管理器同步物品列表
                        await self.item_cache.sync_items_to_cache(username, items_list, asset_type)
                        
                        # 3. 单独更新每个变更的物品
                        for change in changed_assets:
                            asset = change["asset"]
                            await self.item_cache.set_item_details(
                                owner=username,
                                item_id=asset['id'],
                                item_data=asset
                            )
                    else:
                        # 回退到传统方式
                        logger.debug(f"Worker {worker_id}: 使用传统方式更新缓存")
                        cache_keys = config.get_cache_keys()
                        
                        # 格式化缓存键
                        user_data_key = format_cache_key(cache_keys.get("user_data", "game:v2:users:{username}"), 
                                                       username=username)
                        
                        user_items_key = format_cache_key(config.get_item_cache_key(asset_type), username=username)
                        
                        async with self.redis_client.pipeline() as pipe:
                            # 更新用户数据缓存
                            user_json = json.dumps(user, cls=CustomJSONEncoder)
                            pipe.set(user_data_key, user_json, ex=3600)
                            
                            # 更新资产列表缓存
                            cached_items = await self.redis_client.get(user_items_key)
                            items_list = []
                            
                            if cached_items:
                                try:
                                    items_list = json.loads(cached_items.decode('utf-8'))
                                    
                                    # 更新或添加变更的物品
                                    for change in changed_assets:
                                        asset = change["asset"]
                                        operation = change["operation"]
                                        
                                        if operation == "add":
                                            # 添加新物品
                                            items_list.append(asset)
                                        elif operation == "update":
                                            # 更新现有物品
                                            for i, item in enumerate(items_list):
                                                if item.get("id") == asset["id"]:
                                                    items_list[i] = asset
                                                    break
                                except Exception as e:
                                    logger.warning(f"解析缓存的物品列表失败: {str(e)}，将重新获取物品列表")
                                    items_list = []
                            
                            # 如果缓存不存在或解析失败，从数据库获取完整列表
                            if not items_list:
                                items_query = {"owner": username, "type": asset_type}
                                items_cursor = self.db.items.find(items_query)
                                async for item in items_cursor:
                                    item_copy = process_db_item(item)
                                    items_list.append(item_copy)
                            
                            # 更新物品列表缓存
                            if items_list:
                                pipe.set(user_items_key, json.dumps(items_list, cls=CustomJSONEncoder), ex=3600)
                            
                            # 单独缓存每个物品
                            for change in changed_assets:
                                asset = change["asset"]
                                item_key = format_cache_key(
                                    cache_keys.get("item_data", "game:v2:items:{owner}:{item_id}"), 
                                    owner=username, 
                                    item_id=asset['id']
                                )
                                pipe.set(item_key, json.dumps(asset, cls=CustomJSONEncoder), ex=86400)
                            
                            await pipe.execute()
                except Exception as e:
                    logger.warning(f"Worker {worker_id}: 更新Redis缓存失败: {str(e)}")
            
            # 发送资产变更通知
            if notify and changed_assets:
                try:
                    # 准备通知数据
                    notification_assets = []
                    for change in changed_assets:
                        asset = change["asset"]
                        operation = change["operation"]
                        
                        notification_asset = {
                            "id": asset["id"],
                            "defid": asset["defid"]
                        }
                        
                        # 添加类型特定的字段
                        if (asset_type == ItemType.ITEM or asset_type == ItemType.RUNE) and "quantity" in asset:
                            notification_asset["quantity"] = asset["quantity"]
                        elif asset_type == ItemType.EQUIPMENT and "level" in asset:
                            notification_asset["level"] = asset["level"]
                        
                        # 添加属性（如果存在）
                        if "attributes" in asset and asset["attributes"]:
                            notification_asset["attributes"] = asset["attributes"]
                            
                        notification_assets.append(notification_asset)
                    
                    # 发送通知
                    await self.notify_asset_change(
                        username=username,
                        operation_type="add",  # 统一使用add，因为即使是更新也是增加数量
                        asset_type=asset_type,
                        assets_data=notification_assets
                    )
                except Exception as e:
                    logger.warning(f"Worker {worker_id}: 发送资产变更通知失败: {str(e)}")
            
            # 返回结果
            return {
                "success": True,
                "message": "添加成功",
                "added_assets": result_assets,
                "failed_assets": failed_assets
            }
        except Exception as e:
            logger.error(f"Worker {worker_id}: 添加资产失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"添加资产失败: {str(e)}",
                "added_assets": [],
                "failed_assets": failed_assets
            }

    async def _process_assets_with_transaction(self, username, asset_type, asset_docs, asset_ids, now, session):
        """使用事务处理资产添加"""
        # 创建一个列表来存储所有发生变更的资产（包括更新和新增）
        changed_assets = []
        
        # 对物品类型进行特殊处理
        if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
            for asset in asset_docs[:]:  # 使用副本迭代
                existing_item = await self.db.items.find_one({
                    "owner": username, 
                    "type": asset_type, 
                    "defid": asset["defid"]
                }, session=session)
                
                if existing_item:
                    # 更新现有物品
                    new_quantity = existing_item.get("quantity", 0) + asset["quantity"]
                    await self.db.items.update_one(
                        {"id": existing_item["id"]},
                        {"$set": {"quantity": new_quantity, "updated_at": now}},
                        session=session
                    )
                    
                    # 记录更新的物品信息
                    updated_asset = existing_item.copy()
                    updated_asset["quantity"] = new_quantity
                    updated_asset["updated_at"] = now
                    changed_assets.append({"asset": updated_asset, "operation": "update"})
                    
                    # 从待插入列表中移除
                    asset_docs.remove(asset)
                    asset_ids.remove(asset["id"])
        
        # 批量插入新资产
        if asset_docs:
            await self.db.items.insert_many(asset_docs, session=session)
            # 记录新增的物品信息
            for asset in asset_docs:
                changed_assets.append({"asset": asset, "operation": "add"})
        
        # Redis 同步 - 更新用户连接信息的TTL
        if self.redis_client is not None:
            try:
                # 在事务之外执行Redis操作，因为Redis不参与MongoDB事务
                await self.update_user_connection_ttl(username, 3600)
                
                # 在事务完成后同步物品信息到Redis
                # 注意：这部分代码在事务之外执行，但因为在同一函数内，确保事务提交后再执行
                # 获取最新的物品列表，确保包含事务中的变更
                # 获取用户物品列表
                items_query = {"owner": username, "type": asset_type}
                items_cursor = self.db.items.find(items_query, session=session)
                items_list = []
                async for item in items_cursor:
                    item_copy = process_db_item(item)
                    items_list.append(item_copy)
                
                # 注意：Redis缓存更新将在事务提交后在调用方法中执行，
                # 这里只准备数据，不直接操作Redis
            except Exception as e:
                logger.warning(f"在事务处理中准备缓存数据失败: {str(e)}")
                    
        # 返回所有变更信息
        return changed_assets

    async def _process_assets_without_transaction(self, username, asset_type, asset_docs, asset_ids, now):
        """不使用事务处理资产添加（用于不支持事务的MongoDB）"""
        # 创建一个列表来存储所有发生变更的资产（包括更新和新增）
        changed_assets = []
        
        # 检查用户是否存在
        user = await self.db.users.find_one({"id": username})
        if not user:
            logger.error(f"用户 {username} 不存在")
            raise Exception(f"用户 {username} 不存在")
            
        # 处理物品类型的特殊逻辑
        if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
            for asset in asset_docs[:]:  # 使用副本迭代
                existing_item = await self.db.items.find_one({
                    "owner": username, 
                    "type": asset_type, 
                    "defid": asset["defid"]
                })
                
                if existing_item:
                    # 更新现有物品
                    new_quantity = existing_item.get("quantity", 0) + asset["quantity"]
                    await self.db.items.update_one(
                        {"id": existing_item["id"]},
                        {"$set": {"quantity": new_quantity, "updated_at": now}}
                    )
                    
                    # 记录更新的物品信息
                    updated_asset = existing_item.copy()
                    updated_asset["quantity"] = new_quantity
                    updated_asset["updated_at"] = now
                    changed_assets.append({"asset": updated_asset, "operation": "update"})
                    
                    # 从待插入列表中移除
                    asset_docs.remove(asset)
                    asset_ids.remove(asset["id"])
        
        # 批量插入新资产
        if asset_docs:
            # 分批插入以减少失败风险
            batch_size = 50
            for i in range(0, len(asset_docs), batch_size):
                batch = asset_docs[i:i + batch_size]
                await self.db.items.insert_many(batch)
                # 记录新增的物品信息
                for asset in batch:
                    changed_assets.append({"asset": asset, "operation": "add"})
        
        # 更新用户连接TTL
        if self.redis_client is not None:
            try:
                await self.update_user_connection_ttl(username, 3600)
            except Exception as e:
                logger.warning(f"更新用户连接TTL失败: {str(e)}")
        
        # 获取最新的用户数据
        user = await self.db.users.find_one({"id": username})
        
        # 返回更新后的用户数据和变更信息
        return changed_assets, user

    async def get_user_items_by_type(self, username: str, item_type: str, skip: int = 0, limit: int = 300) -> Dict[str, Any]:
        """根据物品类型获取用户物品
        
        Args:
            username (str): 用户ID
            item_type (str): 物品类型，如 "equipment", "item" 等
            skip (int): 跳过的数量，用于分页
            limit (int): 返回的最大数量
            
        Returns:
            Dict: 包含物品列表和总数的字典
        """
        # 首先尝试使用缓存管理器
        if self.item_cache:
            try:
                # 从缓存获取物品列表
                all_items = await self.item_cache.get_user_items(username, item_type)
                
                # 应用分页
                total = len(all_items)
                paged_items = all_items[skip:skip+limit]
                for item in paged_items:
                    # 如果是符文或道具，则返回数量修复数量问题
                    if item_type == ItemType.RUNE or item_type == ItemType.ITEM:
                        item['quantity'] = item.get('quantity', 1)
                logger.info(f"从缓存获取{config.get_item_type_name(item_type)}列表: {paged_items}")
                if all_items:
                    return {
                        "success": True,
                        "from_cache": True,
                        "total": total,
                        "item_type": item_type,
                        "items": paged_items,
                        "pagination": {
                            "skip": skip,
                            "limit": limit,
                            "has_more": skip + len(paged_items) < total
                        }
                    }
            except Exception as e:
                logger.warning(f"从缓存获取物品失败: {str(e)}，将尝试从数据库获取")
        
        # 直接从数据库查询
        try:
            if self.db is None:
                return {
                    "success": False,
                    "message": "数据库连接未初始化",
                    "items": []
                }
            
            # 查询条件
            query = {
                "owner": username,
                "type": item_type
            }
            
            # 获取总数
            total = await self.db.items.count_documents(query)
            
            # 执行查询并应用分页和排序
            cursor = self.db.items.find(query).sort("created_at", -1).skip(skip).limit(limit)
            
            # 处理结果
            items = []
            async for item in cursor:
                # 处理特殊类型
                processed_item = process_db_item(item)
                items.append(processed_item)
            
            # 如果成功获取，更新缓存管理器
            if self.item_cache:
                try:
                    await self.item_cache.sync_items_to_cache(username, items, item_type)
                except Exception as e:
                    logger.warning(f"同步物品到缓存管理器失败: {str(e)}")
            
            return {
                "success": True,
                "from_cache": False,
                "total": total,
                "items": items,
                "item_type": item_type,
                "pagination": {
                    "skip": skip,
                    "limit": limit,
                    "has_more": skip + len(items) < total
                }
            }
        except Exception as e:
            logger.error(f"查询用户 {username} 的物品失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取物品失败: {str(e)}",
                "items": []
            }

    async def notify_asset_change(self, 
                            username: str, 
                            operation_type: str,  # "add", "update", "delete" 
                            asset_type: str,      # "item" or "equipment"
                            assets_data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> bool:
        """
        通过WebSocket向客户端发送资产变更通知
        
        Args:
            username (str): 用户ID
            operation_type (str): 操作类型，'add', 'update', 'delete'
            asset_type (str): 资产类型，'item' 或 'equipment'
            assets_data: 单个资产数据或资产列表
            
        Returns:
            bool: 通知是否成功
        """
        try:
            conn_manager = ServiceLocator.get("conn_manager")
            from models import MessageModel
            from enums import MessageId
            
            # 检查是否有有效的资产数据
            if isinstance(assets_data, list) and len(assets_data) == 0:
                logger.warning(f"收到空的资产变更通知请求: username={username}, operation={operation_type}, asset_type={asset_type}")
                return True  # 空数据视为成功，不发送通知
            
            # 确定是否为批量操作及相应的消息ID
            is_batch = isinstance(assets_data, list) and len(assets_data) > 1
            msg_id = MessageId.ASSET_BATCH_CHANGED if is_batch else MessageId.ASSET_CHANGED
            
            # 准备消息数据
            if is_batch:
                # 批量操作消息
                message_data = {
                    "operation": operation_type,
                    "item_type": asset_type,
                    "timestamp": datetime.now().isoformat(),
                    "assets": assets_data
                }
            else:
                # 单个资产操作消息 - 确保统一格式
                asset_data = assets_data[0] if isinstance(assets_data, list) else assets_data
                message_data = {
                    "operation": operation_type,
                    "item_type": asset_type,
                    "timestamp": datetime.now().isoformat(),
                    "asset": asset_data
                }
            
            # 构建MessageModel
            message = MessageModel(
                msgId=msg_id,
                success=True,
                data=message_data
            )
            
            # 从Redis获取用户连接信息
            if self.redis_client:
                cache_keys = config.get_cache_keys()
                connection_info = await self.redis_client.get(format_cache_key(cache_keys.get("user_connection", "game:v2:users:{username}:connection"), 
                                                                             username=username))
                if connection_info:
                    # 有连接信息，说明用户在线
                    try:
                        info = json.loads(connection_info.decode('utf-8'))
                        logger.info(f"用户 {username} 连接信息: {info}")
                        worker_id = info.get("worker_id")
                        token = info.get("token")
                        
                        if worker_id and token:
                            # 如果是当前worker的连接，直接发送
                            if worker_id == os.getpid():
                                await conn_manager.send_personal_message(
                                    message=message.model_dump(),
                                    token=token
                                )
                            else:
                                # 否则通过RabbitMQ发送到对应worker的队列
                                message_body = json.dumps([token, message.model_dump()]).encode()
                                await conn_manager.rabbit_channel.default_exchange.publish(
                                    aio_pika.Message(
                                        body=message_body,
                                        delivery_mode=aio_pika.DeliveryMode.PERSISTENT
                                    ),
                                    routing_key=f"personal_queue_{worker_id}"
                                )
                            logger.info(f"已发送资产变更通知给用户 {username}，操作: {operation_type}, 资产类型: {asset_type}")
                            return True
                    except json.JSONDecodeError:
                        logger.warning(f"用户连接信息格式错误: {connection_info}")
                else:
                    logger.info(f"用户 {username} 当前不在线，跳过WebSocket通知")
                    return True
            else:
                logger.warning("Redis客户端未初始化，无法获取用户连接信息")
            
            
        except Exception as e:
            logger.error(f"发送资产变更通知失败，用户: {username}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            return False
            
    async def register_user_connection(self, username: str, token: str, worker_id: int = None, ttl: int = 3600) -> bool:
        """
        注册用户WebSocket连接信息到Redis
        
        Args:
            username (str): 用户ID
            token (str): WebSocket连接token
            worker_id (int): 处理连接的worker进程ID，默认为当前进程ID
            ttl (int): Redis键的过期时间（秒）
            
        Returns:
            bool: 是否成功注册
        """
        try:
            if self.user_cache is None:
                logger.warning(f"缓存管理器未初始化，无法注册用户 {username} 的连接信息")
                return False
                
            # 使用用户缓存管理器注册连接
            worker_id = worker_id or os.getpid()
            return await self.user_cache.register_connection(username, token, worker_id, ttl)
            
        except Exception as e:
            logger.error(f"注册用户连接信息失败: {str(e)}")
            return False
            
    async def remove_user_connection(self, username: str = None, token: str = None) -> bool:
        """
        从Redis中移除用户WebSocket连接信息
        
        Args:
            username (str): 用户ID
            token (str): WebSocket连接token
            
        Returns:
            bool: 是否成功移除
        """
        try:
            if self.user_cache is None:
                logger.warning("缓存管理器未初始化，无法移除用户连接信息")
                return False
                
            # 使用用户缓存管理器移除连接
            return await self.user_cache.remove_connection(username, token)
                
        except Exception as e:
            logger.error(f"移除用户连接信息失败: {str(e)}")
            return False
    
    async def update_user_connection_ttl(self, username: str, ttl: int = 3600) -> bool:
        """
        更新用户连接信息的过期时间
        
        Args:
            username (str): 用户ID
            ttl (int): Redis键的过期时间（秒）
            
        Returns:
            bool: 是否成功更新
        """
        try:
            if self.user_cache is None:
                return False
                
            # 使用用户缓存管理器更新连接TTL
            return await self.user_cache.update_connection_ttl(username, ttl)
                
        except Exception as e:
            logger.error(f"更新用户连接信息TTL失败: {str(e)}")
            return False

async def cache_user_data(redis_client, username, user, expire_time=3600):
    """缓存用户数据到Redis
    
    此函数保留向后兼容，但内部使用新的缓存系统
    """
    try:
        if redis_client is None:
            logger.warning("Redis客户端未初始化，跳过缓存用户数据")
            return
            
        # 获取DatabaseManager实例，使用缓存管理器
        db_manager = DatabaseManager()
        if db_manager.user_cache is not None:
            # 使用用户缓存管理器
            await db_manager.user_cache.set_user_data(username, user, expire_time)
            await db_manager.user_cache.set_user_status(username, "online", expire_time)
            logger.debug(f"使用缓存管理器缓存用户 {username} 数据成功")
            return
            
        # 回退到传统方式
        logger.warning(f"缓存用户数据: {json.dumps(user, cls=CustomJSONEncoder)}")    
        cache_keys = config.get_cache_keys()
         
        async with redis_client.pipeline() as pipe:
            await pipe.set(
                format_cache_key(
                        cache_keys.get("user_data", "game:v2:users:{username}:data"),
                        username=username
                    ),
                json.dumps(user, cls=CustomJSONEncoder),
                ex=expire_time
            )
            await pipe.set(
                format_cache_key(
                        cache_keys.get("user_status", "game:v2:users:{username}:status"),
                        username=username
                    ),
                "online",
                ex=expire_time
            )
            await pipe.execute()
    except Exception as e:
        logger.error(f"缓存用户数据失败: {str(e)}")

async def check_user_status(redis_client, username):
    """检查用户状态
    
    此函数保留向后兼容，但内部使用新的缓存系统
    """
    try:
        # 获取DatabaseManager实例，使用缓存管理器
        db_manager = DatabaseManager()
        if db_manager.user_cache is not None:
            # 使用用户缓存管理器
            return await db_manager.user_cache.get_user_status(username)
            
        # 回退到传统方式
        if redis_client is None:
            logger.warning("Redis客户端未初始化，无法检查用户状态，默认返回offline")
            return "offline"
            
        cache_keys = config.get_cache_keys()
        status_key = format_cache_key(
                        cache_keys.get("user_status", "game:v2:users:{username}:status"),
                        username=username
                    )
        status = await redis_client.get(status_key)
        if status is None:
            return "offline"
        status = status.decode('utf-8')
        return status
    except Exception as e:
        logger.error(f"检查用户状态失败，用户: {username}, 错误: {str(e)}")
        return "offline"

def format_cache_key(key_template: str, **kwargs) -> str:
    """格式化缓存键模板，替换占位符
    
    Args:
        key_template (str): 缓存键模板，如 'game:v2:users:{username}'
        **kwargs: 要替换的参数，如 username='user123'
        
    Returns:
        str: 格式化后的缓存键
    """
    try:
        key = key_template
        for k, v in kwargs.items():
            key = key.replace(f"{{{k}}}", str(v))
        return key
    except Exception as e:
        logger.warning(f"格式化缓存键失败: {str(e)}, 模板: {key_template}, 参数: {kwargs}")
        return key_template

def process_db_item(item: dict) -> dict:
    """处理数据库返回的对象，处理特殊类型
    
    Args:
        item (dict): 数据库返回的对象
        
    Returns:
        dict: 处理后的对象
    """
    if not item:
        return item
        
    # 处理ObjectId
    if "_id" in item:
        item["_id"] = str(item["_id"])
    
    # 处理日期
    for field in ["created_at", "updated_at"]:
        if field in item and isinstance(item[field], datetime):
            item[field] = item[field].isoformat()
            
    return item