import json
import logging
import random
import traceback
from typing import Dict, List, Any, Optional
from fastapi import WebSocket

from service_locator import ServiceLocator
from xxsg.monster_cooldown import Monster<PERSON><PERSON>downManager, CooldownType
from config import config  # Replace monster_config import with config
from models import MessageModel
from enums import MessageId
from logger_config import setup_logger
import time

# Initialize logger
logger = setup_logger(__name__)

async def handle_check_cooldown(websocket: WebSocket, username: str, token: str,data: Dict, connection_manager):
    """
    Handle check cooldown request
    
    Expected data format:
    {
        "monster_id": "monster123",
        "guild_id": "guild456"  # Optional
    }
    """
    try:
        monster_id = data.get("monster_id")
        guild_id = data.get("guild_id")
        
        if not monster_id:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Missing monster_id parameter"}
                ).model_dump(),
                token
            )
            return
        
        # Get cooldown manager from service locator
        cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
        if not cooldown_manager:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Monster cooldown system not available"}
                ).model_dump(),
                token
            )
            return
        
        # Check cooldown
        cooldown_info = await cooldown_manager.check_cooldown(monster_id, username, guild_id)
        
        # Get monster info from config
        monster_info = config.get_monster_by_id(monster_id)
        if monster_info:
            cooldown_info["monster_name"] = monster_info.get("name", monster_id)
            cooldown_info["monster_level"] = monster_info.get("level", 1)
            cooldown_info["monster_type"] = monster_info.get("type", "normal")
            cooldown_info["monster_area"] = monster_info.get("area", "unknown")
        
        # Send response
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.CHECK_COOLDOWN,
                data={
                    "monster_id": monster_id,
                    "cooldown_info": cooldown_info
                }
            ).model_dump(),
            token
        )
        
    except Exception as e:
        logger.error(f"Error handling check_cooldown: {str(e)}")
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"Error checking cooldown: {str(e)}"}
            ).model_dump(),
            token
        )

async def handle_monster_killed( websocket: WebSocket, username: str, token: str, data: Dict,connection_manager):
    """
    Handle monster killed notification
    
    Expected data format:
    {
        "monster_id": "monster123",
        "cooldown_type": "personal|guild|global",
        "guild_id": "guild456",  # Required for guild cooldown
        "modifiers": {  # Optional modifiers
            "event_bonus": 0.8,
            "item_bonus": 0.9
        }
    }
    """
    try:
        monster_id = data.get("monster_id")
        cooldown_type_str = data.get("cooldown_type")
        guild_id = data.get("guild_id")
        modifiers = data.get("modifiers", {})
        
        logger.info(f"处理怪物击杀请求 - 怪物ID: {monster_id}, 冷却类型: {cooldown_type_str}, 公会ID: {guild_id}, 用户: {username}")
        
        if not monster_id or not cooldown_type_str:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Missing required parameters"}
                ).model_dump(),
                token
            )
            return
        
        # Validate cooldown type
        try:
            cooldown_type = CooldownType(cooldown_type_str)
            logger.info(f"冷却类型验证通过: {cooldown_type}")
        except ValueError:
            logger.error(f"无效的冷却类型: {cooldown_type_str}")
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"Invalid cooldown type: {cooldown_type_str}"}
                ).model_dump(),
                token
            )
            return
        
        # Check if guild_id is provided for guild cooldown
        if cooldown_type == CooldownType.GUILD and not guild_id:
            logger.error(f"公会冷却需要提供公会ID")
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Guild ID is required for guild cooldown"}
                ).model_dump(),
                token
            )
            return
        
        # Get cooldown manager from service locator
        cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
        if not cooldown_manager:
            logger.error("怪物冷却管理器不可用")
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Monster cooldown system not available"}
                ).model_dump(),
                token
            )
            return
        
        # 检查怪物是否处于冷却状态
        logger.info(f"检查怪物 {monster_id} 是否处于冷却状态 - 用户: {username}, 公会ID: {guild_id}")
        cooldown_info = await cooldown_manager.check_cooldown(monster_id, username, guild_id)
        logger.info(f"怪物 {monster_id} 冷却检查结果: {cooldown_info}")
        
        if cooldown_info["on_cooldown"]:
            # 怪物正在冷却中，不能击杀
            logger.info(f"怪物 {monster_id} 处于冷却中，拒绝击杀请求 - 剩余时间: {cooldown_info['remaining_time']}秒, 冷却类型: {cooldown_info['cooldown_type']}")
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={
                        "error": f"Monster {monster_id} is on cooldown",
                        "cooldown_info": cooldown_info
                    }
                ).model_dump(),
                token
            )
            return
        else:
            logger.info(f"怪物 {monster_id} 不在冷却中，可以击杀")
        
        # Get monster info from config
        monster_info = config.get_monster_by_id(monster_id)
        if not monster_info:
            logger.error(f"未找到怪物配置: {monster_id}")
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"Monster {monster_id} not found in configuration"}
                ).model_dump(),
                token
            )
            return
        
        # Get cooldown time from monster config
        cooldown_time = monster_info.get("cooldown", {}).get(cooldown_type, None)
        if cooldown_time is None:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"No {cooldown_type} cooldown configured for monster {monster_id}"}
                ).model_dump(),
                token
            )
            return
        
        # Calculate dynamic cooldown if modifiers are provided
        if modifiers:
            cooldown_time = await cooldown_manager.calculate_dynamic_cooldown(
                cooldown_time, username, monster_id, modifiers
            )
        
        # Set cooldown
        success = await cooldown_manager.set_cooldown(
            monster_id, username, cooldown_type, cooldown_time, guild_id
        )
        
        if not success:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Failed to set cooldown"}
                ).model_dump(),
                token
            )
            return
        
        # Calculate drops
        drops = await cooldown_manager.get_monster_drops(monster_id, username)
        
        # Calculate rewards
        exp = monster_info.get("exp", 0)
        gold_config = monster_info.get("gold", {"min": 0, "max": 0})
        gold = random.randint(gold_config.get("min", 0), gold_config.get("max", 0)) if isinstance(gold_config, dict) else gold_config
        
        # Send confirmation to the user
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.MONSTER_KILLED,
                data={
                    "monster_id": monster_id,
                    "monster_name": monster_info.get("name", monster_id),
                    "cooldown_type": cooldown_type,
                    "cooldown_time": cooldown_time,
                    "success": True,
                    "drops": drops,
                    "exp": exp,
                    "gold": gold
                }
            ).model_dump(),
            token
        )
        
        # Broadcast to relevant users based on cooldown type
        if cooldown_type == CooldownType.GLOBAL:
            # Broadcast to all users
            await connection_manager.broadcast(
                MessageModel(
                    msgId=MessageId.MONSTER_KILLED,
                    data={
                        "monster_id": monster_id,
                        "monster_name": monster_info.get("name", monster_id),
                        "cooldown_type": cooldown_type,
                        "cooldown_time": cooldown_time,
                        "killed_by": username
                    }
                ).model_dump()
            )
        elif cooldown_type == CooldownType.GUILD and guild_id:
            # Get guild members
            db_manager = ServiceLocator.get("db_manager")
            guild = await db_manager.db.guilds.find_one({"guild_id": guild_id})
            if guild and "members" in guild:
                # Broadcast to guild members
                await connection_manager.broadcast_to_users(
                    MessageModel(
                        msgId=MessageId.MONSTER_KILLED,
                        data={
                            "monster_id": monster_id,
                            "monster_name": monster_info.get("name", monster_id),
                            "cooldown_type": cooldown_type,
                            "cooldown_time": cooldown_time,
                            "killed_by": username,
                            "guild_id": guild_id,
                            "guild_name": guild.get("name", guild_id)
                        }
                    ).model_dump(),
                    guild["members"]
                )
        
        # Add items to user inventory
        if drops:
            db_manager = ServiceLocator.get("db_manager")
            for drop in drops:
                item_data = {
                    "defid": drop.get("item_id"),
                    "quantity": drop.get("quantity", 1),
                    "attributes": {
                        "name": drop.get("name"),
                        "source": f"monster:{monster_id}",
                        "obtained_at": int(time.time())
                    }
                }
                await db_manager.add_user_asset(username, "item", item_data)
        
        # Add exp and gold to user
        if exp > 0 or gold > 0:
            db_manager = ServiceLocator.get("db_manager")
            updates = {}
            if exp > 0:
                updates["profile.exp"] = {"$inc": exp}
            if gold > 0:
                updates["profile.gold"] = {"$inc": gold}
            if updates:
                await db_manager.update_user_fields(username, updates=updates)
        
    except Exception as e:
        logger.error(f"Error handling monster_killed: {str(traceback.format_exc())}")
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"Error processing monster killed: {str(e)}"}
            ).model_dump(),
            token
        )

async def handle_get_all_cooldowns( websocket: WebSocket, username: str, token: str, data: Dict,connection_manager):
    """
    Handle request to get all active cooldowns
    
    Expected data format:
    {
        "guild_id": "guild456",  # Optional
        "monster_ids": ["monster1", "monster2"]  # Optional, specific monsters to check
        "area": "forest"  # Optional, filter by area
        "type": "boss"  # Optional, filter by monster type
    }
    """
    try:
        guild_id = data.get("guild_id")
        monster_ids = data.get("monster_ids")
        area = data.get("area")
        monster_type = data.get("type")
        
        # Get cooldown manager from service locator
        cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
        if not cooldown_manager:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Monster cooldown system not available"}
                ).model_dump(),
                token
            )
            return
        
        # If area or type filter is provided, get matching monster IDs
        if area or monster_type:
            filtered_ids = []
            all_monsters = config.get_monster_config()
            
            for m_id, monster in all_monsters.items():
                matches_area = not area or monster.get("area") == area
                matches_type = not monster_type or monster.get("type") == monster_type
                
                if matches_area and matches_type:
                    filtered_ids.append(m_id)
            
            # If monster_ids already provided, intersect with filtered IDs
            if monster_ids:
                monster_ids = [m_id for m_id in monster_ids if m_id in filtered_ids]
            else:
                monster_ids = filtered_ids
        logger.info(f"monster_ids: {monster_ids}")
        # Get all cooldowns
        cooldowns = await cooldown_manager.get_all_cooldowns(username, guild_id, monster_ids)
        
        # Send response
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.GET_ALL_COOLDOWNS,
                data={
                    "cooldowns": cooldowns,
                    "count": len(cooldowns),
                    "filters": {
                        "guild_id": guild_id,
                        "area": area,
                        "type": monster_type
                    }
                }
            ).model_dump(),
            token
        )
        
    except Exception as e:
        logger.error(f"Error handling get_all_cooldowns: {str(traceback.format_exc())}")
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"Error getting cooldowns: {str(e)}"}
            ).model_dump(),
            token
        )

async def handle_reset_cooldown( websocket: WebSocket, username: str, token: str, data: Dict,connection_manager):
    """
    Handle GM request to reset cooldown (GM only)
    
    Expected data format:
    {
        "monster_id": "monster123",
        "cooldown_type": "personal|guild|global",
        "target_username": "user123",  # Required for personal cooldown
        "guild_id": "guild456"  # Required for guild cooldown
    }
    """
    try:
        # Check if user is GM
        db_manager = ServiceLocator.get("db_manager")
        user = await db_manager.get_user_by_username(username)
        
        if not user or not user.profile or user.profile.get("role") != "gm":
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Permission denied: GM access required"}
                ).model_dump(),
                token
            )
            return
        
        monster_id = data.get("monster_id")
        cooldown_type_str = data.get("cooldown_type")
        target_username = data.get("target_username")
        guild_id = data.get("guild_id")
        
        if not monster_id or not cooldown_type_str:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Missing required parameters"}
                ).model_dump(),
                token
            )
            return
        
        # Validate cooldown type
        try:
            cooldown_type = CooldownType(cooldown_type_str)
        except ValueError:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"Invalid cooldown type: {cooldown_type_str}"}
                ).model_dump(),
                token
            )
            return
        
        # Check required parameters based on cooldown type
        if cooldown_type == CooldownType.PERSONAL and not target_username:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Target username is required for personal cooldown reset"}
                ).model_dump(),
                token
            )
            return
        
        if cooldown_type == CooldownType.GUILD and not guild_id:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Guild ID is required for guild cooldown reset"}
                ).model_dump(),
                token
            )
            return
        
        # Get cooldown manager from service locator
        cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
        if not cooldown_manager:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Monster cooldown system not available"}
                ).model_dump(),
                token
            )
            return
        
        # Get monster info from config
        monster_info = config.get_monster_by_id(monster_id)
        monster_name = monster_id
        if monster_info:
            monster_name = monster_info.get("name", monster_id)
        
        # Reset cooldown
        success = await cooldown_manager.reset_cooldown(
            monster_id, cooldown_type, 
            target_username if cooldown_type == CooldownType.PERSONAL else None,
            guild_id if cooldown_type == CooldownType.GUILD else None
        )
        
        if not success:
            await connection_manager.send_personal_message(
                MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": "Failed to reset cooldown"}
                ).model_dump(),
                token
            )
            return
        
        # Send confirmation to the GM
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.RESET_COOLDOWN,
                data={
                    "monster_id": monster_id,
                    "monster_name": monster_name,
                    "cooldown_type": cooldown_type,
                    "success": True
                }
            ).model_dump(),
            token
        )
        
        # Notify relevant users about the reset
        notification_data = {
            "monster_id": monster_id,
            "monster_name": monster_name,
            "cooldown_type": cooldown_type,
            "reset_by": username,
            "message": f"怪物 {monster_name} 的冷却已被GM重置"
        }
        
        if cooldown_type == CooldownType.GLOBAL:
            # Broadcast to all users
            await connection_manager.broadcast(
                MessageModel(
                    msgId=MessageId.MONSTER_RESPAWNED,
                    data=notification_data
                ).model_dump()
            )
        elif cooldown_type == CooldownType.GUILD and guild_id:
            # Get guild members
            guild = await db_manager.db.guilds.find_one({"guild_id": guild_id})
            if guild and "members" in guild:
                # Broadcast to guild members
                await connection_manager.broadcast_to_users(
                    MessageModel(
                        msgId=MessageId.MONSTER_RESPAWNED,
                        data=notification_data
                    ).model_dump(),
                    guild["members"]
                )
        elif cooldown_type == CooldownType.PERSONAL and target_username:
            # Send to specific user
            await connection_manager.send_personal_message_to_user(
                MessageModel(
                    msgId=MessageId.MONSTER_RESPAWNED,
                    data=notification_data
                ).model_dump(),
                target_username
            )
        
    except Exception as e:
        logger.error(f"Error handling reset_cooldown: {str(traceback.format_exc())}")
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"Error resetting cooldown: {str(e)}"}
            ).model_dump(),
            token
        )

async def handle_get_monsters( websocket: WebSocket, username: str, token: str, data: Dict,connection_manager):
    """
    Handle request to get monster list
    
    Expected data format:
    {
        "area": "forest",  # Optional, filter by area
        "type": "boss",    # Optional, filter by type
        "level_min": 10,   # Optional, filter by minimum level
        "level_max": 50    # Optional, filter by maximum level
    }
    """
    try:
        area = data.get("area")
        monster_type = data.get("type")
        level_min = data.get("level_min")
        level_max = data.get("level_max")
        
        # Get all monsters
        all_monsters = {}
        monsters_list = config.get_monster_config()
        for _,monster in monsters_list.items():
            logger.info(f"monster: {monster}")
            all_monsters[monster.get("id")] = monster
        
        # Apply filters
        filtered_monsters = []
        for monster_id, monster in all_monsters.items():
            # Apply area filter
            if area and monster.get("area") != area:
                continue
                
            # Apply type filter
            if monster_type and monster.get("type") != monster_type:
                continue
                
            # Apply level filters
            level = monster.get("level", 1)
            if level_min is not None and level < level_min:
                continue
            if level_max is not None and level > level_max:
                continue
                
            # Add to filtered list
            monster_copy = monster.copy()
            monster_copy["id"] = monster_id
            filtered_monsters.append(monster_copy)
        
        # Sort by level
        filtered_monsters.sort(key=lambda m: m.get("level", 1))
        
        # Send response
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.GET_MONSTERS,
                data={
                    "monsters": filtered_monsters,
                    "count": len(filtered_monsters),
                    "filters": {
                        "area": area,
                        "type": monster_type,
                        "level_min": level_min,
                        "level_max": level_max
                    }
                }
            ).model_dump(),
            token
        )
        
    except Exception as e:
        logger.error(f"Error handling get_monsters: {str(traceback.format_exc())}")
        await connection_manager.send_personal_message(
            MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"Error getting monsters: {str(e)}"}
            ).model_dump(),
            token
        ) 