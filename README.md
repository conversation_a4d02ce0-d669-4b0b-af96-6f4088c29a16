# Game Server

一个**生产级别**的分布式游戏服务器框架，具有完善的分布式架构、先进的缓存管理系统和实时通信能力。

## 项目简介

本项目是一个企业级多Worker分布式游戏服务器架构，支持用户认证、实时通信、物品管理、武将系统、装备系统等完整游戏功能。采用现代化的异步处理模式，集成了完善的事务管理、分布式锁、多层缓存系统，能够处理大规模高并发游戏场景。

## 🏆 架构状态

**当前状态**: 🟢 **生产就绪** | **架构成熟度**: ⭐⭐⭐⭐⭐

- ✅ **分布式锁系统**: 完整实现，支持死锁检测
- ✅ **事务管理**: MongoDB事务 + 自动降级机制
- ✅ **连接池管理**: 智能动态调整，实时监控
- ✅ **WebSocket管理**: 完善的连接生命周期管理
- ✅ **缓存一致性**: 跨Worker缓存同步机制

> 📋 详细架构分析请查看: [架构状态报告](ARCHITECTURE_STATUS.md)

## 🚀 核心技术特性

### 分布式架构
- **多Worker支持**: 支持水平扩展，负载均衡
- **分布式锁**: Redis分布式锁，防止竞态条件
- **事务管理**: MongoDB事务 + 自动降级机制
- **缓存一致性**: 跨Worker缓存同步，Pub/Sub通知

### 高性能特性
- **连接池管理**: 智能动态调整，实时监控告警
- **异步架构**: 全异步非阻塞设计，高并发支持
- **多层缓存**: 内存 + Redis 多层缓存策略
- **批量操作**: 数据库批量读写优化

### 可靠性保证
- **健康检查**: 自动检测组件状态，故障恢复
- **优雅关闭**: 资源清理，连接安全断开
- **错误恢复**: 自动重试，指数退避策略
- **监控告警**: 实时性能监控，异常告警

## 技术栈

- **后端**: Python 3.8+ (FastAPI + asyncio)
- **数据库**: MongoDB 4.4+ (支持事务)
- **缓存**: Redis 6.0+ (分布式锁 + Pub/Sub)
- **消息队列**: RabbitMQ 3.8+
- **通信**: WebSockets (实时双向通信)
- **容器化**: Docker, docker-compose
- **Web服务器**: Nginx (反向代理 + 负载均衡)

## 系统架构

### 核心组件

#### 🎮 服务器核心
- **main.py**: 应用程序入口点
- **game_server.py**: 主服务器逻辑，处理WebSocket连接
- **ConnectionManager.py**: WebSocket连接生命周期管理
- **websocket_handlers.py**: WebSocket消息路由和处理

#### 🗄️ 数据层
- **mongodb_manager.py**: MongoDB连接池和事务管理
- **redis_manager.py**: Redis连接池和分布式锁
- **models.py**: 数据模型定义和ORM
- **BaseModelORM.py**: 基础ORM模型类

#### 🔧 核心服务
- **auth.py**: 用户认证和授权系统
- **distributed_lock.py**: 分布式锁实现
- **transaction_manager.py**: 事务管理器
- **service_locator.py**: 服务定位器模式

#### 🎯 业务模块
- **general_service_distributed.py**: 武将系统服务
- **equipment_service_distributed.py**: 装备系统服务
- **card_pack_manager_distributed.py**: 卡包系统管理
- **xxsg/monster_cooldown.py**: 怪物冷却系统

#### ⚙️ 系统组件
- **config.py**: 配置管理
- **enums.py**: 枚举定义
- **utils.py**: 工具函数
- **logger_config.py**: 日志配置

### 🚀 分布式缓存架构

项目实现了一套企业级的多层缓存架构，支持分布式环境：

#### 1. **基础缓存层**
- **CacheManager.py**: 统一缓存接口，支持Redis事务
- **CacheKeyBuilder.py**: 智能缓存键构建和管理
- **CacheInvalidationManager.py**: 跨Worker缓存失效通知

#### 2. **领域缓存层**
- **UserCacheManager.py**: 用户数据专用缓存管理
- **ItemCacheManager.py**: 物品数据缓存，支持复杂查询
- **GeneralCacheManager.py**: 武将数据缓存优化

#### 3. **分布式特性**
- **缓存一致性**: Pub/Sub机制保证多Worker缓存同步
- **智能失效**: 基于数据变更的精确缓存失效
- **性能监控**: 缓存命中率和性能指标收集
- **降级策略**: 缓存失败时的数据库直查降级

### 怪物冷却系统架构

项目实现了一套完整的怪物冷却系统，支持多种冷却类型：

1. **冷却类型**
   - 个人冷却：针对单个玩家的冷却
   - 军团冷却：针对整个军团的冷却
   - 全服冷却：针对所有玩家的冷却

2. **主要功能**
   - 动态冷却时间计算（基于VIP等级、活动、道具等）
   - 冷却结束通知推送
   - 冷却数据持久化与恢复
   - 批量冷却查询API
   - GM/运维接口

3. **技术实现**
   - 基于Redis Hash结构的高性能冷却数据存储
   - 分布式锁确保多进程/多节点环境下的一致性
   - MongoDB持久化确保服务重启后数据恢复

## 安装指南

### 使用Docker (推荐)

1. 克隆仓库:
   ```bash
   git clone <repository-url>
   cd game_server
   ```

2. 创建环境配置:
   ```bash
   cp env.example .env
   # 编辑.env文件设置必要参数
   ```

3. 启动服务:
   ```bash
   docker-compose up -d
   ```

### 手动安装

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 安装MongoDB和Redis:
   ```bash
   # 根据您的操作系统安装MongoDB和Redis
   ```

3. 创建配置:
   ```bash
   cp config/server.ini.example config/server.ini
   # 编辑server.ini设置必要参数
   ```

4. 运行服务:
   ```bash
   python main.py
   ```

## 配置指南

### 主要配置文件

- `config/server.ini`: 主配置文件
- `.env`: Docker环境变量
- `config/field_config.json`: 字段验证配置

### 数据库配置

在server.ini中配置MongoDB和Redis连接信息:

```ini
[mongodb]
host = localhost
port = 27017
database = gamedb
username = 
password = 

[redis]
host = localhost
port = 6379
password = 
db = 0
```

## 缓存系统使用指南

### 基本使用

```python
# 获取缓存管理器实例
db_manager = DatabaseManager()

# 用户缓存操作
user_data = await db_manager.user_cache.get_user_data("username123")
await db_manager.user_cache.set_user_data("username123", user_data)

# 物品缓存操作
items = await db_manager.item_cache.get_user_items("username123", "item")
await db_manager.item_cache.add_item(item_data)

# 事务支持
async with db_manager.cache_manager.transaction() as tx:
    await tx.set("key1", "value1")
    await tx.set("key2", "value2")
```

## 怪物冷却系统使用指南

### 基本使用

```python
# 获取冷却管理器实例
cooldown_manager = ServiceLocator.get("monster_cooldown_manager")

# 设置冷却
await cooldown_manager.set_cooldown(
    monster_id="monster123",
    username="player1",
    cooldown_type=CooldownType.PERSONAL,
    cooldown_time=3600  # 1小时冷却
)

# 检查冷却
cooldown_info = await cooldown_manager.check_cooldown(
    monster_id="monster123",
    username="player1"
)

# 获取所有冷却
all_cooldowns = await cooldown_manager.get_all_cooldowns(username="player1")

# 重置冷却（GM功能）
await cooldown_manager.reset_cooldown(
    monster_id="monster123",
    cooldown_type=CooldownType.PERSONAL,
    username="player1"
)

# 动态计算冷却时间
cooldown_time = await cooldown_manager.calculate_dynamic_cooldown(
    base_cooldown=3600,
    username="player1",
    monster_id="monster123",
    modifiers={"vip_bonus": 0.8, "event_bonus": 0.9}
)
```

### WebSocket消息

怪物冷却系统通过WebSocket提供以下消息类型：

| 消息ID | 名称 | 描述 |
|-------|-----|------|
| 100 | CHECK_COOLDOWN | 检查怪物冷却状态 |
| 101 | MONSTER_KILLED | 怪物被击杀，设置冷却 |
| 102 | GET_ALL_COOLDOWNS | 获取所有活跃的冷却 |
| 103 | RESET_COOLDOWN | 重置冷却（仅GM） |
| 104 | MONSTER_RESPAWNED | 怪物重生通知 |

## API文档

游戏服务器提供基于WebSocket的实时API:

- 连接: `ws://<server-host>/ws/<token>`
- 认证: 通过auth.py模块进行用户认证
- 消息格式: JSON格式，包含msgId, data字段

常见消息类型:
- 用户认证
- 物品操作
- 资产变更通知
- 状态更新
- 怪物冷却操作

## 部署指南

### 生产环境部署

1. 优化Docker配置:
   ```bash
   # 编辑docker-compose.yml设置适当的资源限制
   ```

2. 配置Nginx反向代理:
   ```bash
   # 使用项目中的nginx.conf模板
   ```

3. 启用日志轮转:
   ```bash
   # 配置日志轮转避免磁盘空间耗尽
   ```

### 监控与维护

- 使用Docker stats监控容器状态
- 定期清理RabbitMQ队列 (使用rabbitmq_cleaner.py)
- 监控Redis内存使用情况
- 监控怪物冷却系统状态 (使用xxsg/monster_cooldown.py中的监控功能)

## 扩展开发

### 添加新功能

1. **数据模型**: 在`models.py`中定义新的数据模型
2. **数据访问**: 在相应的服务类中添加数据访问方法
   - 用户相关: 扩展用户服务
   - 装备相关: 扩展`equipment_service_distributed.py`
   - 武将相关: 扩展`general_service_distributed.py`
3. **缓存管理**: 为高频访问数据创建专用缓存管理器
4. **消息处理**: 在`websocket_handlers.py`中添加新的消息处理逻辑
5. **分布式锁**: 为关键操作添加分布式锁保护

### 扩展怪物冷却系统

1. 在xxsg/monster_cooldown.py中添加新的冷却类型或功能
2. 在xxsg/monster_handlers.py中添加相应的WebSocket处理程序
3. 在enums.py中添加新的消息ID
4. 在msgManager.py中注册新的处理程序

## License

[MIT License](LICENSE) 
