# 卡包系统使用说明

## 1. 系统简介

本卡包系统支持：
- 普通卡包、限时卡包、限次卡包、每日/每周刷新卡包
- 多worker/分布式环境下的并发安全（Redis分布式锁）
- Redis高性能缓存 + MongoDB持久化
- 全局活动卡包动态开启/关闭（如活动卡池）
- 用户上线/操作时自动同步全局活动卡包

---

## 2. 主要类与接口

### 2.1 CardPackManagerDistributed
- 管理单个用户的所有卡包实例
- 支持抽卡、刷新、持久化、自动同步全局活动卡包

### 2.2 CardPackGlobalManager
- 管理全服活动卡包（如限时活动池）
- 支持动态开启/关闭活动卡包
- 支持全局开关（禁止/允许抽卡）

---

## 3. 集成与使用

### 3.1 用户上线/初始化
```python
manager = CardPackManagerDistributed(user_id)
await manager.load()  # 加载并自动同步全局活动卡包
```

### 3.2 用户抽卡
```python
result = await manager.draw_from_pack(pack_id, use_free=True)
if result:
    print("抽卡成功")
else:
    print("抽卡失败或卡包未开启")
```

### 3.3 刷新所有卡包（如每日定时任务）
```python
await manager.refresh_all()
```

### 3.4 用户下线/定期持久化
- 抽卡/刷新等操作已自动保存，无需手动调用。
- 如需强制保存：
```python
await manager.save()
```

---

## 4. 全局活动卡包管理（GM/后台/定时任务）

### 4.1 动态开启活动卡包
```python
from card_pack_manager_distributed import CardPackGlobalManager
await CardPackGlobalManager.open_card_pack(
    pack_id="10001",  # 活动卡包ID，建议大于10000
    params={"desc": "限时活动卡包"},
    start_time=活动开始时间戳,
    end_time=活动结束时间戳
)
```

### 4.2 关闭活动卡包
```python
await CardPackGlobalManager.close_card_pack("10001")
```

### 4.3 查询当前全服活动卡包
```python
active = await CardPackGlobalManager.get_active_card_packs()
print(active)
```

### 4.4 全局开关（禁止/允许抽卡）
```python
await CardPackGlobalManager.enable_card_pack(pack_id)   # 允许抽卡
await CardPackGlobalManager.disable_card_pack(pack_id)  # 禁止抽卡
```

---

## 5. 典型业务流程

1. GM/后台开启活动卡包 → 全服玩家自动同步 → 用户可见/可抽取
2. 活动结束后关闭活动卡包 → 全服玩家自动移除
3. 用户抽卡、刷新等操作自动持久化到Redis+MongoDB
4. 支持多worker/多进程分布式部署

---

## 6. 注意事项
- 活动卡包建议ID大于10000，便于区分与普通卡包。
- 活动卡包支持自定义参数、开始/结束时间，过期自动隐藏。
- 用户上线/刷新时自动同步全局活动卡包，无需手动管理。
- 所有操作均为异步接口，需在async环境下调用。

---

## 7. 扩展与定制
- 可扩展更多卡包类型、抽卡规则、奖励逻辑。
- 可对接WebSocket/HTTP消息系统，注册抽卡/查询等handler。
- 支持GM后台、定时任务自动管理活动卡包。

---

如需更详细的集成示例或特殊业务需求，请联系开发者。 