"""
玩家会话管理器
管理玩家的WebSocket连接会话，支持多Worker环境
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from redis_manager import RedisManager
from distributed_lock import DistributedLock

logger = logging.getLogger(__name__)


class PlayerSession:
    """玩家会话信息"""
    
    def __init__(self, username: str, token: str, worker_id: int, websocket=None):
        self.username = username
        self.token = token
        self.worker_id = worker_id
        self.websocket = websocket
        self.login_time = datetime.now()
        self.last_activity = datetime.now()
        self.ip_address = None
        self.user_agent = None
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于Redis存储）"""
        return {
            "username": self.username,
            "token": self.token,
            "worker_id": self.worker_id,
            "login_time": self.login_time.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "ip_address": self.ip_address,
            "user_agent": self.user_agent
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PlayerSession":
        """从字典创建会话对象"""
        session = cls(
            username=data["username"],
            token=data["token"],
            worker_id=data["worker_id"]
        )
        session.login_time = datetime.fromisoformat(data["login_time"])
        session.last_activity = datetime.fromisoformat(data["last_activity"])
        session.ip_address = data.get("ip_address")
        session.user_agent = data.get("user_agent")
        return session
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = datetime.now()
    
    def is_expired(self, timeout_seconds: int = 3600) -> bool:
        """检查会话是否过期"""
        return (datetime.now() - self.last_activity).total_seconds() > timeout_seconds


class PlayerSessionManager:
    """玩家会话管理器 - 支持多Worker"""
    
    # Redis键前缀
    SESSION_PREFIX = "player_session:"
    ONLINE_PLAYERS_KEY = "online_players"
    WORKER_SESSIONS_PREFIX = "worker_sessions:"
    
    def __init__(self):
        self.redis_client = None
        self.worker_id = os.getpid()
        self.local_sessions: Dict[str, PlayerSession] = {}  # 本Worker的会话
        self.session_timeout = 3600  # 会话超时时间（秒）
        
    async def _get_redis(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            redis_manager = await RedisManager.get_instance()
            self.redis_client = await redis_manager.get_redis()
        return self.redis_client

    # ==================== 会话管理 ====================
    
    async def create_session(self, username: str, token: str, websocket=None) -> PlayerSession:
        """创建玩家会话"""
        try:
            # 检查是否已有会话
            existing_session = await self.get_session(username)
            if existing_session:
                # 如果是同一个Worker，更新会话
                if existing_session.worker_id == self.worker_id:
                    existing_session.token = token
                    existing_session.websocket = websocket
                    existing_session.update_activity()
                    await self._save_session_to_redis(existing_session)
                    return existing_session
                else:
                    # 如果在其他Worker，需要先清理旧会话
                    await self.remove_session(username)
            
            # 创建新会话
            session = PlayerSession(username, token, self.worker_id, websocket)
            
            # 保存到本地和Redis
            self.local_sessions[username] = session
            await self._save_session_to_redis(session)
            
            # 添加到在线玩家集合
            await self._add_to_online_players(username)
            
            # 添加到Worker会话列表
            await self._add_to_worker_sessions(username)
            
            logger.info(f"创建玩家会话: {username} (Worker: {self.worker_id})")
            return session
            
        except Exception as e:
            logger.error(f"创建玩家会话失败: {username}, 错误: {str(e)}")
            raise

    async def get_session(self, username: str) -> Optional[PlayerSession]:
        """获取玩家会话"""
        try:
            # 先从本地缓存获取
            if username in self.local_sessions:
                session = self.local_sessions[username]
                if not session.is_expired(self.session_timeout):
                    return session
                else:
                    # 会话过期，清理
                    await self.remove_session(username)
                    return None
            
            # 从Redis获取
            redis = await self._get_redis()
            session_key = f"{self.SESSION_PREFIX}{username}"
            session_data = await redis.get(session_key)
            
            if session_data:
                session_dict = json.loads(session_data)
                session = PlayerSession.from_dict(session_dict)
                
                # 检查是否过期
                if session.is_expired(self.session_timeout):
                    await self.remove_session(username)
                    return None
                
                return session
            
            return None
            
        except Exception as e:
            logger.error(f"获取玩家会话失败: {username}, 错误: {str(e)}")
            return None

    async def remove_session(self, username: str) -> bool:
        """移除玩家会话"""
        try:
            redis = await self._get_redis()
            
            # 从本地缓存移除
            if username in self.local_sessions:
                del self.local_sessions[username]
            
            # 从Redis移除
            session_key = f"{self.SESSION_PREFIX}{username}"
            await redis.delete(session_key)
            
            # 从在线玩家集合移除
            await self._remove_from_online_players(username)
            
            # 从Worker会话列表移除
            await self._remove_from_worker_sessions(username)
            
            logger.info(f"移除玩家会话: {username} (Worker: {self.worker_id})")
            return True
            
        except Exception as e:
            logger.error(f"移除玩家会话失败: {username}, 错误: {str(e)}")
            return False

    async def update_session_activity(self, username: str) -> bool:
        """更新会话活动时间"""
        try:
            session = await self.get_session(username)
            if session:
                session.update_activity()
                
                # 如果是本Worker的会话，更新本地缓存
                if session.worker_id == self.worker_id and username in self.local_sessions:
                    self.local_sessions[username] = session
                
                # 更新Redis
                await self._save_session_to_redis(session)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新会话活动时间失败: {username}, 错误: {str(e)}")
            return False

    # ==================== 在线玩家管理 ====================
    
    async def get_online_players(self) -> Set[str]:
        """获取所有在线玩家"""
        try:
            redis = await self._get_redis()
            players = await redis.smembers(self.ONLINE_PLAYERS_KEY)
            return set(players) if players else set()
        except Exception as e:
            logger.error(f"获取在线玩家失败: {str(e)}")
            return set()

    async def get_online_count(self) -> int:
        """获取在线玩家数量"""
        try:
            redis = await self._get_redis()
            return await redis.scard(self.ONLINE_PLAYERS_KEY)
        except Exception as e:
            logger.error(f"获取在线玩家数量失败: {str(e)}")
            return 0

    async def is_player_online(self, username: str) -> bool:
        """检查玩家是否在线"""
        try:
            redis = await self._get_redis()
            return await redis.sismember(self.ONLINE_PLAYERS_KEY, username)
        except Exception as e:
            logger.error(f"检查玩家在线状态失败: {username}, 错误: {str(e)}")
            return False

    async def get_worker_sessions(self, worker_id: int = None) -> List[str]:
        """获取指定Worker的会话列表"""
        try:
            if worker_id is None:
                worker_id = self.worker_id
            
            redis = await self._get_redis()
            sessions_key = f"{self.WORKER_SESSIONS_PREFIX}{worker_id}"
            sessions = await redis.smembers(sessions_key)
            return list(sessions) if sessions else []
            
        except Exception as e:
            logger.error(f"获取Worker会话列表失败: {worker_id}, 错误: {str(e)}")
            return []

    # ==================== 会话清理 ====================
    
    async def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        try:
            cleaned_count = 0
            online_players = await self.get_online_players()
            
            for username in online_players:
                session = await self.get_session(username)
                if not session or session.is_expired(self.session_timeout):
                    await self.remove_session(username)
                    cleaned_count += 1
            
            logger.info(f"清理过期会话完成，清理数量: {cleaned_count}")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
            return 0

    async def cleanup_worker_sessions(self, worker_id: int = None) -> int:
        """清理指定Worker的所有会话"""
        try:
            if worker_id is None:
                worker_id = self.worker_id
            
            sessions = await self.get_worker_sessions(worker_id)
            cleaned_count = 0
            
            for username in sessions:
                await self.remove_session(username)
                cleaned_count += 1
            
            # 清理Worker会话列表
            redis = await self._get_redis()
            sessions_key = f"{self.WORKER_SESSIONS_PREFIX}{worker_id}"
            await redis.delete(sessions_key)
            
            logger.info(f"清理Worker会话完成: {worker_id}, 清理数量: {cleaned_count}")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理Worker会话失败: {worker_id}, 错误: {str(e)}")
            return 0

    # ==================== 私有方法 ====================
    
    async def _save_session_to_redis(self, session: PlayerSession):
        """保存会话到Redis"""
        try:
            redis = await self._get_redis()
            session_key = f"{self.SESSION_PREFIX}{session.username}"
            session_data = json.dumps(session.to_dict(), ensure_ascii=False)
            await redis.setex(session_key, self.session_timeout, session_data)
        except Exception as e:
            logger.error(f"保存会话到Redis失败: {session.username}, 错误: {str(e)}")

    async def _add_to_online_players(self, username: str):
        """添加到在线玩家集合"""
        try:
            redis = await self._get_redis()
            await redis.sadd(self.ONLINE_PLAYERS_KEY, username)
        except Exception as e:
            logger.error(f"添加到在线玩家集合失败: {username}, 错误: {str(e)}")

    async def _remove_from_online_players(self, username: str):
        """从在线玩家集合移除"""
        try:
            redis = await self._get_redis()
            await redis.srem(self.ONLINE_PLAYERS_KEY, username)
        except Exception as e:
            logger.error(f"从在线玩家集合移除失败: {username}, 错误: {str(e)}")

    async def _add_to_worker_sessions(self, username: str):
        """添加到Worker会话列表"""
        try:
            redis = await self._get_redis()
            sessions_key = f"{self.WORKER_SESSIONS_PREFIX}{self.worker_id}"
            await redis.sadd(sessions_key, username)
            await redis.expire(sessions_key, self.session_timeout)
        except Exception as e:
            logger.error(f"添加到Worker会话列表失败: {username}, 错误: {str(e)}")

    async def _remove_from_worker_sessions(self, username: str):
        """从Worker会话列表移除"""
        try:
            redis = await self._get_redis()
            sessions_key = f"{self.WORKER_SESSIONS_PREFIX}{self.worker_id}"
            await redis.srem(sessions_key, username)
        except Exception as e:
            logger.error(f"从Worker会话列表移除失败: {username}, 错误: {str(e)}")
