#!/usr/bin/env python3
"""
测试优化后的DistributedLock功能
"""

import asyncio
import logging
import time
from distributed_lock import DistributedLock

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_basic_lock():
    """测试基本的锁功能"""
    logger.info("=== 测试基本锁功能 ===")
    
    lock = DistributedLock("test_lock", ttl=10)
    
    # 获取锁
    logger.info("尝试获取锁...")
    acquired = await lock.acquire(blocking=True, timeout=5)
    if acquired:
        logger.info("✅ 成功获取锁")
        
        # 检查锁状态
        logger.info(f"锁状态: {lock.is_acquired}")
        
        # 延长锁超时时间
        logger.info("尝试延长锁超时时间...")
        extended = await lock.extend_ttl(20)
        if extended:
            logger.info("✅ 成功延长锁超时时间")
        else:
            logger.warning("❌ 延长锁超时时间失败")
        
        # 释放锁
        await lock.release()
        logger.info("✅ 成功释放锁")
    else:
        logger.error("❌ 获取锁失败")

async def test_context_manager():
    """测试上下文管理器"""
    logger.info("=== 测试上下文管理器 ===")
    
    try:
        async with DistributedLock("test_context_lock", ttl=10) as lock:
            logger.info("✅ 通过上下文管理器获取锁")
            logger.info(f"锁状态: {lock.is_acquired}")
            await asyncio.sleep(1)
            logger.info("✅ 上下文管理器自动释放锁")
    except Exception as e:
        logger.error(f"❌ 上下文管理器测试失败: {e}")

async def test_concurrent_locks():
    """测试并发锁"""
    logger.info("=== 测试并发锁 ===")
    
    async def worker(worker_id: int):
        lock = DistributedLock("concurrent_test_lock", ttl=5)
        
        logger.info(f"Worker {worker_id}: 尝试获取锁...")
        acquired = await lock.acquire(blocking=True, timeout=10)
        
        if acquired:
            logger.info(f"Worker {worker_id}: ✅ 获取锁成功")
            await asyncio.sleep(2)  # 模拟工作
            await lock.release()
            logger.info(f"Worker {worker_id}: ✅ 释放锁")
        else:
            logger.warning(f"Worker {worker_id}: ❌ 获取锁失败")
    
    # 创建多个并发任务
    tasks = [worker(i) for i in range(3)]
    await asyncio.gather(*tasks)

async def test_lock_timeout():
    """测试锁超时"""
    logger.info("=== 测试锁超时 ===")
    
    # 第一个锁
    lock1 = DistributedLock("timeout_test_lock", ttl=3)
    acquired1 = await lock1.acquire()
    
    if acquired1:
        logger.info("✅ 第一个锁获取成功")
        
        # 第二个锁应该超时
        lock2 = DistributedLock("timeout_test_lock", ttl=3)
        acquired2 = await lock2.acquire(blocking=True, timeout=2)
        
        if not acquired2:
            logger.info("✅ 第二个锁正确超时")
        else:
            logger.error("❌ 第二个锁不应该获取成功")
            await lock2.release()
        
        await lock1.release()
        logger.info("✅ 第一个锁释放")
    else:
        logger.error("❌ 第一个锁获取失败")

async def test_connection_failure():
    """测试连接失败情况"""
    logger.info("=== 测试连接失败情况 ===")
    
    # 创建一个无效的锁（Redis连接失败时）
    # 这里我们测试错误处理
    try:
        lock = DistributedLock("invalid_lock", ttl=5)
        acquired = await lock.acquire(blocking=False, timeout=1)
        logger.info(f"锁获取结果: {acquired}")
    except Exception as e:
        logger.info(f"预期的连接错误: {e}")

async def main():
    """主测试函数"""
    logger.info("开始测试优化后的DistributedLock...")
    
    try:
        # 测试基本功能
        await test_basic_lock()
        await asyncio.sleep(1)
        
        # 测试上下文管理器
        await test_context_manager()
        await asyncio.sleep(1)
        
        # 测试并发锁
        await test_concurrent_locks()
        await asyncio.sleep(1)
        
        # 测试锁超时
        await test_lock_timeout()
        await asyncio.sleep(1)
        
        # 测试连接失败
        await test_connection_failure()
        
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        # 关闭Redis连接
        await DistributedLock.close_connection()

if __name__ == "__main__":
    asyncio.run(main()) 