"""
缓存失效通知管理器
实现跨Worker的缓存同步机制，使用Redis Pub/Sub
"""

import asyncio
import json
import logging
import os
import traceback
from typing import Dict, Any, Callable, Optional, Set
from redis_manager import RedisManager
from logger_config import setup_logger

logger = setup_logger(__name__)


class CacheInvalidationManager:
    """缓存失效通知管理器"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.redis_manager = None
        self.pubsub = None
        self.listeners: Dict[str, Set[Callable]] = {}
        self.worker_id = None
        self.is_listening = False
        self._listen_task = None
        
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    await cls._instance.initialize()
        return cls._instance
    
    async def initialize(self):
        """初始化管理器"""
        try:
            self.redis_manager = await RedisManager.get_instance()
            self.worker_id = str(os.getpid())
            
            # 创建专用的Redis连接用于订阅
            redis_client = await self.redis_manager.get_redis()
            self.pubsub = redis_client.pubsub()
            
            # 订阅缓存失效频道
            await self.pubsub.subscribe("cache_invalidation")
            
            # 启动监听任务
            self._listen_task = asyncio.create_task(self._listen_for_invalidations())
            self.is_listening = True
            
            logger.info(f"缓存失效通知管理器初始化完成 (Worker {self.worker_id})")
            
        except Exception as e:
            logger.error(f"缓存失效通知管理器初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    async def register_listener(self, cache_type: str, callback: Callable[[str, Dict], None]):
        """
        注册缓存失效监听器
        
        Args:
            cache_type: 缓存类型 (如 'user', 'item', 'general')
            callback: 回调函数，接收 (cache_key, data) 参数
        """
        if cache_type not in self.listeners:
            self.listeners[cache_type] = set()
        
        self.listeners[cache_type].add(callback)
        logger.debug(f"注册缓存失效监听器: {cache_type}")
    
    async def unregister_listener(self, cache_type: str, callback: Callable):
        """取消注册缓存失效监听器"""
        if cache_type in self.listeners:
            self.listeners[cache_type].discard(callback)
            if not self.listeners[cache_type]:
                del self.listeners[cache_type]
    
    async def invalidate_cache(self, cache_type: str, cache_key: str, data: Optional[Dict] = None):
        """
        发送缓存失效通知
        
        Args:
            cache_type: 缓存类型
            cache_key: 缓存键
            data: 附加数据
        """
        try:
            message = {
                "cache_type": cache_type,
                "cache_key": cache_key,
                "data": data or {},
                "worker_id": self.worker_id,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            # 发布失效通知
            redis_client = await self.redis_manager.get_redis()
            await redis_client.publish(
                "cache_invalidation",
                json.dumps(message)
            )
            
            logger.debug(f"发送缓存失效通知: {cache_type}:{cache_key}")
            
        except Exception as e:
            logger.error(f"发送缓存失效通知失败: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def _listen_for_invalidations(self):
        """监听缓存失效通知"""
        try:
            while self.is_listening:
                try:
                    message = await self.pubsub.get_message(timeout=1.0)
                    if message and message['type'] == 'message':
                        await self._handle_invalidation_message(message['data'])
                        
                except asyncio.TimeoutError:
                    # 超时是正常的，继续监听
                    continue
                except Exception as e:
                    logger.error(f"处理缓存失效消息失败: {str(e)}")
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"缓存失效监听任务异常: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def _handle_invalidation_message(self, message_data):
        """处理缓存失效消息"""
        try:
            if isinstance(message_data, bytes):
                message_data = message_data.decode('utf-8')
            
            message = json.loads(message_data)
            
            # 忽略自己发送的消息
            if message.get('worker_id') == self.worker_id:
                return
            
            cache_type = message.get('cache_type')
            cache_key = message.get('cache_key')
            data = message.get('data', {})
            
            # 调用注册的监听器
            if cache_type in self.listeners:
                for callback in self.listeners[cache_type]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(cache_key, data)
                        else:
                            callback(cache_key, data)
                    except Exception as e:
                        logger.error(f"缓存失效回调执行失败: {str(e)}")
            
            logger.debug(f"处理缓存失效通知: {cache_type}:{cache_key}")
            
        except Exception as e:
            logger.error(f"处理缓存失效消息失败: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.is_listening = False
            
            if self._listen_task and not self._listen_task.done():
                self._listen_task.cancel()
                try:
                    await self._listen_task
                except asyncio.CancelledError:
                    pass
            
            if self.pubsub:
                await self.pubsub.unsubscribe("cache_invalidation")
                await self.pubsub.close()
            
            self.listeners.clear()
            
            logger.info(f"缓存失效通知管理器清理完成 (Worker {self.worker_id})")
            
        except Exception as e:
            logger.error(f"缓存失效通知管理器清理失败: {str(e)}")


# 全局实例
cache_invalidation_manager = None


async def get_cache_invalidation_manager():
    """获取全局缓存失效通知管理器实例"""
    global cache_invalidation_manager
    if cache_invalidation_manager is None:
        cache_invalidation_manager = await CacheInvalidationManager.get_instance()
    return cache_invalidation_manager
