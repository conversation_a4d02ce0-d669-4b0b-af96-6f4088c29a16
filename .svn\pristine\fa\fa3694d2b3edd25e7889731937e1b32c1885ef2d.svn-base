"""
简单的WebSocket测试客户端
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleWebSocketClient:
    """简单的WebSocket测试客户端"""
    
    def __init__(self, server_url: str = "ws://localhost:8000/ws"):
        self.server_url = server_url
        self.websocket = None
        self.username = "test_user_fix"
        self.token = "test_token_fix"
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔌 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            
            # 发送登录消息
            login_message = {
                "msgId": 1,  # LOGIN
                "data": {
                    "username": self.username,
                    "token": self.token
                }
            }
            
            await self.websocket.send(json.dumps(login_message))
            print("📤 发送登录消息")
            
            # 等待登录响应
            response = await self.websocket.recv()
            print(f"📥 登录响应: {response}")
            
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return False
    
    async def send_shop_message(self, msg_id: int, data: dict = None):
        """发送商店相关消息"""
        try:
            if data is None:
                data = {}
            
            message = {
                "msgId": msg_id,
                "data": data
            }
            
            print(f"📤 发送消息: msgId={msg_id}, data={data}")
            await self.websocket.send(json.dumps(message))
            
            # 等待响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            response_data = json.loads(response)
            
            print(f"📥 收到响应: msgId={response_data.get('msgId')}")
            print(f"   数据: {response_data.get('data', {}).get('success', 'N/A')}")
            
            return response_data
            
        except asyncio.TimeoutError:
            print("⏰ 响应超时")
            return None
        except Exception as e:
            print(f"❌ 发送消息失败: {str(e)}")
            return None
    
    async def test_shop_get_list(self):
        """测试获取商店列表"""
        print("\n🏪 测试获取商店列表...")
        response = await self.send_shop_message(350)  # SHOP_GET_LIST
        
        if response and response.get('data', {}).get('success'):
            shops = response['data'].get('shops', [])
            print(f"✅ 获取商店列表成功: {len(shops)} 个商店")
            return True
        else:
            print("❌ 获取商店列表失败")
            return False
    
    async def test_shop_get_items(self, shop_id: str):
        """测试获取商店商品"""
        print(f"\n📦 测试获取商店商品: {shop_id}")
        response = await self.send_shop_message(351, {"shop_id": shop_id})  # SHOP_GET_ITEMS
        
        if response and response.get('data', {}).get('success'):
            items = response['data'].get('items', [])
            print(f"✅ 获取商店商品成功: {len(items)} 个商品")
            return True
        else:
            print("❌ 获取商店商品失败")
            return False
    
    async def test_shop_get_limits(self):
        """测试获取限购状态"""
        print(f"\n📊 测试获取限购状态...")
        response = await self.send_shop_message(356)  # SHOP_GET_LIMITS
        
        if response and response.get('data', {}).get('success'):
            print(f"✅ 获取限购状态成功")
            return True
        else:
            print("❌ 获取限购状态失败")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 连接已断开")
    
    async def run_tests(self):
        """运行所有测试"""
        print("🧪 开始WebSocket修复验证测试")
        print("=" * 50)
        
        try:
            # 连接服务器
            if not await self.connect():
                print("❌ 无法连接服务器，测试终止")
                return False
            
            # 等待一下确保连接稳定
            await asyncio.sleep(1)
            
            # 测试获取商店列表
            success1 = await self.test_shop_get_list()
            
            # 测试获取限购状态
            success2 = await self.test_shop_get_limits()
            
            # 如果有商店，测试获取商品
            success3 = True  # 默认成功，因为可能没有商店
            
            if success1:
                # 这里可以添加获取商品的测试
                # 但需要先有商店数据
                pass
            
            total_tests = 2
            passed_tests = sum([success1, success2])
            
            print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
            
            if passed_tests == total_tests:
                print("🎉 WebSocket修复验证成功！")
                return True
            else:
                print("⚠️  部分测试失败")
                return False
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            return False
        
        finally:
            await self.disconnect()


async def main():
    """主函数"""
    print("🔧 WebSocket处理器修复验证")
    print("确保游戏服务器正在运行...")
    
    client = SimpleWebSocketClient()
    success = await client.run_tests()
    
    if success:
        print("\n✅ 验证完成：WebSocket处理器修复成功")
    else:
        print("\n❌ 验证失败：请检查服务器状态和修复")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试失败: {str(e)}")
