"""
游戏管理器
统一管理游戏中的玩家、消息推送、事件处理等，支持多Worker环境
"""

import os
import uuid
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable, Set
from models import MessageModel
from enums import MessageId, ItemType
from service_locator import ServiceLocator
from UserCacheManager import UserCacheManager
from ItemCacheManager import ItemCacheManager
from guild_cache_manager import GuildCacheManager
from game_events import (
    GameEvent, GameEventType, GameEventFactory, EventHandler,
    PlayerLoginEvent, PlayerLogoutEvent, NotificationLevel
)
from player_session_manager import PlayerSessionManager, PlayerSession
from game_notification_manager import GameNotificationManager
from distributed_lock import DistributedLock
from logger_config import setup_logger
from guild_service_distributed import GuildServiceDistributed

logger = setup_logger(__name__)


class GameManager:
    """游戏管理器单例 - 支持多Worker"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.worker_id = os.getpid()
        self.session_manager: Optional[PlayerSessionManager] = None
        self.notification_manager: Optional[GameNotificationManager] = None
        self.user_cache: Optional[UserCacheManager] = None
        self.item_cache: Optional[ItemCacheManager] = None
        self.guild_service: Optional[GuildServiceDistributed] = None
        self.connection_manager = None
        
        # 事件处理器
        self.event_handlers: Dict[GameEventType, List[EventHandler]] = {}
        
        # 配置
        self.config = {
            "max_online_players": 10000,
            "session_timeout": 3600,
            "push_batch_size": 100,
            "event_queue_size": 1000,
            "notification_retention": 86400,
        }
        
        # 初始化标志
        self.initialized = False
    
    @classmethod
    async def get_instance(cls) -> "GameManager":
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    await cls._instance.initialize()
        return cls._instance
    
    async def initialize(self):
        """初始化游戏管理器"""
        if self.initialized:
            return
        
        try:
            logger.info(f"开始初始化游戏管理器 (Worker: {self.worker_id})")

            # 初始化各个管理器
            logger.info(f"创建PlayerSessionManager...")
            self.session_manager = await PlayerSessionManager.get_instance()
            logger.info(f"创建GameNotificationManager...")
            self.notification_manager = GameNotificationManager()

            # 获取其他服务
            logger.info(f"获取UserCacheManager...")
            self.user_cache = await UserCacheManager.get_instance()
            logger.info(f"获取ItemCacheManager...")
            self.item_cache = await ItemCacheManager.get_instance()
            logger.info(f"获取GuildServiceDistributed...")
            self.guild_service = GuildServiceDistributed()
            logger.info(f"获取ConnectionManager...")
            self.connection_manager = ServiceLocator.get("conn_manager")            
            # 注册到服务定位器    
            ServiceLocator.register("notification_manager", self.notification_manager)
            ServiceLocator.register("game_manager", self)
            
            # 启动通知监听器 (暂时禁用，避免Redis连接问题)
            # asyncio.create_task(self.notification_manager.start_notification_listener())
            
            # 注册默认事件处理器
            await self._register_default_event_handlers()
            
            self.initialized = True
            logger.info(f"游戏管理器初始化完成 (Worker: {self.worker_id})")
            
        except Exception as e:
            logger.error(f"初始化游戏管理器失败: {str(e)}")
            raise

    # ==================== 玩家管理 ====================
    
    async def on_player_login(self, username: str, token: str, websocket) -> bool:
        """玩家登录处理"""
        try:
            logger.info(f"处理玩家登录: {username} (Worker: {self.worker_id})")
            
            # 创建会话
            session = await self.session_manager.create_session(username, token, websocket)
            
            # 更新用户缓存中的在线状态
            if self.user_cache:
                await self.user_cache.update_user_field(username, "last_login", datetime.now())
            
            # 推送登录欢迎信息
            await self._send_welcome_message(username)
            
            # 推送玩家基础数据
            await self._push_player_initial_data(username)
            
            # 推送用户货币信息
            await self._push_user_currency(username)
            
            # 处理邮件模板
            await self._process_login_mail_templates(username)

            # 检查未收取的附件
            await self._check_unclaimed_attachments(username)

            #推送玩家进入游戏
            await self._push_enter_game(username)

            # 触发登录事件
            login_event = GameEventFactory.create_player_login_event(
                username, token, self.worker_id
            )
            await self.trigger_event(GameEventType.PLAYER_LOGIN, login_event.model_dump())
            
            logger.info(f"玩家登录处理完成: {username}")
            return True
            
        except Exception as e:
            logger.error(f"处理玩家登录失败: {username}, 错误: {str(e)}")
            return False
    #用户货币信息
    async def _push_user_currency(self, username: str):
        """推送用户货币信息"""
        try:
            from currency_service import CurrencyService
            currency_service = CurrencyService()
            currency_info = await currency_service.get_all_currencies(username)
            if currency_info:
                message = MessageModel(
                    msgId=MessageId.USER_CURRENCY,
                    success=True,
                    data=currency_info
                ).model_dump()
                if self.connection_manager:
                    await self.connection_manager.send_personal_message_to_user(message, username)
                    logger.debug(f"推送用户货币信息成功: {username}")
        except Exception as e:
            logger.error(f"推送用户货币信息失败: {username}, 错误: {str(e)}")

    async def _push_enter_game(self, username: str):
        """推送玩家进入游戏"""
        try:
            message = MessageModel(
                msgId=MessageId.ENTER_GAME,
                success=True,
                data={}
            ).model_dump()
            if self.connection_manager:
                await self.connection_manager.send_personal_message_to_user(message, username)
                logger.debug(f"推送进入游戏成功: {username}")
        except Exception as e:
            logger.error(f"推送进入游戏失败: {username}, 错误: {str(e)}")

    async def _process_login_mail_templates(self, username: str):
        """处理用户登录时的邮件模板"""
        try:
            from mail_service_distributed import MailServiceDistributed
            mail_service = MailServiceDistributed()
            created_count = await mail_service.process_login_mail_templates(username)
            if created_count > 0:
                logger.info(f"为用户 {username} 从模板创建了 {created_count} 封邮件")
        except Exception as e:
            logger.error(f"处理用户登录邮件模板失败: {username}, 错误: {str(e)}")

    async def _check_unclaimed_attachments(self, username: str):
        """检查用户是否有未收取的附件"""
        try:
            from mail_service_distributed import MailServiceDistributed
            mail_service = MailServiceDistributed()
            # 获取用户的邮件列表
            mail_list_response = await mail_service.get_mail_list(username, 1, 50)  # 获取前50封邮件
            if mail_list_response.success and mail_list_response.data:
                unclaimed_count = 0
                unclaimed_mails = []

                for mail_item in mail_list_response.data.get("mails", []):
                    # 检查是否有未收取的附件
                    if (mail_item.get("has_attachments") and
                        mail_item.get("attachment_status") == "UNCLAIMED"):
                        unclaimed_count += 1
                        unclaimed_mails.append({
                            "mail_id": mail_item.get("mail_id"),
                            "title": mail_item.get("title"),
                            "sender_name": mail_item.get("sender_name")
                        })

                if unclaimed_count > 0:
                    logger.info(f"用户 {username} 有 {unclaimed_count} 封邮件包含未收取的附件")

                    # 推送未收取附件通知
                    await self._push_unclaimed_attachments_notification(username, unclaimed_count, unclaimed_mails)
                else:
                    logger.debug(f"用户 {username} 没有未收取的附件")

        except Exception as e:
            logger.error(f"检查用户未收取附件失败: {username}, 错误: {str(e)}")

    async def _push_unclaimed_attachments_notification(self, username: str, count: int, mails: list):
        """推送未收取附件通知"""
        try:
            connection_manager = ServiceLocator.get("conn_manager")
            if connection_manager:
                # 构建通知消息
                notification = {
                    "type": "unclaimed_attachments",
                    "count": count,
                    "message": f"您有 {count} 封邮件包含未收取的附件",
                    "mails": mails[:5]  # 最多显示5封邮件的信息
                }

                # 发送给特定用户
                await connection_manager.send_to_user(username, {
                    "msgId": 999,  # 使用特定的消息ID表示附件通知
                    "success": True,
                    "data": notification
                })

                logger.info(f"已向用户 {username} 推送未收取附件通知")

        except Exception as e:
            logger.error(f"推送未收取附件通知失败: {username}, 错误: {str(e)}")

    async def on_player_logout(self, username: str, reason: str = "normal") -> bool:
        """玩家登出处理"""
        try:
            logger.info(f"处理玩家登出: {username}, 原因: {reason} (Worker: {self.worker_id})")
            
            # 移除会话
            await self.session_manager.remove_session(username)
            
            # 更新用户缓存中的离线状态
            if self.user_cache:
                await self.user_cache.update_user_field(username, "last_logout", datetime.now())
            
            # 触发登出事件
            logout_event = GameEventFactory.create_player_logout_event(
                username, self.worker_id, reason
            )
            await self.trigger_event(GameEventType.PLAYER_LOGOUT, logout_event.model_dump())
            
            logger.info(f"玩家登出处理完成: {username}")
            return True
            
        except Exception as e:
            logger.error(f"处理玩家登出失败: {username}, 错误: {str(e)}")
            return False

    # ==================== 事件处理 ====================
    
    async def register_event_handler(self, event_type: GameEventType, handler: EventHandler):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"注册事件处理器: {event_type}")

    async def unregister_event_handler(self, event_type: GameEventType, handler: EventHandler):
        """注销事件处理器"""
        if event_type in self.event_handlers:
            if handler in self.event_handlers[event_type]:
                self.event_handlers[event_type].remove(handler)
                logger.info(f"注销事件处理器: {event_type}")

    async def trigger_event(self, event_type: GameEventType, data: Dict[str, Any]) -> bool:
        """触发事件"""
        try:
            # 创建事件对象
            event = GameEvent(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                timestamp=datetime.now(),
                source_worker=self.worker_id,
                data=data
            )
            
            # 执行事件处理器
            if event_type in self.event_handlers:
                for handler in self.event_handlers[event_type]:
                    try:
                        await handler.handle(event)
                    except Exception as e:
                        logger.error(f"事件处理器执行失败: {event_type}, 错误: {str(e)}")
            
            logger.debug(f"事件触发完成: {event_type}")
            return True
            
        except Exception as e:
            logger.error(f"触发事件失败: {event_type}, 错误: {str(e)}")
            return False

    # ==================== 私有方法 ====================
    
    async def _send_welcome_message(self, username: str):
        """发送欢迎消息"""
        try:
            welcome_message = MessageModel(
                msgId=MessageId.SYSTEM_NOTIFICATION,
                success=True,
                data={
                    "message": f"欢迎回来，{username}！",
                    "level": "info",
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()

            # 直接使用ConnectionManager发送
            if self.connection_manager:
                await self.connection_manager.send_personal_message_to_user(welcome_message, username)

        except Exception as e:
            logger.error(f"发送欢迎消息失败: {username}, 错误: {str(e)}")

    async def _push_player_initial_data(self, username: str):
        """推送玩家初始数据"""
        try:
            # 推送角色信息
            await self._push_role_info(username)
            
            # 推送公会信息
            await self._push_guild_info(username)
            
            # 推送道具信息
            await self._push_items_info(username)
            
            # 推送装备信息
            await self._push_equipment_info(username)

            # 推送符文信息
            await self._push_rune_info(username)

        except Exception as e:
            logger.error(f"推送玩家初始数据失败: {username}, 错误: {str(e)}")
    async def _push_role_info(self, username: str):
        """推送角色信息"""
        try:
            if self.user_cache:
                user = await self.user_cache.get_user_by_username(username)
                if user:
                    # 构建角色信息消息
                    message = MessageModel(
                        msgId=MessageId.ROLE_INFO,
                        success=True,
                        data=user.serialize(exclude_fields=["password"])
                    ).model_dump()  

                    # 直接推送
                    if self.connection_manager:
                        await self.connection_manager.send_personal_message_to_user(message, username)
            else:
                logger.error("用户缓存不可用，无法推送角色信息")
        except Exception as e:
            logger.error(f"推送角色信息失败: {username}, 错误: {str(e)}")

    async def _push_guild_info(self, username: str):
        """推送公会信息"""
        try:
            if self.guild_service:
                # 获取玩家公会ID
                guild_id = await self.guild_service.get_player_guild_id(username)
                if guild_id:
                    # 获取公会信息
                    guild_response = await self.guild_service.get_guild_info(guild_id)
                    if guild_response.success:
                        guild_info = guild_response.data.get("guild")

                        # 构建公会信息消息
                        message = MessageModel(
                            msgId=MessageId.GUILD_NOTIFICATION,
                            success=True,
                            data={
                                "type": "guild_info_update",
                                "guild_info": guild_info
                            }
                        ).model_dump()

                        # 直接推送
                        if self.connection_manager:
                            await self.connection_manager.send_personal_message_to_user(message, username)
                            logger.debug(f"推送公会信息成功: {username}")
                    else:
                        logger.warning(f"获取公会信息失败: {username}")
                else:
                    logger.debug(f"玩家未加入公会，无需推送公会信息: {username}")
            else:
                logger.error("公会服务不可用，无法推送公会信息")
        except Exception as e:
            logger.error(f"推送公会信息失败: {username}, 错误: {str(e)}")

    async def _push_items_info(self, username: str):
        """推送道具信息"""
        try:
            # 使用已初始化的道具缓存管理器
            if self.item_cache:
                # 获取道具列表
                items = await self.item_cache.get_user_items_by_type(username, "item", 0, 100)

                # 构建道具信息消息
                message = MessageModel(
                    msgId=MessageId.GET_ITEMS,
                    success=True,
                    data=items
                ).model_dump()

                # 直接推送
                if self.connection_manager:
                    await self.connection_manager.send_personal_message_to_user(message, username)
                    logger.debug(f"推送道具信息成功: {username}, 数量: {len(items) if items else 0}")

        except Exception as e:
            logger.error(f"推送道具信息失败: {username}, 错误: {str(e)}")

    async def _push_equipment_info(self, username: str):
        """推送装备信息"""
        try:
            # 使用已初始化的道具缓存管理器
            if self.item_cache:
                # 获取装备列表
                equipments = await self.item_cache.get_user_items_by_type(username, ItemType.EQUIPMENT, 0, 100)

                # 构建装备信息消息
                message = MessageModel(
                    msgId=MessageId.GET_EQUIPMENT,
                    success=True,
                    data=equipments
                ).model_dump()

                # 直接推送
                if self.connection_manager:
                    await self.connection_manager.send_personal_message_to_user(message, username)
                    logger.debug(f"推送装备信息成功: {username}, 数量: {len(equipments) if equipments else 0}")

        except Exception as e:
            logger.error(f"推送装备信息失败: {username}, 错误: {str(e)}")

    async def _push_rune_info(self, username: str):
        """推送符文信息"""
        try:
            # 使用已初始化的道具缓存管理器
            if self.item_cache:
                # 获取符文列表
                runes = await self.item_cache.get_user_items_by_type(username, ItemType.RUNE, 0, 100)

                # 构建符文信息消息
                message = MessageModel(
                    msgId=MessageId.GET_RUNE,
                    success=True,
                    data=runes
                ).model_dump()

                # 直接推送
                if self.connection_manager:
                    await self.connection_manager.send_personal_message_to_user(message, username)
                    logger.debug(f"推送符文信息成功: {username}, 数量: {len(runes) if runes else 0}")

        except Exception as e:
            logger.error(f"推送符文信息失败: {username}, 错误: {str(e)}")

    async def _register_default_event_handlers(self):
        """注册默认事件处理器"""
        try:
            # 这里可以注册默认的事件处理器
            # 例如：登录事件、登出事件等的默认处理逻辑
            pass
        except Exception as e:
            logger.error(f"注册默认事件处理器失败: {str(e)}")

    # ==================== 维护方法 ====================
    
    async def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        return await self.session_manager.cleanup_expired_sessions()

    async def cleanup_worker_sessions(self, worker_id: int = None) -> int:
        """清理指定Worker的所有会话"""
        return await self.session_manager.cleanup_worker_sessions(worker_id)

    async def get_worker_stats(self) -> Dict[str, Any]:
        """获取Worker统计信息"""
        try:
            online_count = await self.get_online_count()
            worker_sessions = await self.session_manager.get_worker_sessions(self.worker_id)
            
            return {
                "worker_id": self.worker_id,
                "total_online": online_count,
                "local_sessions": len(worker_sessions),
                "initialized": self.initialized,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取Worker统计信息失败: {str(e)}")
            return {}

    async def shutdown(self):
        """关闭游戏管理器"""
        try:
            logger.info(f"关闭游戏管理器 (Worker: {self.worker_id})")
            
            # 清理本Worker的所有会话
            await self.cleanup_worker_sessions(self.worker_id)
            
            # 触发关闭事件
            await self.trigger_event(GameEventType.SYSTEM_MAINTENANCE, {
                "action": "worker_shutdown",
                "worker_id": self.worker_id
            })
            
            logger.info(f"游戏管理器关闭完成 (Worker: {self.worker_id})")
            
        except Exception as e:
            logger.error(f"关闭游戏管理器失败: {str(e)}")
