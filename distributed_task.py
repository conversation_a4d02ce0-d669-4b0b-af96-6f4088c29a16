import functools
import asyncio
import logging
import os
from typing import Callable, Any
from distributed_lock import DistributedLock

logger = logging.getLogger(__name__)

# DistributedLock 现在自动管理Redis连接，不需要传入 redis_client

def distributed_task(redis_client=None, lock_key: str = None, ttl: int = 30):
    """
    分布式定时任务装饰器。
    :param redis_client: Redis 客户端（已废弃，保留兼容性）
    :param lock_key: 锁的唯一key（默认用函数名）
    :param ttl: 锁超时时间（秒）
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            key = lock_key or f"lock:scheduled:{func.__name__}"
            worker_id = os.getpid()

            # 新版本的DistributedLock自动管理Redis连接，不需要传递redis_client
            lock = DistributedLock(key, ttl)

            try:
                logger.debug(f"Worker {worker_id} 尝试获取锁: {key}")
                async with lock:
                    logger.info(f"Worker {worker_id} 成功获取锁并执行任务: {func.__name__}")
                    result = await func(*args, **kwargs)
                    logger.info(f"Worker {worker_id} 任务执行完成: {func.__name__}")
                    return result

            except TimeoutError as e:
                # 未获取到锁，直接跳过
                logger.debug(f"Worker {worker_id} 未能获取锁，跳过任务: {func.__name__} - {str(e)}")
                return None

            except Exception as e:
                # 其他异常也要处理
                logger.error(f"Worker {worker_id} 执行任务时发生错误: {func.__name__} - {str(e)}")
                return None

        return wrapper
    return decorator