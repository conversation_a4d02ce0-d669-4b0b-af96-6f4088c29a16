2025-08-05 18:06:43,935 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:44,720 - models - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:44,750 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:45,218 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:45,228 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:45,239 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:45,252 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:45,263 - auth - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,027 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,068 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,090 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,101 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,113 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,113 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 70c865f1)
2025-08-05 18:06:48,122 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,161 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,175 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,182 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,190 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,271 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,283 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,291 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,299 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,308 - game_server - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:48,309 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:06:48,309 - msgManager - INFO - Monster handlers registered
2025-08-05 18:06:48,313 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:06:48,316 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:06:48,330 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:06:48,331 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:06:48,331 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52199479)
2025-08-05 18:06:48,385 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:06:48,436 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:06:48,439 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:06:48,443 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:06:48,444 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 19948)
2025-08-05 18:06:48,445 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:06:48,608 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:06:48,628 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:06:49,156 - ConnectionManager - INFO - 后台任务已启动 (Worker 19948)
2025-08-05 18:06:49,157 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 19948)
2025-08-05 18:06:49,158 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:06:49,158 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:06:49,158 - game_server - INFO - 游戏配置加载完成 (Worker: 19948)
2025-08-05 18:06:49,159 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:06:52,820 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:06:52,821 - ConnectionManager - INFO - Redis连接池状态 (Worker 19948): 使用中=2, 可用=0, 总计=2
2025-08-05 18:06:52,821 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 19948): 2/2 (100.0%)
2025-08-05 18:06:52,824 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 19948): 连接中
2025-08-05 18:06:52,865 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 52199479)
2025-08-05 18:06:52,866 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52199479)
2025-08-05 18:06:52,872 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:52,873 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:06:52,875 - ConnectionManager - INFO - Worker 19948 开始消费广播消息，消费者标签: ctag1.b49bf74b885e49e185fe69b17657e2b0
2025-08-05 18:06:52,924 - ConnectionManager - INFO - Worker 19948 开始消费个人消息，消费者标签: ctag1.2f9cb1cb929742da8165cc2ca35e2d26
2025-08-05 18:06:52,996 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 19948)
2025-08-05 18:06:53,094 - distributed_lock - INFO - Worker 19948 成功获取锁: scheduler_initialization
2025-08-05 18:06:53,094 - game_server_scheduler_integration - INFO - Worker 19948 获得调度器初始化权限
2025-08-05 18:06:53,098 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:06:53,099 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:06:53,100 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:06:53,100 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:06:53,101 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:06:53,101 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:06:53,101 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:06:53,102 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:06:53,102 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:06:53,103 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:06:53,103 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:06:53,104 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:06:53,106 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:06:53,107 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:06:53,107 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:06:53,108 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:06:53,195 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:06:53,195 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:06:53,196 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:06:53,196 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:06:53,504 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:06:53,587 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:06:53,588 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:06:53,588 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:06:53,589 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:06:53,590 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:06:53,590 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:06:53,677 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:06:53,808 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:06:53,809 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:06:53,809 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:06:53,810 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:06:53,810 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:06:53,811 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:06:53,814 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:53,814 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:06:53,815 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:06:53,816 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:53,816 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:06:53,817 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:06:53,950 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:53,951 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:06:53,951 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:06:54,086 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:54,086 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:06:54,086 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:06:54,087 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:54,087 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:06:54,088 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:06:54,088 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:06:54,088 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:06:54,089 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:06:54,089 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:06:54,090 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,091 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,091 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,091 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,092 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,092 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:06:54,093 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:06:54,093 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:06:54,094 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:06:54,095 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:06:54,095 - game_server_scheduler_integration - INFO - Worker 19948 调度器初始化成功
2025-08-05 18:06:54,140 - game_server - INFO - 统一调度器初始化成功 (Worker: 19948)
2025-08-05 18:06:54,146 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 19948)
2025-08-05 18:06:54,147 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:06:54,148 - game_server - INFO - 日志清理管理器已启动 (Worker: 19948)
2025-08-05 18:06:54,149 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:06:54,149 - game_server - INFO - Monster cooldown manager initialized (Worker: 19948)
2025-08-05 18:06:54,292 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:06:54,292 - game_server - INFO - 公会系统初始化成功 (Worker: 19948)
2025-08-05 18:06:54,292 - game_server - INFO - 邮件系统初始化成功 (Worker: 19948)
2025-08-05 18:06:54,294 - game_server - INFO - 商店系统初始化成功 (Worker: 19948)
2025-08-05 18:06:54,294 - game_server - INFO - 初始化完成 (Worker: 19948)
2025-08-05 18:07:00,880 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:07:04,096 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:14 CST)" (scheduled at 2025-08-05 18:07:04.085997+08:00)
2025-08-05 18:07:04,138 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:04,138 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:04,260 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:04,596 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:04,596 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:07:04,597 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:04,597 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:04,641 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:14 CST)" executed successfully
2025-08-05 18:07:14,098 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:24 CST)" (scheduled at 2025-08-05 18:07:14.085997+08:00)
2025-08-05 18:07:14,140 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:14,140 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:14,261 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:14,592 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:14,593 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 18:07:14,593 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:14,594 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:14,635 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:24 CST)" executed successfully
2025-08-05 18:07:19,168 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:07:23,965 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:07:53 CST)" (scheduled at 2025-08-05 18:07:23.950819+08:00)
2025-08-05 18:07:24,010 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:07:24,011 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:24,091 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:34 CST)" (scheduled at 2025-08-05 18:07:24.085997+08:00)
2025-08-05 18:07:24,135 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:24,135 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:24,136 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行怪物冷却持久化任务
2025-08-05 18:07:24,268 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:24,468 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:07:24,468 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 怪物冷却持久化完成
2025-08-05 18:07:24,469 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 18:07:24,469 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:07:24,470 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:24,514 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:07:53 CST)" executed successfully
2025-08-05 18:07:24,613 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:24,614 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:07:24,614 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:24,615 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:24,660 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:34 CST)" executed successfully
2025-08-05 18:07:30,086 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:07:34,096 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:44 CST)" (scheduled at 2025-08-05 18:07:34.085997+08:00)
2025-08-05 18:07:34,142 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:34,143 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:34,272 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:34,623 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:34,623 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:07:34,624 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:34,624 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:34,669 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:44 CST)" executed successfully
2025-08-05 18:07:44,089 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:54 CST)" (scheduled at 2025-08-05 18:07:44.085997+08:00)
2025-08-05 18:07:44,133 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:44,143 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:44,276 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:44,623 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:44,623 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:07:44,624 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:44,624 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:44,672 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:07:54 CST)" executed successfully
2025-08-05 18:07:49,171 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:07:52,828 - ConnectionManager - INFO - Redis连接池状态 (Worker 19948): 使用中=2, 可用=2, 总计=4
2025-08-05 18:07:52,829 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 19948): 连接中
2025-08-05 18:07:53,821 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:08:53 CST)" (scheduled at 2025-08-05 18:07:53.816515+08:00)
2025-08-05 18:07:53,866 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:push_online
2025-08-05 18:07:53,866 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:53,867 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行推送在线人数任务
2025-08-05 18:07:53,868 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 18:07:53,957 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:08:23 CST)" (scheduled at 2025-08-05 18:07:53.950819+08:00)
2025-08-05 18:07:54,001 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:07:54,002 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:54,088 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:04 CST)" (scheduled at 2025-08-05 18:07:54.085997+08:00)
2025-08-05 18:07:54,132 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:07:54,132 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:07:54,134 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行怪物冷却持久化任务
2025-08-05 18:07:54,256 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:07:54,478 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:07:54,478 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 怪物冷却持久化完成
2025-08-05 18:07:54,479 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:07:54,480 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:07:54,480 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:54,526 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:08:23 CST)" executed successfully
2025-08-05 18:07:54,586 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:07:54,586 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 18:07:54,586 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:07:54,587 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:54,630 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:04 CST)" executed successfully
2025-08-05 18:07:55,098 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:07:55,098 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:07:55,105 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 在线人数推送完成，当前在线: 0
2025-08-05 18:07:55,105 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.24秒
2025-08-05 18:07:55,105 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:07:55,106 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:07:55,150 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:08:53 CST)" executed successfully
2025-08-05 18:08:00,296 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:08:04,096 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:14 CST)" (scheduled at 2025-08-05 18:08:04.085997+08:00)
2025-08-05 18:08:04,140 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:04,141 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:04,273 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:04,623 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:04,624 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:08:04,625 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:04,625 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:04,670 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:14 CST)" executed successfully
2025-08-05 18:08:14,095 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:24 CST)" (scheduled at 2025-08-05 18:08:14.085997+08:00)
2025-08-05 18:08:14,140 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:14,140 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:14,276 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:14,630 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:14,631 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:08:14,631 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:14,632 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:14,679 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:24 CST)" executed successfully
2025-08-05 18:08:19,180 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:08:23,965 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:08:53 CST)" (scheduled at 2025-08-05 18:08:23.950819+08:00)
2025-08-05 18:08:24,011 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:08:24,014 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:24,088 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:34 CST)" (scheduled at 2025-08-05 18:08:24.085997+08:00)
2025-08-05 18:08:24,131 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:24,131 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:24,154 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行怪物冷却持久化任务
2025-08-05 18:08:24,253 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:24,514 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:08:24,514 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 怪物冷却持久化完成
2025-08-05 18:08:24,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:08:24,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:08:24,516 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:24,562 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:08:53 CST)" executed successfully
2025-08-05 18:08:24,591 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:24,593 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:08:24,595 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:24,601 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:24,647 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:34 CST)" executed successfully
2025-08-05 18:08:30,452 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:08:34,086 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:44 CST)" (scheduled at 2025-08-05 18:08:34.085997+08:00)
2025-08-05 18:08:34,131 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:34,132 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:34,253 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:34,588 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:34,589 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:08:34,589 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:34,590 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:34,635 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:44 CST)" executed successfully
2025-08-05 18:08:44,092 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:54 CST)" (scheduled at 2025-08-05 18:08:44.085997+08:00)
2025-08-05 18:08:44,134 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:44,134 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:44,255 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:44,591 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:44,592 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:08:44,592 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:44,593 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:44,634 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:08:54 CST)" executed successfully
2025-08-05 18:08:49,186 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:08:52,838 - ConnectionManager - INFO - Redis连接池状态 (Worker 19948): 使用中=2, 可用=2, 总计=4
2025-08-05 18:08:52,838 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 19948): 连接中
2025-08-05 18:08:53,822 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:09:53 CST)" (scheduled at 2025-08-05 18:08:53.816515+08:00)
2025-08-05 18:08:53,865 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:push_online
2025-08-05 18:08:53,865 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:53,865 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行推送在线人数任务
2025-08-05 18:08:53,959 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:09:23 CST)" (scheduled at 2025-08-05 18:08:53.950819+08:00)
2025-08-05 18:08:54,000 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:08:54,001 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:54,064 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:08:54,064 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:08:54,100 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:04 CST)" (scheduled at 2025-08-05 18:08:54.085997+08:00)
2025-08-05 18:08:54,125 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 开始执行怪物冷却持久化任务
2025-08-05 18:08:54,145 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:08:54,145 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:08:54,275 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:08:54,463 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:08:54,463 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 怪物冷却持久化完成
2025-08-05 18:08:54,463 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:08:54,464 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:08:54,464 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:54,507 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:09:23 CST)" executed successfully
2025-08-05 18:08:54,626 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:08:54,629 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:08:54,634 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:08:54,644 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:54,693 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:04 CST)" executed successfully
2025-08-05 18:08:55,545 - scheduler_tasks_unified - INFO - [定时任务] Worker 19948 在线人数推送完成，当前在线: 0
2025-08-05 18:08:55,547 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.68秒
2025-08-05 18:08:55,547 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:08:55,548 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:08:55,595 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:09:53 CST)" executed successfully
2025-08-05 18:09:00,696 - ConnectionManager - INFO - 连接状态 (Worker 19948): 活跃连接数=0, 用户数=0
2025-08-05 18:09:04,097 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:14 CST)" (scheduled at 2025-08-05 18:09:04.085997+08:00)
2025-08-05 18:09:04,141 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:09:04,141 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:09:04,272 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:09:04,627 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:09:04,627 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:09:04,628 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:09:04,628 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:09:04,679 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:14 CST)" executed successfully
2025-08-05 18:09:14,100 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:24 CST)" (scheduled at 2025-08-05 18:09:14.085997+08:00)
2025-08-05 18:09:14,147 - distributed_lock - INFO - Worker 19948 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:09:14,147 - distributed_task - INFO - Worker 19948 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:09:14,289 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:09:14,641 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:09:14,649 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:09:14,650 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:09:14,653 - distributed_task - INFO - Worker 19948 任务执行完成: direct_wrapper
2025-08-05 18:09:14,700 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:24 CST)" executed successfully
2025-08-05 18:09:17,805 - game_server - INFO - 关闭服务器... (Worker: 19948)
2025-08-05 18:09:17,807 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 18:09:18,640 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:09:18,641 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 18:09:18,644 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 18:09:18,647 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 18:09:18,648 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 18:09:18,649 - game_server - INFO - 日志清理管理器已停止
2025-08-05 18:09:18,656 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 18:09:18,656 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 18:09:18,657 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 18:09:18,657 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 18:09:18,657 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 18:09:18,658 - game_server - INFO - 统一调度器已关闭
2025-08-05 18:09:18,658 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 18:09:18,660 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 19948)
2025-08-05 18:09:18,851 - game_server - INFO - 服务器资源已清理 (Worker: 19948)
