# -*- coding: utf-8 -*-
"""
公会系统数据模型
定义公会相关的所有数据结构和模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field
import time
from UserCacheManager import SimpleUserInfo

class GuildStatus(str, Enum):
    """公会状态枚举"""
    ACTIVE = "active"
    DISBANDED = "disbanded"
    SUSPENDED = "suspended"


class GuildPosition(str, Enum):
    """公会职位枚举"""
    LEADER = "leader"           # 会长
    VICE_LEADER = "vice_leader" # 副会长
    ELDER = "elder"             # 长老
    MEMBER = "member"           # 成员


class ApplicationStatus(str, Enum):
    """申请状态枚举"""
    PENDING = "pending"         # 待处理
    APPROVED = "approved"       # 已通过
    REJECTED = "rejected"       # 已拒绝
    EXPIRED = "expired"         # 已过期


class Guild(BaseModel):
    """公会基本信息模型"""
    guild_id: str = Field(..., description="公会唯一ID")
    name: str = Field(..., min_length=2, max_length=20, description="公会名称")
    description: str = Field(default="", max_length=200, description="公会简介")
    logo: str = Field(default="default", description="公会徽章ID")
    level: int = Field(default=1, ge=1, le=10, description="公会等级")
    exp: int = Field(default=0, ge=0, description="公会经验")
    max_members: int = Field(default=30, ge=10, le=100, description="最大成员数")
    member_count: int = Field(default=1, ge=0, description="当前成员数")
    leader_id: str = Field(..., description="会长玩家ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    status: GuildStatus = Field(default=GuildStatus.ACTIVE, description="公会状态")
    
    # 加入条件
    join_condition: Dict[str, Any] = Field(default_factory=lambda: {
        "level_required": 1,        # 要求玩家等级
        "power_required": 0,        # 要求战力
        "approval_required": True   # 是否需要审批
    }, description="加入条件")
    
    # 统计信息
    stats: Dict[str, int] = Field(default_factory=lambda: {
        "total_contribution": 0,    # 总贡献度
        "wins": 0,                  # 胜利次数
        "losses": 0                 # 失败次数
    }, description="公会统计")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "guild_id": self.guild_id,
            "name": self.name,
            "description": self.description,
            "logo": self.logo,
            "level": self.level,
            "exp": self.exp,
            "max_members": self.max_members,
            "member_count": self.member_count,
            "leader_id": self.leader_id,
            "created_at": self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at,
            "updated_at": self.updated_at.isoformat() if isinstance(self.updated_at, datetime) else self.updated_at,
            "status": self.status,
            "join_condition": self.join_condition,
            "stats": self.stats
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Guild":
        """从字典创建Guild对象"""
        # 处理时间字段
        if isinstance(data.get("created_at"), str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        if isinstance(data.get("updated_at"), str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"])
        
        return cls(**data)


class GuildMember(BaseModel):
    """公会成员模型"""
    guild_id: str = Field(..., description="公会ID")
    playerInfo:SimpleUserInfo = Field(None, description="简化的用户信息")
    position: GuildPosition = Field(default=GuildPosition.MEMBER, description="职位")
    contribution: int = Field(default=0, ge=0, description="贡献度")
    weekly_contribution: int = Field(default=0, ge=0, description="本周贡献")
    total_contribution: int = Field(default=0, ge=0, description="总贡献")
    joined_at: datetime = Field(default_factory=datetime.now, description="加入时间")
    last_active: datetime = Field(default_factory=datetime.now, description="最后活跃时间")

    @property
    def player_id(self) -> str:
        """获取玩家ID的便捷属性"""
        return self.playerInfo.id if self.playerInfo else ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "guild_id": self.guild_id,
            "player_id": self.player_id,  # 添加player_id字段
            "playerInfo": self.playerInfo.serialize() if self.playerInfo else None,
            "position": self.position,
            "contribution": self.contribution,
            "weekly_contribution": self.weekly_contribution,
            "total_contribution": self.total_contribution,
            "joined_at": self.joined_at.isoformat() if isinstance(self.joined_at, datetime) else self.joined_at,
            "last_active": self.last_active.isoformat() if isinstance(self.last_active, datetime) else self.last_active,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GuildMember":
        """从字典创建GuildMember对象"""
        # 处理时间字段
        if isinstance(data.get("joined_at"), str):
            data["joined_at"] = datetime.fromisoformat(data["joined_at"])
        if isinstance(data.get("last_active"), str):
            data["last_active"] = datetime.fromisoformat(data["last_active"])

        # 处理playerInfo字段兼容性
        # 如果数据库中存储的是player_id而不是playerInfo，需要创建SimpleUserInfo对象
        if "player_id" in data and not data.get("playerInfo"):
            # 创建一个基本的SimpleUserInfo对象
            data["playerInfo"] = SimpleUserInfo(
                id=data["player_id"],
                nickname=data.get("player_name", ""),  # 如果有player_name字段
                level=1,  # 默认等级
                fight_power=0  # 默认战力
            )

        # 移除可能存在的player_id字段，避免冲突
        data.pop("player_id", None)
        data.pop("player_name", None)  # 也移除可能的player_name字段

        return cls(**data)


class GuildApplication(BaseModel):
    """公会申请模型"""
    application_id: str = Field(..., description="申请ID")
    guild_id: str = Field(..., description="公会ID")
    player_id: str = Field(..., description="申请者ID")
    player_name: str = Field(..., description="申请者名称")
    message: str = Field(default="", max_length=100, description="申请留言")
    status: ApplicationStatus = Field(default=ApplicationStatus.PENDING, description="申请状态")
    applied_at: datetime = Field(default_factory=datetime.now, description="申请时间")
    processed_at: Optional[datetime] = Field(default=None, description="处理时间")
    processed_by: Optional[str] = Field(default=None, description="处理人ID")
    
    # 申请者基本信息
    player_level: int = Field(default=1, description="申请者等级")
    player_power: int = Field(default=0, description="申请者战力")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "application_id": self.application_id,
            "guild_id": self.guild_id,
            "player_id": self.player_id,
            "player_name": self.player_name,
            "message": self.message,
            "status": self.status,
            "applied_at": self.applied_at.isoformat() if isinstance(self.applied_at, datetime) else self.applied_at,
            "processed_at": self.processed_at.isoformat() if isinstance(self.processed_at, datetime) and self.processed_at else self.processed_at,
            "processed_by": self.processed_by,
            "player_level": self.player_level,
            "player_power": self.player_power
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GuildApplication":
        """从字典创建GuildApplication对象"""
        # 处理时间字段
        if isinstance(data.get("applied_at"), str):
            data["applied_at"] = datetime.fromisoformat(data["applied_at"])
        if data.get("processed_at") and isinstance(data["processed_at"], str):
            data["processed_at"] = datetime.fromisoformat(data["processed_at"])
        
        return cls(**data)


# 请求和响应模型
class CreateGuildRequest(BaseModel):
    """创建公会请求"""
    name: str = Field(..., min_length=2, max_length=20, description="公会名称")
    description: str = Field(default="", max_length=200, description="公会简介")
    logo: str = Field(default="default", description="公会徽章ID")


class UpdateGuildRequest(BaseModel):
    """更新公会信息请求"""
    name: Optional[str] = Field(None, min_length=2, max_length=20, description="公会名称")
    description: Optional[str] = Field(None, max_length=200, description="公会简介")
    logo: Optional[str] = Field(None, description="公会徽章ID")
    join_condition: Optional[Dict[str, Any]] = Field(None, description="加入条件")


class ApplyGuildRequest(BaseModel):
    """申请加入公会请求"""
    guild_id: str = Field(..., description="公会ID")
    message: str = Field(default="", max_length=100, description="申请留言")


class ProcessApplicationRequest(BaseModel):
    """处理申请请求"""
    application_id: str = Field(..., description="申请ID")
    action: str = Field(..., description="操作：approve/reject")
    reason: str = Field(default="", max_length=100, description="处理原因")


class ChangeMemberPositionRequest(BaseModel):
    """变更成员职位请求"""
    player_id: str = Field(..., description="成员玩家ID")
    position: GuildPosition = Field(..., description="新职位")


class GuildResponse(BaseModel):
    """公会操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(default="", description="响应消息")
    data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")
    error: Optional[str] = Field(default=None, description="错误信息")


class GuildListResponse(BaseModel):
    """公会列表响应"""
    success: bool = Field(..., description="操作是否成功")
    guilds: List[Dict[str, Any]] = Field(default_factory=list, description="公会列表")
    total: int = Field(default=0, description="总数量")
    page: int = Field(default=1, description="当前页")
    page_size: int = Field(default=20, description="每页大小")
