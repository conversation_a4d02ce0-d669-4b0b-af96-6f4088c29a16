from typing import Dict, Callable, Any, Optional
import logging
import json
import asyncio
import traceback
from fastapi import WebSocket
from datetime import datetime
from enums import MessageId
import websocket_handlers
import inspect
from logger_config import setup_logger
from models import MessageModel
from equipment_handlers_distributed import register_equipment_handlers_distributed
from general_handlers_distributed import GeneralHandlersDistributed
# Import monster handlers
from xxsg.monster_handlers import (
    handle_check_cooldown, 
    handle_monster_killed, 
    handle_get_all_cooldowns, 
    handle_reset_cooldown,
    handle_get_monsters
)

# 初始化日志系统
logger = setup_logger(__name__)


def register_general_handlers_distributed(message_manager):
    """注册武将相关的分布式消息处理器"""
    general_handlers = GeneralHandlersDistributed()
    
    # 注册所有武将相关的消息处理器
    message_manager.register_handler(MessageId.GET_GENERALS, general_handlers.handle_get_generals)
    message_manager.register_handler(MessageId.DRAW_GENERAL, general_handlers.handle_draw_general)
    message_manager.register_handler(MessageId.SYNTHESIS_GENERAL, general_handlers.handle_synthesis_general)
    message_manager.register_handler(MessageId.LEVEL_UP_GENERAL, general_handlers.handle_level_up_general)
    message_manager.register_handler(MessageId.STAR_UP_GENERAL, general_handlers.handle_star_up_general)
    message_manager.register_handler(MessageId.AWAKEN_GENERAL, general_handlers.handle_awaken_general)
    message_manager.register_handler(MessageId.TRAIN_GENERAL, general_handlers.handle_train_general)
    message_manager.register_handler(MessageId.DEPLOY_GENERAL, general_handlers.handle_deploy_general)
    message_manager.register_handler(MessageId.RETREAT_GENERAL, general_handlers.handle_retreat_general)
    message_manager.register_handler(MessageId.GET_FORMATION, general_handlers.handle_get_formation)
    message_manager.register_handler(MessageId.SAVE_FORMATION, general_handlers.handle_save_formation)
    message_manager.register_handler(MessageId.EQUIP_GENERAL, general_handlers.handle_equip_general)
    message_manager.register_handler(MessageId.UNEQUIP_GENERAL, general_handlers.handle_unequip_general)
    message_manager.register_handler(MessageId.CHANGE_GENERAL_STATE, general_handlers.handle_change_general_state)
    message_manager.register_handler(MessageId.LOCK_GENERAL, general_handlers.handle_lock_general)
    message_manager.register_handler(MessageId.UNLOCK_GENERAL, general_handlers.handle_unlock_general)
    
    logger.info("武将相关消息处理器注册完成")


class MessageManager:
    def __init__(self):
        self.handlers = {}
        self._register_default_handlers()
        self._register_monster_handlers()
        
        # 注册武将相关处理器
        register_general_handlers_distributed(self)
        
    def _register_default_handlers(self):
        """注册默认的消息处理器"""
        # 系统消息
        self.register_handler(MessageId.HEARTBEAT, websocket_handlers.heartbeat_handler)
        
        # 聊天消息
        self.register_handler(MessageId.CHAT, websocket_handlers.chat_handler)
        self.register_handler(MessageId.PRIVATE_CHAT, websocket_handlers.private_chat_handler)
        self.register_handler(MessageId.GROUP_CHAT, websocket_handlers.group_chat_handler)
        self.register_handler(MessageId.BROADCAST_MESSAGE, websocket_handlers.broadcast_message_handler)
        
        # 道具相关
        self.register_handler(MessageId.GET_ITEMS, websocket_handlers.get_items_handler)
        self.register_handler(MessageId.DELETE_ITEM, websocket_handlers.delete_item_handler)
        self.register_handler(MessageId.SET_NICKNAME, websocket_handlers.set_nickname_handler)
        self.register_handler(MessageId.ROLE_INFO, websocket_handlers.role_info_handler)
        self.register_handler(MessageId.ENTER_GAME, websocket_handlers.enter_game_handler)
        self.register_handler(MessageId.GM, websocket_handlers.gm_handler)
        
        # 装备相关处理器
        register_equipment_handlers_distributed(self)

    def _register_monster_handlers(self):
        """Register monster-related message handlers"""
        self.register_handler(MessageId.CHECK_COOLDOWN, handle_check_cooldown)
        self.register_handler(MessageId.MONSTER_KILLED, handle_monster_killed)  # 怪物击杀 需要判断是否已经刷新
        self.register_handler(MessageId.GET_ALL_COOLDOWNS, handle_get_all_cooldowns)
        self.register_handler(MessageId.RESET_COOLDOWN, handle_reset_cooldown)
        self.register_handler(MessageId.GET_MONSTERS, handle_get_monsters)
        logger.info("Monster handlers registered")

    def register_handler(self, msg_id: int, handler: Callable):
        """注册消息处理器"""
        if msg_id in self.handlers:
            logger.warning(f"覆盖已存在的消息处理器: {msg_id}")
        self.handlers[msg_id] = handler
        logger.debug(f"注册消息处理器: {msg_id} -> {handler.__name__}")

    async def dispatch_message(self, msg_id: int, data: dict, websocket: WebSocket, username: str, token: str, connection_manager):
        """分发消息到对应的处理器"""
        try:
            if msg_id not in self.handlers:
                logger.warning(f"未找到消息处理器: {msg_id}")
                await connection_manager.send_personal_message(MessageModel(
                    msgId=MessageId.ERROR,
                    data={"error": f"未知的消息类型: {msg_id}"}
                ).model_dump(), token)
                return
            
            handler = self.handlers[msg_id]
            logger.debug(f"分发消息 {msg_id} 到 {handler.__name__}")
            
            # 执行处理器
            result = await handler(websocket, username, token, data, connection_manager)
            
            # 记录处理结果
            if result:
                logger.debug(f"消息 {msg_id} 处理完成，结果: {result}")
            
            return result
        except Exception as e:
            logger.error(f"消息分发失败: {str(e)}")
            logger.error(traceback.format_exc())
            await connection_manager.send_personal_message(MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"消息处理失败: {str(e)}"}
            ).model_dump(), token)
            return MessageModel(
                msgId=MessageId.ERROR,
                data={"error": f"消息处理失败: {str(e)}"}
            )