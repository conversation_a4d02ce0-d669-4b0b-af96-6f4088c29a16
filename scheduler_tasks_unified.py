"""
统一的调度任务定义
包含所有调度任务的定义和实现
"""

import asyncio
from typing import Dict, Any
from logger_config import setup_logger
from service_locator import ServiceLocator
from unified_scheduler_manager import (
    UnifiedSchedulerManager,
    SchedulerMode,
    ServiceDependency,
    TaskDefinition,
    ExecutionMode
)
from scheduler_health_checks import (
    SchedulerHealthChecks, 
    ServiceInitializers,
    with_metrics
)
from models import MessageModel
from enums import MessageId
from player_session_manager import PlayerSessionManager

logger = setup_logger(__name__)

class UnifiedSchedulerTasks:
    """统一调度任务实现"""
    
    def __init__(self, scheduler_manager: UnifiedSchedulerManager):
        self.scheduler_manager = scheduler_manager
        self._register_service_dependencies()
        self._register_task_definitions()
    
    def _register_service_dependencies(self):
        """注册服务依赖"""
        
        # Redis管理器
        self.scheduler_manager.register_service_dependency(
            ServiceDependency(
                name="Redis管理器",
                service_key="redis_manager",
                required=True,
                health_check=SchedulerHealthChecks.check_redis_health,
                initialization=ServiceInitializers.init_redis_manager
            )
        )
        
        # MongoDB管理器
        self.scheduler_manager.register_service_dependency(
            ServiceDependency(
                name="MongoDB管理器",
                service_key="mongodb_manager",
                required=True,
                health_check=SchedulerHealthChecks.check_mongodb_health,
                initialization=ServiceInitializers.init_mongodb_manager
            )
        )
        
        # 连接管理器
        self.scheduler_manager.register_service_dependency(
            ServiceDependency(
                name="连接管理器",
                service_key="conn_manager",
                required=True,
                health_check=SchedulerHealthChecks.check_connection_manager_health,
                initialization=ServiceInitializers.init_connection_manager
            )
        )
        
        # 怪物冷却管理器
        self.scheduler_manager.register_service_dependency(
            ServiceDependency(
                name="怪物冷却管理器",
                service_key="monster_cooldown_manager",
                required=True,
                health_check=SchedulerHealthChecks.check_monster_cooldown_manager_health,
                initialization=ServiceInitializers.init_monster_cooldown_manager
            )
        )
    
    def _register_task_definitions(self):
        """注册任务定义"""
        
        # 每日重置任务
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="daily_reset",
                func=self.daily_reset_task,
                trigger="cron",
                trigger_args={"hour": 0, "minute": 0},
                dependencies=["conn_manager"],
                lock_key="lock:scheduled:daily_reset",
                lock_ttl=300,  # 5分钟锁定时间
                enabled=True,
                execution_mode=ExecutionMode.DIRECT,
                max_retries=2,
                retry_delay=10
            )
        )
        
        # 在线人数推送任务
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="push_online_count",
                func=self.push_online_count_task,
                trigger="interval",
                trigger_args={"minutes": 1},
                dependencies=["conn_manager"],
                lock_key="lock:scheduled:push_online",
                lock_ttl=60,
                enabled=True
            )
        )
        
        # 怪物冷却持久化任务
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="monster_cooldown_persist",
                func=self.monster_cooldown_persist_task,
                trigger="interval",
                trigger_args={"seconds": 30},
                dependencies=["monster_cooldown_manager"],
                lock_key="lock:scheduled:monster_cooldown_persist",
                lock_ttl=60,
                enabled=True
            )
        )
        
        # 怪物冷却通知任务（暂时改为直接模式进行调试）
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="monster_cooldown_notify",
                func=self.monster_cooldown_notify_task,
                trigger="interval",
                trigger_args={"seconds": 10},
                dependencies=["monster_cooldown_manager", "conn_manager"],
                lock_key="lock:scheduled:monster_cooldown_notify",
                lock_ttl=30,
                enabled=True,
                execution_mode=ExecutionMode.DIRECT,  # 暂时改为直接模式
                max_retries=1,
                retry_delay=2
            )
        )

        # 清理过期邮件模板任务
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="cleanup_expired_mail_templates",
                func=self.cleanup_expired_mail_templates_task,
                trigger="interval",
                trigger_args={"hours": 1},  # 每小时执行一次
                dependencies=[],  # 移除依赖
                lock_key="lock:scheduled:cleanup_expired_mail_templates",
                lock_ttl=300,  # 5分钟超时
                enabled=True
            )
        )

        # 清理旧的邮件处理记录任务
        self.scheduler_manager.register_task(
            TaskDefinition(
                name="cleanup_old_processed_records",
                func=self.cleanup_old_processed_records_task,
                trigger="interval",
                trigger_args={"hours": 24},  # 每天执行一次
                dependencies=[],  # 移除依赖
                lock_key="lock:scheduled:cleanup_old_processed_records",
                lock_ttl=600,  # 10分钟超时
                enabled=True
            )
        )
    
    @with_metrics("daily_reset")
    async def daily_reset_task(self):
        """每日重置任务"""
        logger.info("[定时任务] 每日0点重置任务执行")
        
        try:
            connection_manager = ServiceLocator.get("conn_manager")
            if connection_manager is None:
                raise Exception("连接管理器不可用")
            
            # 广播每日重置消息
            await connection_manager.broadcast(
                MessageModel(
                    msgId=MessageId.CHAT,
                    data={"content": "每日重置完成！新的一天开始了！"}
                ).model_dump(), 
                0
            )
            
            logger.info("[定时任务] 每日重置任务完成")
            
        except Exception as e:
            logger.error(f"[定时任务] 每日重置任务失败: {str(e)}")
            raise
    
    @with_metrics("push_online_count")
    async def push_online_count_task(self):
        """推送在线人数任务"""
        import os
        worker_id = os.getpid()
        logger.info(f"[定时任务] Worker {worker_id} 开始执行推送在线人数任务")

        try:
            session_manager = await PlayerSessionManager.get_instance()
            if session_manager:
                online_count = await session_manager.get_online_count()            
                connection_manager = ServiceLocator.get("conn_manager")
                # 广播在线人数
                await connection_manager.broadcast(
                    MessageModel(
                        msgId=MessageId.CHAT,
                        data={"content": f"当前在线人数: {online_count}"}
                    ).model_dump(),
                    0
                )

                logger.info(f"[定时任务] Worker {worker_id} 在线人数推送完成，当前在线: {online_count}")
            else:
                logger.error(f"[定时任务] Worker {worker_id} 会话管理器不可用")
        except Exception as e:
            logger.error(f"[定时任务] Worker {worker_id} 推送在线人数任务失败: {str(e)}")
            raise
    
    @with_metrics("monster_cooldown_persist")
    async def monster_cooldown_persist_task(self):
        """怪物冷却持久化任务"""
        import os
        worker_id = os.getpid()
        logger.info(f"[定时任务] Worker {worker_id} 开始执行怪物冷却持久化任务")

        try:
            monster_cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
            if monster_cooldown_manager is None:
                raise Exception("怪物冷却管理器不可用")

            # 执行持久化
            success = await monster_cooldown_manager.persist_to_mongodb()

            if success:
                logger.info(f"[定时任务] Worker {worker_id} 怪物冷却持久化完成")
            else:
                logger.warning(f"[定时任务] Worker {worker_id} 怪物冷却持久化失败")

        except Exception as e:
            logger.error(f"[定时任务] Worker {worker_id} 怪物冷却持久化任务失败: {str(e)}")
            raise

    @with_metrics("cleanup_expired_mail_templates")
    async def cleanup_expired_mail_templates_task(self):
        """清理过期邮件模板任务"""
        import os
        worker_id = os.getpid()
        logger.info(f"[定时任务] Worker {worker_id} 开始执行清理过期邮件模板任务")

        try:
            # 直接创建邮件服务实例
            from mail_service_distributed import MailServiceDistributed
            mail_service = MailServiceDistributed()

            # 执行清理
            result = await mail_service.cleanup_expired_templates()

            if result.success:
                deleted_count = result.data.get("deleted_count", 0)
                logger.info(f"[定时任务] Worker {worker_id} 清理过期邮件模板完成，删除了 {deleted_count} 个模板")
            else:
                logger.warning(f"[定时任务] Worker {worker_id} 清理过期邮件模板失败: {result.error}")

        except Exception as e:
            logger.error(f"[定时任务] Worker {worker_id} 清理过期邮件模板任务失败: {str(e)}")
            raise

    @with_metrics("cleanup_old_processed_records")
    async def cleanup_old_processed_records_task(self):
        """清理旧的邮件处理记录任务"""
        import os
        worker_id = os.getpid()
        logger.info(f"[定时任务] Worker {worker_id} 开始执行清理旧邮件处理记录任务")

        try:
            # 直接创建邮件服务实例
            from mail_service_distributed import MailServiceDistributed
            mail_service = MailServiceDistributed()

            # 执行清理（清理30天前的记录）
            result = await mail_service.cleanup_old_processed_records(30)

            if result.success:
                deleted_count = result.data.get("deleted_count", 0)
                logger.info(f"[定时任务] Worker {worker_id} 清理旧邮件处理记录完成，删除了 {deleted_count} 条记录")
            else:
                logger.warning(f"[定时任务] Worker {worker_id} 清理旧邮件处理记录失败: {result.error}")

        except Exception as e:
            logger.error(f"[定时任务] Worker {worker_id} 清理旧邮件处理记录任务失败: {str(e)}")
            raise
    
    @with_metrics("monster_cooldown_notify")
    async def monster_cooldown_notify_task(self):
        """怪物冷却通知任务"""
        logger.info("[定时任务] 怪物冷却通知任务执行")
        
        try:
            monster_cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
            if monster_cooldown_manager is None:
                raise Exception("怪物冷却管理器不可用")

            # 执行冷却通知
            notified_count = await monster_cooldown_manager.notify_expired_cooldowns()

            logger.info(f"[定时任务] 怪物冷却通知完成，已通知 {notified_count} 个怪物刷新")

        except Exception as e:
            logger.error(f"[定时任务] 怪物冷却通知任务失败: {str(e)}")
            raise

async def create_standalone_scheduler() -> UnifiedSchedulerManager:
    """创建独立调度器"""
    scheduler_manager = UnifiedSchedulerManager(SchedulerMode.STANDALONE)
    UnifiedSchedulerTasks(scheduler_manager)
    return scheduler_manager

async def create_integrated_scheduler() -> UnifiedSchedulerManager:
    """创建集成调度器（用于游戏服务器）"""
    scheduler_manager = UnifiedSchedulerManager(SchedulerMode.INTEGRATED)
    UnifiedSchedulerTasks(scheduler_manager)
    return scheduler_manager

async def main():
    """独立调度器主函数"""
    logger.info("启动独立统一调度器...")
    
    try:
        # 创建调度器
        scheduler = await create_standalone_scheduler()
        
        # 启动调度器
        if await scheduler.start():
            logger.info("独立调度器启动成功，进入运行循环...")
            
            # 运行循环
            while scheduler.is_running:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                # 可以在这里添加额外的监控逻辑
                health_status = await scheduler.check_service_health()
                unhealthy_services = [
                    scheduler.service_dependencies[key].name 
                    for key, healthy in health_status.items() 
                    if not healthy
                ]
                
                if unhealthy_services:
                    logger.warning(f"检测到不健康的服务: {unhealthy_services}")
        else:
            logger.error("独立调度器启动失败")
            
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭调度器...")
    except Exception as e:
        logger.error(f"独立调度器运行异常: {str(e)}")
    finally:
        if 'scheduler' in locals():
            await scheduler.stop()
        logger.info("独立调度器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
