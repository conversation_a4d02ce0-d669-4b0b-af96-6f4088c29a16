import configparser
import os
import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
import base64
from enums import ItemType
logger = logging.getLogger(__name__)

class ConfigError(Exception):
    """配置错误异常"""
    pass

class Config:
    def __init__(self, config_file="config/server.ini", field_config_file="config/field_config.json"):
        # 禁用插值功能，避免日志格式字符串中的%被错误解析
        self.config = configparser.ConfigParser(interpolation=None)
        self.config_file = config_file
        self.field_config_file = field_config_file
        self.field_config = {}
        self.last_mtime = 0
        self.field_config_mtime = 0
        self.lock = asyncio.Lock()
        self._game_configs = {}  # 存储游戏配置
        self.load_config()

    def load_config(self):
        """读取 server.ini 配置文件"""
        try:
            if not os.path.exists(self.config_file):
                logger.error(f"配置文件 {self.config_file} 不存在")
                raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config.read_file(f)
            for section in self.config.sections():
                for key, value in self.config[section].items():
                    self.config[section][key] = os.path.expandvars(value)
            self.validate_config()
            logger.info(f"成功加载配置文件 {self.config_file}: {dict(self.config._sections)}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
    def validate_config(self):
        """验证 server.ini 配置完整性"""
        required_fields = {
            "Server": ["host", "port"],
            "Redis": ["host", "port"],
            "MongoDB": ["host", "port", "username", "password", "database"],
            "JWT": ["secret_key", "algorithm", "access_token_expire_minutes"],
            "RabbitMQ": ["host", "port", "username", "password", "virtual_host"]
        }
        for section, fields in required_fields.items():
            for field in fields:
                value = self.config.get(section, field, fallback=None)
                if not value:
                    raise ConfigError(f"缺少必要的配置项: [{section}]{field}")
                if field in ["port", "access_token_expire_minutes"] and not value.isdigit():
                    raise ConfigError(f"配置项 [{section}]{field} 必须是整数: {value}")
                if field == "secret_key" and len(value) < 32:
                    raise ConfigError(f"配置项 [{section}]{field} 长度不足 32 字符")

    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        # 检查两种可能的配置节名称：server 和 Server
        section_name = None
        if self.config.has_section('server'):
            section_name = 'server'
        elif self.config.has_section('Server'):
            section_name = 'Server'
            
        if not section_name:
            return {
                "host": "0.0.0.0",
                "port": 8000,
                "debug": True,
                "workers": 4
            }
        
        return {
            "host": self.config.get(section_name, 'host', fallback='0.0.0.0'),
            "port": self.config.getint(section_name, 'port', fallback=8000),
            "debug": self.config.getboolean(section_name, 'debug', fallback=True),
            "workers": self.config.getint(section_name, 'workers', fallback=4)
        }

    def get_redis_config(self):
        """获取Redis配置"""
        # 检查两种可能的配置节名称：redis 和 Redis
        section_name = None
        if self.config.has_section('redis'):
            section_name = 'redis'
        elif self.config.has_section('Redis'):
            section_name = 'Redis'
            
        if not section_name:
            return {
                "host": "localhost",
                "port": 6379,
                "password": None,
                "db": 0,
                "max_connections": 200,
                "socket_timeout": 5.0,
                "socket_connect_timeout": 5.0,
            }
        
        return {
            "host": self.config.get(section_name, "host", fallback="localhost"),
            "port": self.config.getint(section_name, "port", fallback=6379),
            "password": self.config.get(section_name, "password", fallback=None),
            "db": self.config.getint(section_name, "db", fallback=0),
            "max_connections": self.config.getint(section_name, "max_connections", fallback=200),
            "socket_timeout": self.config.getfloat(section_name, "socket_timeout", fallback=5.0),
            "socket_connect_timeout": self.config.getfloat(section_name, "socket_connect_timeout", fallback=5.0),
        }

    def get_mongodb_config(self):
        """获取MongoDB配置"""
        # 检查两种可能的配置节名称：mongodb 和 MongoDB
        section_name = None
        if self.config.has_section('mongodb'):
            section_name = 'mongodb'
        elif self.config.has_section('MongoDB'):
            section_name = 'MongoDB'
            
        if not section_name:
            return {
                "host": "localhost",
                "port": 27017,
                "username": None,
                "password": None,
                "database": "game_server",
            }
        
        return {
            "host": self.config.get(section_name, "host", fallback="localhost"),
            "port": self.config.getint(section_name, "port", fallback=27017),
            "username": self.config.get(section_name, "username", fallback=None),
            "password": self.config.get(section_name, "password", fallback=None),
            "database": self.config.get(section_name, "database", fallback="game_server"),
        }

    def get_jwt_config(self):
        """获取JWT配置"""
        # 检查两种可能的配置节名称：jwt 和 JWT
        section_name = None
        if self.config.has_section('jwt'):
            section_name = 'jwt'
        elif self.config.has_section('JWT'):
            section_name = 'JWT'
            
        if not section_name:
            return {
                "secret_key": "your-secret-key",
                "algorithm": "HS256",
                "access_token_expire_minutes": 1440,
            }
        
        return {
            "secret_key": self.config.get(section_name, "secret_key", fallback="your-secret-key"),
            "algorithm": self.config.get(section_name, "algorithm", fallback="HS256"),
            "access_token_expire_minutes": self.config.getint(section_name, "access_token_expire_minutes", fallback=1440),
        }

    def get_rabbitmq_config(self) -> Dict[str, Any]:
        """获取RabbitMQ配置"""
        # 检查两种可能的配置节名称：rabbitmq 和 RabbitMQ
        section_name = None
        if self.config.has_section('rabbitmq'):
            section_name = 'rabbitmq'
        elif self.config.has_section('RabbitMQ'):
            section_name = 'RabbitMQ'
        
        if not section_name:
            return {
                "host": "localhost",
                "port": 5672,
                "username": "guest",
                "password": "guest",
                "virtual_host": "/",
                "exchange_name": "game_exchange",
                "queue_name": "game_queue",
                "broadcast_exchange": "broadcast_exchange",
                "personal_exchange": "personal_exchange",
                "max_retries": 5,
                "retry_delay": 2
            }
        
        return {
            "host": self.config.get(section_name, 'host', fallback='localhost'),
            "port": self.config.getint(section_name, 'port', fallback=5672),
            "username": self.config.get(section_name, 'username', fallback='guest'),
            "password": self.config.get(section_name, 'password', fallback='guest'),
            "virtual_host": self.config.get(section_name, 'virtual_host', fallback='/'),
            "exchange_name": self.config.get(section_name, 'exchange_name', fallback='game_exchange'),
            "queue_name": self.config.get(section_name, 'queue_name', fallback='game_queue'),
            "broadcast_exchange": self.config.get(section_name, 'broadcast_exchange', fallback='broadcast_exchange'),
            "personal_exchange": self.config.get(section_name, 'personal_exchange', fallback='personal_exchange'),
            "max_retries": self.config.getint(section_name, 'max_retries', fallback=5),
            "retry_delay": self.config.getint(section_name, 'retry_delay', fallback=2)
        }
    def get_cache_keys(self):
        version = "v2"
        prefix = f"game:{version}"
        return {
            "user_data": f"{prefix}:users:{{username}}", #用户数据
            "user_status": f"{prefix}:users:{{username}}:status", #用户状态
            "user_last_connection": f"{prefix}:users:{{username}}:last_connection", #用户最后连接时间       
            "item_data": f"{prefix}:items:{{owner}}:{{item_id}}", #道具数据 包含 装备 符文 道具 
            "user_items": f"{prefix}:users:{{username}}:items", #用户道具
            "user_equipment": f"{prefix}:users:{{username}}:equipment", #用户装备
            "user_rune": f"{prefix}:users:{{username}}:rune", #用户符文
            "user_connection": f"{prefix}:users:{{username}}:connection", #用户连接
            "connection_websocket": f"{prefix}:connections:{{token}}", #连接websocket
            "broadcast_channel": f"{prefix}:channels:global", #广播频道
            "user_channel": f"{prefix}:users:{{username}}:channel", #用户频道
            "token_to_user": f"{prefix}:token_to_user:{{token}}", #token到用户
            "generals_data": f"{prefix}:generals:{{player_id}}", #玩家武将列表
            "general_data": f"{prefix}:generals:{{player_id}}:{{general_id}}", #武将详情
            "general_properties": f"{prefix}:generals:{{player_id}}:{{general_id}}:properties", #武将属性计算结果
            "general_fight_power": f"{prefix}:generals:{{player_id}}:{{general_id}}:fight_power", #武将战斗力
            "formation_data": f"{prefix}:formation:{{player_id}}" #玩家阵容
        }

    def get_log_config(self):
        """获取日志配置"""
        # 检查两种可能的配置节名称：log 和 Log
        section_name = None
        if self.config.has_section('log'):
            section_name = 'log'
        elif self.config.has_section('Log'):
            section_name = 'Log'
            
        if not section_name:
            return {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/server.log",
                "max_bytes": 10485760,
                "backup_count": 5
            }
        
        return {
            "level": self.config.get(section_name, 'level', fallback='INFO'),
            "format": self.config.get(section_name, 'format', fallback='%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            "file": self.config.get(section_name, 'file', fallback='logs/server.log'),
            "max_bytes": self.config.getint(section_name, 'max_bytes', fallback=10485760),
            "backup_count": self.config.getint(section_name, 'backup_count', fallback=5)
        }

    def load_game_config(self, config_name: str) -> Dict[str, Any]:
        """加载指定游戏配置文件"""
        config_path = f"config/game/{config_name}.json"
        try:
            if not os.path.exists(config_path):
                logger.warning(f"游戏配置文件不存在: {config_path}")
                return {}
                
            with open(config_path, 'r', encoding='utf-8') as f:
                #b64str = base64.b64decode(f.read().encode('utf-8')).decode('utf-8')
                data = json.loads(f.read().encode('utf-8'))
                result = {}
                for item in data:                                        
                    result[item["id"]] = item
            logger.info(f"成功加载游戏配置 {config_name}: {len(data) if isinstance(data, list) else '1'} 项")
            return result
        except json.JSONDecodeError:
            logger.error(f"游戏配置文件格式错误: {config_path}")
            return {}
        except Exception as e:
            logger.error(f"加载游戏配置失败 {config_path}: {str(e)}")
            return {}

    def get_game_config(self, config_name: str) -> Dict[str, Any]:
        """获取游戏配置，如果未加载则自动加载"""
        if config_name not in self._game_configs:
            self._game_configs[config_name] = self.load_game_config(config_name)
            
        return self._game_configs[config_name]

    def reload_game_config(self, config_name: str = None) -> bool:
        """重新加载指定游戏配置或全部游戏配置"""
        try:
            if config_name:
                # 重新加载指定配置
                self._game_configs[config_name] = self.load_game_config(config_name)
                logger.info(f"重新加载游戏配置: {config_name}")
            else:
                # 重新加载所有已加载的配置
                for name in list(self._game_configs.keys()):
                    self._game_configs[name] = self.load_game_config(name)
                logger.info(f"重新加载所有游戏配置: {list(self._game_configs.keys())}")
            return True
        except Exception as e:
            logger.error(f"重新加载游戏配置失败: {str(e)}")
            return False

    def get_item_config(self) -> List[Dict[str, Any]]:
        """获取道具配置"""
        return self.get_game_config("cfg_item")
    def get_equipment_config(self) -> List[Dict[str, Any]]:
        """获取装备配置"""
        return self.get_game_config("cfg_weapon")
    def get_rune_config(self) -> List[Dict[str, Any]]:
        """获取符文配置"""
        return self.get_game_config("cfg_rune")
    
    def get_general_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取武将模板配置"""
        return self.get_game_config("cfg_career")
    
    def get_general_template_by_id(self, template_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取武将模板"""
        templates = self.get_general_templates()
        return templates.get(int(template_id)) or templates.get(template_id)
    
    def get_general_templates_by_rarity(self, rarity: str) -> List[Dict[str, Any]]:
        """根据稀有度获取武将模板列表"""
        templates = self.get_general_templates()
        result = []
        for template in templates.values():
            if isinstance(template, dict) and template.get("rarity") == rarity:
                result.append(template)
        return result
    
    def get_general_templates_by_type(self, general_type: str) -> List[Dict[str, Any]]:
        """根据武将类型获取模板列表"""
        templates = self.get_general_templates()
        result = []
        for template in templates.values():
            if isinstance(template, dict) and template.get("type") == general_type:
                result.append(template)
        return result
    
    def get_card_packages(self) -> List[Dict[str, Any]]:
        """获取卡包配置"""
        return self.get_game_config("cfg_cardsPackage")
    
    def get_card_package_by_id(self, package_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取卡包配置"""
        packages = self.get_card_packages()
        return packages.get(str(package_id)) or packages.get(package_id)
    
    def get_card_expectations(self) -> List[Dict[str, Any]]:
        """获取卡池配置"""
        return self.get_game_config("cfg_cardsExpect")
    
    def get_card_expectations_by_package(self, package_id: int) -> List[Dict[str, Any]]:
        """根据卡包ID获取卡池配置"""
        expectations = self.get_card_expectations()
        result = []
        for _key,value in expectations.items():
            if value.get("pack_id") == package_id:
                result.append(value)
        logger.info(f"expectations: {result}")
        return result
    
    def get_draw_pool_by_package(self, package_id: int) -> List[Dict[str, Any]]:
        """根据卡包ID获取抽卡池"""
        expectations = self.get_card_expectations_by_package(package_id)
        templates = self.get_general_templates()
        result = []        
        for value in expectations:
            role_id = value.get("role_id")
            expect_value = value.get("expect", 0)
            conditions = value.get("conditions", 0)
            
            # 获取武将模板
            template = templates.get(role_id) or templates.get(role_id)
            logger.info(f"template: {template}")
            if template:
                result.append({
                    "template_id": role_id,
                    "template": template,
                    "expect": expect_value,
                    "conditions": conditions
                })
        
        return result

    async def monitor_game_configs(self, interval: int = 60):
        """监控游戏配置文件变化，自动重新加载"""
        config_dir = "config/game"
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            
        last_modified = {}
        
        while True:
            try:
                for config_name in self._game_configs.keys():
                    config_path = f"{config_dir}/{config_name}.json"
                    if os.path.exists(config_path):
                        mtime = os.path.getmtime(config_path)
                        if config_name not in last_modified or mtime > last_modified[config_name]:
                            logger.info(f"检测到游戏配置文件变更: {config_name}")
                            self._game_configs[config_name] = self.load_game_config(config_name)
                            last_modified[config_name] = mtime
            except Exception as e:
                logger.error(f"监控游戏配置文件失败: {str(e)}")
                
            await asyncio.sleep(interval)
    #获取道具缓存键
    def get_item_cache_key(self, item_type: ItemType):
        cache_keys = self.get_cache_keys()
        match item_type:
            case ItemType.ITEM:
                return cache_keys.get("user_items", "game:v2:users:{username}:items")
            case ItemType.EQUIPMENT:
                return cache_keys.get("user_equipment", "game:v2:users:{username}:equipment")
            case ItemType.RUNE:
                return cache_keys.get("user_rune", "game:v2:users:{username}:rune")
            case _:
                return cache_keys.get("user_items", "game:v2:users:{username}:items")
    #获取道具类型名称
    def get_item_type_name(self, item_type: ItemType):
        match item_type:
            case ItemType.ITEM:
                return "道具"
            case ItemType.EQUIPMENT:
                return "装备"
            case ItemType.RUNE:
                return "符文"
            case _:
                return "道具"
    #获取道具配置
    def get_item_config_by_type(self,item_type:ItemType):
        match item_type:
            case ItemType.ITEM:
                return self.get_item_config()
            case ItemType.EQUIPMENT:
                return self.get_equipment_config()
            case ItemType.RUNE:
                return self.get_rune_config()
            case _:
                return self.get_item_config()
    #验证id是否在配置中
    def is_valid_id(self,item_type:ItemType,item_id:int):
        item_config = self.get_item_config_by_type(item_type)
        return item_id in item_config.keys()

    def get_monster_config(self) -> Dict[str, Dict[str, Any]]:
        """获取怪物配置"""
        return self.get_game_config("monsters")
    
    def get_monster_by_id(self, monster_id: str) -> Optional[Dict[str, Any]]:
        """获取指定怪物的配置"""
        monsters = self.get_monster_config()    
        return monsters.get(monster_id)
    
    def get_monsters_by_type(self, monster_type: str) -> List[Dict[str, Any]]:
        """获取指定类型的所有怪物"""
        monsters = self.get_monster_config()
        return [monster for monster in monsters if monster.get("type") == monster_type]
    
    def get_monsters_by_area(self, area: str) -> List[Dict[str, Any]]:
        """获取指定区域的所有怪物"""
        monsters = self.get_monster_config()
        return [monster for monster in monsters if monster.get("area") == area]
    
    def get_monster_cooldown(self, monster_id: str, cooldown_type: str) -> Optional[int]:
        """获取怪物的冷却时间（秒）"""
        monster = self.get_monster_by_id(monster_id)
        if not monster:
            return None
        
        cooldown = monster.get("cooldown", {})
        return cooldown.get(cooldown_type)
    
    def calculate_monster_cooldown(self, monster_id: str, cooldown_type: str, modifiers: Dict[str, float] = None) -> Optional[int]:
        """计算怪物的实际冷却时间，考虑修正因子"""
        base_cooldown = self.get_monster_cooldown(monster_id, cooldown_type)
        if base_cooldown is None:
            return None
        
        # 如果没有修正因子，直接返回基础冷却时间
        if not modifiers:
            return base_cooldown
        
        # 应用修正因子
        cooldown = base_cooldown
        for modifier, value in modifiers.items():
            cooldown *= value
        
        # 确保冷却时间至少为1秒
        return max(1, int(cooldown))
    
    def get_monster_drops(self, monster_id: str) -> List[Dict[str, Any]]:
        """获取怪物的掉落物品"""
        monster = self.get_monster_by_id(monster_id)
        if not monster:
            return []
        
        return monster.get("drops", [])
# 全局配置实例
config = Config()