<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格配置格式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 5px 0;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 价格配置格式测试</h1>
        
        <div class="test-section">
            <div class="test-title">📊 测试用例</div>
            <button class="btn btn-primary" onclick="testPriceFormats()">运行价格格式测试</button>
            <button class="btn btn-primary" onclick="testConversions()">运行格式转换测试</button>
            <button class="btn btn-primary" onclick="clearResults()">清除结果</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">📝 测试结果</div>
            <div id="testResults" class="test-result">点击按钮开始测试...</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/item-manager.js"></script>

    <script>
        // 测试工具
        class PriceFormatTester {
            constructor() {
                this.results = [];
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
                this.results.push(`[${timestamp}] ${message}`);
                this.updateDisplay();
            }

            updateDisplay() {
                const resultsEl = document.getElementById('testResults');
                if (resultsEl) {
                    resultsEl.textContent = this.results.join('\n');
                }
            }

            clear() {
                this.results = [];
                this.updateDisplay();
            }
        }

        const tester = new PriceFormatTester();

        function testPriceFormats() {
            tester.log('🚀 开始价格格式测试', 'info');
            
            // 创建ItemManager实例用于测试
            const itemManager = new ItemManager();
            
            // 测试数据
            const testCases = [
                {
                    name: "前端格式",
                    config: { currency: "gold", amount: 111 },
                    expected: "111 金币"
                },
                {
                    name: "后端格式",
                    config: { currency_type: "gold", base_price: 111 },
                    expected: "111 金币"
                },
                {
                    name: "字符串金额",
                    config: { currency: "diamond", amount: "200" },
                    expected: "200 钻石"
                },
                {
                    name: "零金额",
                    config: { currency_type: "arena_coin", base_price: 0 },
                    expected: "0 竞技场币"
                },
                {
                    name: "空配置",
                    config: null,
                    expected: "未设置"
                },
                {
                    name: "格式错误",
                    config: { invalid: "data" },
                    expected: "格式错误"
                }
            ];

            testCases.forEach((testCase, index) => {
                tester.log(`\n${index + 1}. 测试 ${testCase.name}:`);
                tester.log(`   输入: ${JSON.stringify(testCase.config)}`);
                
                try {
                    const result = itemManager.formatPrice(testCase.config);
                    tester.log(`   输出: ${result}`);
                    tester.log(`   期望: ${testCase.expected}`);
                    
                    if (result === testCase.expected) {
                        tester.log(`   ✅ 通过`, 'success');
                    } else {
                        tester.log(`   ❌ 失败`, 'error');
                    }
                } catch (error) {
                    tester.log(`   💥 异常: ${error.message}`, 'error');
                }
            });

            tester.log('\n✅ 价格格式测试完成', 'success');
        }

        function testConversions() {
            tester.log('\n🔄 开始格式转换测试', 'info');
            
            const itemManager = new ItemManager();
            
            // 测试格式转换
            const conversionTests = [
                {
                    name: "前端到后端转换",
                    input: { currency: "gold", amount: 111 },
                    operation: "normalize"
                },
                {
                    name: "后端到前端转换",
                    input: { currency_type: "gold", base_price: 111 },
                    operation: "display"
                },
                {
                    name: "往返转换",
                    input: { currency: "diamond", amount: 500 },
                    operation: "roundtrip"
                }
            ];

            conversionTests.forEach((test, index) => {
                tester.log(`\n${index + 1}. 测试 ${test.name}:`);
                tester.log(`   输入: ${JSON.stringify(test.input)}`);
                
                try {
                    if (test.operation === "normalize") {
                        const normalized = itemManager.normalizePriceConfig(test.input);
                        tester.log(`   标准化结果: ${JSON.stringify(normalized)}`);
                        
                        // 验证字段
                        if (normalized && normalized.currency_type && normalized.base_price !== undefined) {
                            tester.log(`   ✅ 标准化成功`, 'success');
                        } else {
                            tester.log(`   ❌ 标准化失败`, 'error');
                        }
                    } else if (test.operation === "display") {
                        const display = itemManager.convertPriceConfigForDisplay(test.input);
                        tester.log(`   显示格式: ${JSON.stringify(display)}`);
                        
                        // 验证字段
                        if (display && display.currency && display.amount !== undefined) {
                            tester.log(`   ✅ 显示转换成功`, 'success');
                        } else {
                            tester.log(`   ❌ 显示转换失败`, 'error');
                        }
                    } else if (test.operation === "roundtrip") {
                        // 往返转换测试
                        const normalized = itemManager.normalizePriceConfig(test.input);
                        const display = itemManager.convertPriceConfigForDisplay(normalized);
                        
                        tester.log(`   标准化: ${JSON.stringify(normalized)}`);
                        tester.log(`   显示格式: ${JSON.stringify(display)}`);
                        
                        // 验证往返转换是否保持数据一致性
                        if (display.currency === test.input.currency && 
                            parseInt(display.amount) === parseInt(test.input.amount)) {
                            tester.log(`   ✅ 往返转换成功`, 'success');
                        } else {
                            tester.log(`   ❌ 往返转换失败`, 'error');
                        }
                    }
                } catch (error) {
                    tester.log(`   💥 异常: ${error.message}`, 'error');
                }
            });

            tester.log('\n✅ 格式转换测试完成', 'success');
        }

        function clearResults() {
            tester.clear();
            tester.log('测试结果已清除，点击按钮开始新的测试...');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            tester.log('🎯 价格配置格式测试工具已就绪');
            tester.log('点击上方按钮开始测试...');
        });
    </script>
</body>
</html>
