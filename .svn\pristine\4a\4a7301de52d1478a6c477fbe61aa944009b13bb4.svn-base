# Game Server

一个高性能、可扩展的游戏服务器框架，具有先进的缓存管理系统和实时通信能力。

## 项目简介

本项目是一个多功能游戏服务器架构，支持用户认证、实时通信、物品管理、资产交易等功能。采用高效的异步处理模式，集成了先进的多层缓存系统，能够处理高并发游戏场景中的数据需求。

## 技术栈

- **后端**: Python 3.8+ (asyncio)
- **数据库**: MongoDB (持久化存储)
- **缓存**: Redis (高级缓存管理系统)
- **消息队列**: RabbitMQ
- **通信**: WebSockets
- **容器化**: Docker, docker-compose
- **Web服务器**: Nginx (反向代理)

## 系统架构

### 核心组件

- **game_server.py**: 主服务器入口，处理WebSocket连接
- **game_database.py**: 数据库访问层，包含高级缓存系统
- **ConnectionManager.py**: WebSocket连接管理
- **auth.py**: 用户认证系统
- **event_bus.py**: 事件总线，处理系统内部通信
- **models.py**: 数据模型定义
- **config.py**: 配置管理
- **enums.py**: 枚举定义

### 缓存系统架构

项目实现了一套先进的三层缓存架构：

1. **基础缓存层** (CacheManager)
   - 提供统一的缓存接口
   - 支持事务操作
   - 批量数据处理
   - 智能序列化/反序列化

2. **领域缓存层**
   - UserCacheManager: 用户数据缓存管理
   - ItemCacheManager: 物品数据缓存管理

3. **应用缓存层**
   - 集成到数据库操作中
   - 自动缓存查询结果
   - 缓存失效管理

## 安装指南

### 使用Docker (推荐)

1. 克隆仓库:
   ```bash
   git clone <repository-url>
   cd game_server
   ```

2. 创建环境配置:
   ```bash
   cp env.example .env
   # 编辑.env文件设置必要参数
   ```

3. 启动服务:
   ```bash
   docker-compose up -d
   ```

### 手动安装

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 安装MongoDB和Redis:
   ```bash
   # 根据您的操作系统安装MongoDB和Redis
   ```

3. 创建配置:
   ```bash
   cp config/server.ini.example config/server.ini
   # 编辑server.ini设置必要参数
   ```

4. 运行服务:
   ```bash
   python main.py
   ```

## 配置指南

### 主要配置文件

- `config/server.ini`: 主配置文件
- `.env`: Docker环境变量
- `config/field_config.json`: 字段验证配置

### 数据库配置

在server.ini中配置MongoDB和Redis连接信息:

```ini
[mongodb]
host = localhost
port = 27017
database = gamedb
username = 
password = 

[redis]
host = localhost
port = 6379
password = 
db = 0
```

## 缓存系统使用指南

### 基本使用

```python
# 获取缓存管理器实例
db_manager = DatabaseManager()

# 用户缓存操作
user_data = await db_manager.user_cache.get_user_data("username123")
await db_manager.user_cache.set_user_data("username123", user_data)

# 物品缓存操作
items = await db_manager.item_cache.get_user_items("username123", "item")
await db_manager.item_cache.add_item(item_data)

# 事务支持
async with db_manager.cache_manager.transaction() as tx:
    await tx.set("key1", "value1")
    await tx.set("key2", "value2")
```

## API文档

游戏服务器提供基于WebSocket的实时API:

- 连接: `ws://<server-host>/ws/<token>`
- 认证: 通过auth.py模块进行用户认证
- 消息格式: JSON格式，包含msgId, data字段

常见消息类型:
- 用户认证
- 物品操作
- 资产变更通知
- 状态更新

## 部署指南

### 生产环境部署

1. 优化Docker配置:
   ```bash
   # 编辑docker-compose.yml设置适当的资源限制
   ```

2. 配置Nginx反向代理:
   ```bash
   # 使用项目中的nginx.conf模板
   ```

3. 启用日志轮转:
   ```bash
   # 配置日志轮转避免磁盘空间耗尽
   ```

### 监控与维护

- 使用Docker stats监控容器状态
- 定期清理RabbitMQ队列 (使用rabbitmq_cleaner.py)
- 监控Redis内存使用情况

## 扩展开发

### 添加新功能

1. 在models.py中定义新的数据模型
2. 在game_database.py中添加相关数据访问方法
3. 为高频访问数据创建专用缓存管理器
4. 在WebSocket处理程序中添加新的消息处理逻辑

## License

[MIT License](LICENSE) 
