[{"id": 1, "qua": 1, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 40, "max_val": 80, "maxLevel": 200}, {"id": 2, "qua": 2, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 60, "max_val": 120, "maxLevel": 200}, {"id": 3, "qua": 3, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 80, "max_val": 160, "maxLevel": 200}, {"id": 4, "qua": 4, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 100, "max_val": 200, "maxLevel": 200}, {"id": 5, "qua": 5, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 120, "max_val": 240, "maxLevel": 200}, {"id": 6, "qua": 6, "kind": 1, "blevel": 1, "bonly": 0, "key": "pow", "min_val": 140, "max_val": 280, "maxLevel": 200}, {"id": 7, "qua": 1, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 40, "max_val": 80, "maxLevel": 200}, {"id": 8, "qua": 2, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 60, "max_val": 120, "maxLevel": 200}, {"id": 9, "qua": 3, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 80, "max_val": 160, "maxLevel": 200}, {"id": 10, "qua": 4, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 100, "max_val": 200, "maxLevel": 200}, {"id": 11, "qua": 5, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 120, "max_val": 240, "maxLevel": 200}, {"id": 12, "qua": 6, "kind": 1, "blevel": 1, "bonly": 0, "key": "int", "min_val": 140, "max_val": 280, "maxLevel": 200}, {"id": 13, "qua": 1, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 40, "max_val": 80, "maxLevel": 200}, {"id": 14, "qua": 2, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 60, "max_val": 120, "maxLevel": 200}, {"id": 15, "qua": 3, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 80, "max_val": 160, "maxLevel": 200}, {"id": 16, "qua": 4, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 100, "max_val": 200, "maxLevel": 200}, {"id": 17, "qua": 5, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 120, "max_val": 240, "maxLevel": 200}, {"id": 18, "qua": 6, "kind": 1, "blevel": 1, "bonly": 0, "key": "dex", "min_val": 140, "max_val": 280, "maxLevel": 200}, {"id": 19, "qua": 1, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 40, "max_val": 80, "maxLevel": 200}, {"id": 20, "qua": 2, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 60, "max_val": 120, "maxLevel": 200}, {"id": 21, "qua": 3, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 80, "max_val": 160, "maxLevel": 200}, {"id": 22, "qua": 4, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 100, "max_val": 200, "maxLevel": 200}, {"id": 23, "qua": 5, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 120, "max_val": 240, "maxLevel": 200}, {"id": 24, "qua": 6, "kind": 1, "blevel": 1, "bonly": 0, "key": "con", "min_val": 140, "max_val": 280, "maxLevel": 200}, {"id": 25, "qua": 1, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 28, "max_val": 56, "maxLevel": 200}, {"id": 26, "qua": 2, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 42, "max_val": 84, "maxLevel": 200}, {"id": 27, "qua": 3, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 56, "max_val": 112, "maxLevel": 200}, {"id": 28, "qua": 4, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 70, "max_val": 140, "maxLevel": 200}, {"id": 29, "qua": 5, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 84, "max_val": 168, "maxLevel": 200}, {"id": 30, "qua": 6, "kind": 1, "blevel": 1, "bonly": 0, "key": "def", "min_val": 98, "max_val": 196, "maxLevel": 200}, {"id": 31, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "sk", "min_val": 4175, "max_val": 4175, "maxLevel": 200}, {"id": 32, "qua": 4, "kind": 2, "blevel": 0, "bonly": 1, "key": "int_p", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 33, "qua": 4, "kind": 2, "blevel": 0, "bonly": 1, "key": "pow_p", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 34, "qua": 4, "kind": 2, "blevel": 0, "bonly": 1, "key": "dex_p", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 35, "qua": 4, "kind": 2, "blevel": 0, "bonly": 1, "key": "def_p", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 36, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "pow_p", "min_val": 12, "max_val": 12, "maxLevel": 200}, {"id": 37, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "int_p", "min_val": 12, "max_val": 12, "maxLevel": 200}, {"id": 38, "qua": 4, "kind": 1, "blevel": 0, "bonly": 1, "key": "trt", "min_val": 50, "max_val": 100, "maxLevel": 200}, {"id": 39, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "trt", "min_val": 60, "max_val": 120, "maxLevel": 200}, {"id": 40, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "trtpd", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 41, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "trtad", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 42, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "cirt_odd", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 43, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "cirt_hit", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 44, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "red", "min_val": 5, "max_val": 5, "maxLevel": 200}, {"id": 45, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "cirt_odd", "min_val": 5, "max_val": 12, "maxLevel": 200}, {"id": 46, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "pow_p", "min_val": 10, "max_val": 18, "maxLevel": 200}, {"id": 47, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "int_p", "min_val": 10, "max_val": 18, "maxLevel": 200}, {"id": 48, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "trtad", "min_val": 8, "max_val": 16, "maxLevel": 200}, {"id": 49, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "mmaskl", "min_val": 3, "max_val": 5, "maxLevel": 200}, {"id": 50, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "amaskl", "min_val": 3, "max_val": 5, "maxLevel": 200}, {"id": 51, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "con", "min_val": 500, "max_val": 500, "maxLevel": 200}, {"id": 52, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "shield_dmg", "min_val": 10, "max_val": 18, "maxLevel": 200}, {"id": 53, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "dex_p", "min_val": 10, "max_val": 18, "maxLevel": 200}, {"id": 54, "qua": 5, "kind": 2, "blevel": 0, "bonly": 1, "key": "dex_p", "min_val": 12, "max_val": 12, "maxLevel": 200}, {"id": 55, "qua": 5, "kind": 1, "blevel": 0, "bonly": 1, "key": "red", "min_val": 5, "max_val": 12, "maxLevel": 200}, {"id": 56, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "red", "min_val": 10, "max_val": 18, "maxLevel": 200}, {"id": 57, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "dmg_p", "min_val": 12, "max_val": 20, "maxLevel": 200}, {"id": 58, "qua": 6, "kind": 2, "blevel": 0, "bonly": 1, "key": "sk", "min_val": 4757, "max_val": 4757, "maxLevel": 200}, {"id": 59, "qua": 6, "kind": 1, "blevel": 0, "bonly": 1, "key": "pkl_p", "min_val": 18, "max_val": 30, "maxLevel": 200}]