2025-08-05 20:33:38,514 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,285 - models - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,317 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,696 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,707 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,718 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,732 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:39,747 - auth - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,294 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,349 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,374 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,385 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,396 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,397 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: b0095fa1)
2025-08-05 20:33:42,407 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,439 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,449 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,458 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,466 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,546 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,559 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,570 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,581 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,590 - game_server - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:42,592 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 20:33:42,593 - msgManager - INFO - Monster handlers registered
2025-08-05 20:33:42,593 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 20:33:42,595 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 20:33:42,606 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 20:33:42,608 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 20:33:42,610 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 67c73ac3)
2025-08-05 20:33:42,659 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 20:33:42,725 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 20:33:42,727 - game_server - INFO - 模板引擎初始化成功
2025-08-05 20:33:42,732 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:33:42,733 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 41220)
2025-08-05 20:33:42,734 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 20:33:42,908 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:33:42,911 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 20:33:43,408 - ConnectionManager - INFO - 后台任务已启动 (Worker 41220)
2025-08-05 20:33:43,411 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 41220)
2025-08-05 20:33:43,422 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:43,423 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:43,423 - game_server - INFO - 游戏配置加载完成 (Worker: 41220)
2025-08-05 20:33:43,424 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:33:47,822 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:33:47,823 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=0, 总计=2
2025-08-05 20:33:47,823 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 41220): 2/2 (100.0%)
2025-08-05 20:33:47,824 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:33:47,871 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 67c73ac3)
2025-08-05 20:33:47,871 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 67c73ac3)
2025-08-05 20:33:47,878 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:47,879 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 20:33:47,880 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 20:33:47,890 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:47,891 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 20:33:47,891 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:47,892 - ConnectionManager - INFO - Worker 41220 开始消费广播消息，消费者标签: ctag1.91e63fdf33bf467bb79965ecd70221e4
2025-08-05 20:33:47,940 - ConnectionManager - INFO - Worker 41220 开始消费个人消息，消费者标签: ctag1.f1627d995854481ca2d6562f5a429137
2025-08-05 20:33:48,027 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 41220)
2025-08-05 20:33:48,098 - distributed_lock - INFO - Worker 41220 获取锁超时: scheduler_initialization
2025-08-05 20:33:48,098 - game_server_scheduler_integration - INFO - Worker 41220 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 20:33:48,098 - game_server - INFO - 统一调度器初始化成功 (Worker: 41220)
2025-08-05 20:33:48,106 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 41220)
2025-08-05 20:33:48,106 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 20:33:48,107 - game_server - INFO - 日志清理管理器已启动 (Worker: 41220)
2025-08-05 20:33:48,108 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:33:48,108 - game_server - INFO - Monster cooldown manager initialized (Worker: 41220)
2025-08-05 20:33:48,446 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 20:33:48,535 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:33:48,535 - game_server - INFO - 公会系统初始化成功 (Worker: 41220)
2025-08-05 20:33:48,535 - game_server - INFO - 邮件系统初始化成功 (Worker: 41220)
2025-08-05 20:33:48,537 - game_server - INFO - 商店系统初始化成功 (Worker: 41220)
2025-08-05 20:33:48,538 - game_server - INFO - 初始化完成 (Worker: 41220)
2025-08-05 20:34:00,909 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:34:13,430 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:34:30,094 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:34:43,432 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:34:47,825 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:34:47,826 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:34:49,816 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:34:49,816 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:35:00,312 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:35:13,444 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:35:30,494 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:35:37,538 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost closed. Reconnecting after 5 seconds.
2025-08-05 20:35:43,450 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:35:47,836 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:35:47,839 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:35:48,920 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:35:48,922 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:36:00,687 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:36:13,464 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:36:30,915 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:36:43,468 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:36:47,852 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:36:47,853 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:36:48,989 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:36:48,990 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:37:00,071 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:37:13,471 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:37:30,275 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:37:43,479 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:37:47,861 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:37:47,862 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:37:48,921 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:37:48,921 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:38:00,493 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:38:13,492 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:38:30,682 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:38:43,507 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:38:47,869 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:38:47,870 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:38:48,919 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:38:48,920 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:39:00,897 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:39:13,509 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:39:30,102 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:39:43,513 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:39:47,871 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:39:47,872 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:39:48,991 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:39:48,991 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:40:06,348 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:40:13,528 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:40:30,618 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:40:43,539 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:40:47,883 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:40:47,884 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:40:48,920 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:40:48,923 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:41:00,841 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:41:13,542 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:41:30,053 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:41:43,560 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:41:47,886 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:41:47,887 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:41:48,920 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:41:48,920 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:42:00,262 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:42:13,568 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:42:30,439 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:42:43,572 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:42:47,902 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:42:47,904 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:42:48,945 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:42:48,945 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:43:00,653 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:43:13,580 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:43:30,862 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:43:43,589 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:43:47,917 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:43:47,918 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:43:48,894 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:43:48,894 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:44:00,049 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:44:13,603 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:44:30,265 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:44:43,607 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:44:47,922 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:44:47,925 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:44:48,992 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:44:48,995 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:45:00,469 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:45:13,610 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:45:30,658 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:45:43,625 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:45:47,935 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:45:47,935 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:45:48,921 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:45:48,921 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:46:00,823 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:46:13,640 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:46:30,033 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:46:43,645 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:46:47,943 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:46:47,943 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:46:49,007 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:46:49,007 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:47:00,246 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:47:13,652 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:47:30,469 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:47:43,663 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:47:47,957 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:47:47,958 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:47:48,906 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:47:48,906 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:00,640 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:48:13,668 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:48:30,872 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:48:43,676 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:48:44,989 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-55' coro=<OneShotCallback.__task_inner() done, defined at D:\python_evns\hj\Lib\site-packages\aio_pika\tools.py:300> exception=ChannelInvalidStateError('No active transport in channel')>
Traceback (most recent call last):
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\tools.py", line 306, in __task_inner
    await self.callback(*args, **kwargs)
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\robust_channel.py", line 119, in _on_close
    await self.restore()
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\robust_channel.py", line 94, in restore
    await self.reopen()
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\robust_channel.py", line 132, in reopen
    await super().reopen()
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\channel.py", line 244, in reopen
    await self._open()
  File "D:\python_evns\hj\Lib\site-packages\aio_pika\channel.py", line 169, in _open
    raise ChannelInvalidStateError("No active transport in channel")
aiormq.exceptions.ChannelInvalidStateError: No active transport in channel
2025-08-05 20:48:47,964 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=3, 可用=0, 总计=3
2025-08-05 20:48:47,964 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 41220): 3/3 (100.0%)
2025-08-05 20:48:47,965 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:48:48,913 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:48:48,914 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:50,015 - distributed_lock - ERROR - 获取锁时发生错误: shop:create:342, 错误: Timeout reading from *************:6379
2025-08-05 20:48:57,262 - shop_service - ERROR - 创建商店时发生错误: Failed to acquire lock: shop:create:342
2025-08-05 20:49:00,276 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:49:13,691 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:49:30,593 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:49:44,490 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:49:47,976 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:49:47,978 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:49:49,056 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:49:49,057 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:50:01,002 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:50:14,502 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:50:30,837 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:50:44,510 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:50:48,006 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:50:48,006 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:50:48,943 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:50:48,945 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:51:00,065 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:51:14,512 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:51:30,275 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:51:44,513 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:51:48,019 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:51:48,020 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:51:48,911 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:51:48,912 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:52:00,475 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:52:14,520 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:52:30,672 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:52:44,524 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:52:48,032 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:52:48,033 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:52:48,979 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:52:48,979 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:53:00,912 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:53:14,540 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:53:30,100 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:53:44,556 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:53:48,035 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:53:48,036 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:53:48,943 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:53:48,943 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:54:00,335 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:54:14,566 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:54:30,540 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:54:44,580 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:54:48,053 - ConnectionManager - INFO - Redis连接池状态 (Worker 41220): 使用中=2, 可用=1, 总计=3
2025-08-05 20:54:48,054 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41220): 连接中
2025-08-05 20:54:48,899 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:54:48,900 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:55:00,702 - ConnectionManager - INFO - 连接状态 (Worker 41220): 活跃连接数=0, 用户数=0
2025-08-05 20:55:04,527 - game_server - INFO - 关闭服务器... (Worker: 41220)
2025-08-05 20:55:04,528 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 20:55:09,544 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 20:55:09,545 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:55:09,724 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:55:10,129 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:55:10,129 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 20:55:10,130 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:10,132 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:10,133 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 20:55:10,133 - game_server - INFO - 日志清理管理器已停止
2025-08-05 20:55:10,134 - game_server - INFO - 统一调度器已关闭
2025-08-05 20:55:10,135 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 41220)
2025-08-05 20:55:10,179 - game_server - INFO - 服务器资源已清理 (Worker: 41220)
