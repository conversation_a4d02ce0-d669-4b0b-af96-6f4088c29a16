2025-08-06 08:43:57,304 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:00,585 - models - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:00,838 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:02,640 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:02,667 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:02,731 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:02,751 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:02,768 - auth - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,185 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,312 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,467 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,553 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,571 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,573 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 9e536ee2)
2025-08-06 08:44:06,589 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,655 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,710 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,724 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,741 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:06,974 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:07,009 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:07,021 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:07,033 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:07,046 - game_server - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:07,047 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-06 08:44:07,047 - msgManager - INFO - Monster handlers registered
2025-08-06 08:44:07,048 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-06 08:44:07,062 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-06 08:44:07,133 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-06 08:44:07,133 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-06 08:44:07,135 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: cd84f907)
2025-08-06 08:44:07,286 - game_server - INFO - 邮件管理API路由注册成功
2025-08-06 08:44:07,350 - game_server - INFO - 商店系统API路由注册成功
2025-08-06 08:44:07,350 - game_server - INFO - 模板引擎初始化成功
2025-08-06 08:44:07,352 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-06 08:44:07,353 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 24640)
2025-08-06 08:44:07,354 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-06 08:44:07,515 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-06 08:44:07,539 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-06 08:44:07,980 - ConnectionManager - INFO - 后台任务已启动 (Worker 24640)
2025-08-06 08:44:07,980 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 24640)
2025-08-06 08:44:08,002 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-06 08:44:08,003 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-06 08:44:08,003 - game_server - INFO - 游戏配置加载完成 (Worker: 24640)
2025-08-06 08:44:08,003 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:44:11,067 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-06 08:44:11,068 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=0, 总计=2
2025-08-06 08:44:11,068 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 24640): 2/2 (100.0%)
2025-08-06 08:44:11,069 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:44:11,111 - ConnectionManager - INFO - Worker 24640 开始消费广播消息，消费者标签: ctag1.5682fc09756842f584e9fe3b660cb479
2025-08-06 08:44:11,111 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: cd84f907)
2025-08-06 08:44:11,112 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: cd84f907)
2025-08-06 08:44:11,132 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:11,134 - game_server - INFO - 调度器监控API路由已注册
2025-08-06 08:44:11,135 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-06 08:44:11,143 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-06 08:44:11,144 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-06 08:44:11,144 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-06 08:44:11,178 - ConnectionManager - INFO - Worker 24640 开始消费个人消息，消费者标签: ctag1.db5ddc4e8bd64034a5ae0c2e9d1124e8
2025-08-06 08:44:11,277 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 24640)
2025-08-06 08:44:11,368 - distributed_lock - INFO - Worker 24640 成功获取锁: scheduler_initialization
2025-08-06 08:44:11,369 - game_server_scheduler_integration - INFO - Worker 24640 获得调度器初始化权限
2025-08-06 08:44:11,407 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-06 08:44:11,408 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-06 08:44:11,408 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-06 08:44:11,409 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-06 08:44:11,409 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-06 08:44:11,410 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-06 08:44:11,410 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-06 08:44:11,410 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-06 08:44:11,411 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-06 08:44:11,411 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-06 08:44:11,417 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-06 08:44:11,418 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-06 08:44:11,418 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-06 08:44:11,419 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-06 08:44:11,419 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-06 08:44:11,419 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-06 08:44:11,504 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-06 08:44:11,504 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-06 08:44:11,505 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-06 08:44:11,505 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-06 08:44:11,933 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-06 08:44:12,026 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-06 08:44:12,026 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-06 08:44:12,027 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-06 08:44:12,028 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-06 08:44:12,028 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-06 08:44:12,029 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-06 08:44:12,118 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-06 08:44:12,252 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-06 08:44:12,252 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-06 08:44:12,253 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-06 08:44:12,253 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-06 08:44:12,254 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-06 08:44:12,254 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-06 08:44:12,259 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,260 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-06 08:44:12,260 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-06 08:44:12,261 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,262 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-06 08:44:12,262 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-06 08:44:12,391 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,391 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-06 08:44:12,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-06 08:44:12,530 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,530 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-06 08:44:12,531 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-06 08:44:12,531 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,532 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-06 08:44:12,532 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-06 08:44:12,533 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-06 08:44:12,534 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-06 08:44:12,534 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-06 08:44:12,534 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-06 08:44:12,552 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,552 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,553 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,554 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,554 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,555 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-06 08:44:12,555 - apscheduler.scheduler - INFO - Scheduler started
2025-08-06 08:44:12,556 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-06 08:44:12,557 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-06 08:44:12,558 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-06 08:44:12,560 - game_server_scheduler_integration - INFO - Worker 24640 调度器初始化成功
2025-08-06 08:44:12,607 - game_server - INFO - 统一调度器初始化成功 (Worker: 24640)
2025-08-06 08:44:12,619 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 24640)
2025-08-06 08:44:12,620 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-06 08:44:12,620 - game_server - INFO - 日志清理管理器已启动 (Worker: 24640)
2025-08-06 08:44:12,621 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-06 08:44:12,621 - game_server - INFO - Monster cooldown manager initialized (Worker: 24640)
2025-08-06 08:44:12,768 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-06 08:44:12,769 - game_server - INFO - 公会系统初始化成功 (Worker: 24640)
2025-08-06 08:44:12,769 - game_server - INFO - 邮件系统初始化成功 (Worker: 24640)
2025-08-06 08:44:12,771 - game_server - INFO - 商店系统初始化成功 (Worker: 24640)
2025-08-06 08:44:12,771 - game_server - INFO - 初始化完成 (Worker: 24640)
2025-08-06 08:44:22,556 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:32 CST)" (scheduled at 2025-08-06 08:44:22.530007+08:00)
2025-08-06 08:44:22,599 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:44:22,600 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:44:22,726 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:44:23,063 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:44:23,064 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:44:23,064 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:44:23,065 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:44:23,108 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:32 CST)" executed successfully
2025-08-06 08:44:30,189 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:44:32,540 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:42 CST)" (scheduled at 2025-08-06 08:44:32.530007+08:00)
2025-08-06 08:44:32,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:44:32,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:44:32,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:44:33,052 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:44:33,052 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:44:33,053 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:44:33,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:44:33,099 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:42 CST)" executed successfully
2025-08-06 08:44:38,018 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:44:42,392 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:45:12 CST)" (scheduled at 2025-08-06 08:44:42.391016+08:00)
2025-08-06 08:44:42,435 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:44:42,435 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:44:42,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:52 CST)" (scheduled at 2025-08-06 08:44:42.530007+08:00)
2025-08-06 08:44:42,560 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:44:42,588 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:44:42,588 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:44:42,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:44:42,903 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:44:42,903 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:44:42,904 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:44:42,904 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:44:42,905 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:44:42,948 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:45:12 CST)" executed successfully
2025-08-06 08:44:43,062 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:44:43,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:44:43,063 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:44:43,064 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:44:43,110 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:44:52 CST)" executed successfully
2025-08-06 08:44:52,538 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:02 CST)" (scheduled at 2025-08-06 08:44:52.530007+08:00)
2025-08-06 08:44:52,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:44:52,582 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:44:52,712 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:44:53,053 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:44:53,053 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:44:53,053 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:44:53,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:44:53,097 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:02 CST)" executed successfully
2025-08-06 08:45:00,425 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:45:02,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:12 CST)" (scheduled at 2025-08-06 08:45:02.530007+08:00)
2025-08-06 08:45:02,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:02,583 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:02,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:03,065 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:03,066 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:45:03,071 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:03,072 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:03,115 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:12 CST)" executed successfully
2025-08-06 08:45:08,022 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:45:11,073 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:45:11,073 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:45:12,265 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:46:12 CST)" (scheduled at 2025-08-06 08:45:12.261883+08:00)
2025-08-06 08:45:12,308 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:45:12,309 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:12,310 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:45:12,311 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-06 08:45:12,396 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:45:42 CST)" (scheduled at 2025-08-06 08:45:12.391016+08:00)
2025-08-06 08:45:12,440 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:45:12,441 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:12,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:22 CST)" (scheduled at 2025-08-06 08:45:12.530007+08:00)
2025-08-06 08:45:12,569 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:45:12,583 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:12,583 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:12,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:12,913 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:45:12,914 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:45:12,914 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:45:12,915 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:45:12,915 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:12,960 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:45:42 CST)" executed successfully
2025-08-06 08:45:13,063 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:13,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:45:13,065 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:13,065 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:13,109 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:22 CST)" executed successfully
2025-08-06 08:45:13,493 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:45:13,493 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:45:13,499 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:45:13,500 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.19秒
2025-08-06 08:45:13,500 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:45:13,500 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:13,543 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:46:12 CST)" executed successfully
2025-08-06 08:45:22,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:32 CST)" (scheduled at 2025-08-06 08:45:22.530007+08:00)
2025-08-06 08:45:22,585 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:22,585 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:22,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:23,063 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:23,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:45:23,064 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:23,065 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:23,110 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:32 CST)" executed successfully
2025-08-06 08:45:30,607 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:45:32,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:42 CST)" (scheduled at 2025-08-06 08:45:32.530007+08:00)
2025-08-06 08:45:32,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:32,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:32,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:33,051 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:33,051 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:45:33,052 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:33,053 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:33,095 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:42 CST)" executed successfully
2025-08-06 08:45:38,028 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:45:42,402 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:46:12 CST)" (scheduled at 2025-08-06 08:45:42.391016+08:00)
2025-08-06 08:45:42,445 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:45:42,445 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:42,530 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:52 CST)" (scheduled at 2025-08-06 08:45:42.530007+08:00)
2025-08-06 08:45:42,572 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:45:42,574 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:42,574 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:42,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:42,911 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:45:42,912 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:45:42,912 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:45:42,912 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:45:42,914 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:42,957 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:46:12 CST)" executed successfully
2025-08-06 08:45:43,054 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:43,054 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:45:43,055 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:43,055 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:43,099 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:45:52 CST)" executed successfully
2025-08-06 08:45:52,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:02 CST)" (scheduled at 2025-08-06 08:45:52.530007+08:00)
2025-08-06 08:45:52,583 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:45:52,583 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:45:52,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:45:53,053 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:45:53,053 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:45:53,054 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:45:53,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:45:53,098 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:02 CST)" executed successfully
2025-08-06 08:46:00,845 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:46:02,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:12 CST)" (scheduled at 2025-08-06 08:46:02.530007+08:00)
2025-08-06 08:46:02,585 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:02,585 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:02,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:03,064 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:03,065 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:46:03,065 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:03,065 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:03,109 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:12 CST)" executed successfully
2025-08-06 08:46:08,040 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:46:11,075 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:46:11,075 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:46:12,275 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:47:12 CST)" (scheduled at 2025-08-06 08:46:12.261883+08:00)
2025-08-06 08:46:12,318 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:46:12,318 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:12,319 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:46:12,393 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:46:42 CST)" (scheduled at 2025-08-06 08:46:12.391016+08:00)
2025-08-06 08:46:12,436 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:46:12,436 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:12,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:22 CST)" (scheduled at 2025-08-06 08:46:12.530007+08:00)
2025-08-06 08:46:12,547 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:46:12,548 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:46:12,567 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:46:12,586 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:12,586 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:12,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:12,913 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:46:12,916 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:46:12,923 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-06 08:46:12,924 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:46:12,924 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:12,968 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:46:42 CST)" executed successfully
2025-08-06 08:46:13,049 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:13,050 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:46:13,050 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:13,051 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:13,094 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:22 CST)" executed successfully
2025-08-06 08:46:13,748 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:46:13,748 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.43秒
2025-08-06 08:46:13,749 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:46:13,749 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:13,792 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:47:12 CST)" executed successfully
2025-08-06 08:46:22,542 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:32 CST)" (scheduled at 2025-08-06 08:46:22.530007+08:00)
2025-08-06 08:46:22,584 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:22,585 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:22,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:23,056 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:23,057 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:46:23,057 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:23,058 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:23,102 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:32 CST)" executed successfully
2025-08-06 08:46:30,043 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:46:32,541 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:42 CST)" (scheduled at 2025-08-06 08:46:32.530007+08:00)
2025-08-06 08:46:32,586 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:32,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:32,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:33,065 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:33,066 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:46:33,066 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:33,067 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:33,111 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:42 CST)" executed successfully
2025-08-06 08:46:38,042 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:46:42,402 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:47:12 CST)" (scheduled at 2025-08-06 08:46:42.391016+08:00)
2025-08-06 08:46:42,447 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:46:42,447 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:42,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:52 CST)" (scheduled at 2025-08-06 08:46:42.530007+08:00)
2025-08-06 08:46:42,575 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:46:42,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:42,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:42,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:42,924 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:46:42,926 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:46:42,931 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-06 08:46:42,932 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:46:42,933 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:42,976 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:47:12 CST)" executed successfully
2025-08-06 08:46:43,063 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:43,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:46:43,063 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:43,064 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:43,110 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:46:52 CST)" executed successfully
2025-08-06 08:46:52,543 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:02 CST)" (scheduled at 2025-08-06 08:46:52.530007+08:00)
2025-08-06 08:46:52,585 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:46:52,586 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:46:52,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:46:53,066 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:46:53,067 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:46:53,067 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:46:53,068 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:46:53,116 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:02 CST)" executed successfully
2025-08-06 08:47:00,260 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:47:02,542 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:12 CST)" (scheduled at 2025-08-06 08:47:02.530007+08:00)
2025-08-06 08:47:02,585 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:02,586 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:02,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:03,071 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:03,071 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:47:03,072 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:03,072 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:03,116 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:12 CST)" executed successfully
2025-08-06 08:47:08,058 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:47:11,076 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:47:11,077 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:47:12,276 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:48:12 CST)" (scheduled at 2025-08-06 08:47:12.261883+08:00)
2025-08-06 08:47:12,319 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:47:12,320 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:12,320 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:47:12,392 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:47:42 CST)" (scheduled at 2025-08-06 08:47:12.391016+08:00)
2025-08-06 08:47:12,436 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:47:12,436 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:12,542 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:22 CST)" (scheduled at 2025-08-06 08:47:12.530007+08:00)
2025-08-06 08:47:12,565 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:47:12,565 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:47:12,566 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:47:12,586 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:12,586 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:12,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:12,918 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:47:12,919 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:47:12,919 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:47:12,920 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:47:12,920 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:12,964 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:47:42 CST)" executed successfully
2025-08-06 08:47:13,062 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:13,062 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:47:13,062 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:13,063 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:13,107 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:22 CST)" executed successfully
2025-08-06 08:47:13,706 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:47:13,706 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.39秒
2025-08-06 08:47:13,706 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:47:13,707 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:13,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:48:12 CST)" executed successfully
2025-08-06 08:47:22,542 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:32 CST)" (scheduled at 2025-08-06 08:47:22.530007+08:00)
2025-08-06 08:47:22,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:22,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:22,725 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:23,067 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:23,067 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:47:23,068 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:23,068 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:23,113 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:32 CST)" executed successfully
2025-08-06 08:47:30,508 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:47:32,543 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:42 CST)" (scheduled at 2025-08-06 08:47:32.530007+08:00)
2025-08-06 08:47:32,586 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:32,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:32,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:33,057 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:33,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:47:33,059 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:33,061 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:33,104 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:42 CST)" executed successfully
2025-08-06 08:47:38,059 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:47:42,392 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:48:12 CST)" (scheduled at 2025-08-06 08:47:42.391016+08:00)
2025-08-06 08:47:42,436 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:47:42,437 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:42,543 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:52 CST)" (scheduled at 2025-08-06 08:47:42.530007+08:00)
2025-08-06 08:47:42,570 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:47:42,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:42,587 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:42,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:42,915 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:47:42,915 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:47:42,915 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:47:42,916 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:47:42,916 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:42,959 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:48:12 CST)" executed successfully
2025-08-06 08:47:43,067 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:43,068 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:47:43,068 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:43,069 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:43,112 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:47:52 CST)" executed successfully
2025-08-06 08:47:52,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:02 CST)" (scheduled at 2025-08-06 08:47:52.530007+08:00)
2025-08-06 08:47:52,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:47:52,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:47:52,712 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:47:53,065 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:47:53,066 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:47:53,066 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:47:53,067 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:47:53,111 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:02 CST)" executed successfully
2025-08-06 08:48:00,710 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:48:02,544 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:12 CST)" (scheduled at 2025-08-06 08:48:02.530007+08:00)
2025-08-06 08:48:02,589 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:02,590 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:02,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:03,073 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:03,073 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:48:03,074 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:03,074 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:03,118 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:12 CST)" executed successfully
2025-08-06 08:48:08,061 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:48:11,078 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:48:11,079 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:48:12,262 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:49:12 CST)" (scheduled at 2025-08-06 08:48:12.261883+08:00)
2025-08-06 08:48:12,305 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:48:12,306 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:12,306 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:48:12,395 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:48:42 CST)" (scheduled at 2025-08-06 08:48:12.391016+08:00)
2025-08-06 08:48:12,440 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:48:12,441 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:12,524 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:48:12,525 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:48:12,530 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:22 CST)" (scheduled at 2025-08-06 08:48:12.530007+08:00)
2025-08-06 08:48:12,574 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:48:12,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:12,577 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:12,708 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:12,932 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:48:12,932 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:48:12,933 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-06 08:48:12,933 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:48:12,933 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:12,980 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:48:42 CST)" executed successfully
2025-08-06 08:48:13,056 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:13,056 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:48:13,057 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:13,057 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:13,102 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:22 CST)" executed successfully
2025-08-06 08:48:13,814 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:48:13,814 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.51秒
2025-08-06 08:48:13,814 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:48:13,815 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:13,860 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:49:12 CST)" executed successfully
2025-08-06 08:48:22,545 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:32 CST)" (scheduled at 2025-08-06 08:48:22.530007+08:00)
2025-08-06 08:48:22,593 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:22,594 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:22,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:23,060 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:23,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:48:23,065 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:23,067 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:23,116 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:32 CST)" executed successfully
2025-08-06 08:48:30,943 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:48:32,530 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:42 CST)" (scheduled at 2025-08-06 08:48:32.530007+08:00)
2025-08-06 08:48:32,574 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:32,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:32,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:33,058 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:33,059 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:48:33,059 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:33,059 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:33,105 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:42 CST)" executed successfully
2025-08-06 08:48:38,071 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:48:42,396 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:49:12 CST)" (scheduled at 2025-08-06 08:48:42.391016+08:00)
2025-08-06 08:48:42,440 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:48:42,440 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:42,545 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:52 CST)" (scheduled at 2025-08-06 08:48:42.530007+08:00)
2025-08-06 08:48:42,567 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:48:42,591 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:42,591 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:42,726 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:42,901 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:48:42,901 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:48:42,902 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:48:42,902 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:48:42,903 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:42,950 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:49:12 CST)" executed successfully
2025-08-06 08:48:43,075 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:43,076 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:48:43,077 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:43,078 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:43,123 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:48:52 CST)" executed successfully
2025-08-06 08:48:52,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:02 CST)" (scheduled at 2025-08-06 08:48:52.530007+08:00)
2025-08-06 08:48:52,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:48:52,582 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:48:52,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:48:53,058 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:48:53,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:48:53,058 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:48:53,059 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:48:53,103 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:02 CST)" executed successfully
2025-08-06 08:49:00,094 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:49:02,545 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:12 CST)" (scheduled at 2025-08-06 08:49:02.530007+08:00)
2025-08-06 08:49:02,593 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:02,594 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:02,725 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:03,073 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:03,073 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:49:03,074 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:03,074 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:03,117 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:12 CST)" executed successfully
2025-08-06 08:49:08,078 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:49:11,081 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:49:11,083 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:49:12,264 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:50:12 CST)" (scheduled at 2025-08-06 08:49:12.261883+08:00)
2025-08-06 08:49:12,310 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:49:12,311 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:12,311 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:49:12,393 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:49:42 CST)" (scheduled at 2025-08-06 08:49:12.391016+08:00)
2025-08-06 08:49:12,437 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:49:12,437 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:12,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:22 CST)" (scheduled at 2025-08-06 08:49:12.530007+08:00)
2025-08-06 08:49:12,568 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:49:12,574 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:49:12,574 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:49:12,588 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:12,588 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:12,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:12,920 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:49:12,921 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:49:12,921 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:49:12,922 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:49:12,922 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:12,966 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:49:42 CST)" executed successfully
2025-08-06 08:49:13,056 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:13,056 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:49:13,057 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:13,057 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:13,100 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:22 CST)" executed successfully
2025-08-06 08:49:13,490 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:49:13,491 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.18秒
2025-08-06 08:49:13,491 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:49:13,492 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:13,541 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:50:12 CST)" executed successfully
2025-08-06 08:49:22,530 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:32 CST)" (scheduled at 2025-08-06 08:49:22.530007+08:00)
2025-08-06 08:49:22,573 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:22,573 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:22,701 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:23,043 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:23,044 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:49:23,045 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:23,046 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:23,092 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:32 CST)" executed successfully
2025-08-06 08:49:30,311 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:49:32,546 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:42 CST)" (scheduled at 2025-08-06 08:49:32.530007+08:00)
2025-08-06 08:49:32,589 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:32,590 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:32,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:33,094 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:33,094 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-06 08:49:33,094 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:33,096 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:33,139 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:42 CST)" executed successfully
2025-08-06 08:49:38,080 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:49:42,391 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:50:12 CST)" (scheduled at 2025-08-06 08:49:42.391016+08:00)
2025-08-06 08:49:42,434 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:49:42,434 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:42,547 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:52 CST)" (scheduled at 2025-08-06 08:49:42.530007+08:00)
2025-08-06 08:49:42,563 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:49:42,592 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:42,593 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:42,724 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:42,906 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:49:42,906 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:49:42,906 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:49:42,907 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:49:42,907 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:42,949 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:50:12 CST)" executed successfully
2025-08-06 08:49:43,075 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:43,076 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:49:43,076 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:43,077 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:43,123 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:49:52 CST)" executed successfully
2025-08-06 08:49:52,534 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:02 CST)" (scheduled at 2025-08-06 08:49:52.530007+08:00)
2025-08-06 08:49:52,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:49:52,582 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:49:52,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:49:53,058 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:49:53,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:49:53,059 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:49:53,059 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:49:53,103 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:02 CST)" executed successfully
2025-08-06 08:50:00,555 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:50:02,545 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:12 CST)" (scheduled at 2025-08-06 08:50:02.530007+08:00)
2025-08-06 08:50:02,591 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:02,592 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:02,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:03,068 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:03,068 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:50:03,069 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:03,069 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:03,114 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:12 CST)" executed successfully
2025-08-06 08:50:08,083 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:50:11,101 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:50:11,102 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:50:12,265 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:51:12 CST)" (scheduled at 2025-08-06 08:50:12.261883+08:00)
2025-08-06 08:50:12,313 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:50:12,314 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:12,314 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:50:12,396 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:50:42 CST)" (scheduled at 2025-08-06 08:50:12.391016+08:00)
2025-08-06 08:50:12,441 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:50:12,441 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:12,517 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:50:12,517 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:50:12,533 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:22 CST)" (scheduled at 2025-08-06 08:50:12.530007+08:00)
2025-08-06 08:50:12,576 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:50:12,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:12,577 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:12,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:12,930 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:50:12,930 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:50:12,930 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:50:12,932 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:50:12,932 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:12,976 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:50:42 CST)" executed successfully
2025-08-06 08:50:13,056 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:13,056 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:50:13,057 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:13,057 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:13,102 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:22 CST)" executed successfully
2025-08-06 08:50:14,642 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:50:14,643 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.33秒
2025-08-06 08:50:14,644 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:50:14,644 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:14,690 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:51:12 CST)" executed successfully
2025-08-06 08:50:22,531 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:32 CST)" (scheduled at 2025-08-06 08:50:22.530007+08:00)
2025-08-06 08:50:22,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:22,576 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:22,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:23,047 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:23,048 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:50:23,049 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:23,050 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:23,093 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:32 CST)" executed successfully
2025-08-06 08:50:30,114 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:50:32,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:42 CST)" (scheduled at 2025-08-06 08:50:32.530007+08:00)
2025-08-06 08:50:32,580 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:32,581 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:32,715 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:33,059 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:33,059 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:50:33,060 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:33,060 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:33,104 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:42 CST)" executed successfully
2025-08-06 08:50:38,096 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:50:42,397 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:51:12 CST)" (scheduled at 2025-08-06 08:50:42.391016+08:00)
2025-08-06 08:50:42,441 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:50:42,442 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:42,531 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:52 CST)" (scheduled at 2025-08-06 08:50:42.530007+08:00)
2025-08-06 08:50:42,570 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:50:42,578 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:42,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:42,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:42,914 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:50:42,915 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:50:42,915 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:50:42,916 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:50:42,917 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:42,960 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:51:12 CST)" executed successfully
2025-08-06 08:50:43,038 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:43,039 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-06 08:50:43,039 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:43,040 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:43,086 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:50:52 CST)" executed successfully
2025-08-06 08:50:52,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:02 CST)" (scheduled at 2025-08-06 08:50:52.530007+08:00)
2025-08-06 08:50:52,578 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:50:52,579 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:50:52,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:50:53,053 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:50:53,053 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:50:53,055 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:50:53,055 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:50:53,099 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:02 CST)" executed successfully
2025-08-06 08:51:00,298 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:51:02,544 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:12 CST)" (scheduled at 2025-08-06 08:51:02.530007+08:00)
2025-08-06 08:51:02,588 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:02,588 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:02,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:03,061 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:03,061 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:51:03,061 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:03,062 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:03,104 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:12 CST)" executed successfully
2025-08-06 08:51:08,099 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:51:11,115 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:51:11,118 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:51:12,266 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:52:12 CST)" (scheduled at 2025-08-06 08:51:12.261883+08:00)
2025-08-06 08:51:12,309 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:51:12,309 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:12,309 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:51:12,398 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:51:42 CST)" (scheduled at 2025-08-06 08:51:12.391016+08:00)
2025-08-06 08:51:12,440 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:51:12,440 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:12,531 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:22 CST)" (scheduled at 2025-08-06 08:51:12.530007+08:00)
2025-08-06 08:51:12,546 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:51:12,547 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:51:12,569 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:51:12,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:12,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:12,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:12,904 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:51:12,904 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:51:12,905 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:51:12,905 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:51:12,906 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:12,950 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:51:42 CST)" executed successfully
2025-08-06 08:51:13,045 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:13,046 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:51:13,047 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:13,047 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:13,092 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:22 CST)" executed successfully
2025-08-06 08:51:13,713 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:51:13,713 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.40秒
2025-08-06 08:51:13,714 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:51:13,714 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:13,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:52:12 CST)" executed successfully
2025-08-06 08:51:22,531 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:32 CST)" (scheduled at 2025-08-06 08:51:22.530007+08:00)
2025-08-06 08:51:22,577 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:22,577 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:22,708 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:23,059 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:23,060 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:51:23,063 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:23,065 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:23,111 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:32 CST)" executed successfully
2025-08-06 08:51:30,533 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:51:32,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:42 CST)" (scheduled at 2025-08-06 08:51:32.530007+08:00)
2025-08-06 08:51:32,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:32,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:32,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:33,069 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:33,070 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:51:33,071 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:33,071 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:33,119 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:42 CST)" executed successfully
2025-08-06 08:51:38,114 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:51:42,400 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:52:12 CST)" (scheduled at 2025-08-06 08:51:42.391016+08:00)
2025-08-06 08:51:42,443 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:51:42,443 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:42,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:52 CST)" (scheduled at 2025-08-06 08:51:42.530007+08:00)
2025-08-06 08:51:42,571 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:51:42,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:42,576 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:42,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:42,921 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:51:42,921 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:51:42,922 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:51:42,922 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:51:42,922 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:42,967 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:52:12 CST)" executed successfully
2025-08-06 08:51:43,045 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:43,045 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:51:43,046 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:43,046 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:43,089 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:51:52 CST)" executed successfully
2025-08-06 08:51:52,531 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:02 CST)" (scheduled at 2025-08-06 08:51:52.530007+08:00)
2025-08-06 08:51:52,574 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:51:52,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:51:52,707 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:51:53,046 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:51:53,047 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:51:53,049 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:51:53,053 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:51:53,099 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:02 CST)" executed successfully
2025-08-06 08:52:00,749 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:52:02,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:12 CST)" (scheduled at 2025-08-06 08:52:02.530007+08:00)
2025-08-06 08:52:02,575 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:02,575 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:02,703 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:03,056 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:03,056 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:52:03,057 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:03,057 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:03,103 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:12 CST)" executed successfully
2025-08-06 08:52:08,117 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:52:11,134 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:52:11,134 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:52:12,266 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:53:12 CST)" (scheduled at 2025-08-06 08:52:12.261883+08:00)
2025-08-06 08:52:12,310 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:52:12,311 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:12,313 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:52:12,393 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:52:42 CST)" (scheduled at 2025-08-06 08:52:12.391016+08:00)
2025-08-06 08:52:12,436 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:52:12,437 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:12,521 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:52:12,522 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:52:12,533 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:22 CST)" (scheduled at 2025-08-06 08:52:12.530007+08:00)
2025-08-06 08:52:12,568 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:52:12,580 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:12,580 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:12,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:12,910 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:52:12,911 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:52:12,911 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:52:12,912 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:52:12,912 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:12,959 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:52:42 CST)" executed successfully
2025-08-06 08:52:13,063 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:13,063 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:52:13,064 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:13,064 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:13,109 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:22 CST)" executed successfully
2025-08-06 08:52:13,723 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:52:13,723 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.41秒
2025-08-06 08:52:13,723 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:52:13,724 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:13,768 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:53:12 CST)" executed successfully
2025-08-06 08:52:22,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:32 CST)" (scheduled at 2025-08-06 08:52:22.530007+08:00)
2025-08-06 08:52:22,579 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:22,580 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:22,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:23,061 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:23,062 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:52:23,062 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:23,063 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:23,107 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:32 CST)" executed successfully
2025-08-06 08:52:30,968 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:52:32,534 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:42 CST)" (scheduled at 2025-08-06 08:52:32.530007+08:00)
2025-08-06 08:52:32,579 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:32,579 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:32,708 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:33,053 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:33,053 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:52:33,054 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:33,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:33,098 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:42 CST)" executed successfully
2025-08-06 08:52:38,120 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:52:42,401 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:53:12 CST)" (scheduled at 2025-08-06 08:52:42.391016+08:00)
2025-08-06 08:52:42,445 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:52:42,446 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:42,533 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:52 CST)" (scheduled at 2025-08-06 08:52:42.530007+08:00)
2025-08-06 08:52:42,577 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:42,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:42,578 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:52:42,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:42,918 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:52:42,918 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:52:42,919 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-06 08:52:42,919 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:52:42,920 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:42,966 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:53:12 CST)" executed successfully
2025-08-06 08:52:43,046 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:43,047 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:52:43,047 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:43,047 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:43,090 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:52:52 CST)" executed successfully
2025-08-06 08:52:52,533 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:02 CST)" (scheduled at 2025-08-06 08:52:52.530007+08:00)
2025-08-06 08:52:52,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:52:52,577 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:52:52,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:52:53,050 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:52:53,050 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:52:53,051 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:52:53,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:52:53,100 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:02 CST)" executed successfully
2025-08-06 08:53:00,218 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:53:02,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:12 CST)" (scheduled at 2025-08-06 08:53:02.530007+08:00)
2025-08-06 08:53:02,579 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:02,579 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:02,715 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:03,315 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:03,318 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.60秒
2025-08-06 08:53:03,326 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:03,326 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:03,370 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:12 CST)" executed successfully
2025-08-06 08:53:08,135 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:53:11,148 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:53:11,148 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:53:12,264 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:54:12 CST)" (scheduled at 2025-08-06 08:53:12.261883+08:00)
2025-08-06 08:53:12,307 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:53:12,307 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:12,308 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:53:12,400 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:53:42 CST)" (scheduled at 2025-08-06 08:53:12.391016+08:00)
2025-08-06 08:53:12,443 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:53:12,444 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:12,532 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:22 CST)" (scheduled at 2025-08-06 08:53:12.530007+08:00)
2025-08-06 08:53:12,567 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:53:12,567 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:53:12,574 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:53:12,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:12,576 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:12,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:12,926 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:53:12,926 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:53:12,927 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:53:12,927 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:53:12,927 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:12,971 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:53:42 CST)" executed successfully
2025-08-06 08:53:13,057 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:13,057 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:53:13,058 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:13,058 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:13,103 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:22 CST)" executed successfully
2025-08-06 08:53:13,280 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:53:13,280 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.97秒
2025-08-06 08:53:13,281 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:53:13,281 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:13,327 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:54:12 CST)" executed successfully
2025-08-06 08:53:22,535 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:32 CST)" (scheduled at 2025-08-06 08:53:22.530007+08:00)
2025-08-06 08:53:22,580 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:22,580 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:22,708 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:23,052 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:23,053 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:53:23,053 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:23,054 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:23,097 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:32 CST)" executed successfully
2025-08-06 08:53:30,405 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:53:32,535 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:42 CST)" (scheduled at 2025-08-06 08:53:32.530007+08:00)
2025-08-06 08:53:32,590 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:32,590 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:32,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:33,066 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:33,067 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:53:33,067 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:33,068 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:33,116 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:42 CST)" executed successfully
2025-08-06 08:53:38,151 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:53:42,402 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:54:12 CST)" (scheduled at 2025-08-06 08:53:42.391016+08:00)
2025-08-06 08:53:42,447 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:53:42,448 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:42,534 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:52 CST)" (scheduled at 2025-08-06 08:53:42.530007+08:00)
2025-08-06 08:53:42,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:42,582 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:42,582 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:53:42,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:42,932 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:53:42,933 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:53:42,933 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:53:42,933 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:53:42,934 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:42,977 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:54:12 CST)" executed successfully
2025-08-06 08:53:43,054 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:43,054 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:53:43,054 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:43,055 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:43,097 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:53:52 CST)" executed successfully
2025-08-06 08:53:52,535 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:02 CST)" (scheduled at 2025-08-06 08:53:52.530007+08:00)
2025-08-06 08:53:52,578 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:53:52,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:53:52,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:53:53,051 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:53:53,051 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:53:53,051 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:53:53,052 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:53:53,095 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:02 CST)" executed successfully
2025-08-06 08:54:00,619 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:54:02,538 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:12 CST)" (scheduled at 2025-08-06 08:54:02.530007+08:00)
2025-08-06 08:54:02,584 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:02,585 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:02,715 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:03,065 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:03,065 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:54:03,066 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:03,066 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:03,110 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:12 CST)" executed successfully
2025-08-06 08:54:08,154 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:54:11,153 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:54:11,153 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:54:12,269 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:55:12 CST)" (scheduled at 2025-08-06 08:54:12.261883+08:00)
2025-08-06 08:54:12,313 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:54:12,313 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:12,314 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:54:12,391 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:54:42 CST)" (scheduled at 2025-08-06 08:54:12.391016+08:00)
2025-08-06 08:54:12,433 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:54:12,433 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:12,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:22 CST)" (scheduled at 2025-08-06 08:54:12.530007+08:00)
2025-08-06 08:54:12,545 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:54:12,546 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:54:12,565 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:54:12,581 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:12,581 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:12,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:12,917 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:54:12,917 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:54:12,918 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-06 08:54:12,918 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:54:12,919 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:12,964 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:54:42 CST)" executed successfully
2025-08-06 08:54:13,065 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:13,065 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-06 08:54:13,066 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:13,066 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:13,109 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:22 CST)" executed successfully
2025-08-06 08:54:13,496 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:54:13,496 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.18秒
2025-08-06 08:54:13,497 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:54:13,497 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:13,543 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:55:12 CST)" executed successfully
2025-08-06 08:54:22,537 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:32 CST)" (scheduled at 2025-08-06 08:54:22.530007+08:00)
2025-08-06 08:54:22,581 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:22,581 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:22,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:23,057 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:23,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:54:23,058 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:23,059 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:23,106 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:32 CST)" executed successfully
2025-08-06 08:54:30,805 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:54:32,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:42 CST)" (scheduled at 2025-08-06 08:54:32.530007+08:00)
2025-08-06 08:54:32,581 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:32,581 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:32,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:33,054 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:33,055 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:54:33,055 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:33,057 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:33,107 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:42 CST)" executed successfully
2025-08-06 08:54:38,156 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:54:42,407 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:55:12 CST)" (scheduled at 2025-08-06 08:54:42.391016+08:00)
2025-08-06 08:54:42,455 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:54:42,455 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:42,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:52 CST)" (scheduled at 2025-08-06 08:54:42.530007+08:00)
2025-08-06 08:54:42,580 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:42,580 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:42,584 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:54:42,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:42,949 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:54:42,949 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:54:42,952 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-06 08:54:42,955 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:54:42,958 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:43,106 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:55:12 CST)" executed successfully
2025-08-06 08:54:44,174 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:44,174 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.47秒
2025-08-06 08:54:44,175 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:44,176 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:44,333 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:54:52 CST)" executed successfully
2025-08-06 08:54:52,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:02 CST)" (scheduled at 2025-08-06 08:54:52.530007+08:00)
2025-08-06 08:54:52,582 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:54:52,582 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:54:52,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:54:53,058 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:54:53,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:54:53,059 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:54:53,060 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:54:53,102 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:02 CST)" executed successfully
2025-08-06 08:55:00,982 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:55:02,540 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:12 CST)" (scheduled at 2025-08-06 08:55:02.530007+08:00)
2025-08-06 08:55:02,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:02,589 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:02,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:03,068 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:03,068 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:55:03,069 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:03,072 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:03,118 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:12 CST)" executed successfully
2025-08-06 08:55:08,157 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:55:11,168 - ConnectionManager - INFO - Redis连接池状态 (Worker 24640): 使用中=2, 可用=2, 总计=4
2025-08-06 08:55:11,169 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 24640): 连接中
2025-08-06 08:55:12,272 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:56:12 CST)" (scheduled at 2025-08-06 08:55:12.261883+08:00)
2025-08-06 08:55:12,373 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:push_online
2025-08-06 08:55:12,374 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:12,375 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行推送在线人数任务
2025-08-06 08:55:12,404 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:55:42 CST)" (scheduled at 2025-08-06 08:55:12.391016+08:00)
2025-08-06 08:55:12,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:22 CST)" (scheduled at 2025-08-06 08:55:12.530007+08:00)
2025-08-06 08:55:12,576 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:55:12,576 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:12,670 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:12,670 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:12,675 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-06 08:55:12,675 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-06 08:55:13,174 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:55:13,273 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-06 08:55:13,273 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-06 08:55:13,274 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-06 08:55:13,378 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 在线人数推送完成，当前在线: 0
2025-08-06 08:55:13,379 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.00秒
2025-08-06 08:55:13,380 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-06 08:55:13,381 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:13,472 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-06 08:56:12 CST)" executed successfully
2025-08-06 08:55:13,972 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:55:13,972 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:55:13,973 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.80秒
2025-08-06 08:55:13,973 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:55:13,974 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:14,073 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:55:42 CST)" executed successfully
2025-08-06 08:55:15,573 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:15,978 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:15,978 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.41秒
2025-08-06 08:55:15,979 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:15,979 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:16,024 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:22 CST)" executed successfully
2025-08-06 08:55:22,545 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:32 CST)" (scheduled at 2025-08-06 08:55:22.530007+08:00)
2025-08-06 08:55:22,590 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:22,590 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:22,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:23,075 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:23,075 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:55:23,076 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:23,076 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:23,122 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:32 CST)" executed successfully
2025-08-06 08:55:30,203 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:55:32,536 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:42 CST)" (scheduled at 2025-08-06 08:55:32.530007+08:00)
2025-08-06 08:55:32,587 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:32,590 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:32,726 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:33,138 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:33,141 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.41秒
2025-08-06 08:55:33,141 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:33,142 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:33,187 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:42 CST)" executed successfully
2025-08-06 08:55:38,173 - ConnectionManager - INFO - 连接状态 (Worker 24640): 活跃连接数=0, 用户数=0
2025-08-06 08:55:42,397 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:56:12 CST)" (scheduled at 2025-08-06 08:55:42.391016+08:00)
2025-08-06 08:55:42,539 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:52 CST)" (scheduled at 2025-08-06 08:55:42.530007+08:00)
2025-08-06 08:55:42,578 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-06 08:55:42,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:42,672 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:42,672 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:42,872 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 开始执行怪物冷却持久化任务
2025-08-06 08:55:42,973 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-06 08:55:42,973 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-06 08:55:42,974 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-06 08:55:43,672 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-06 08:55:43,672 - scheduler_tasks_unified - INFO - [定时任务] Worker 24640 怪物冷却持久化完成
2025-08-06 08:55:43,673 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.80秒
2025-08-06 08:55:43,673 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-06 08:55:43,674 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:43,776 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-06 08:56:12 CST)" executed successfully
2025-08-06 08:55:45,119 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:45,463 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:45,463 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-06 08:55:45,463 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:45,464 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:45,508 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:55:52 CST)" executed successfully
2025-08-06 08:55:52,535 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:56:02 CST)" (scheduled at 2025-08-06 08:55:52.530007+08:00)
2025-08-06 08:55:52,578 - distributed_lock - INFO - Worker 24640 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-06 08:55:52,578 - distributed_task - INFO - Worker 24640 成功获取锁并执行任务: direct_wrapper
2025-08-06 08:55:52,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-06 08:55:53,057 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-06 08:55:53,058 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-06 08:55:53,058 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-06 08:55:53,059 - distributed_task - INFO - Worker 24640 任务执行完成: direct_wrapper
2025-08-06 08:55:53,104 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-06 08:56:02 CST)" executed successfully
