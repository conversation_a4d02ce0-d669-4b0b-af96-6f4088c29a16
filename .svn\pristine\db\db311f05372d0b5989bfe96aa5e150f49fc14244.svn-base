from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.base import JobLookupError
from typing import Callable, Any, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class SchedulerManager:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.started = False

    def start(self):
        if not self.started:
            self.scheduler.start()
            self.started = True
            logger.info("[SchedulerManager] APScheduler started.")

    def shutdown(self, wait: bool = False):
        if self.started:
            self.scheduler.shutdown(wait=wait)
            self.started = False
            logger.info("[SchedulerManager] APScheduler shutdown.")

    def add_job(self, func: Callable, trigger: str = 'interval', id: Optional[str] = None, **trigger_args):
        """
        注册定时任务。
        :param func: 任务函数
        :param trigger: 触发器类型（interval/cron/date）
        :param id: 任务唯一ID
        :param trigger_args: 触发器参数
        :return: Job对象
        """
        job = self.scheduler.add_job(func, trigger, id=id, **trigger_args)
        logger.info(f"[SchedulerManager] Job added: {job}")
        return job

    def remove_job(self, job_id: str):
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"[SchedulerManager] Job removed: {job_id}")
        except JobLookupError:
            logger.warning(f"[SchedulerManager] Job not found: {job_id}")

    def get_job(self, job_id: str):
        return self.scheduler.get_job(job_id)

    def get_jobs(self):
        return self.scheduler.get_jobs()

    def pause_job(self, job_id: str):
        self.scheduler.pause_job(job_id)
        logger.info(f"[SchedulerManager] Job paused: {job_id}")

    def resume_job(self, job_id: str):
        self.scheduler.resume_job(job_id)
        logger.info(f"[SchedulerManager] Job resumed: {job_id}") 