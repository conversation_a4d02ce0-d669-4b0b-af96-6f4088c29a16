import asyncio
import os
import time
import uuid
import logging
from typing import Optional
import redis.asyncio as redis
from config import config
from redis_manager import RedisManager

logger = logging.getLogger(__name__)

class DistributedLock:
    """分布式锁 - 自动管理Redis连接"""
    
    _redis_client = None
    
    @classmethod
    async def _get_redis_client(cls):
        """获取Redis客户端（单例模式）"""
        if cls._redis_client is None:
            redis_manager = await RedisManager.get_instance()
            cls._redis_client = await redis_manager.get_redis()     
        return cls._redis_client
    
    @classmethod
    async def close_connection(cls):
        """关闭Redis连接"""
        if cls._redis_client:
            await cls._redis_client.close()
            cls._redis_client = None
        logger.info("Redis连接已关闭")
    
    def __init__(self, key: str, ttl: int = 30):
        """
        初始化分布式锁
        :param key: 锁的键名
        :param ttl: 锁超时时间（秒）
        """
        self.key = key
        self.ttl = ttl
        self.lock_value = str(uuid.uuid4())
        self._redis = None
        self._acquired = False

    async def acquire(self, blocking: bool = True, timeout: int = 10) -> bool:
        """
        获取分布式锁
        :param blocking: 是否阻塞等待
        :param timeout: 最大等待时间（秒）
        :return: 是否获取成功
        """
        try:
            # 获取Redis客户端
            self._redis = await self._get_redis_client()
            
            start = time.time()
            while True:
                try:
                    # SETNX + EXPIRE 原子操作
                    result = await self._redis.set(self.key, self.lock_value, ex=self.ttl, nx=True)
                    logger.debug(f"尝试获取锁 {self.key}: {result}")
                    if result:
                        self._acquired = True
                        logger.info(f"Worker {os.getpid()} 成功获取锁: {self.key}")
                        return True

                    if not blocking or (time.time() - start) > timeout:
                        logger.info(f"Worker {os.getpid()} 获取锁超时: {self.key}")
                        return False
                    
                    await asyncio.sleep(0.1)
                    
                except redis.ConnectionError as e:
                    logger.warning(f"Redis连接错误，重试获取锁: {self.key}, 错误: {str(e)}")
                    await asyncio.sleep(0.5)
                    continue
                except Exception as e:
                    logger.error(f"获取锁时发生错误: {self.key}, 错误: {str(e)}")
                    return False
                    
        except Exception as e:
            logger.error(f"初始化Redis连接失败: {str(e)}")
            return False

    async def release(self):
        """
        释放分布式锁（仅持有者可释放）
        """
        if not self._acquired or not self._redis:
            logger.warning(f"尝试释放未获取的锁: {self.key}")
            return
        
        try:
            # Lua脚本保证原子性
            lua = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """
            result = await self._redis.eval(lua, 1, self.key, self.lock_value)
            
            if result:
                logger.debug(f"成功释放锁: {self.key}")
            else:
                logger.warning(f"锁已被其他进程释放或过期: {self.key}")
                
            self._acquired = False
            
        except redis.ConnectionError as e:
            logger.warning(f"Redis连接错误，释放锁失败: {self.key}, 错误: {str(e)}")
        except Exception as e:
            logger.error(f"释放锁时发生错误: {self.key}, 错误: {str(e)}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        acquired = await self.acquire()
        if not acquired:
            raise TimeoutError(f"Failed to acquire lock: {self.key}")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.release()
    
    @property
    def is_acquired(self) -> bool:
        """检查锁是否已获取"""
        return self._acquired
    
    async def extend_ttl(self, new_ttl: int) -> bool:
        """
        延长锁的超时时间
        :param new_ttl: 新的超时时间（秒）
        :return: 是否成功延长
        """
        if not self._acquired or not self._redis:
            return False
        
        try:
            # Lua脚本检查锁是否仍然属于当前进程
            lua = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('expire', KEYS[1], ARGV[2])
            else
                return 0
            end
            """
            result = await self._redis.eval(lua, 1, self.key, self.lock_value, new_ttl)
            
            if result:
                self.ttl = new_ttl
                logger.debug(f"成功延长锁超时时间: {self.key}, 新TTL: {new_ttl}")
                return True
            else:
                logger.warning(f"延长锁超时时间失败，锁可能已过期: {self.key}")
                return False
                
        except Exception as e:
            logger.error(f"延长锁超时时间时发生错误: {self.key}, 错误: {str(e)}")
            return False 