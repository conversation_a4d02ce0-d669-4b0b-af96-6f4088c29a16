"""
商店折扣计算服务
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from shop_database_manager import ShopDatabaseManager
from shop_cache_service import ShopCacheService
from shop_models import ShopDiscount, ShopItemConfig, PriceInfo, DiscountType, ScopeType

logger = logging.getLogger(__name__)


class ShopDiscountService:
    """商店折扣计算服务"""
    
    def __init__(self):
        self.db_manager = ShopDatabaseManager()
        self.cache_service = ShopCacheService()
    
    # ==================== 折扣计算 ====================
    
    async def calculate_final_price(self, player_id: str, config_id: str, quantity: int) -> PriceInfo:
        """计算最终价格"""
        try:
            # 1. 获取商品配置
            config = await self.db_manager.get_item_config(config_id)
            if not config:
                raise ValueError("商品配置不存在")
            
            # 安全获取基础价格，支持多种格式
            price_config = config.price_config or {}
            base_price = (
                price_config.get('base_price') or
                price_config.get('amount') or
                0
            )
            original_price = base_price * quantity
            
            # 2. 获取适用的折扣
            applicable_discounts = await self._get_applicable_discounts(player_id, config)
            
            # 3. 计算最佳折扣
            best_discount = await self._get_best_discount(applicable_discounts, player_id)
            
            # 4. 应用折扣
            if best_discount:
                final_price = await self._apply_discount(original_price, best_discount)
                discount_amount = original_price - final_price
                discounts_applied = [best_discount.discount_id]
            else:
                final_price = original_price
                discount_amount = 0
                discounts_applied = []
            
            # 安全获取货币类型
            currency_type = (
                price_config.get('currency_type') or
                price_config.get('currency') or
                'gold'
            )

            return PriceInfo(
                original_price=original_price,
                final_price=final_price,
                currency_type=currency_type,
                discounts_applied=discounts_applied,
                discount_amount=discount_amount
            )
            
        except Exception as e:
            logger.error(f"计算最终价格时发生错误: {str(e)}")
            # 返回原价作为fallback
            try:
                config = await self.db_manager.get_item_config(config_id)
                if config and config.price_config:
                    # 安全获取价格信息
                    price_config = config.price_config or {}
                    base_price = (
                        price_config.get('base_price') or
                        price_config.get('amount') or
                        0
                    )
                    currency_type = (
                        price_config.get('currency_type') or
                        price_config.get('currency') or
                        'gold'
                    )
                    original_price = base_price * quantity

                    return PriceInfo(
                        original_price=original_price,
                        final_price=original_price,
                        currency_type=currency_type,
                        discounts_applied=[],
                        discount_amount=0
                    )
                else:
                    # 如果配置不存在或价格配置为空，返回默认值
                    logger.warning(f"商品配置 {config_id} 不存在或价格配置为空，返回默认价格")
                    return PriceInfo(
                        original_price=0,
                        final_price=0,
                        currency_type='gold',
                        discounts_applied=[],
                        discount_amount=0
                    )
            except Exception as fallback_error:
                logger.error(f"获取fallback价格时也发生错误: {str(fallback_error)}")
                # 最后的兜底方案
                return PriceInfo(
                    original_price=0,
                    final_price=0,
                    currency_type='gold',
                    discounts_applied=[],
                    discount_amount=0
                )
    
    async def _get_applicable_discounts(self, player_id: str, config: ShopItemConfig) -> List[ShopDiscount]:
        """获取适用的折扣"""
        try:
            applicable_discounts = []
            
            # 1. 获取商品级别折扣
            item_discounts = await self.db_manager.get_applicable_discounts(ScopeType.ITEM, config.config_id)
            applicable_discounts.extend(item_discounts)
            
            # 2. 获取商店级别折扣
            shop_discounts = await self.db_manager.get_applicable_discounts(ScopeType.SHOP, config.shop_id)
            applicable_discounts.extend(shop_discounts)
            
            # 3. 获取全局折扣
            global_discounts = await self.db_manager.get_applicable_discounts(ScopeType.GLOBAL, "")
            applicable_discounts.extend(global_discounts)
            
            # 4. 过滤符合条件的折扣
            valid_discounts = []
            for discount in applicable_discounts:
                if await self._check_discount_conditions(discount, player_id, config):
                    valid_discounts.append(discount)
            
            return valid_discounts
            
        except Exception as e:
            logger.error(f"获取适用折扣时发生错误: {str(e)}")
            return []
    
    async def _check_discount_conditions(self, discount: ShopDiscount, player_id: str, config: ShopItemConfig) -> bool:
        """检查折扣使用条件"""
        try:
            conditions = discount.conditions
            
            # 1. 检查时间条件
            now = datetime.now()
            if conditions.get("time_range"):
                start_time = datetime.fromisoformat(conditions["time_range"]["start"])
                end_time = datetime.fromisoformat(conditions["time_range"]["end"])
                if not (start_time <= now <= end_time):
                    return False
            
            # 2. 检查玩家等级条件
            if conditions.get("player_level"):
                # 这里需要调用玩家服务获取玩家等级
                # player_level = await self._get_player_level(player_id)
                # if player_level < conditions["player_level"]:
                #     return False
                pass
            
            # 3. 检查VIP等级条件
            if conditions.get("vip_level"):
                # 这里需要调用玩家服务获取VIP等级
                # vip_level = await self._get_player_vip_level(player_id)
                # if vip_level < conditions["vip_level"]:
                #     return False
                pass
            
            # 4. 检查首次购买条件
            if conditions.get("first_purchase"):
                # 检查是否首次购买该商品
                purchases = await self.db_manager.get_player_purchases(player_id, limit=1)
                has_purchased = any(p.config_id == config.config_id for p in purchases)
                if has_purchased:
                    return False
            
            # 5. 检查使用次数限制
            if conditions.get("usage_limit"):
                # 这里需要检查折扣使用次数
                # 可以在购买记录中统计或单独维护折扣使用记录
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"检查折扣条件时发生错误: {str(e)}")
            return False
    
    async def _get_best_discount(self, discounts: List[ShopDiscount], player_id: str) -> Optional[ShopDiscount]:
        """获取最佳折扣"""
        try:
            if not discounts:
                return None
            
            # 1. 按优先级排序
            discounts.sort(key=lambda x: x.priority, reverse=True)
            
            # 2. 处理互斥组
            selected_discounts = []
            used_mutex_groups = set()
            
            for discount in discounts:
                # 检查互斥组
                if discount.mutex_groups:
                    has_conflict = any(group in used_mutex_groups for group in discount.mutex_groups)
                    if has_conflict:
                        continue
                    
                    # 添加到已使用的互斥组
                    used_mutex_groups.update(discount.mutex_groups)
                
                selected_discounts.append(discount)
                
                # 如果不支持叠加，只取第一个
                if not discount.stackable:
                    break
            
            # 3. 返回最高优先级的折扣（简化版本，不支持叠加）
            return selected_discounts[0] if selected_discounts else None
            
        except Exception as e:
            logger.error(f"获取最佳折扣时发生错误: {str(e)}")
            return None
    
    async def _apply_discount(self, original_price: int, discount: ShopDiscount) -> int:
        """应用折扣"""
        try:
            rule = discount.discount_rule
            discount_type = rule["type"]
            
            if discount_type == DiscountType.PERCENTAGE:
                # 百分比折扣
                discount_rate = rule["value"]  # 0.8 表示8折
                final_price = int(original_price * discount_rate)
                
            elif discount_type == DiscountType.FIXED_AMOUNT:
                # 固定金额折扣
                discount_amount = rule["value"]
                final_price = original_price - discount_amount
                
            elif discount_type == DiscountType.FORMULA:
                # 自定义公式折扣
                formula = rule["formula"]
                # 这里可以实现自定义公式计算
                # 为了安全起见，建议使用预定义的公式而不是eval
                final_price = original_price  # 默认不折扣
                
            else:
                final_price = original_price
            
            # 应用最低价格限制
            min_price = rule.get("min_price", 1)
            final_price = max(final_price, min_price)
            
            # 应用最大折扣限制
            if rule.get("max_discount"):
                max_discount_amount = rule["max_discount"]
                actual_discount = original_price - final_price
                if actual_discount > max_discount_amount:
                    final_price = original_price - max_discount_amount
            
            return final_price
            
        except Exception as e:
            logger.error(f"应用折扣时发生错误: {str(e)}")
            return original_price
    
    # ==================== 折扣管理 ====================
    
    async def create_discount(self, discount: ShopDiscount) -> bool:
        """创建折扣"""
        try:
            # 生成折扣ID
            if not discount.discount_id:
                discount.discount_id = self.db_manager.generate_discount_id()
            
            # 设置创建时间
            discount.created_at = datetime.now()
            discount.updated_at = datetime.now()
            
            # 保存到数据库
            success = await self.db_manager.create_discount(discount)
            
            if success:
                # 缓存折扣配置
                await self.cache_service.cache_discount_config(discount)
                logger.info(f"折扣创建成功: {discount.discount_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建折扣时发生错误: {str(e)}")
            return False
    
    async def get_discount(self, discount_id: str) -> Optional[ShopDiscount]:
        """获取折扣配置"""
        try:
            # 先从缓存获取
            discount = await self.cache_service.get_cached_discount_config(discount_id)
            if discount:
                return discount
            
            # 从数据库获取
            # 这里需要在数据库管理器中添加get_discount方法
            # discount = await self.db_manager.get_discount(discount_id)
            # if discount:
            #     await self.cache_service.cache_discount_config(discount)
            
            return None
            
        except Exception as e:
            logger.error(f"获取折扣配置时发生错误: {str(e)}")
            return None
    
    # ==================== 折扣验证 ====================
    
    async def validate_discount_config(self, discount: ShopDiscount) -> Dict[str, Any]:
        """验证折扣配置"""
        try:
            errors = []
            warnings = []
            
            # 1. 验证折扣规则
            rule = discount.discount_rule
            if rule["type"] == DiscountType.PERCENTAGE:
                if not (0 < rule["value"] <= 1):
                    errors.append("百分比折扣值必须在0-1之间")
            elif rule["type"] == DiscountType.FIXED_AMOUNT:
                if rule["value"] <= 0:
                    errors.append("固定金额折扣必须大于0")
            
            # 2. 验证时间范围
            conditions = discount.conditions
            if conditions.get("time_range"):
                start_time = datetime.fromisoformat(conditions["time_range"]["start"])
                end_time = datetime.fromisoformat(conditions["time_range"]["end"])
                if start_time >= end_time:
                    errors.append("开始时间必须早于结束时间")
                if end_time < datetime.now():
                    warnings.append("结束时间已过期")
            
            # 3. 验证适用范围
            if not discount.scope_values and discount.scope_type != ScopeType.GLOBAL:
                errors.append("非全局折扣必须指定适用范围")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings
            }
            
        except Exception as e:
            logger.error(f"验证折扣配置时发生错误: {str(e)}")
            return {
                "valid": False,
                "errors": [f"验证失败: {str(e)}"],
                "warnings": []
            }
    
    # ==================== 统计分析 ====================
    
    async def get_discount_usage_stats(self, discount_id: str, days: int = 7) -> Dict[str, Any]:
        """获取折扣使用统计"""
        try:
            # 这里需要从购买记录中统计折扣使用情况
            # 可以统计使用次数、节省金额、受益玩家数等
            
            return {
                "discount_id": discount_id,
                "period_days": days,
                "usage_count": 0,
                "total_saved": 0,
                "unique_players": 0
            }
            
        except Exception as e:
            logger.error(f"获取折扣使用统计时发生错误: {str(e)}")
            return {}
    
    async def get_popular_discounts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门折扣"""
        try:
            # 这里可以根据使用频率、节省金额等指标排序
            return []
            
        except Exception as e:
            logger.error(f"获取热门折扣时发生错误: {str(e)}")
            return []
