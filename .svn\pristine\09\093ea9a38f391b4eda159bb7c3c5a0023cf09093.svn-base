"""
角色系统数据模型
基于客户端roleData.lua设计服务器权威的角色数据模型
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enums import RoleState, ItemType
import uuid


class RoleProperties(BaseModel):
    """角色基础属性"""
    hp: int = Field(default=100, description="生命值")
    bp: int = Field(default=100, description="体力值")
    pow: int = Field(default=10, description="力量")
    dex: int = Field(default=10, description="敏捷")
    intelligence: int = Field(default=10, description="智力")
    con: int = Field(default=10, description="体质")
    men: int = Field(default=10, description="精神")
    luck: int = Field(default=10, description="幸运")
    
    # 装备加成属性
    weapon_pow: int = Field(default=0, description="武器力量加成")
    weapon_dex: int = Field(default=0, description="武器敏捷加成")
    weapon_intelligence: int = Field(default=0, description="武器智力加成")
    armor_hp: int = Field(default=0, description="防具生命加成")
    armor_con: int = Field(default=0, description="防具体质加成")
    armor_men: int = Field(default=0, description="防具精神加成")
    
    # 称号加成
    title_pow: int = Field(default=0, description="称号力量加成")
    title_dex: int = Field(default=0, description="称号敏捷加成")
    title_intelligence: int = Field(default=0, description="称号智力加成")
    title_con: int = Field(default=0, description="称号体质加成")
    title_men: int = Field(default=0, description="称号精神加成")
    title_luck: int = Field(default=0, description="称号幸运加成")
    
    # 套装加成
    set_pow: int = Field(default=0, description="套装力量加成")
    set_dex: int = Field(default=0, description="套装敏捷加成")
    set_intelligence: int = Field(default=0, description="套装智力加成")
    set_con: int = Field(default=0, description="套装体质加成")
    set_men: int = Field(default=0, description="套装精神加成")
    set_luck: int = Field(default=0, description="套装幸运加成")
    
    def get_total_properties(self) -> Dict[str, int]:
        """获取总属性（基础+加成）"""
        return {
            "hp": self.hp + self.armor_hp,
            "bp": self.bp,
            "pow": self.pow + self.weapon_pow + self.title_pow + self.set_pow,
            "dex": self.dex + self.weapon_dex + self.title_dex + self.set_dex,
            "intelligence": self.intelligence + self.weapon_intelligence + self.title_intelligence + self.set_intelligence,
            "con": self.con + self.armor_con + self.title_con + self.set_con,
            "men": self.men + self.armor_men + self.title_men + self.set_men,
            "luck": self.luck + self.title_luck + self.set_luck
        }
    
    def calculate_fight_power(self) -> int:
        """计算战斗力"""
        total = self.get_total_properties()
        # 战斗力计算公式：基础属性加权 + 生命值
        fight_power = (
            total["pow"] * 2 +  # 力量权重2
            total["dex"] * 1.5 +  # 敏捷权重1.5
            total["intelligence"] * 1.5 +  # 智力权重1.5
            total["con"] * 1.2 +  # 体质权重1.2
            total["men"] * 1.2 +  # 精神权重1.2
            total["luck"] * 0.5 +  # 幸运权重0.5
            total["hp"] * 0.1     # 生命值权重0.1
        )
        return int(fight_power)


class RoleSkill(BaseModel):
    """角色技能"""
    skill_id: str = Field(description="技能ID")
    skill_name: str = Field(description="技能名称")
    skill_type: str = Field(description="技能类型：active/passive/special")
    skill_level: int = Field(default=1, description="技能等级")
    is_equipped: bool = Field(default=False, description="是否装备")
    cooldown: int = Field(default=0, description="冷却时间（秒）")
    description: str = Field(default="", description="技能描述")


class RoleSoldier(BaseModel):
    """角色士兵"""
    soldier_type: str = Field(description="士兵类型")
    soldier_name: str = Field(description="士兵名称")
    soldier_level: int = Field(default=1, description="士兵等级")
    soldier_count: int = Field(default=0, description="士兵数量")
    soldier_max: int = Field(default=100, description="最大士兵数量")


class RoleEquipment(BaseModel):
    """角色装备"""
    weapon_id: Optional[str] = Field(default=None, description="武器ID")
    weapon_name: Optional[str] = Field(default=None, description="武器名称")
    weapon_level: int = Field(default=0, description="武器等级")
    weapon_star: int = Field(default=0, description="武器星级")
    
    armor_id: Optional[str] = Field(default=None, description="防具ID")
    armor_name: Optional[str] = Field(default=None, description="防具名称")
    armor_level: int = Field(default=0, description="防具等级")
    armor_star: int = Field(default=0, description="防具星级")
    
    general_star_id: Optional[str] = Field(default=None, description="将星ID")
    general_star_name: Optional[str] = Field(default=None, description="将星名称")
    general_star_level: int = Field(default=0, description="将星等级")


class Role(BaseModel):
    """角色主模型"""
    role_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="角色ID")
    owner: str = Field(description="角色所有者")
    name: str = Field(description="角色名称")
    avatar: str = Field(default="default", description="角色头像")
    
    # 基础信息
    level: int = Field(default=1, description="角色等级")
    exp: int = Field(default=0, description="当前经验")
    exp_max: int = Field(default=100, description="升级所需经验")
    star: int = Field(default=1, description="角色星级")
    rebirth: int = Field(default=0, description="转生次数")
    
    # 状态
    state: RoleState = Field(default=RoleState.IDLE, description="角色状态")
    is_locked: bool = Field(default=False, description="是否锁定")
    
    # 属性
    properties: RoleProperties = Field(default_factory=RoleProperties, description="角色属性")
    
    # 装备
    equipment: RoleEquipment = Field(default_factory=RoleEquipment, description="角色装备")
    
    # 技能
    skills: List[RoleSkill] = Field(default_factory=list, description="角色技能")
    
    # 士兵
    soldiers: List[RoleSoldier] = Field(default_factory=list, description="角色士兵")
    
    # 称号
    title: Optional[str] = Field(default=None, description="当前称号")
    titles: List[str] = Field(default_factory=list, description="已获得称号列表")
    
    # 套装
    current_set: Optional[str] = Field(default=None, description="当前套装")
    set_bonus: Dict[str, Any] = Field(default_factory=dict, description="套装加成")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    # 计算属性
    @property
    def fight_power(self) -> int:
        """计算战斗力"""
        return self.properties.calculate_fight_power()
    
    @property
    def total_properties(self) -> Dict[str, int]:
        """获取总属性"""
        return self.properties.get_total_properties()
    
    def can_level_up(self) -> bool:
        """检查是否可以升级"""
        return self.exp >= self.exp_max
    
    def can_star_up(self) -> bool:
        """检查是否可以升星"""
        # 这里可以根据具体规则实现
        return self.level >= (self.star * 10)
    
    def add_exp(self, exp_amount: int) -> Dict[str, Any]:
        """添加经验值，返回升级信息"""
        self.exp += exp_amount
        result = {
            "leveled_up": False,
            "new_level": self.level,
            "exp_gained": exp_amount,
            "current_exp": self.exp,
            "exp_max": self.exp_max
        }
        
        # 检查是否可以升级
        while self.can_level_up():
            self.level += 1
            self.exp -= self.exp_max
            self.exp_max = self.calculate_exp_max()
            result["leveled_up"] = True
            result["new_level"] = self.level
            result["current_exp"] = self.exp
            result["exp_max"] = self.exp_max
        
        self.updated_at = datetime.now()
        return result
    
    def calculate_exp_max(self) -> int:
        """计算升级所需经验"""
        # 简单的经验计算公式，可以根据需要调整
        return int(self.level * 100 * (1 + self.level * 0.1))
    
    def equip_weapon(self, weapon_id: str, weapon_name: str, weapon_level: int = 0, weapon_star: int = 0):
        """装备武器"""
        self.equipment.weapon_id = weapon_id
        self.equipment.weapon_name = weapon_name
        self.equipment.weapon_level = weapon_level
        self.equipment.weapon_star = weapon_star
        self.updated_at = datetime.now()
    
    def unequip_weapon(self):
        """卸下武器"""
        self.equipment.weapon_id = None
        self.equipment.weapon_name = None
        self.equipment.weapon_level = 0
        self.equipment.weapon_star = 0
        self.updated_at = datetime.now()
    
    def equip_armor(self, armor_id: str, armor_name: str, armor_level: int = 0, armor_star: int = 0):
        """装备防具"""
        self.equipment.armor_id = armor_id
        self.equipment.armor_name = armor_name
        self.equipment.armor_level = armor_level
        self.equipment.armor_star = armor_star
        self.updated_at = datetime.now()
    
    def unequip_armor(self):
        """卸下防具"""
        self.equipment.armor_id = None
        self.equipment.armor_name = None
        self.equipment.armor_level = 0
        self.equipment.armor_star = 0
        self.updated_at = datetime.now()
    
    def learn_skill(self, skill_id: str, skill_name: str, skill_type: str, description: str = ""):
        """学习技能"""
        # 检查是否已学会
        for skill in self.skills:
            if skill.skill_id == skill_id:
                return False  # 已学会
        
        new_skill = RoleSkill(
            skill_id=skill_id,
            skill_name=skill_name,
            skill_type=skill_type,
            description=description
        )
        self.skills.append(new_skill)
        self.updated_at = datetime.now()
        return True
    
    def equip_skill(self, skill_id: str, slot: int = 0):
        """装备技能"""
        for skill in self.skills:
            if skill.skill_id == skill_id:
                skill.is_equipped = True
                self.updated_at = datetime.now()
                return True
        return False
    
    def unequip_skill(self, skill_id: str):
        """卸下技能"""
        for skill in self.skills:
            if skill.skill_id == skill_id:
                skill.is_equipped = False
                self.updated_at = datetime.now()
                return True
        return False
    
    def change_state(self, new_state: RoleState):
        """改变角色状态"""
        self.state = new_state
        self.updated_at = datetime.now()
    
    def lock(self):
        """锁定角色"""
        self.is_locked = True
        self.updated_at = datetime.now()
    
    def unlock(self):
        """解锁角色"""
        self.is_locked = False
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "role_id": self.role_id,
            "owner": self.owner,
            "name": self.name,
            "avatar": self.avatar,
            "level": self.level,
            "exp": self.exp,
            "exp_max": self.exp_max,
            "star": self.star,
            "rebirth": self.rebirth,
            "state": self.state.value,
            "is_locked": self.is_locked,
            "properties": self.properties.dict(),
            "equipment": self.equipment.dict(),
            "skills": [skill.dict() for skill in self.skills],
            "soldiers": [soldier.dict() for soldier in self.soldiers],
            "title": self.title,
            "titles": self.titles,
            "current_set": self.current_set,
            "set_bonus": self.set_bonus,
            "fight_power": self.fight_power,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Role':
        """从字典创建角色实例"""
        # 处理枚举类型
        if "state" in data and isinstance(data["state"], str):
            data["state"] = RoleState(data["state"])
        
        # 处理时间戳
        if "created_at" in data and isinstance(data["created_at"], str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data and isinstance(data["updated_at"], str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"])
        
        return cls(**data)


class RoleCreateRequest(BaseModel):
    """创建角色请求"""
    name: str = Field(description="角色名称")
    avatar: str = Field(default="default", description="角色头像")


class RoleUpdateRequest(BaseModel):
    """更新角色请求"""
    name: Optional[str] = Field(default=None, description="角色名称")
    avatar: Optional[str] = Field(default=None, description="角色头像")
    level: Optional[int] = Field(default=None, description="角色等级")
    exp: Optional[int] = Field(default=None, description="经验值")
    star: Optional[int] = Field(default=None, description="星级")
    state: Optional[RoleState] = Field(default=None, description="角色状态")


class RoleResponse(BaseModel):
    """角色响应"""
    success: bool = Field(description="是否成功")
    data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")
    error: Optional[str] = Field(default=None, description="错误信息") 