<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件系统管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #f0f0f0;
        }

        .tab {
            padding: 15px 30px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #4facfe;
            border-bottom-color: #4facfe;
        }

        .tab:hover {
            color: #4facfe;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .attachments-section {
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .attachment-item {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .attachment-item .form-group {
            margin-bottom: 0;
            flex: 1;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .user-search {
            position: relative;
        }

        .user-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .user-suggestion {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .user-suggestion:hover {
            background: #f8f9fa;
        }

        .user-suggestion:last-child {
            border-bottom: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .attachment-item {
                flex-direction: column;
                align-items: stretch;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                text-align: left;
                border-bottom: 1px solid #f0f0f0;
                border-right: 3px solid transparent;
            }
            
            .tab.active {
                border-bottom-color: #f0f0f0;
                border-right-color: #4facfe;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 邮件系统管理</h1>
            <p>发送系统邮件、管理附件、广播消息</p>
        </div>

        <div class="content">
            <div class="alert alert-success" id="successAlert"></div>
            <div class="alert alert-error" id="errorAlert"></div>

            <div class="tabs">
                <button class="tab active" onclick="switchTab('system-mail')">发送系统邮件</button>
                <button class="tab" onclick="switchTab('broadcast-mail')">广播邮件</button>
                <button class="tab" onclick="switchTab('management')">邮件管理</button>
            </div>

            <!-- 发送系统邮件 -->
            <div id="system-mail" class="tab-content active">
                <h2>📨 发送系统邮件</h2>
                <form id="systemMailForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiverType">收件人类型</label>
                            <select id="receiverType" name="receiverType" required>
                                <option value="username">用户名</option>
                                <option value="nickname">昵称</option>
                            </select>
                        </div>
                        <div class="form-group user-search">
                            <label for="receiverValue">收件人</label>
                            <input type="text" id="receiverValue" name="receiverValue" placeholder="输入用户名或昵称" required>
                            <div class="user-suggestions" id="userSuggestions"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="systemTitle">邮件标题</label>
                        <input type="text" id="systemTitle" name="title" placeholder="输入邮件标题" required maxlength="50">
                    </div>

                    <div class="form-group">
                        <label for="systemContent">邮件内容</label>
                        <textarea id="systemContent" name="content" placeholder="输入邮件内容" required maxlength="500"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="systemExpireDays">过期天数</label>
                        <input type="number" id="systemExpireDays" name="expireDays" value="7" min="1" max="30" required>
                    </div>

                    <div class="attachments-section">
                        <h3>📎 附件 (道具)</h3>
                        <div id="systemAttachments">
                            <!-- 附件项将在这里动态添加 -->
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="addAttachment('system')">+ 添加附件</button>
                    </div>

                    <button type="submit" class="btn btn-primary">发送系统邮件</button>
                </form>
            </div>

            <!-- 广播邮件 -->
            <div id="broadcast-mail" class="tab-content">
                <h2>📢 发送广播邮件</h2>
                <form id="broadcastMailForm">
                    <div class="form-group">
                        <label for="broadcastTitle">邮件标题</label>
                        <input type="text" id="broadcastTitle" name="title" placeholder="输入邮件标题" required maxlength="50">
                    </div>

                    <div class="form-group">
                        <label for="broadcastContent">邮件内容</label>
                        <textarea id="broadcastContent" name="content" placeholder="输入邮件内容" required maxlength="500"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="targetType">发送范围</label>
                            <select id="targetType" name="targetType" onchange="toggleTargetUsers()">
                                <option value="all">全服玩家</option>
                                <option value="specific">指定玩家</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="broadcastExpireDays">过期天数</label>
                            <input type="number" id="broadcastExpireDays" name="expireDays" value="7" min="1" max="30" required>
                        </div>
                    </div>

                    <div class="form-group" id="targetUsersGroup" style="display: none;">
                        <label for="targetUsers">目标玩家 (用户名，逗号分隔)</label>
                        <textarea id="targetUsers" name="targetUsers" placeholder="user1,user2,user3"></textarea>
                    </div>

                    <div class="attachments-section">
                        <h3>📎 附件 (道具)</h3>
                        <div id="broadcastAttachments">
                            <!-- 附件项将在这里动态添加 -->
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="addAttachment('broadcast')">+ 添加附件</button>
                    </div>

                    <button type="submit" class="btn btn-primary">发送广播邮件</button>
                </form>
            </div>

            <!-- 邮件管理 -->
            <div id="management" class="tab-content">
                <h2>🛠️ 邮件管理</h2>
                
                <div class="form-group">
                    <h3>清理过期邮件</h3>
                    <p>删除所有已过期的邮件和相关附件</p>
                    <button type="button" class="btn btn-danger" onclick="cleanupExpiredMails()">清理过期邮件</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let attachmentCounter = 0;

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 显示消息
        function showMessage(message, type = 'success') {
            const alertElement = document.getElementById(type === 'success' ? 'successAlert' : 'errorAlert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 3000);
        }

        // 添加附件
        function addAttachment(type) {
            attachmentCounter++;
            const container = document.getElementById(type + 'Attachments');
            
            const attachmentDiv = document.createElement('div');
            attachmentDiv.className = 'attachment-item';
            attachmentDiv.innerHTML = `
                <div class="form-group">
                    <label>道具ID</label>
                    <input type="text" name="attachments[${attachmentCounter}][item_id]" placeholder="道具ID" required>
                </div>
                <div class="form-group">
                    <label>道具名称</label>
                    <input type="text" name="attachments[${attachmentCounter}][item_name]" placeholder="道具名称" required>
                </div>
                <div class="form-group">
                    <label>数量</label>
                    <input type="number" name="attachments[${attachmentCounter}][quantity]" value="1" min="1" required>
                </div>
                <div class="form-group">
                    <label>品质</label>
                    <input type="number" name="attachments[${attachmentCounter}][quality]" value="1" min="1" max="10" required>
                </div>
                <button type="button" class="btn btn-danger" onclick="removeAttachment(this)">删除</button>
            `;
            
            container.appendChild(attachmentDiv);
        }

        // 删除附件
        function removeAttachment(button) {
            button.parentElement.remove();
        }

        // 切换目标用户输入框
        function toggleTargetUsers() {
            const targetType = document.getElementById('targetType').value;
            const targetUsersGroup = document.getElementById('targetUsersGroup');
            
            if (targetType === 'specific') {
                targetUsersGroup.style.display = 'block';
            } else {
                targetUsersGroup.style.display = 'none';
            }
        }

        // 搜索用户
        async function searchUser(query) {
            if (query.length < 2) {
                document.getElementById('userSuggestions').style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`/admin/mail/search_user/${encodeURIComponent(query)}`);
                const data = await response.json();
                
                const suggestionsDiv = document.getElementById('userSuggestions');
                suggestionsDiv.innerHTML = '';
                
                if (data.success && data.users.length > 0) {
                    data.users.forEach(user => {
                        const suggestionDiv = document.createElement('div');
                        suggestionDiv.className = 'user-suggestion';
                        suggestionDiv.innerHTML = `
                            <strong>${user.username}</strong>
                            ${user.nickname && user.nickname !== user.username ? `<br><small>昵称: ${user.nickname}</small>` : ''}
                        `;
                        suggestionDiv.onclick = () => {
                            document.getElementById('receiverValue').value = user.username;
                            suggestionsDiv.style.display = 'none';
                        };
                        suggestionsDiv.appendChild(suggestionDiv);
                    });
                    suggestionsDiv.style.display = 'block';
                } else {
                    suggestionsDiv.innerHTML = '<div class="user-suggestion">未找到用户</div>';
                    suggestionsDiv.style.display = 'block';
                }
            } catch (error) {
                console.error('搜索用户失败:', error);
            }
        }

        // 处理表单数据
        function processFormData(formData) {
            const data = {};
            const attachments = [];
            
            for (let [key, value] of formData.entries()) {
                if (key.startsWith('attachments[')) {
                    // 解析附件数据
                    const match = key.match(/attachments\[(\d+)\]\[(\w+)\]/);
                    if (match) {
                        const index = parseInt(match[1]);
                        const field = match[2];
                        
                        if (!attachments[index]) {
                            attachments[index] = {};
                        }
                        attachments[index][field] = value;
                    }
                } else {
                    data[key] = value;
                }
            }
            
            // 过滤空的附件
            data.attachments = attachments.filter(attachment => 
                attachment && attachment.item_id && attachment.item_name
            );
            
            return data;
        }

        // 发送系统邮件
        async function sendSystemMail(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = processFormData(formData);
            
            // 转换数据格式
            const requestData = {
                receiver_type: data.receiverType,
                receiver_value: data.receiverValue,
                title: data.title,
                content: data.content,
                expire_days: parseInt(data.expireDays),
                attachments: data.attachments
            };

            try {
                const response = await fetch('/admin/mail/send_system_mail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage(result.message, 'success');
                    event.target.reset();
                    document.getElementById('systemAttachments').innerHTML = '';
                } else {
                    showMessage(result.detail || result.message || '发送失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 发送广播邮件
        async function sendBroadcastMail(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = processFormData(formData);
            
            // 处理目标用户
            let targetUsers = null;
            if (data.targetType === 'specific' && data.targetUsers) {
                targetUsers = data.targetUsers.split(',').map(user => user.trim()).filter(user => user);
            }
            
            const requestData = {
                title: data.title,
                content: data.content,
                expire_days: parseInt(data.expireDays),
                target_type: data.targetType,
                target_users: targetUsers,
                attachments: data.attachments
            };

            try {
                const response = await fetch('/admin/mail/send_broadcast_mail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage(result.message, 'success');
                    event.target.reset();
                    document.getElementById('broadcastAttachments').innerHTML = '';
                    toggleTargetUsers(); // 重置目标用户显示
                } else {
                    showMessage(result.detail || result.message || '发送失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 清理过期邮件
        async function cleanupExpiredMails() {
            if (!confirm('确定要清理所有过期邮件吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch('/admin/mail/cleanup_expired');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.detail || result.message || '清理失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定表单提交事件
            document.getElementById('systemMailForm').addEventListener('submit', sendSystemMail);
            document.getElementById('broadcastMailForm').addEventListener('submit', sendBroadcastMail);
            
            // 绑定用户搜索事件
            const receiverInput = document.getElementById('receiverValue');
            let searchTimeout;
            
            receiverInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchUser(this.value);
                }, 300);
            });
            
            // 点击其他地方隐藏用户建议
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.user-search')) {
                    document.getElementById('userSuggestions').style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
