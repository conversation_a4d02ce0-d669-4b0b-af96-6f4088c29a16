"""
游戏通知管理器
处理游戏中的各种消息推送，支持多Worker环境
"""

import os
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from models import MessageModel
from enums import MessageId
from redis_manager import RedisManager
from service_locator import ServiceLocator
from game_events import GameEventType, NotificationLevel

logger = logging.getLogger(__name__)


class GameNotificationManager:
    """游戏通知管理器"""
    
    # Redis频道
    NOTIFICATION_CHANNEL = "game_notifications"
    BROADCAST_CHANNEL = "game_broadcast"
    
    def __init__(self):
        self.redis_client = None
        self.worker_id = os.getpid()
        self.connection_manager = None
        self.notification_handlers: Dict[str, Callable] = {}
        
    async def _get_redis(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            redis_manager = await RedisManager.get_instance()
            self.redis_client = await redis_manager.get_redis()
        return self.redis_client
    
    async def _get_connection_manager(self):
        """获取连接管理器"""
        if self.connection_manager is None:
            self.connection_manager = ServiceLocator.get("conn_manager")
        return self.connection_manager

    # ==================== 消息推送 ====================
    
    async def push_to_player(self, username: str, message: Dict[str, Any]) -> bool:
        """向指定玩家推送消息"""
        try:
            connection_manager = await self._get_connection_manager()
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False

            # 直接使用ConnectionManager的方法，它会自动处理跨Worker的情况
            success = await connection_manager.send_personal_message_to_user(message, username)
            if success:
                logger.debug(f"推送消息给玩家成功: {username}")
            else:
                logger.debug(f"推送消息给玩家失败: {username}")
            return success

        except Exception as e:
            logger.error(f"推送消息给玩家失败: {username}, 错误: {str(e)}")
            return False

    async def push_to_players(self, usernames: List[str], message: Dict[str, Any]) -> Dict[str, bool]:
        """向多个玩家推送消息"""
        try:
            connection_manager = await self._get_connection_manager()
            if not connection_manager:
                logger.error("连接管理器不可用")
                return {username: False for username in usernames}

            # 直接使用ConnectionManager的broadcast_to_users方法
            results = await connection_manager.broadcast_to_users(message, usernames)

            # 转换结果格式
            success_list = results.get("success", [])
            failed_list = results.get("failed", [])

            result_dict = {}
            for username in success_list:
                result_dict[username] = True
            for username in failed_list:
                result_dict[username] = False

            success_count = len(success_list)
            logger.info(f"批量推送消息完成，成功: {success_count}, 失败: {len(failed_list)}")
            return result_dict

        except Exception as e:
            logger.error(f"批量推送消息失败: {str(e)}")
            return {username: False for username in usernames}

    async def broadcast_message(self, message: Dict[str, Any],
                              condition: Callable[[str], bool] = None) -> bool:
        """广播消息给所有在线玩家"""
        try:
            connection_manager = await self._get_connection_manager()
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False

            if condition is None:
                # 无条件广播，直接使用ConnectionManager的broadcast方法
                await connection_manager.broadcast(message)
                logger.info("广播消息发送完成（全员）")
                return True
            else:
                # 有条件广播，需要先获取符合条件的玩家列表
                online_players = await self._get_online_players()
                target_players = [player for player in online_players if condition(player)]

                if target_players:
                    # 使用ConnectionManager的broadcast_to_users方法
                    results = await connection_manager.broadcast_to_users(message, target_players)
                    success_count = len(results.get("success", []))
                    logger.info(f"条件广播消息发送完成，成功: {success_count}/{len(target_players)}")
                    return success_count > 0
                else:
                    logger.info("没有符合条件的在线玩家")
                    return True

        except Exception as e:
            logger.error(f"广播消息失败: {str(e)}")
            return False

    async def _get_online_players(self) -> List[str]:
        """获取在线玩家列表"""
        try:
            session_manager = ServiceLocator.get("session_manager")
            if session_manager:
                online_players = await session_manager.get_online_players()
                return list(online_players)
            return []
        except Exception as e:
            logger.error(f"获取在线玩家列表失败: {str(e)}")
            return []

    async def push_system_notification(self, message: str, level: NotificationLevel = NotificationLevel.INFO,
                                     target_players: List[str] = None) -> bool:
        """推送系统通知"""
        try:
            notification_message = MessageModel(
                msgId=MessageId.SYSTEM_NOTIFICATION,
                success=True,
                data={
                    "message": message,
                    "level": level,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            if target_players:
                # 推送给指定玩家
                results = await self.push_to_players(target_players, notification_message)
                success_count = sum(results.values())
                logger.info(f"系统通知推送完成，目标: {len(target_players)}, 成功: {success_count}")
                return success_count > 0
            else:
                # 广播给所有玩家
                sent_count = await self.broadcast_message(notification_message)
                logger.info(f"系统通知广播完成，发送数量: {sent_count}")
                return sent_count > 0
                
        except Exception as e:
            logger.error(f"推送系统通知失败: {str(e)}")
            return False









    # ==================== 通知处理器管理 ====================
    
    def register_notification_handler(self, notification_type: str, handler: Callable):
        """注册通知处理器"""
        self.notification_handlers[notification_type] = handler
        logger.info(f"注册通知处理器: {notification_type}")

    def unregister_notification_handler(self, notification_type: str):
        """注销通知处理器"""
        if notification_type in self.notification_handlers:
            del self.notification_handlers[notification_type]
            logger.info(f"注销通知处理器: {notification_type}")

    async def handle_custom_notification(self, notification_type: str, data: Dict[str, Any]) -> bool:
        """处理自定义通知"""
        try:
            if notification_type in self.notification_handlers:
                handler = self.notification_handlers[notification_type]
                await handler(data)
                return True
            else:
                logger.warning(f"未找到通知处理器: {notification_type}")
                return False
                
        except Exception as e:
            logger.error(f"处理自定义通知失败: {notification_type}, 错误: {str(e)}")
            return False
