from enum import IntEnum, Enum

class MessageId(IntEnum):
    GM = 1001 # GM命令
    ERROR = 1000 # 错误
    HEARTBEAT = 0 # 心跳
    CHAT = 1 # 聊天
    PRIVATE_CHAT =2 # 私聊
    GROUP_CHAT = 3 # 群聊
    GET_ITEMS = 4 # 获取道具
    DELETE_ITEM = 5 # 删除道具
    BROADCAST_MESSAGE = 6 # 广播消息
    SET_NICKNAME = 7 # 设置昵称
    ROLE_INFO = 8 # 角色信息 
    ENTER_GAME = 9 # 进入游戏
    CREATE_ROLE = 10 # 创建角色
    ASSET_CHANGED = 11       # 单个资产变更
    ASSET_BATCH_CHANGED = 12 # 批量资产变更
class ItemType(str,Enum):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文