# 邮件系统管理界面使用指南

## 🎯 **功能概览**

邮件系统管理界面提供了一个直观的Web界面来管理游戏内的邮件系统，支持发送系统邮件、广播邮件和管理附件。

## 🚀 **快速开始**

### **1. 安装依赖**
```bash
pip install jinja2>=3.0.0 python-multipart>=0.0.5
```

### **2. 启动服务器**
```bash
python game_server.py
```
*注意：邮件模板数据库会在首次使用时自动初始化，无需手动操作*

### **3. 访问管理界面**
在浏览器中打开：
```
http://localhost:8000/admin/mail/
```

## 📧 **功能详解**

### **1. 发送系统邮件**

#### **基本信息**
- **收件人类型**: 
  - `用户名`: 直接使用游戏内用户名
  - `昵称`: 通过昵称查找用户
- **收件人**: 输入用户名或昵称，支持自动搜索提示
- **邮件标题**: 最多50个字符
- **邮件内容**: 最多500个字符
- **过期天数**: 1-30天，默认7天

#### **附件系统**
- **道具类型**: 目前只支持道具(ITEM)类型
- **道具ID**: 游戏内道具的唯一标识
- **道具名称**: 显示给玩家的道具名称
- **数量**: 发放的道具数量
- **品质**: 道具品质等级(1-10)

#### **使用步骤**
1. 选择收件人类型
2. 输入收件人信息（支持搜索提示）
3. 填写邮件标题和内容
4. 设置过期天数
5. 添加附件（可选）
6. 点击"发送系统邮件"

### **2. 发送广播邮件**

#### **发送范围**
- **全服玩家**: 发送给所有注册用户
- **指定玩家**: 发送给特定的用户列表

#### **目标用户设置**
- 选择"指定玩家"时，在文本框中输入用户名
- 多个用户名用逗号分隔，例如：`user1,user2,user3`

#### **使用步骤**
1. 填写邮件标题和内容
2. 选择发送范围
3. 如果选择"指定玩家"，输入目标用户列表
4. 设置过期天数
5. 添加附件（可选）
6. 点击"发送广播邮件"

### **3. 邮件管理**

#### **清理过期邮件**
- 删除所有已过期的邮件和相关附件
- 操作不可撤销，请谨慎使用
- 点击"清理过期邮件"并确认

## 🔧 **API接口**

### **发送系统邮件**
```http
POST /admin/mail/send_system_mail
Content-Type: application/json

{
  "receiver_type": "username",
  "receiver_value": "player1",
  "title": "系统奖励",
  "content": "恭喜您获得每日登录奖励！",
  "expire_days": 7,
  "attachments": [
    {
      "item_id": "1001",
      "item_name": "金币",
      "quantity": 100,
      "quality": 1
    }
  ]
}
```

### **发送广播邮件**
```http
POST /admin/mail/send_broadcast_mail
Content-Type: application/json

{
  "title": "维护补偿",
  "content": "感谢您的耐心等待，这是维护补偿。",
  "expire_days": 7,
  "target_type": "all",
  "target_users": null,
  "attachments": [
    {
      "item_id": "2001",
      "item_name": "经验药水",
      "quantity": 5,
      "quality": 3
    }
  ]
}
```

### **搜索用户**
```http
GET /admin/mail/search_user/{query}
```

### **清理过期邮件**
```http
GET /admin/mail/cleanup_expired
```

## 📱 **界面特性**

### **响应式设计**
- 支持桌面和移动设备
- 自适应布局
- 触摸友好的交互

### **用户体验**
- 实时搜索用户提示
- 表单验证和错误提示
- 成功/失败消息反馈
- 动态添加/删除附件

### **安全特性**
- 输入验证和长度限制
- 确认对话框防止误操作
- 错误处理和日志记录

## 🎨 **界面截图说明**

### **主界面**
- 顶部：标题和描述
- 标签页：发送系统邮件、广播邮件、邮件管理
- 表单：直观的输入界面
- 按钮：现代化的操作按钮

### **发送系统邮件页面**
- 收件人选择：类型下拉框 + 搜索输入框
- 邮件内容：标题和内容输入框
- 附件管理：动态添加/删除附件项
- 发送按钮：醒目的主要操作按钮

### **广播邮件页面**
- 发送范围：全服或指定玩家
- 目标用户：条件显示的用户列表输入
- 其他功能与系统邮件类似

### **邮件管理页面**
- 清理功能：简洁的管理操作
- 确认对话框：防止误操作

## 🔍 **故障排除**

### **常见问题**

#### **1. 用户搜索不到**
- 确认用户名或昵称输入正确
- 检查用户是否已注册
- 查看服务器日志确认用户数据

#### **2. 邮件发送失败**
- 检查收件人是否存在
- 验证附件数据格式
- 查看服务器错误日志

#### **3. 附件发放失败**
- 确认道具ID在游戏中存在
- 检查背包系统是否正常
- 验证道具数量和品质参数

#### **4. 界面无法访问**
- 确认服务器已启动
- 检查端口是否被占用
- 验证路由注册是否成功

### **日志查看**
服务器日志会记录所有邮件操作：
- 邮件发送成功/失败
- 用户搜索请求
- 附件发放结果
- 错误详细信息

## 📊 **性能考虑**

### **批量操作**
- 广播邮件会逐个发送给目标用户
- 大量用户时可能需要较长时间
- 建议分批次发送大规模广播

### **缓存优化**
- 用户搜索结果会被缓存
- 邮件数据使用Redis缓存
- 定期清理过期数据

### **资源限制**
- 邮件标题：50字符
- 邮件内容：500字符
- 附件数量：10个
- 过期天数：1-30天

## 🛡️ **安全建议**

### **访问控制**
- 建议在生产环境中添加身份验证
- 限制管理界面的访问IP
- 使用HTTPS协议

### **操作审计**
- 记录所有管理操作
- 监控异常发送行为
- 定期备份邮件数据

### **数据保护**
- 定期清理过期邮件
- 监控存储空间使用
- 备份重要邮件数据

## 🎉 **总结**

邮件系统管理界面提供了：

- ✅ **直观的Web界面** - 无需命令行操作
- ✅ **完整的邮件功能** - 系统邮件、广播邮件、附件管理
- ✅ **用户友好的设计** - 响应式布局、实时反馈
- ✅ **强大的搜索功能** - 支持用户名和昵称搜索
- ✅ **灵活的附件系统** - 支持道具发放，预留扩展接口
- ✅ **完善的错误处理** - 详细的错误信息和日志记录

现在您可以通过简单的Web界面轻松管理游戏内的邮件系统！🚀
