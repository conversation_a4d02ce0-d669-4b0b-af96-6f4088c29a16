import requests
import json
import time
import websocket
import threading
import uuid
from statistics import mean, stdev
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MessageLatencyTester:
    def __init__(self, server_url="http://101.35.12.106:8080/game_server", ws_url="ws://101.35.12.106:8080/game_server/ws/{}"):
        self.server_url = server_url
        self.ws_url = ws_url
        self.token = None
        self.ws = None
        self.sent_messages = {}  # 消息 ID -> 发送时间
        self.latencies = []  # 记录每次消息的延迟
        self.username = "150082842357"
        self.password = "123456t"
        self.message_count = 1  # 发送的消息数量
        self.lock = threading.Lock()

    def login(self):
        """登录并获取 token"""
        try:
            # 注册用户
            response = requests.post(
                f"{self.server_url}/register",
                json={"username": self.username, "password": self.password}
            )
            if response.status_code != 200 or not response.json().get("success"):
                logger.error(f"注册失败: {response.json()}")                

            # 登录
            response = requests.post(
                f"{self.server_url}/login",
                json={"username": self.username, "password": self.password}
            )
            if response.status_code == 200 and response.json().get("success"):
                self.token = response.json()["data"]["access_token"]
                logger.info(f"登录成功，用户: {self.username}, token: {self.token[:10]}...")
                return True
            else:
                logger.error(f"登录失败: {response.json()}")
                return False
        except Exception as e:
            logger.error(f"登录异常: {str(e)}")
            return False

    def on_message(self, ws, message):
        """处理 WebSocket 接收到的消息"""
        try:
            data = json.loads(message)
            msg_id = data.get("msgId")
            content = data.get("data", {}).get("content", "")
            sender = data.get("data", {}).get("sender", "")

            if msg_id == 1 and sender == self.username:  # 公共聊天消息，匹配发送者
                message_id = content.split("test_message_")[1]
                if message_id in self.sent_messages:
                    with self.lock:
                        send_time = self.sent_messages.pop(message_id)
                        latency = (time.time() - send_time) * 1000  # 转换为毫秒
                        self.latencies.append(latency)
                        logger.debug(f"消息 {message_id} 延迟: {latency:.2f} 毫秒")
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")

    def on_error(self, ws, error):
        """处理 WebSocket 错误"""
        logger.error(f"WebSocket 错误: {str(error)}")

    def on_close(self, ws, close_status_code, close_msg):
        """处理 WebSocket 关闭"""
        logger.info(f"WebSocket 关闭: 代码={close_status_code}, 原因={close_msg}")

    def on_open(self, ws):
        """WebSocket 连接打开后发送测试消息"""
        logger.info("WebSocket 连接已打开，开始发送测试消息")
        for i in range(self.message_count):
            message_id = str(uuid.uuid4())
            message = {
                "msgId": 1,  # 公共聊天消息
                "data": {"content": f"test_message_{message_id}"}
            }
            with self.lock:
                self.sent_messages[message_id] = time.time()
            ws.send(json.dumps(message))
            logger.debug(f"发送消息 {message_id}")
            time.sleep(0.01)  # 控制发送速率，避免服务器过载

    def connect_websocket(self):
        """建立 WebSocket 连接"""
        if not self.token:
            logger.error("未获取 token，无法连接 WebSocket")
            return False
        url=f"ws://101.35.12.106:8080/ws/{self.token}"
        try:
            self.ws = websocket.WebSocketApp(
                url,                
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            logger.info(url)
            ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
            ws_thread.start()
            return True
        except Exception as e:
            logger.error(f"WebSocket 连接失败: {str(e)}")
            return False

    def run_test(self):
        """运行测试"""
        logger.info("开始消息延迟测试")
        if not self.login():
            return

        if not self.connect_websocket():
            return

        # 等待测试完成
        start_time = time.time()
        while len(self.latencies) < self.message_count and time.time() - start_time < 60:
            time.sleep(0.1)

        # 输出统计结果
        if self.latencies:
            avg_latency = mean(self.latencies)
            min_latency = min(self.latencies)
            max_latency = max(self.latencies)
            std_latency = stdev(self.latencies) if len(self.latencies) > 1 else 0
            logger.info(f"测试完成，发送 {self.message_count} 条消息，接收 {len(self.latencies)} 条")
            logger.info(f"平均延迟: {avg_latency:.2f} 毫秒")
            logger.info(f"最小延迟: {min_latency:.2f} 毫秒")
            logger.info(f"最大延迟: {max_latency:.2f} 毫秒")
            logger.info(f"标准差: {std_latency:.2f} 毫秒")
        else:
            logger.warning("未接收到任何消息，可能服务器未响应")

        # 清理
        if self.ws:
            self.ws.close()

if __name__ == "__main__":
    tester = MessageLatencyTester()
    tester.run_test()