import json
import random
import uuid
import logging
import threading
import time
from queue import Queue
from locust import HttpUser, task, between, events
import requests
import websocket
import string
from typing import List, Dict, Optional
PAYLOAD_SIZE = 100
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# 共享用户池
USER_POOL: List[Dict[str, str]] = []
USER_POOL_LOCK = threading.Lock()
# 生成随机 payload
def generate_payload(size: int) -> str:
    return ''.join(random.choices(string.ascii_letters + string.digits, k=size))
class GameServerUser(HttpUser):
    wait_time = between(5, 10)  # 任务间随机等待 5-10 秒
    host = "http://*************:8080/game_server"  # 服务器地址

    # 初始化用户池
    @classmethod
    def on_user_create(cls):
        """在用户创建时初始化用户池"""
        with USER_POOL_LOCK:
            if not USER_POOL:
                logger.info("用户池初始化完成")


    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token: Optional[str] = None
        self.username: Optional[str] = None
        self.item_ids: List[str] = []  # 存储用户拥有的道具 ID
        self.ws: Optional[websocket.WebSocket] = None
        self.ws_thread: Optional[threading.Thread] = None
        self.ws_queue: Queue = Queue()  # 接收 WebSocket 消息
        self.running = False

    def on_start(self):
        """初始化用户：注册、登录、建立 WebSocket 连接"""
        try:
            # 动态生成用户名
            self.username = f"j{uuid.uuid4().hex[:12]}"
            password = "test1123"

            # 注册用户
            resp = self.client.post(
                "/register",
                json={"username": self.username, "password": password},
                name="register",
                timeout=10
            )
            if resp.status_code == 200:
                data = resp.json()
                logger.warning(f"注册: {self.username}, {data}")
                if not data.get("success"):
                    logger.warning(f"注册失败: {self.username}, {data.get('message')}")
                    return
            else:
                logger.error(f"注册请求失败: {self.username}, 状态码: {resp}")
                return

            # 登录获取 token
            resp = self.client.post(
                "/login",
                json={"username": self.username, "password": password},
                name="login",
                timeout=10
            )
            if resp.status_code == 200:
                data = resp.json()
                if data.get("success"):
                    self.token = data["data"]["access_token"]
                    with USER_POOL_LOCK:
                        USER_POOL.append({"username": self.username, "token": self.token})
                else:
                    logger.error(f"登录失败: {self.username}, {data.get('message')}")
                    return
            else:
                logger.error(f"登录请求失败: {self.username}, 状态码: {resp}")
                return

            # 启动 WebSocket 连接
            self.running = True
            self.ws = websocket.WebSocket()
            ws_url = f"ws://*************:8080/ws/{self.token}"
            try:
                self.ws.connect(ws_url)
                self.ws_thread = threading.Thread(target=self.receive_messages, daemon=True)
                self.ws_thread.start()
                logger.info(f"WebSocket 连接成功: {self.username}")
            except Exception as e:
                logger.error(f"WebSocket 连接失败: {self.username}, 错误: {str(e)}")
                self.running = False
                return

            logger.info(f"用户初始化成功: {self.username}")
        except Exception as e:
            logger.error(f"用户初始化失败: {self.username}, 错误: {str(e)}")
            self.environment.runner.quit()

    def receive_messages(self):
        """接收 WebSocket 消息"""
        while self.running:
            try:
                message = self.ws.recv()
                data = json.loads(message)
                if data.get("msgId") == -1:
                    logger.warning(f"WebSocket 错误: {self.username}, {data.get('data', {}).get('error', '')}")
                elif data.get("msgId") == 2 and data.get("data", {}).get("item", {}).get("id"):
                    self.item_ids.append(data["data"]["item"]["id"])
                if data.get('error'):
                    logger.error(f"WebSocket 错误: {self.username}, {data['error']}")
                    events.request.fire(
                        request_type="WebSocket",
                        name="send_heartbeat",
                        response_time=0,
                        response_length=0,
                        exception= Exception()
                    )
                self.ws_queue.put(data)
            except websocket.WebSocketConnectionClosedException:
                logger.info(f"WebSocket 断开: {self.username}")
                self.running = False
                break
            except Exception as e:
                logger.error(f"接收消息失败: {self.username}, 错误: {str(e)}")
                break

    @task(10)
    def send_heartbeat(self):
        """发送心跳消息"""
        if not self.running or not self.ws:
            return
        try:
            start_time = time.time()
            self.ws.send(json.dumps({
                "msgId": 0,
                "data": {"type": "heartbeat"}
            }))
            events.request.fire(
                request_type="WebSocket",
                name="send_heartbeat",
                response_time=(time.time() - start_time) * 1000,
                response_length=0,
                exception=None
            )
        except Exception as e:
            logger.error(f"发送心跳失败: {self.username}, 错误: {str(e)}")
            events.request.fire(
                request_type="WebSocket",
                name="send_heartbeat",
                response_time=0,
                response_length=0,
                exception=e
            )

    @task(1)
    def send_private_chat(self):
        """发送私聊消息"""
        if not self.running or not self.ws:    
            return
        with USER_POOL_LOCK:
            if len(USER_POOL) < 2:
                return
            target_user = random.choice([u["username"] for u in USER_POOL if u["username"] != self.username])
        try:
            start_time = time.time()
            msg = {
                "msgId": 1,
                "data": {
                    "type": "private",
                    "target_user": target_user,
                    "message": f"Hi from {self.username} at {time.time()}  and say {generate_payload(PAYLOAD_SIZE)}!"
                }
            }
            self.ws.send(json.dumps(msg))
            events.request.fire(
                request_type="WebSocket",
                name="send_private_chat",
                response_time=(time.time() - start_time) * 1000,
                response_length=len(msg["data"]["message"]),
                exception=None
            )
        except Exception as e:
            logger.error(f"发送私聊失败: {self.username} -> {target_user}, 错误: {str(e)}")
            events.request.fire(
                request_type="WebSocket",
                name="send_private_chat",
                response_time=0,
                response_length=0,
                exception=e
            )
    @task(1)
    def send_chat(self):
        try:
            start_time = time.time()
            if not self.running or not self.ws:
                return
            message = {
                "msgId": 1,
                "data": {"content": f"Chat from {self.username}: say {generate_payload(PAYLOAD_SIZE//8)}"}
            }
            self.ws.send(json.dumps(message))
            events.request.fire(
                request_type="WebSocket",
                name="send_chat",
                response_time=(time.time() - start_time) * 1000,
                response_length=len(json.dumps(message)),
                exception=None
            )
        except Exception as e:
            logger.error(f"发送聊天消息失败: {self.username}, 错误: {str(e)}")
            events.request.fire(
                request_type="WebSocket",
                name="send_chat",
                response_time=0,
                response_length=0,
                exception=e
            )
    @task(150)
    def send_item_operation(self):
        if not self.running or not self.ws:
            return
        try:
            start_time = time.time()
            op_type = random.choice(["add", "increase"])
            if op_type == "add":
                message = {
                    "msgId": 5,
                    "data": {
                        "defid": random.randint(1, 1000),
                        "attributes": {"type": "test", "value": generate_payload(PAYLOAD_SIZE // 4)},
                        "quantity": random.randint(1, 10)
                    }
                }
            elif op_type == "increase":
                message = {
                    "msgId": 8,
                    "data": {
                        "defid": random.randint(1, 1000),
                        "amount": random.randint(1, 5)
                    }
                }
        
            self.ws.send(json.dumps(message))
            events.request.fire(
                request_type="WebSocket",
                name="send_item_operation",
                response_time=(time.time() - start_time) * 1000,
                response_length=len(json.dumps(message)),
                exception=None
            )
        except Exception as e:
            logger.error(f"发送道具失败: {self.username} 错误: {str(e)}")
            events.request.fire(
                request_type="WebSocket",
                name="send_item_operation",
                response_time=0,
                response_length=0,
                exception=e
            )
    def on_stop(self):
        """清理资源"""
        try:
            self.running = False
            if self.ws:
                self.ws.close()
            if self.ws_thread:
                self.ws_thread.join(timeout=1)
            logger.info(f"用户清理完成: {self.username}")
        except Exception as e:
            logger.error(f"用户清理失败: {self.username}, 错误: {str(e)}")