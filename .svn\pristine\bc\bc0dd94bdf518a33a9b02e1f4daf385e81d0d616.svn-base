import logging
import traceback
import json
import uuid
import hashlib
import time
import functools
from fastapi import WebSocket, HTTPException
from typing import Dict, Any, Optional, Callable, Union
from fastapi.responses import JSONResponse
from datetime import datetime
from enum import Enum
from bson import ObjectId
from enums import ItemType
# 使用基本的日志配置，避免循环导入
logger = logging.getLogger(__name__)

def generate_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())

def calculate_hash(data: str) -> str:
    """计算字符串的哈希值"""
    return hashlib.md5(data.encode('utf-8')).hexdigest()

def generate_timestamp_id() -> str:
    """生成基于时间戳的ID"""
    return f"{int(time.time() * 1000)}"

def safe_operation(operation_name: str = "操作", log_errors: bool = True):
    """
    安全操作装饰器，提供统一的异常处理和资源清理

    Args:
        operation_name: 操作名称，用于日志记录
        log_errors: 是否记录错误日志
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    logger.error(f"{operation_name}失败: {str(e)}")
                    logger.error(traceback.format_exc())
                # 重新抛出异常，让调用者决定如何处理
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    logger.error(f"{operation_name}失败: {str(e)}")
                    logger.error(traceback.format_exc())
                # 重新抛出异常，让调用者决定如何处理
                raise

        # 根据函数是否为协程选择合适的包装器
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

async def handle_error(websocket: Optional[WebSocket] = None, 
                      message: str = "An error occurred", 
                      code: int = 500, 
                      exception: Optional[Exception] = None) -> Dict[str, Any]:
    """处理错误并返回标准错误响应"""
    error_data = {
        "success": False,
        "code": code,
        "message": message
    }
    
    if exception:
        logger.error(f"Error: {message}, Exception: {str(exception)}")
        logger.error(traceback.format_exc())
    else:
        logger.error(f"Error: {message}")
    
    if websocket:
        try:
            await websocket.send_json({
                "type": "error",
                "data": error_data
            })
        except Exception as e:
            logger.error(f"Failed to send error to websocket: {str(e)}")
    
    return error_data
def format_log_message(message: str, username: Optional[str] = None, 
                      worker_id: Optional[str] = None) -> str:
    """格式化日志消息"""
    prefix = []
    if username:
        prefix.append(f"User: {username}")
    if worker_id:
        prefix.append(f"Worker: {worker_id}")
    
    if prefix:
        return f"[{' | '.join(prefix)}] {message}"
    return message

class JSONEncoder(json.JSONEncoder):
    """扩展的JSON编码器，支持更多类型"""
    def default(self, obj):
        from datetime import datetime, date
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def to_json(data: Any) -> str:
    """将数据转换为JSON字符串"""
    return json.dumps(data, cls=JSONEncoder)

def from_json(json_str: str) -> Any:
    """将JSON字符串转换为数据"""
    return json.loads(json_str)
def process_db_item(item: dict) -> dict:
        """处理数据库返回的对象，处理特殊类型"""
        if not item:
            return item
        # 处理ObjectId
        if "_id" in item:
            item["_id"] = str(item["_id"])
        # 处理日期
        for field in ["created_at", "updated_at"]:
            if field in item and isinstance(item[field], datetime):
                item[field] = item[field].isoformat()
        return item
def format_cache_key(key_template: str, **kwargs) -> str:
    """格式化缓存键模板，替换占位符
    
    Args:
        key_template (str): 缓存键模板，如 'game:v2:users:{username}'
        **kwargs: 要替换的参数，如 username='user123'
        
    Returns:
        str: 格式化后的缓存键
    """
    try:
        key = key_template
        for k, v in kwargs.items():
            key = key.replace(f"{{{k}}}", str(v))
        return key
    except Exception as e:
        logger.warning(f"格式化缓存键失败: {str(e)}, 模板: {key_template}, 参数: {kwargs}")
        return key_template

class HTTPError(HTTPException):
    """HTTP错误类"""
    def __init__(self, status_code: int = 500, detail: str = "Internal Server Error"):
        super().__init__(status_code=status_code, detail=detail)

async def handle_error_json(
    msg: str = "服务器错误",
    code: int = 500,
    log_level: str = "error",
    exception: Optional[Exception] = None
) -> JSONResponse:
    """统一处理错误，返回 JSONResponse，使用真实的 HTTP 状态码"""
    log_message = f"{msg}{f': {str(exception)}' if exception else ''}"
    if exception:
        logger.debug(f"堆栈: {traceback.format_exc()}")
    getattr(logger, log_level)(log_message)
    return JSONResponse(
        status_code=200,
        content={
            "success": False,
            "code": code,
            "message": log_message
        },
        headers={"Content-Type": "application/json"}
    )
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        try:
            if isinstance(obj, ObjectId):
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, bytes):
                return obj.decode('utf-8')
            elif isinstance(obj, Enum):  # 添加枚举支持
                return obj.value
            return super().default(obj)
        except Exception as e:
            logger.error(f"JSON 编码失败: {str(e)}，对象: {obj}")
            return str(obj)
class CustomJSONDecoder(json.JSONDecoder):
    def __init__(self, *args, **kwargs):
        super().__init__(object_hook=self.custom_object_hook, *args, **kwargs)
    
    def custom_object_hook(self, obj):
        """处理反序列化时的类型转换"""
        if isinstance(obj, dict):
            # 处理type字段
            if "type" in obj:
                type_value = obj["type"]
                if type_value == "equipment":
                    obj["type"] = ItemType.EQUIPMENT
                elif type_value == "item":
                    obj["type"] = ItemType.ITEM
                elif type_value == "rune":
                    obj["type"] = ItemType.RUNE

        return obj