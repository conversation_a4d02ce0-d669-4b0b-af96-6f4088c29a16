# -*- coding: utf-8 -*-

# 简单测试GuildMember的player_id属性
import sys
import os

# 测试基本的字典操作
test_data = {
    "guild_id": "guild123",
    "player_id": "player123",
    "position": "member",
    "contribution": 100
}

print("Test data:", test_data)
print("Player ID:", test_data.get("player_id"))

# 测试属性访问
class TestMember:
    def __init__(self, player_info):
        self.playerInfo = player_info
    
    @property
    def player_id(self):
        return self.playerInfo.id if self.playerInfo else ""

class TestUserInfo:
    def __init__(self, user_id):
        self.id = user_id

# 创建测试对象
user_info = TestUserInfo("player123")
member = TestMember(user_info)

print("Member player_id:", member.player_id)
print("Test passed!")
