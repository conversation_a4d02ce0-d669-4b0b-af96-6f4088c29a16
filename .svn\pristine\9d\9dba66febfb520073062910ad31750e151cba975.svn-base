from enum import IntEnum, auto

class MessageId(IntEnum):
    GM = -2 # GM命令
    ERROR = -1 # 错误
    HEARTBEAT = 0 # 心跳
    CHAT = auto() # 聊天
    PRIVATE_CHAT = auto() # 私聊
    GROUP_CHAT = auto() # 群聊
    GET_ITEMS = auto() # 获取道具
    DELETE_ITEM = auto() # 删除道具
    BROADCAST_MESSAGE = auto() # 广播消息
    SET_NICKNAME = auto() # 设置昵称
    ROLE_INFO = auto() # 角色信息
    ENTER_GAME = auto() # 进入游戏
    CREATE_ROLE = auto() # 创建角色
class ItemType(str):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文