from enum import IntEnum

class MessageId(IntEnum):
    HEARTBEAT = 0 # 心跳
    CHAT = 1 # 聊天
    PRIVATE_CHAT = 2 # 私聊
    GROUP_CHAT = 3 # 群聊
    ADD_EQUIP = 4 # 添加装备
    ADD_ITEM = 5 # 添加道具
    GET_ITEMS = 6 # 获取道具
    DELETE_ITEM = 7 # 删除道具
    INCREASE_ITEM_QUANTITY = 8 # 增加道具数量
    DECREASE_ITEM_QUANTITY = 9 # 减少道具数量
    BROADCAST_MESSAGE = 10 # 广播消息
    BATCH_ADD_ITEMS = 11 # 批量添加道具
    SET_NICKNAME = 12 # 设置昵称
    ROLE_INFO = 13 # 角色信息
    ENTER_GAME = 14 # 进入游戏
    CREATE_ROLE = 15 # 创建角色
    ERROR = -1 # 错误
    GM = -2 # GM命令
class ItemType(str):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文