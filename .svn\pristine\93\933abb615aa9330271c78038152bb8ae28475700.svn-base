"""
调度器服务健康检查实现
为各种服务提供健康检查方法
"""

import asyncio
import time
from typing import Any, Dict
from logger_config import setup_logger
from redis_manager import RedisManager
from mongodb_manager import MongoDBManager
from ConnectionManager import ConnectionManager
from xxsg.monster_cooldown import MonsterCooldownManager

logger = setup_logger(__name__)

class SchedulerHealthChecks:
    """调度器服务健康检查"""
    
    @staticmethod
    async def check_redis_health(redis_manager: RedisManager) -> bool:
        """检查Redis服务健康状态"""
        try:
            redis_client = await redis_manager.get_redis()
            if redis_client is None:
                return False

            # 执行简单的ping命令
            result = await redis_client.ping()
            return result is True

        except Exception as e:
            logger.error(f"Redis健康检查失败: {str(e)}")
            return False
    
    @staticmethod
    async def check_mongodb_health(mongodb_manager: MongoDBManager) -> bool:
        """检查MongoDB服务健康状态"""
        try:
            # 检查管理器实例
            if mongodb_manager is None:
                logger.error("MongoDB管理器实例为None")
                return False

            # 获取数据库实例
            db = await mongodb_manager.get_db()
            if db is None:
                logger.error("无法获取MongoDB数据库实例")
                return False

            # 执行简单的ping命令
            result = await db.command("ping")
            if result and result.get("ok") == 1.0:
                return True
            else:
                logger.error(f"MongoDB ping命令返回异常结果: {result}")
                return False

        except Exception as e:
            logger.error(f"MongoDB健康检查失败: {str(e)}")
            return False
    
    @staticmethod
    async def check_connection_manager_health(conn_manager: ConnectionManager) -> bool:
        """检查连接管理器健康状态"""
        try:
            # 检查连接管理器是否已初始化且未关闭
            if hasattr(conn_manager, '_closed') and conn_manager._closed:
                return False
            
            # 检查是否有活跃的连接管理能力
            if hasattr(conn_manager, 'active_connections'):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"连接管理器健康检查失败: {str(e)}")
            return False
    
    @staticmethod
    async def check_monster_cooldown_manager_health(cooldown_manager: MonsterCooldownManager) -> bool:
        """检查怪物冷却管理器健康状态"""
        try:
            # 检查管理器实例
            if cooldown_manager is None:
                logger.error("怪物冷却管理器实例为None")
                return False

            # 检查Redis连接
            redis_client = await cooldown_manager._get_redis_client()
            if redis_client is None:
                logger.error("无法从怪物冷却管理器获取Redis客户端")
                return False

            # 执行简单的Redis操作测试
            test_key = "health_check:monster_cooldown"
            await redis_client.set(test_key, "test", ex=5)
            result = await redis_client.get(test_key)
            await redis_client.delete(test_key)

            if result is not None:
                logger.debug("怪物冷却管理器健康检查通过")
                return True
            else:
                logger.error("怪物冷却管理器Redis操作测试失败")
                return False

        except Exception as e:
            logger.error(f"怪物冷却管理器健康检查失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

class ServiceInitializers:
    """服务初始化器"""
    
    @staticmethod
    async def init_redis_manager() -> RedisManager:
        """初始化Redis管理器"""
        logger.info("初始化Redis管理器...")
        redis_manager = await RedisManager.get_instance()
        
        # 测试连接
        redis_client = await redis_manager.get_redis()
        await redis_client.ping()
        
        logger.info("Redis管理器初始化完成")
        return redis_manager
    
    @staticmethod
    async def init_mongodb_manager() -> MongoDBManager:
        """初始化MongoDB管理器"""
        logger.info("初始化MongoDB管理器...")
        mongodb_manager = await MongoDBManager.get_instance()
        
        # 测试连接
        db = await mongodb_manager.get_db()
        if db is not None:
            await db.command("ping")
        else:
            raise Exception("无法获取MongoDB数据库实例")
        
        logger.info("MongoDB管理器初始化完成")
        return mongodb_manager
    
    @staticmethod
    async def init_connection_manager() -> ConnectionManager:
        """初始化连接管理器"""
        logger.info("初始化连接管理器...")
        connection_manager = ConnectionManager()
        await connection_manager.initialize()
        
        logger.info("连接管理器初始化完成")
        return connection_manager
    
    @staticmethod
    async def init_monster_cooldown_manager() -> MonsterCooldownManager:
        """初始化怪物冷却管理器"""
        logger.info("初始化怪物冷却管理器...")

        try:
            # 确保配置已加载
            from config import config
            config.get_monster_config()

            cooldown_manager = MonsterCooldownManager()

            # 测试Redis连接
            redis_client = await cooldown_manager._get_redis_client()
            if redis_client is None:
                raise Exception("无法获取Redis客户端")

            # 测试Redis连接
            await redis_client.ping()
            logger.info("怪物冷却管理器Redis连接测试成功")

            # 尝试恢复冷却数据（如果失败不影响初始化）
            try:
                restore_success = await cooldown_manager.restore_from_mongodb()
                if restore_success:
                    logger.info("怪物冷却数据恢复成功")
                else:
                    logger.warning("怪物冷却数据恢复失败，但不影响管理器初始化")
            except Exception as e:
                logger.warning(f"恢复怪物冷却数据时出错，但不影响管理器初始化: {str(e)}")

            logger.info("怪物冷却管理器初始化完成")
            return cooldown_manager

        except Exception as e:
            logger.error(f"初始化怪物冷却管理器失败: {str(e)}")
            raise

class TaskMetrics:
    """任务执行指标收集"""
    
    def __init__(self):
        self.execution_count: Dict[str, int] = {}
        self.failure_count: Dict[str, int] = {}
        self.last_execution_time: Dict[str, float] = {}
        self.avg_execution_time: Dict[str, float] = {}
        self.total_execution_time: Dict[str, float] = {}
    
    def record_task_start(self, task_name: str) -> float:
        """记录任务开始执行"""
        start_time = time.time()
        logger.debug(f"任务 {task_name} 开始执行")
        return start_time
    
    def record_task_completion(self, task_name: str, start_time: float, success: bool = True):
        """记录任务执行完成"""
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 更新执行次数
        self.execution_count[task_name] = self.execution_count.get(task_name, 0) + 1
        
        if success:
            # 更新执行时间统计
            self.last_execution_time[task_name] = execution_time
            self.total_execution_time[task_name] = self.total_execution_time.get(task_name, 0) + execution_time
            self.avg_execution_time[task_name] = (
                self.total_execution_time[task_name] / self.execution_count[task_name]
            )
            
            logger.info(f"任务 {task_name} 执行成功，耗时: {execution_time:.2f}秒")
        else:
            # 更新失败次数
            self.failure_count[task_name] = self.failure_count.get(task_name, 0) + 1
            logger.error(f"任务 {task_name} 执行失败，耗时: {execution_time:.2f}秒")
    
    def get_task_stats(self, task_name: str) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            "execution_count": self.execution_count.get(task_name, 0),
            "failure_count": self.failure_count.get(task_name, 0),
            "success_rate": self._calculate_success_rate(task_name),
            "last_execution_time": self.last_execution_time.get(task_name, 0),
            "avg_execution_time": self.avg_execution_time.get(task_name, 0)
        }
    
    def _calculate_success_rate(self, task_name: str) -> float:
        """计算任务成功率"""
        total = self.execution_count.get(task_name, 0)
        failures = self.failure_count.get(task_name, 0)
        
        if total == 0:
            return 0.0
        
        return ((total - failures) / total) * 100.0
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务统计信息"""
        all_tasks = set(self.execution_count.keys()) | set(self.failure_count.keys())
        return {task_name: self.get_task_stats(task_name) for task_name in all_tasks}

# 全局任务指标收集器
task_metrics = TaskMetrics()

def with_metrics(task_name: str):
    """任务指标收集装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = task_metrics.record_task_start(task_name)
            try:
                result = await func(*args, **kwargs)
                task_metrics.record_task_completion(task_name, start_time, success=True)
                return result
            except Exception as e:
                task_metrics.record_task_completion(task_name, start_time, success=False)
                raise e
        return wrapper
    return decorator
