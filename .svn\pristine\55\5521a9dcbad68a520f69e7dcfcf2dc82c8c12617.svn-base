[Server]
host = 0.0.0.0
port = 8000
workers = 4
debug = true

[Redis]
host = *************
port = 6379
password = jnmogod
db = 0
max_connections = 10000
socket_timeout = 5
socket_connect_timeout = 5

[MongoDB]
host = *************
port = 27017
username = root
password = Pg5aZODAq8W4ZqgT
database = game_server

[JWT]
secret_key = your-secret-keyyour-secret-keyyour-secret-keyyour-secret-keyyour-secret-key
algorithm = HS256
access_token_expire_minutes = 1440

[RabbitMQ]
host = *************
port = 5672
username = admin
password = P4e3Bcm2qTA0EawK
virtual_host = bthost
exchange_name = game_exchange
queue_name = game_queue
broadcast_exchange = broadcast_exchange
personal_exchange = personal_exchange
max_retries = 5
retry_delay = 2

[Log]
level = INFO
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
file = logs/server.log
max_bytes = 10485760
backup_count = 5