<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法测试</title>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="result"></div>
    
    <script>
        console.log('开始测试JavaScript语法...');
        
        // 测试加载API模块
        try {
            console.log('测试1: 加载API模块...');
        } catch (error) {
            console.error('API模块错误:', error);
            document.getElementById('result').innerHTML += '<p style="color: red;">API模块语法错误: ' + error.message + '</p>';
        }
    </script>
    
    <!-- 加载JavaScript文件 -->
    <script src="js/api.js"></script>
    <script>
        try {
            console.log('测试2: API类实例化...');
            if (window.shopAPI) {
                console.log('✅ API模块加载成功');
                document.getElementById('result').innerHTML += '<p style="color: green;">✅ API模块加载成功</p>';
            } else {
                throw new Error('shopAPI未定义');
            }
        } catch (error) {
            console.error('API实例化错误:', error);
            document.getElementById('result').innerHTML += '<p style="color: red;">❌ API实例化错误: ' + error.message + '</p>';
        }
    </script>
    
    <script src="js/shop-manager.js"></script>
    <script>
        try {
            console.log('测试3: ShopManager类实例化...');
            if (window.shopManager) {
                console.log('✅ ShopManager模块加载成功');
                document.getElementById('result').innerHTML += '<p style="color: green;">✅ ShopManager模块加载成功</p>';
            } else {
                throw new Error('shopManager未定义');
            }
        } catch (error) {
            console.error('ShopManager实例化错误:', error);
            document.getElementById('result').innerHTML += '<p style="color: red;">❌ ShopManager实例化错误: ' + error.message + '</p>';
        }
    </script>
    
    <script src="js/item-manager.js"></script>
    <script>
        try {
            console.log('测试4: ItemManager类实例化...');
            if (window.itemManager) {
                console.log('✅ ItemManager模块加载成功');
                document.getElementById('result').innerHTML += '<p style="color: green;">✅ ItemManager模块加载成功</p>';
            } else {
                throw new Error('itemManager未定义');
            }
        } catch (error) {
            console.error('ItemManager实例化错误:', error);
            document.getElementById('result').innerHTML += '<p style="color: red;">❌ ItemManager实例化错误: ' + error.message + '</p>';
        }
    </script>
    
    <script src="js/main.js"></script>
    <script>
        try {
            console.log('测试5: Main模块加载...');
            if (window.shopAdminApp) {
                console.log('✅ Main模块加载成功');
                document.getElementById('result').innerHTML += '<p style="color: green;">✅ Main模块加载成功</p>';
            } else {
                throw new Error('shopAdminApp未定义');
            }
        } catch (error) {
            console.error('Main模块错误:', error);
            document.getElementById('result').innerHTML += '<p style="color: red;">❌ Main模块错误: ' + error.message + '</p>';
        }
        
        // 最终测试结果
        setTimeout(() => {
            console.log('所有测试完成');
            document.getElementById('result').innerHTML += '<hr><p><strong>测试完成！请查看浏览器控制台获取详细信息。</strong></p>';
        }, 1000);
    </script>
</body>
</html>
