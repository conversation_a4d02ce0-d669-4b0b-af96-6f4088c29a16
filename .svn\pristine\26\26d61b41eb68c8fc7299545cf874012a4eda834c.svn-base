<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>切换开关颜色测试</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            background-color: #f8fafc;
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 40px;
            font-size: 2rem;
        }
        .test-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        .toggle-demo {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }
        .toggle-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .toggle-label-demo {
            font-weight: 600;
            color: #374151;
        }
        .color-info {
            font-size: 0.875rem;
            color: #6b7280;
            text-align: center;
            margin-top: 10px;
        }
        .contrast-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .contrast-item {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .bg-green {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        .bg-red {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        .bg-dark {
            background: #1f2937;
            color: white;
        }
        .bg-light {
            background: #f3f4f6;
            color: #1f2937;
        }
        .text-shadow-strong {
            text-shadow: 0 1px 3px rgba(0,0,0,0.8);
        }
        .text-shadow-medium {
            text-shadow: 0 1px 2px rgba(0,0,0,0.6);
        }
        .text-shadow-light {
            text-shadow: 0 1px 1px rgba(0,0,0,0.4);
        }
        .instructions {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
        .instructions ul {
            color: #1e3a8a;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎨 切换开关颜色可见性测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ul>
                <li>请测试下方的切换开关，确认"已激活"和"已禁用"文字清晰可见</li>
                <li>尝试在不同的光线条件下查看</li>
                <li>如果您有视觉障碍，请测试高对比度模式</li>
                <li>点击开关测试状态切换时的文字可见性</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="section-title">🔄 实际切换开关测试</div>
            <div class="toggle-demo">
                <div class="toggle-item">
                    <div class="toggle-label-demo">商店状态控制</div>
                    <div class="shop-status-toggle">
                        <div class="toggle-switch">
                            <input type="checkbox" id="toggle-test-1" class="toggle-input" checked>
                            <label for="toggle-test-1" class="toggle-slider">
                                <span class="toggle-text">已激活</span>
                            </label>
                        </div>
                    </div>
                    <div class="color-info">当前状态：激活（绿色背景）</div>
                </div>

                <div class="toggle-item">
                    <div class="toggle-label-demo">商店状态控制</div>
                    <div class="shop-status-toggle">
                        <div class="toggle-switch">
                            <input type="checkbox" id="toggle-test-2" class="toggle-input">
                            <label for="toggle-test-2" class="toggle-slider">
                                <span class="toggle-text">已禁用</span>
                            </label>
                        </div>
                    </div>
                    <div class="color-info">当前状态：禁用（红色背景）</div>
                </div>

                <div class="toggle-item">
                    <div class="toggle-label-demo">可交互测试</div>
                    <div class="shop-status-toggle">
                        <div class="toggle-switch">
                            <input type="checkbox" id="toggle-test-3" class="toggle-input" onchange="updateToggleText(this)">
                            <label for="toggle-test-3" class="toggle-slider">
                                <span class="toggle-text">已禁用</span>
                            </label>
                        </div>
                    </div>
                    <div class="color-info">点击测试状态切换</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="section-title">🎨 文字对比度测试</div>
            <div class="contrast-test">
                <div class="contrast-item bg-green">
                    <div class="text-shadow-strong">已激活</div>
                    <small>绿色背景 + 强阴影</small>
                </div>
                <div class="contrast-item bg-red">
                    <div class="text-shadow-strong">已禁用</div>
                    <small>红色背景 + 强阴影</small>
                </div>
                <div class="contrast-item bg-green">
                    <div class="text-shadow-medium">已激活</div>
                    <small>绿色背景 + 中等阴影</small>
                </div>
                <div class="contrast-item bg-red">
                    <div class="text-shadow-medium">已禁用</div>
                    <small>红色背景 + 中等阴影</small>
                </div>
                <div class="contrast-item bg-green">
                    <div class="text-shadow-light">已激活</div>
                    <small>绿色背景 + 轻微阴影</small>
                </div>
                <div class="contrast-item bg-red">
                    <div class="text-shadow-light">已禁用</div>
                    <small>红色背景 + 轻微阴影</small>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="section-title">♿ 无障碍测试</div>
            <div class="contrast-test">
                <div class="contrast-item bg-dark">
                    <div style="font-weight: 900; text-shadow: 0 0 0 #ffffff;">已激活</div>
                    <small>高对比度模式</small>
                </div>
                <div class="contrast-item bg-light">
                    <div style="font-weight: 900; color: #000000;">已禁用</div>
                    <small>高对比度模式</small>
                </div>
            </div>
            <p style="margin-top: 20px; color: #6b7280; font-size: 0.875rem;">
                💡 提示：在系统设置中启用高对比度模式来测试无障碍支持
            </p>
        </div>

        <div class="test-section">
            <div class="section-title">📊 可见性评分</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="text-align: center; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2rem; color: #10b981;">✅</div>
                    <div style="font-weight: 600; margin: 10px 0;">优秀</div>
                    <div style="font-size: 0.875rem; color: #6b7280;">文字清晰可见，对比度高</div>
                </div>
                <div style="text-align: center; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2rem; color: #f59e0b;">⚠️</div>
                    <div style="font-weight: 600; margin: 10px 0;">一般</div>
                    <div style="font-size: 0.875rem; color: #6b7280;">文字可见但需要仔细看</div>
                </div>
                <div style="text-align: center; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2rem; color: #ef4444;">❌</div>
                    <div style="font-weight: 600; margin: 10px 0;">较差</div>
                    <div style="font-size: 0.875rem; color: #6b7280;">文字难以看清，需要优化</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateToggleText(checkbox) {
            const textSpan = checkbox.nextElementSibling.querySelector('.toggle-text');
            const infoDiv = checkbox.closest('.toggle-item').querySelector('.color-info');
            
            if (checkbox.checked) {
                textSpan.textContent = '已激活';
                infoDiv.textContent = '当前状态：激活（绿色背景）';
            } else {
                textSpan.textContent = '已禁用';
                infoDiv.textContent = '当前状态：禁用（红色背景）';
            }
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 切换开关颜色测试页面已加载');
            console.log('请测试各个开关的文字可见性');
            
            // 添加键盘支持提示
            document.addEventListener('keydown', function(e) {
                if (e.key === 'h' && e.ctrlKey) {
                    alert('💡 快捷键提示：\n- 使用Tab键导航到开关\n- 使用空格键切换状态\n- 使用Ctrl+H显示此帮助');
                }
            });
        });
    </script>
</body>
</html>
