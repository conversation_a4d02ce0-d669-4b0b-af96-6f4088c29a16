import sys
import os
import asyncio
import json
import time
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from xxsg.monster_cooldown import Monster<PERSON>ooldownManager, CooldownType
from game_database import DatabaseManager
from service_locator import ServiceLocator

async def test_monster_cooldown():
    """Test the monster cooldown system"""
    print("Initializing database manager...")
    db_manager = DatabaseManager()
    await db_manager.startup()
    
    # Register the database manager with the service locator
    ServiceLocator.register("db_manager", db_manager)
    
    # Create a cooldown manager
    cooldown_manager = MonsterCooldownManager()
    
    # Test monster IDs and usernames
    monster_id = "test_monster_001"
    username = "test_user"
    guild_id = "test_guild"
    
    print("\n=== Testing Personal Cooldown ===")
    # Set personal cooldown
    print(f"Setting personal cooldown for monster {monster_id}, user {username}...")
    success = await cooldown_manager.set_cooldown(
        monster_id, username, CooldownType.PERSONAL, 60  # 60 seconds cooldown
    )
    print(f"Set cooldown result: {success}")
    
    # Check personal cooldown
    print(f"Checking personal cooldown for monster {monster_id}, user {username}...")
    cooldown_info = await cooldown_manager.check_cooldown(monster_id, username)
    print(f"Cooldown info: {json.dumps(cooldown_info, indent=2)}")
    
    print("\n=== Testing Guild Cooldown ===")
    # Set guild cooldown
    monster_id_2 = "test_monster_002"
    print(f"Setting guild cooldown for monster {monster_id_2}, guild {guild_id}...")
    success = await cooldown_manager.set_cooldown(
        monster_id_2, username, CooldownType.GUILD, 120, guild_id  # 120 seconds cooldown
    )
    print(f"Set cooldown result: {success}")
    
    # Check guild cooldown
    print(f"Checking guild cooldown for monster {monster_id_2}, guild {guild_id}...")
    cooldown_info = await cooldown_manager.check_cooldown(monster_id_2, None, guild_id)
    print(f"Cooldown info: {json.dumps(cooldown_info, indent=2)}")
    
    print("\n=== Testing Global Cooldown ===")
    # Set global cooldown
    monster_id_3 = "test_monster_003"
    print(f"Setting global cooldown for monster {monster_id_3}...")
    success = await cooldown_manager.set_cooldown(
        monster_id_3, username, CooldownType.GLOBAL, 180  # 180 seconds cooldown
    )
    print(f"Set cooldown result: {success}")
    
    # Check global cooldown
    print(f"Checking global cooldown for monster {monster_id_3}...")
    cooldown_info = await cooldown_manager.check_cooldown(monster_id_3)
    print(f"Cooldown info: {json.dumps(cooldown_info, indent=2)}")
    
    print("\n=== Testing Get All Cooldowns ===")
    # Get all cooldowns for the user
    print(f"Getting all cooldowns for user {username}...")
    all_cooldowns = await cooldown_manager.get_all_cooldowns(username)
    print(f"All cooldowns: {json.dumps(all_cooldowns, indent=2)}")
    
    print("\n=== Testing Reset Cooldown ===")
    # Reset personal cooldown
    print(f"Resetting personal cooldown for monster {monster_id}, user {username}...")
    success = await cooldown_manager.reset_cooldown(monster_id, CooldownType.PERSONAL, username)
    print(f"Reset cooldown result: {success}")
    
    # Check if cooldown was reset
    print(f"Checking if cooldown was reset...")
    cooldown_info = await cooldown_manager.check_cooldown(monster_id, username)
    print(f"Cooldown info after reset: {json.dumps(cooldown_info, indent=2)}")
    
    print("\n=== Testing Dynamic Cooldown Calculation ===")
    # Test dynamic cooldown calculation
    base_cooldown = 3600  # 1 hour
    modifiers = {
        "vip_bonus": 0.8,  # 20% reduction
        "event_bonus": 0.9,  # 10% reduction
        "item_bonus": 0.95  # 5% reduction
    }
    print(f"Calculating dynamic cooldown with base {base_cooldown}s and modifiers {modifiers}...")
    dynamic_cooldown = await cooldown_manager.calculate_dynamic_cooldown(
        base_cooldown, username, monster_id, modifiers
    )
    print(f"Dynamic cooldown result: {dynamic_cooldown}s (reduction: {base_cooldown - dynamic_cooldown}s)")
    
    print("\n=== Testing Persistence ===")
    # Test persistence to MongoDB
    print("Persisting cooldowns to MongoDB...")
    success = await cooldown_manager.persist_to_mongodb()
    print(f"Persist result: {success}")
    
    # Reset all cooldowns for cleanup
    print("\n=== Cleanup ===")
    await cooldown_manager.reset_cooldown(monster_id, CooldownType.PERSONAL, username)
    await cooldown_manager.reset_cooldown(monster_id_2, CooldownType.GUILD, None, guild_id)
    await cooldown_manager.reset_cooldown(monster_id_3, CooldownType.GLOBAL)
    
    # Shutdown database
    await db_manager.shutdown()
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(test_monster_cooldown()) 