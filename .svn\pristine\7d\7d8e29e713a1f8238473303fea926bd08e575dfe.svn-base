# -*- coding: utf-8 -*-
"""
测试GuildCacheManager的get_instance方法
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_guild_cache_manager():
    """测试GuildCacheManager单例"""
    try:
        print("正在导入GuildCacheManager...")
        from guild_cache_manager import GuildCacheManager
        
        print("正在获取GuildCacheManager实例...")
        instance1 = await GuildCacheManager.get_instance()
        print(f"第一个实例: {instance1}")
        
        print("再次获取GuildCacheManager实例...")
        instance2 = await GuildCacheManager.get_instance()
        print(f"第二个实例: {instance2}")
        
        # 验证是否为同一个实例
        if instance1 is instance2:
            print("✅ 单例模式工作正常")
            return True
        else:
            print("❌ 单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("=== 测试GuildCacheManager单例模式 ===")
    success = await test_guild_cache_manager()
    
    if success:
        print("🎉 GuildCacheManager修复成功！")
    else:
        print("💥 GuildCacheManager仍有问题")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
