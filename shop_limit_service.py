"""
商店限购管理服务
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, Optional
from distributed_lock import DistributedLock
from shop_event_broadcaster import get_shop_event_broadcaster
from shop_database_manager import ShopDatabaseManager
from shop_cache_service import ShopCacheService
from shop_models import LimitCheckResult, LimitType, ShopItemConfig

logger = logging.getLogger(__name__)


class ShopLimitService:
    """商店限购管理服务"""
    
    def __init__(self):
        self.db_manager = ShopDatabaseManager()
        self.cache_service = ShopCacheService()
    
    # ==================== 限购检查 ====================
    
    async def check_purchase_limit(self, player_id: str, config_id: str, quantity: int) -> LimitCheckResult:
        """检查购买限制"""
        try:
            # 1. 获取商品配置
            config = await self.db_manager.get_item_config(config_id)
            if not config:
                return LimitCheckResult(
                    can_purchase=False,
                    remaining=0,
                    current_count=0,
                    limit_count=0,
                    limit_type=LimitType.NONE,
                    error="商品配置不存在"
                )
            
            # 2. 检查是否有限购
            if not config.purchase_limit or config.purchase_limit.get("type") == LimitType.NONE:
                return LimitCheckResult(
                    can_purchase=True,
                    remaining=float('inf'),
                    current_count=0,
                    limit_count=0,
                    limit_type=LimitType.NONE
                )
            
            limit_type = config.purchase_limit["type"]
            limit_count = config.purchase_limit["count"]
            
            # 3. 懒加载检查和重置
            await self._check_and_reset_if_needed(player_id, config_id, limit_type)
            
            # 4. 获取当前购买计数
            current_count = await self.cache_service.get_counter(player_id, config_id, limit_type)
            
            # 5. 计算剩余可购买数量
            remaining = limit_count - current_count
            can_purchase = remaining >= quantity
            
            return LimitCheckResult(
                can_purchase=can_purchase,
                remaining=max(0, remaining),
                current_count=current_count,
                limit_count=limit_count,
                limit_type=limit_type
            )
            
        except Exception as e:
            logger.error(f"检查购买限制时发生错误: {str(e)}")
            return LimitCheckResult(
                can_purchase=False,
                remaining=0,
                current_count=0,
                limit_count=0,
                limit_type=LimitType.NONE,
                error=f"检查失败: {str(e)}"
            )
    
    async def update_purchase_count(self, player_id: str, config_id: str, quantity: int) -> bool:
        """更新购买计数"""
        try:
            # 1. 获取商品配置
            config = await self.db_manager.get_item_config(config_id)
            if not config or not config.purchase_limit:
                return True
            
            limit_type = config.purchase_limit["type"]
            if limit_type == LimitType.NONE:
                return True
            
            # 2. 计算周期信息
            period_info = self._calculate_limit_period(config)
            
            # 3. 更新缓存计数
            ttl = int((period_info["period_end"] - datetime.now()).total_seconds())
            await self.cache_service.increment_counter(player_id, config_id, limit_type, quantity, ttl)
            
            # 4. 更新数据库计数器
            await self.db_manager.increment_counter(
                player_id=player_id,
                scope_value=config_id,
                limit_type=limit_type,
                increment=quantity,
                limit_count=config.purchase_limit["count"],
                period_start=period_info["period_start"],
                period_end=period_info["period_end"]
            )
            
            logger.debug(f"更新购买计数: {player_id}:{config_id} +{quantity}")
            return True
            
        except Exception as e:
            logger.error(f"更新购买计数时发生错误: {str(e)}")
            return False
    
    # ==================== 懒加载重置 ====================
    
    async def _check_and_reset_if_needed(self, player_id: str, config_id: str, limit_type: str) -> bool:
        """检查并在需要时重置玩家限购 (带分布式锁)"""
        lock_key = f"reset:lock:{player_id}:{limit_type}"
        
        try:
            async with DistributedLock(lock_key, 30):
                # 获取时间戳
                global_reset = await self.cache_service.get_global_reset_timestamp(limit_type)
                player_check = await self.cache_service.get_player_check_timestamp(player_id, limit_type)
                
                if global_reset and (not player_check or global_reset > player_check):
                    # 需要重置
                    await self._reset_player_counters(player_id, limit_type)
                    
                    # 更新玩家检查时间
                    ttl = self._get_cache_ttl(limit_type)
                    await self.cache_service.set_player_check_timestamp(player_id, limit_type, global_reset, ttl)
                    
                    logger.info(f"玩家限购数据已重置: {player_id}:{limit_type}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查并重置限购时发生错误: {str(e)}")
            return False
    
    async def _reset_player_counters(self, player_id: str, limit_type: str):
        """重置玩家计数器"""
        try:
            # 清理缓存
            await self.cache_service.reset_player_counters(player_id, limit_type)
            
            # 异步清理数据库记录
            asyncio.create_task(
                self._cleanup_player_db_counters(player_id, limit_type)
            )
            
        except Exception as e:
            logger.error(f"重置玩家计数器时发生错误: {str(e)}")
    
    async def _cleanup_player_db_counters(self, player_id: str, limit_type: str):
        """清理玩家数据库计数器"""
        try:
            # 获取玩家的计数器
            counters = await self.db_manager.get_player_counters(player_id, limit_type)
            
            # 删除过期的计数器
            now = datetime.now()
            for counter in counters:
                if counter.period_end < now:
                    # 这里可以添加删除逻辑，或者让定时任务处理
                    pass
            
        except Exception as e:
            logger.error(f"清理玩家数据库计数器时发生错误: {str(e)}")
    
    # ==================== 全局重置 ====================
    
    async def trigger_global_reset(self, limit_type: str) -> bool:
        """触发全局重置"""
        try:
            reset_time = datetime.now().isoformat()
            
            # 1. 更新全局重置时间戳
            await self.cache_service.set_global_reset_timestamp(limit_type, reset_time)
            
            # 2. 异步清理过期计数器
            asyncio.create_task(self._cleanup_expired_counters(limit_type))

            # 3. 广播限购重置事件
            asyncio.create_task(self._broadcast_limits_reset(limit_type))

            logger.info(f"{limit_type} 限购重置完成，时间戳: {reset_time}")
            return True
            
        except Exception as e:
            logger.error(f"触发全局重置时发生错误: {str(e)}")
            return False
    
    async def _cleanup_expired_counters(self, limit_type: str):
        """清理过期计数器"""
        try:
            deleted_count = await self.db_manager.cleanup_expired_counters(limit_type)
            logger.info(f"清理过期 {limit_type} 计数器: {deleted_count} 个")
            
        except Exception as e:
            logger.error(f"清理过期计数器时发生错误: {str(e)}")

    async def _broadcast_limits_reset(self, limit_type: str):
        """广播限购重置事件"""
        try:
            broadcaster = get_shop_event_broadcaster()
            await broadcaster.broadcast_limits_reset(limit_type)

        except Exception as e:
            logger.error(f"广播限购重置事件时发生错误: {str(e)}")

    # ==================== 周期计算 ====================
    
    def _calculate_limit_period(self, config: ShopItemConfig) -> Dict:
        """计算限购周期"""
        now = datetime.now()
        limit_type = config.purchase_limit["type"]
        reset_config = config.purchase_limit.get("reset_time", {"hour": 0, "minute": 0})
        
        if limit_type == LimitType.DAILY:
            # 每日限购
            today = now.date()
            reset_time = datetime.combine(today, time(reset_config["hour"], reset_config["minute"]))
            
            if now < reset_time:
                period_start = reset_time - timedelta(days=1)
                period_end = reset_time
            else:
                period_start = reset_time
                period_end = reset_time + timedelta(days=1)
                
        elif limit_type == LimitType.WEEKLY:
            # 每周限购
            days_since_monday = now.weekday()
            monday = now.date() - timedelta(days=days_since_monday)
            period_start = datetime.combine(monday, time(reset_config["hour"], reset_config["minute"]))
            period_end = period_start + timedelta(days=7)
            
        elif limit_type == LimitType.MONTHLY:
            # 每月限购
            first_day = now.replace(day=1, hour=reset_config["hour"], minute=reset_config["minute"], second=0, microsecond=0)
            if now.month == 12:
                next_month = first_day.replace(year=first_day.year + 1, month=1)
            else:
                next_month = first_day.replace(month=first_day.month + 1)
            
            period_start = first_day
            period_end = next_month
            
        elif limit_type == LimitType.PERMANENT:
            # 永久限购
            period_start = config.created_at
            period_end = datetime.max
            
        else:
            # 默认情况
            period_start = now
            period_end = now + timedelta(days=1)
        
        return {
            "period_start": period_start,
            "period_end": period_end,
            "limit_type": limit_type
        }
    
    def _get_cache_ttl(self, limit_type: str) -> int:
        """获取缓存TTL"""
        if limit_type == LimitType.DAILY:
            return 86400  # 24小时
        elif limit_type == LimitType.WEEKLY:
            return 604800  # 7天
        elif limit_type == LimitType.MONTHLY:
            return 2592000  # 30天
        elif limit_type == LimitType.PERMANENT:
            return 31536000  # 1年
        return 86400
    
    # ==================== 统计和查询 ====================
    
    async def get_player_limit_status(self, player_id: str, limit_type: str) -> Dict:
        """获取玩家限购状态"""
        try:
            # 获取所有计数器
            counters = await self.cache_service.get_all_counters(player_id, limit_type)
            
            # 获取重置时间信息
            global_reset = await self.cache_service.get_global_reset_timestamp(limit_type)
            player_check = await self.cache_service.get_player_check_timestamp(player_id, limit_type)
            
            return {
                "player_id": player_id,
                "limit_type": limit_type,
                "counters": counters,
                "global_reset_time": global_reset,
                "last_check_time": player_check,
                "total_items": len(counters)
            }
            
        except Exception as e:
            logger.error(f"获取玩家限购状态时发生错误: {str(e)}")
            return {}
    
    async def get_limit_statistics(self, limit_type: str) -> Dict:
        """获取限购统计信息"""
        try:
            # 这里可以添加更多统计逻辑
            global_reset = await self.cache_service.get_global_reset_timestamp(limit_type)
            
            return {
                "limit_type": limit_type,
                "last_global_reset": global_reset,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取限购统计信息时发生错误: {str(e)}")
            return {}
