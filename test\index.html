<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏服务器客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        h1, h2 {
            color: #333;
        }
        section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        label {
            display: block;
            margin: 10px 0 5px;
        }
        input, button {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #28a745;
            color: #fff;
            border: none;
            cursor: pointer;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background-color: #218838;
        }
        #chatMessages, #itemsList {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .error {
            color: #dc3545;
            font-size: 0.9em;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>游戏服务器客户端</h1>

    <!-- 用户认证 -->
    <section>
        <h2>用户认证</h2>
        <div>
            <h3>注册 / 登录</h3>
            <label>用户名（3-40 字符，仅字母数字，无空格）:</label>
            <input type="text" id="username" placeholder="请输入用户名">
            <label>密码（6-20 字符，含字母和数字，无空格）:</label>
            <input type="password" id="password" placeholder="请输入密码">
            <div>
                <button id="registerBtn" onclick="register()">注册</button>
                <button id="loginBtn" onclick="login()">登录</button>
            </div>
            <p id="authError" class="error"></p>
        </div>
        <button id="logoutBtn" onclick="logout()" disabled>登出</button>
    </section>

    <!-- 用户信息 -->
    <section>
        <h2>用户信息</h2>
        <button id="getUserInfoBtn" onclick="getUserInfo()" disabled>获取用户信息</button>
        <div id="userInfo"></div>
    </section>

    <!-- 聊天与物品管理 -->
    <section>
        <h2>聊天与物品管理</h2>
        <p id="wsStatus" class="status">WebSocket 状态: 未连接</p>
        <div>
            <!-- 聊天 -->
            <h3>聊天</h3>
            <div id="chatMessages"></div>
            <label>公共聊天消息:</label>
            <input type="text" id="chatContent" placeholder="输入消息">
            <button id="sendChatBtn" onclick="sendChat()" disabled>发送</button>

            <label>私人消息（目标用户名）:</label>
            <input type="text" id="privateTarget" placeholder="目标用户名">
            <input type="text" id="privateContent" placeholder="消息内容">
            <button id="sendPrivateBtn" onclick="sendPrivateChat()" disabled>发送私人消息</button>

            <label>群组消息（用户名列表，用逗号分隔）:</label>
            <input type="text" id="groupTargets" placeholder="user1,user2">
            <input type="text" id="groupContent" placeholder="消息内容">
            <button id="sendGroupBtn" onclick="sendGroupChat()" disabled>发送群组消息</button>

            <label>广播消息:</label>
            <input type="text" id="broadcastContent" placeholder="广播内容">
            <button id="sendBroadcastBtn" onclick="sendBroadcast()" disabled>发送广播</button>
            <p id="chatError" class="error"></p>

            <!-- 物品管理 -->
            <h3>物品管理</h3>
            <div>
                <label>添加物品</label>
                <input type="number" id="itemDefId" placeholder="定义 ID">
                <input type="number" id="itemQuantity" placeholder="数量" value="1">
                <button id="addItemBtn" onclick="addItem()" disabled>添加物品</button>
            </div>
            <div>
                <label>添加装备</label>
                <input type="number" id="equipDefId" placeholder="定义 ID">
                <input type="text" id="attrKey" placeholder="属性键（如 stats）">
                <input type="text" id="attrValue" placeholder="属性值">
                <button id="addAttrBtn" onclick="addAttribute()" disabled>添加属性</button>
                <div id="attributes"></div>
                <button id="addEquipBtn" onclick="addEquip()" disabled>添加装备</button>
            </div>
            <button id="getItemsBtn" onclick="getItems()" disabled>获取物品列表</button>
            <div id="itemsList"></div>
            <p id="itemsError" class="error"></p>
        </div>
    </section>

    <script>
        // 配置服务器地址（建议通过环境变量或配置动态设置）
        const SERVER_URL = 'http://localhost:8000';
        const WS_URL = 'ws://localhost:8000/ws/';
        let token = localStorage.getItem('token') || null;
        let ws = null;
        let attributes = {}; // 装备属性存储
        let isLoggedIn = !!token;
        let isWsConnected = false;

        // 初始化 UI 状态
        updateUI();

        // 更新 UI 状态
        function updateUI() {
            document.getElementById('logoutBtn').disabled = !isLoggedIn;
            document.getElementById('getUserInfoBtn').disabled = !isLoggedIn;
            document.getElementById('sendChatBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('sendPrivateBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('sendGroupBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('sendBroadcastBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('addItemBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('addAttrBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('addEquipBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('getItemsBtn').disabled = !isLoggedIn || !isWsConnected;
            document.getElementById('wsStatus').textContent = `WebSocket 状态: ${isWsConnected ? '已连接' : '未连接'}`;
        }

        // 显示错误信息
        function showError(elementId, message) {
            document.getElementById(elementId).textContent = message;
            setTimeout(() => document.getElementById(elementId).textContent = '', 5000);
        }

        // 发起 HTTP 请求
        async function makeRequest(url, method, data, includeToken = false) {
            const headers = {
                'Content-Type': 'application/json',
            };
            if (includeToken && token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            try {
                const response = await fetch(`${SERVER_URL}${url}`, {
                    method,
                    headers,
                    body: data ? JSON.stringify(data) : null,
                });
                const result = await response.json();
                if (!response.ok) {
                    throw new Error(result.message || `HTTP 错误: ${response.status}`);
                }
                return result;
            } catch (error) {
                throw error;
            }
        }

        // 注册
        async function register() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            try {
                // 确保密码格式一致
                const result = await makeRequest('/register', 'POST', { 
                    username, 
                    password,
                    password_encoded: true // 标记密码已处理
                });
                if (result.success) {
                    showError('authError', '注册成功，请登录');
                } else {
                    showError('authError', result.message || '注册失败');
                }
            } catch (error) {
                showError('authError', error.message || '注册失败');
            }
        }

        // 登录并初始化 WebSocket
        async function login() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            try {
                // 确保密码格式一致
                const result = await makeRequest('/login', 'POST', { 
                    username, 
                    password,
                    password_encoded: true // 标记密码已处理
                });
                if (result.success) {
                    token = result.data.access_token;
                    localStorage.setItem('token', token);
                    isLoggedIn = true;
                    showError('authError', '登录成功');
                    await initWebSocket();
                    updateUI();
                } else {
                    showError('authError', result.message || '登录失败');
                }
            } catch (error) {
                showError('authError', error.message || '登录失败');
            }
        }

        // 登出
        async function logout() {
            try {
                const result = await makeRequest('/logout', 'POST', null, true);
                if (result.success) {
                    token = null;
                    localStorage.removeItem('token');
                    isLoggedIn = false;
                    if (ws) {
                        ws.close();
                        ws = null;
                        isWsConnected = false;
                    }
                    showError('authError', '登出成功');
                    updateUI();
                } else {
                    showError('authError', result.message || '登出失败');
                }
            } catch (error) {
                showError('authError', error.message || '登出失败');
            }
        }

        // 获取用户信息
        async function getUserInfo() {
            try {
                const result = await makeRequest('/userinfo', 'GET', null, true);
                if (result.success) {
                    const user = result.data;
                    document.getElementById('userInfo').innerHTML = `
                        <p>用户名: ${user.id}</p>
                        <p>金币: ${user.gold}</p>
                        <p>物品: ${user.items.length ? user.items.map(item => item.id).join(', ') : '无'}</p>
                    `;
                } else {
                    showError('authError', result.message || '获取用户信息失败');
                }
            } catch (error) {
                showError('authError', error.message || '获取用户信息失败');
            }
        }

        // 初始化 WebSocket
        async function initWebSocket() {
            if (!token) {
                showError('authError', '请先登录');
                return;
            }
            try {
                ws = new WebSocket(`${WS_URL}${token}`);
                ws.onopen = () => {
                    isWsConnected = true;
                    updateUI();
                    showError('chatError', 'WebSocket 连接成功');
                    // 启动心跳
                    setInterval(() => {
                        if (isWsConnected && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({ msgId: 0, data: { heartbeat: true } }));
                        }
                    }, 20000);
                };
                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (error) {
                        showError('chatError', '消息格式无效');
                    }
                };
                ws.onclose = () => {
                    isWsConnected = false;
                    updateUI();
                    showError('chatError', 'WebSocket 连接断开');
                };
                ws.onerror = () => {
                    isWsConnected = false;
                    updateUI();
                    showError('chatError', 'WebSocket 连接错误');
                };
            } catch (error) {
                showError('chatError', 'WebSocket 初始化失败');
            }
        }

        // 处理 WebSocket 消息
        function handleWebSocketMessage(message) {
            const { msgId, data } = message;
            if (data.error) {
                showError('chatError', data.error);
                return;
            }
            switch (msgId) {
                case 0: // 心跳或系统消息
                    if (data.message) {
                        document.getElementById('chatMessages').innerHTML += `<p>系统: ${data.message}</p>`;
                    }
                    break;
                case 1: // 公共聊天
                    document.getElementById('chatMessages').innerHTML += `<p>${data.sender}: ${data.content}</p>`;
                    break;
                case 2: // 私人消息
                    document.getElementById('chatMessages').innerHTML += `<p>[私聊] ${data.sender}: ${data.content}</p>`;
                    break;
                case 3: // 群组消息
                    document.getElementById('chatMessages').innerHTML += `<p>[群聊] ${data.sender}: ${data.content}</p>`;
                    break;
                case 10: // 广播
                    document.getElementById('chatMessages').innerHTML += `<p>[广播] ${data.sender}: ${data.content}</p>`;
                    break;
                case 4: // 添加装备
                    showError('itemsError', data.message);
                    getItems(); // 刷新物品列表
                    break;
                case 5: // 添加物品
                    showError('itemsError', data.message);
                    getItems();
                    break;
                case 6: // 获取物品列表
                    displayItems(data.items);
                    break;
                case 7: // 删除物品
                    showError('itemsError', data.message);
                    break;
                case 8: // 增加物品数量
                case 9: // 减少物品数量
                    showError('itemsError', data.message);
                    getItems();
                    break;
                default:
                    showError('chatError', '未知消息类型');
            }
            document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
        }

        // 发送公共聊天消息
        function sendChat() {
            const content = document.getElementById('chatContent').value.trim();
            if (!content) {
                showError('chatError', '消息内容不能为空');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 1, data: { content } }));
                document.getElementById('chatContent').value = '';
            } else {
                showError('chatError', 'WebSocket 未连接');
            }
        }

        // 发送私人消息
        function sendPrivateChat() {
            const target = document.getElementById('privateTarget').value.trim();
            const content = document.getElementById('privateContent').value.trim();
            if (!target || !content) {
                showError('chatError', '目标用户名和消息内容不能为空');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 2, data: { target, content } }));
                document.getElementById('privateTarget').value = '';
                document.getElementById('privateContent').value = '';
            } else {
                showError('chatError', 'WebSocket 未连接');
            }
        }

        // 发送群组消息
        function sendGroupChat() {
            const targets = document.getElementById('groupTargets').value.split(',').map(t => t.trim()).filter(t => t);
            const content = document.getElementById('groupContent').value.trim();
            if (!targets.length || !content) {
                showError('chatError', '目标用户列表和消息内容不能为空');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 3, data: { targets, content } }));
                document.getElementById('groupTargets').value = '';
                document.getElementById('groupContent').value = '';
            } else {
                showError('chatError', 'WebSocket 未连接');
            }
        }

        // 发送广播消息
        function sendBroadcast() {
            const content = document.getElementById('broadcastContent').value.trim();
            if (!content) {
                showError('chatError', '广播内容不能为空');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 10, data: { content } }));
                document.getElementById('broadcastContent').value = '';
            } else {
                showError('chatError', 'WebSocket 未连接');
            }
        }

        // 添加装备属性
        function addAttribute() {
            const key = document.getElementById('attrKey').value.trim();
            const value = document.getElementById('attrValue').value.trim();
            if (!key || !value) {
                showError('itemsError', '属性键和值不能为空');
                return;
            }
            try {
                const parsedValue = JSON.parse(value); // 支持 JSON 值（如数字或对象）
                attributes[key] = parsedValue;
            } catch {
                attributes[key] = value; // 按字符串处理
            }
            document.getElementById('attributes').innerHTML += `<p>${key}: ${value}</p>`;
            document.getElementById('attrKey').value = '';
            document.getElementById('attrValue').value = '';
        }

        // 添加装备
        function addEquip() {
            const defId = parseInt(document.getElementById('equipDefId').value);
            if (isNaN(defId)) {
                showError('itemsError', '定义 ID 必须为整数');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 4, data: { defid: defId, attributes } }));
                attributes = {};
                document.getElementById('attributes').innerHTML = '';
                document.getElementById('equipDefId').value = '';
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 添加物品
        function addItem() {
            const defId = parseInt(document.getElementById('itemDefId').value);
            const quantity = parseInt(document.getElementById('itemQuantity').value);
            if (isNaN(defId) || isNaN(quantity) || quantity < 1) {
                showError('itemsError', '定义 ID 和数量必须为正整数');
                return;
            }
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 5, data: { defid: defId, quantity, attributes: {} } }));
                document.getElementById('itemDefId').value = '';
                document.getElementById('itemQuantity').value = '1';
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 获取物品列表
        function getItems() {
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 6, data: { skip: 0, limit: 100 } }));
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 显示物品列表
        function displayItems(items) {
            const itemsList = document.getElementById('itemsList');
            itemsList.innerHTML = '';
            if (!items.length) {
                itemsList.innerHTML = '<p>无物品</p>';
                return;
            }
            items.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.innerHTML = `
                    <p>ID: ${item.id}, 定义 ID: ${item.defid}, 类型: ${item.type}, 
                    数量: ${item.quantity || 'N/A'}, 属性: ${JSON.stringify(item.attributes)}</p>
                    <button onclick="deleteItem('${item.id}')">删除</button>
                    ${item.type === 'item' ? `
                        <button onclick="increaseQuantity('${item.defid}')">增加数量</button>
                        <button onclick="decreaseQuantity('${item.defid}')">减少数量</button>
                    ` : ''}
                `;
                itemsList.appendChild(itemDiv);
            });
        }

        // 删除物品
        function deleteItem(itemId) {
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 7, data: { item_id: itemId } }));
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 增加物品数量
        function increaseQuantity(defId) {
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 8, data: { defid: parseInt(defId), amount: 1 } }));
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 减少物品数量
        function decreaseQuantity(defId) {
            if (ws && isWsConnected) {
                ws.send(JSON.stringify({ msgId: 9, data: { defid: parseInt(defId), amount: 1 } }));
            } else {
                showError('itemsError', 'WebSocket 未连接');
            }
        }

        // 自动尝试恢复 WebSocket 连接
        if (token) {
            initWebSocket();
        }
    </script>
</body>
</html>