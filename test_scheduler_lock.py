"""
测试调度器分布式锁功能
验证定时任务的分布式锁是否正常工作
"""

import asyncio
import logging
import os
import time
from distributed_lock import DistributedLock
from distributed_task import distributed_task

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@distributed_task(lock_key="test:scheduler:lock", ttl=10)
async def test_scheduler_task():
    """模拟调度器任务"""
    worker_id = os.getpid()
    logger.info(f"Worker {worker_id} 开始执行调度器任务")
    
    # 模拟任务执行时间
    await asyncio.sleep(3)
    
    logger.info(f"Worker {worker_id} 调度器任务执行完成")
    return f"Worker {worker_id} completed"

async def simulate_multiple_workers():
    """模拟多个worker同时执行任务"""
    worker_id = os.getpid()
    
    # 创建多个并发任务，模拟多个worker
    tasks = []
    for i in range(4):  # 模拟4个worker
        task = asyncio.create_task(test_scheduler_task())
        tasks.append(task)
    
    logger.info(f"Worker {worker_id} 启动了 4 个并发任务（模拟多worker）")
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful_tasks = [r for r in results if isinstance(r, str)]
    failed_tasks = [r for r in results if isinstance(r, Exception)]
    none_results = [r for r in results if r is None]
    
    logger.info(f"Worker {worker_id} 多worker模拟结果:")
    logger.info(f"  成功执行: {len(successful_tasks)} 个")
    logger.info(f"  执行失败: {len(failed_tasks)} 个")
    logger.info(f"  跳过执行（未获取锁）: {len(none_results)} 个")
    
    if len(successful_tasks) == 1:
        logger.info("✅ 分布式锁工作正常！只有一个任务成功执行")
    else:
        logger.warning(f"❌ 分布式锁可能存在问题！{len(successful_tasks)} 个任务同时执行")
    
    return len(successful_tasks)

async def test_lock_timeout():
    """测试锁超时机制"""
    worker_id = os.getpid()
    lock_key = "test:timeout:lock"
    
    logger.info(f"Worker {worker_id} 测试锁超时机制")
    
    # 第一个锁，持有较长时间
    lock1 = DistributedLock(lock_key, 5)  # 5秒超时
    
    try:
        async with lock1:
            logger.info(f"Worker {worker_id} 获取第一个锁成功")
            
            # 在持有锁期间，尝试获取第二个锁
            lock2 = DistributedLock(lock_key, 2)  # 2秒超时
            
            try:
                async with lock2:
                    logger.error(f"Worker {worker_id} 意外获取了第二个锁！")
            except Exception as e:
                logger.info(f"Worker {worker_id} 第二个锁正确失败: {type(e).__name__}")
            
            # 等待一段时间
            await asyncio.sleep(3)
            logger.info(f"Worker {worker_id} 第一个锁任务完成")
            
    except Exception as e:
        logger.error(f"Worker {worker_id} 第一个锁失败: {str(e)}")

async def main():
    """主测试函数"""
    worker_id = os.getpid()
    logger.info(f"开始调度器分布式锁测试 - Worker {worker_id}")
    
    # 测试1: 模拟多worker并发
    logger.info("=== 测试1: 模拟多worker并发执行 ===")
    successful_count = await simulate_multiple_workers()
    
    await asyncio.sleep(2)
    
    # 测试2: 锁超时机制
    logger.info("=== 测试2: 锁超时机制 ===")
    await test_lock_timeout()
    
    await asyncio.sleep(2)
    
    # 测试3: 连续执行测试
    logger.info("=== 测试3: 连续执行测试 ===")
    for i in range(3):
        logger.info(f"第 {i+1} 次执行:")
        result = await test_scheduler_task()
        logger.info(f"结果: {result}")
        await asyncio.sleep(1)
    
    logger.info(f"调度器分布式锁测试完成 - Worker {worker_id}")

if __name__ == "__main__":
    asyncio.run(main())
