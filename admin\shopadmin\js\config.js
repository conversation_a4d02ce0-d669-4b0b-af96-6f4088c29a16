/**
 * 商店管理系统 - 配置文件
 * 用于配置API地址和其他系统参数
 */

window.ShopAdminConfig = {
    // ==================== API配置 ====================
    
    /**
     * API服务器配置
     * 根据不同环境配置不同的API地址
     */
    api: {
        // 开发环境API地址
        development: {
            baseURL: 'http://localhost:8000',
            timeout: 10000
        },
        
        // 生产环境API地址
        production: {
            baseURL: '',  // 使用相对路径
            timeout: 10000
        },
        
        // 测试环境API地址
        testing: {
            baseURL: 'http://test-server:8000',
            timeout: 15000
        }
    },
    
    // ==================== 环境检测 ====================
    
    /**
     * 获取当前环境
     * @returns {string} 环境名称
     */
    getCurrentEnvironment() {
        // 检查URL或其他标识来确定环境
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1' || window.location.protocol === 'file:') {
            return 'development';
        } else if (hostname.includes('test')) {
            return 'testing';
        } else {
            return 'production';
        }
    },
    
    /**
     * 获取当前环境的API配置
     * @returns {Object} API配置
     */
    getApiConfig() {
        const env = this.getCurrentEnvironment();
        return this.api[env] || this.api.development;
    },
    
    // ==================== 功能配置 ====================
    
    /**
     * 功能开关配置
     */
    features: {
        // 是否启用调试模式
        debug: true,
        
        // 是否显示详细日志
        verboseLogging: true,
        
        // 是否启用自动刷新
        autoRefresh: false,
        
        // 自动刷新间隔（毫秒）
        autoRefreshInterval: 30000,
        
        // 是否启用离线模式
        offlineMode: false
    },
    
    // ==================== UI配置 ====================
    
    /**
     * 界面配置
     */
    ui: {
        // 每页显示数量
        pageSize: 20,
        
        // 搜索防抖延迟（毫秒）
        searchDebounce: 300,
        
        // 消息提示显示时间（毫秒）
        messageTimeout: 3000,
        
        // 加载动画延迟（毫秒）
        loadingDelay: 200,
        
        // 主题配置
        theme: {
            primaryColor: '#007bff',
            successColor: '#28a745',
            warningColor: '#ffc107',
            errorColor: '#dc3545',
            infoColor: '#17a2b8'
        }
    },
    
    // ==================== 数据配置 ====================
    
    /**
     * 数据相关配置
     */
    data: {
        // 商店类型选项
        shopTypes: [
            { value: 'normal', label: '普通商店' },
            { value: 'guild', label: '公会商店' },
            { value: 'vip', label: 'VIP商店' },
            { value: 'event', label: '活动商店' },
            { value: 'arena', label: '竞技场商店' }
        ],
        
        // 道具品质选项
        itemQualities: [
            { value: 1, label: '普通', color: '#ffffff' },
            { value: 2, label: '优秀', color: '#1eff00' },
            { value: 3, label: '稀有', color: '#0070dd' },
            { value: 4, label: '史诗', color: '#a335ee' },
            { value: 5, label: '传说', color: '#ff8000' },
            { value: 6, label: '神话', color: '#e6cc80' }
        ],
        
        // 货币类型选项
        currencyTypes: [
            { value: 'gold', label: '金币' },
            { value: 'diamond', label: '钻石' },
            { value: 'honor', label: '荣誉' },
            { value: 'guild_coin', label: '公会币' }
        ],
        
        // 限购类型选项
        limitTypes: [
            { value: 'daily', label: '每日限购' },
            { value: 'weekly', label: '每周限购' },
            { value: 'monthly', label: '每月限购' },
            { value: 'total', label: '总计限购' }
        ]
    },
    
    // ==================== 验证配置 ====================
    
    /**
     * 数据验证规则
     */
    validation: {
        // 商店ID规则
        shopId: {
            minLength: 3,
            maxLength: 50,
            pattern: /^[a-zA-Z0-9_]+$/
        },
        
        // 商店名称规则
        shopName: {
            minLength: 1,
            maxLength: 100
        },
        
        // 道具模板ID规则
        itemTemplateId: {
            minLength: 1,
            maxLength: 50,
            pattern: /^[a-zA-Z0-9_]+$/
        },
        
        // 数量规则
        quantity: {
            min: 1,
            max: 999999
        },
        
        // 价格规则
        price: {
            min: 0,
            max: 999999999
        }
    }
};

// ==================== 全局方法 ====================

/**
 * 获取配置值
 * @param {string} path - 配置路径，如 'api.development.baseURL'
 * @param {*} defaultValue - 默认值
 * @returns {*} 配置值
 */
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = window.ShopAdminConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

/**
 * 设置配置值
 * @param {string} path - 配置路径
 * @param {*} value - 配置值
 */
window.setConfig = function(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = window.ShopAdminConfig;
    
    for (const key of keys) {
        if (!target[key] || typeof target[key] !== 'object') {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = value;
};

// 输出配置信息到控制台（仅在调试模式下）
if (window.ShopAdminConfig.features.debug) {
    console.log('[ShopAdminConfig] 配置已加载:', window.ShopAdminConfig);
    console.log('[ShopAdminConfig] 当前环境:', window.ShopAdminConfig.getCurrentEnvironment());
    console.log('[ShopAdminConfig] API配置:', window.ShopAdminConfig.getApiConfig());
}
