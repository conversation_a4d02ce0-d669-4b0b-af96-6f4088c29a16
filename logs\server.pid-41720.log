2025-08-05 20:33:38,551 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,286 - models - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,318 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,699 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,711 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,721 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,735 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:39,751 - auth - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,296 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,354 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,378 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,387 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,398 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,399 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: c16a646f)
2025-08-05 20:33:42,409 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,444 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,456 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,465 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,475 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,554 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,567 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,577 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,586 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,595 - game_server - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:42,595 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 20:33:42,596 - msgManager - INFO - Monster handlers registered
2025-08-05 20:33:42,596 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 20:33:42,598 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 20:33:42,608 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 20:33:42,609 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 20:33:42,610 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5b99a7f9)
2025-08-05 20:33:42,660 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 20:33:42,724 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 20:33:42,725 - game_server - INFO - 模板引擎初始化成功
2025-08-05 20:33:42,730 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:33:42,732 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 41720)
2025-08-05 20:33:42,733 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 20:33:42,899 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:33:42,911 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 20:33:43,393 - ConnectionManager - INFO - 后台任务已启动 (Worker 41720)
2025-08-05 20:33:43,393 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 41720)
2025-08-05 20:33:43,408 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:43,410 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:43,412 - game_server - INFO - 游戏配置加载完成 (Worker: 41720)
2025-08-05 20:33:43,414 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:33:47,658 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:33:47,658 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=0, 总计=2
2025-08-05 20:33:47,659 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 41720): 2/2 (100.0%)
2025-08-05 20:33:47,659 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:33:47,701 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 5b99a7f9)
2025-08-05 20:33:47,702 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5b99a7f9)
2025-08-05 20:33:47,709 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:47,710 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 20:33:47,711 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 20:33:47,719 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:33:47,720 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 20:33:47,721 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:33:47,721 - ConnectionManager - INFO - Worker 41720 开始消费广播消息，消费者标签: ctag1.99f04f5432e24378847054abe2ea1c13
2025-08-05 20:33:47,775 - ConnectionManager - INFO - Worker 41720 开始消费个人消息，消费者标签: ctag1.cbf503e85ee6491fab479f7821ef61fe
2025-08-05 20:33:47,860 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 41720)
2025-08-05 20:33:47,953 - distributed_lock - INFO - Worker 41720 成功获取锁: scheduler_initialization
2025-08-05 20:33:47,954 - game_server_scheduler_integration - INFO - Worker 41720 获得调度器初始化权限
2025-08-05 20:33:47,976 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 20:33:47,976 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 20:33:47,976 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 20:33:47,977 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 20:33:47,977 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 20:33:47,977 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 20:33:47,978 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 20:33:47,978 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 20:33:47,979 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 20:33:47,979 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 20:33:47,979 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 20:33:47,980 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 20:33:47,980 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 20:33:47,981 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 20:33:47,981 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 20:33:47,982 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 20:33:48,074 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 20:33:48,075 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 20:33:48,076 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 20:33:48,077 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 20:33:48,369 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 20:33:48,462 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 20:33:48,465 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 20:33:48,466 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 20:33:48,466 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 20:33:48,466 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 20:33:48,467 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:33:48,556 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 20:33:48,689 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:33:48,690 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 20:33:48,693 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 20:33:48,695 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 20:33:48,695 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 20:33:48,696 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 20:33:48,707 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,707 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 20:33:48,711 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 20:33:48,716 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,716 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 20:33:48,717 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 20:33:48,850 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,850 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 20:33:48,851 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 20:33:48,984 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,984 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 20:33:48,985 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 20:33:48,986 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,987 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 20:33:48,987 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 20:33:48,988 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 20:33:48,988 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 20:33:48,989 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 20:33:48,989 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 20:33:48,992 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,992 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,993 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,993 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,994 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,994 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 20:33:48,995 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 20:33:48,995 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 20:33:48,996 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 20:33:48,996 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 20:33:48,997 - game_server_scheduler_integration - INFO - Worker 41720 调度器初始化成功
2025-08-05 20:33:49,042 - game_server - INFO - 统一调度器初始化成功 (Worker: 41720)
2025-08-05 20:33:49,049 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 41720)
2025-08-05 20:33:49,049 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 20:33:49,049 - game_server - INFO - 日志清理管理器已启动 (Worker: 41720)
2025-08-05 20:33:49,050 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:33:49,051 - game_server - INFO - Monster cooldown manager initialized (Worker: 41720)
2025-08-05 20:33:49,204 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:33:49,204 - game_server - INFO - 公会系统初始化成功 (Worker: 41720)
2025-08-05 20:33:49,204 - game_server - INFO - 邮件系统初始化成功 (Worker: 41720)
2025-08-05 20:33:49,208 - game_server - INFO - 商店系统初始化成功 (Worker: 41720)
2025-08-05 20:33:49,208 - game_server - INFO - 初始化完成 (Worker: 41720)
2025-08-05 20:33:58,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:08 CST)" (scheduled at 2025-08-05 20:33:58.984488+08:00)
2025-08-05 20:33:59,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:33:59,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:33:59,170 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:33:59,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:33:59,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:33:59,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:33:59,520 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:33:59,564 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:08 CST)" executed successfully
2025-08-05 20:34:00,739 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:34:08,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:18 CST)" (scheduled at 2025-08-05 20:34:08.984488+08:00)
2025-08-05 20:34:09,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:09,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:09,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:09,509 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:09,509 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:34:09,509 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:09,510 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:09,553 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:18 CST)" executed successfully
2025-08-05 20:34:13,430 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:34:18,861 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:34:48 CST)" (scheduled at 2025-08-05 20:34:18.850591+08:00)
2025-08-05 20:34:18,905 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:34:18,906 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:18,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:28 CST)" (scheduled at 2025-08-05 20:34:18.984488+08:00)
2025-08-05 20:34:19,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:19,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:19,035 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:34:19,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:19,383 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:34:19,383 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:34:19,384 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:34:19,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:34:19,385 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:19,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:34:48 CST)" executed successfully
2025-08-05 20:34:19,515 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:19,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:34:19,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:19,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:19,560 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:28 CST)" executed successfully
2025-08-05 20:34:28,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:38 CST)" (scheduled at 2025-08-05 20:34:28.984488+08:00)
2025-08-05 20:34:29,044 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:29,045 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:29,182 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:29,529 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:29,530 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:34:29,530 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:29,531 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:29,577 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:38 CST)" executed successfully
2025-08-05 20:34:30,929 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:34:38,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:48 CST)" (scheduled at 2025-08-05 20:34:38.984488+08:00)
2025-08-05 20:34:39,126 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:39,128 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:39,427 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:40,627 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:40,628 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.20秒
2025-08-05 20:34:40,628 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:40,628 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:40,724 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:48 CST)" executed successfully
2025-08-05 20:34:43,432 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:34:47,669 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:34:47,669 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:34:48,722 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:35:48 CST)" (scheduled at 2025-08-05 20:34:48.715924+08:00)
2025-08-05 20:34:48,770 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:34:48,772 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:48,774 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:34:48,779 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 20:34:48,861 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:35:18 CST)" (scheduled at 2025-08-05 20:34:48.850591+08:00)
2025-08-05 20:34:48,907 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:34:48,909 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:48,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:58 CST)" (scheduled at 2025-08-05 20:34:48.984488+08:00)
2025-08-05 20:34:49,041 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:34:49,044 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:49,045 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:49,062 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:34:49,069 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:34:49,191 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:49,385 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:34:49,386 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:34:49,386 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:34:49,387 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:34:49,387 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:49,431 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:35:18 CST)" executed successfully
2025-08-05 20:34:49,546 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:49,547 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:34:49,547 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:49,547 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:49,591 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:34:58 CST)" executed successfully
2025-08-05 20:34:50,052 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:34:50,052 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.28秒
2025-08-05 20:34:50,052 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:34:50,053 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:50,101 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:35:48 CST)" executed successfully
2025-08-05 20:34:58,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:08 CST)" (scheduled at 2025-08-05 20:34:58.984488+08:00)
2025-08-05 20:34:59,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:34:59,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:34:59,166 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:34:59,534 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:34:59,535 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:34:59,535 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:34:59,536 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:34:59,580 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:08 CST)" executed successfully
2025-08-05 20:35:00,110 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:35:08,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:18 CST)" (scheduled at 2025-08-05 20:35:08.984488+08:00)
2025-08-05 20:35:09,046 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:09,047 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:09,177 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:09,526 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:09,527 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:35:09,527 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:09,528 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:09,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:18 CST)" executed successfully
2025-08-05 20:35:13,444 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:35:18,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:35:48 CST)" (scheduled at 2025-08-05 20:35:18.850591+08:00)
2025-08-05 20:35:18,904 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:35:18,904 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:18,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:28 CST)" (scheduled at 2025-08-05 20:35:18.984488+08:00)
2025-08-05 20:35:19,041 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:35:19,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:19,050 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:19,184 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:19,401 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:35:19,402 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:35:19,402 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:35:19,403 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:35:19,403 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:19,457 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:35:48 CST)" executed successfully
2025-08-05 20:35:19,542 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:19,542 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:35:19,542 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:19,543 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:19,586 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:28 CST)" executed successfully
2025-08-05 20:35:28,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:38 CST)" (scheduled at 2025-08-05 20:35:28.984488+08:00)
2025-08-05 20:35:29,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:29,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:29,174 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:29,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:29,524 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:35:29,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:29,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:29,569 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:38 CST)" executed successfully
2025-08-05 20:35:30,310 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:35:38,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:48 CST)" (scheduled at 2025-08-05 20:35:38.984488+08:00)
2025-08-05 20:35:39,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:39,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:39,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:39,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:39,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:35:39,523 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:39,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:39,568 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:48 CST)" executed successfully
2025-08-05 20:35:43,450 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:35:47,684 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:35:47,685 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:35:48,718 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:36:48 CST)" (scheduled at 2025-08-05 20:35:48.715924+08:00)
2025-08-05 20:35:48,762 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:35:48,762 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:48,763 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:35:48,858 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:36:18 CST)" (scheduled at 2025-08-05 20:35:48.850591+08:00)
2025-08-05 20:35:48,902 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:35:48,905 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:48,972 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:35:48,972 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:35:48,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:58 CST)" (scheduled at 2025-08-05 20:35:48.984488+08:00)
2025-08-05 20:35:49,052 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:35:49,053 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:49,053 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:49,195 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:49,411 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:35:49,411 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:35:49,411 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:35:49,412 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:35:49,412 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:49,457 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:36:18 CST)" executed successfully
2025-08-05 20:35:49,552 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:49,552 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:35:49,553 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:49,553 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:49,597 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:35:58 CST)" executed successfully
2025-08-05 20:35:50,372 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:35:50,373 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.61秒
2025-08-05 20:35:50,374 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:35:50,374 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:50,421 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:36:48 CST)" executed successfully
2025-08-05 20:35:58,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:08 CST)" (scheduled at 2025-08-05 20:35:58.984488+08:00)
2025-08-05 20:35:59,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:35:59,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:35:59,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:35:59,512 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:35:59,512 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:35:59,513 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:35:59,513 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:35:59,559 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:08 CST)" executed successfully
2025-08-05 20:36:00,516 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:36:08,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:18 CST)" (scheduled at 2025-08-05 20:36:08.984488+08:00)
2025-08-05 20:36:09,029 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:09,029 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:09,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:09,515 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:09,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:09,516 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:09,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:09,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:18 CST)" executed successfully
2025-08-05 20:36:13,464 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:36:18,863 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:36:48 CST)" (scheduled at 2025-08-05 20:36:18.850591+08:00)
2025-08-05 20:36:18,910 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:36:18,910 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:18,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:28 CST)" (scheduled at 2025-08-05 20:36:18.984488+08:00)
2025-08-05 20:36:19,029 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:19,029 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:19,044 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:36:19,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:19,388 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:36:19,388 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:36:19,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:36:19,391 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:36:19,391 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:19,435 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:36:48 CST)" executed successfully
2025-08-05 20:36:19,515 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:19,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:19,516 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:19,517 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:19,564 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:28 CST)" executed successfully
2025-08-05 20:36:28,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:38 CST)" (scheduled at 2025-08-05 20:36:28.984488+08:00)
2025-08-05 20:36:29,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:29,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:29,170 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:29,520 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:29,520 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:29,520 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:29,521 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:29,566 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:38 CST)" executed successfully
2025-08-05 20:36:30,730 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:36:38,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:48 CST)" (scheduled at 2025-08-05 20:36:38.984488+08:00)
2025-08-05 20:36:39,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:39,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:39,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:39,510 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:39,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:39,511 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:39,511 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:39,555 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:48 CST)" executed successfully
2025-08-05 20:36:43,468 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:36:47,696 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:36:47,697 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:36:48,717 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:37:48 CST)" (scheduled at 2025-08-05 20:36:48.715924+08:00)
2025-08-05 20:36:48,762 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:36:48,764 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:48,765 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:36:48,855 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:37:18 CST)" (scheduled at 2025-08-05 20:36:48.850591+08:00)
2025-08-05 20:36:48,903 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:36:48,904 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:48,994 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:58 CST)" (scheduled at 2025-08-05 20:36:48.984488+08:00)
2025-08-05 20:36:49,038 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:49,038 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:49,039 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:36:49,039 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:36:49,041 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:36:49,172 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:49,390 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:36:49,390 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:36:49,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:36:49,392 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:36:49,393 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:49,439 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:37:18 CST)" executed successfully
2025-08-05 20:36:49,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:49,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:49,517 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:49,518 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:49,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:36:58 CST)" executed successfully
2025-08-05 20:36:49,670 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:36:49,671 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.91秒
2025-08-05 20:36:49,671 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:36:49,672 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:49,715 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:37:48 CST)" executed successfully
2025-08-05 20:36:58,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:08 CST)" (scheduled at 2025-08-05 20:36:58.984488+08:00)
2025-08-05 20:36:59,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:36:59,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:36:59,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:36:59,514 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:36:59,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:36:59,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:36:59,515 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:36:59,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:08 CST)" executed successfully
2025-08-05 20:37:00,910 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:37:08,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:18 CST)" (scheduled at 2025-08-05 20:37:08.984488+08:00)
2025-08-05 20:37:09,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:09,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:09,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:09,512 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:09,512 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:37:09,513 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:09,514 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:09,560 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:18 CST)" executed successfully
2025-08-05 20:37:13,471 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:37:18,858 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:37:48 CST)" (scheduled at 2025-08-05 20:37:18.850591+08:00)
2025-08-05 20:37:18,903 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:37:18,904 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:18,993 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:28 CST)" (scheduled at 2025-08-05 20:37:18.984488+08:00)
2025-08-05 20:37:19,035 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:37:19,037 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:19,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:19,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:19,386 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:37:19,386 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:37:19,387 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:37:19,387 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:37:19,388 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:19,432 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:37:48 CST)" executed successfully
2025-08-05 20:37:19,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:19,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:37:19,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:19,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:19,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:28 CST)" executed successfully
2025-08-05 20:37:28,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:38 CST)" (scheduled at 2025-08-05 20:37:28.984488+08:00)
2025-08-05 20:37:29,029 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:29,030 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:29,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:29,520 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:29,520 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:37:29,521 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:29,521 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:29,566 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:38 CST)" executed successfully
2025-08-05 20:37:30,121 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:37:38,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:48 CST)" (scheduled at 2025-08-05 20:37:38.984488+08:00)
2025-08-05 20:37:39,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:39,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:39,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:39,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:39,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:37:39,519 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:39,519 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:39,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:48 CST)" executed successfully
2025-08-05 20:37:43,478 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:37:47,705 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:37:47,705 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:37:48,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:38:48 CST)" (scheduled at 2025-08-05 20:37:48.715924+08:00)
2025-08-05 20:37:48,777 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:37:48,777 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:48,778 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:37:48,865 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:38:18 CST)" (scheduled at 2025-08-05 20:37:48.850591+08:00)
2025-08-05 20:37:48,910 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:37:48,910 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:48,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:58 CST)" (scheduled at 2025-08-05 20:37:48.984488+08:00)
2025-08-05 20:37:49,006 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:37:49,006 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:37:49,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:49,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:49,050 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:37:49,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:49,411 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:37:49,412 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:37:49,412 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:37:49,413 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:37:49,414 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:49,462 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:38:18 CST)" executed successfully
2025-08-05 20:37:49,513 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:49,514 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:37:49,514 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:49,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:49,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:37:58 CST)" executed successfully
2025-08-05 20:37:50,352 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:37:50,352 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.57秒
2025-08-05 20:37:50,353 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:37:50,354 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:50,397 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:38:48 CST)" executed successfully
2025-08-05 20:37:58,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:08 CST)" (scheduled at 2025-08-05 20:37:58.984488+08:00)
2025-08-05 20:37:59,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:37:59,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:37:59,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:37:59,501 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:37:59,502 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:37:59,502 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:37:59,503 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:37:59,546 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:08 CST)" executed successfully
2025-08-05 20:38:00,295 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:38:08,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:18 CST)" (scheduled at 2025-08-05 20:38:08.984488+08:00)
2025-08-05 20:38:09,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:09,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:09,166 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:09,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:09,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:38:09,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:09,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:09,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:18 CST)" executed successfully
2025-08-05 20:38:13,492 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:38:18,861 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:38:48 CST)" (scheduled at 2025-08-05 20:38:18.850591+08:00)
2025-08-05 20:38:18,906 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:38:18,906 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:18,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:28 CST)" (scheduled at 2025-08-05 20:38:18.984488+08:00)
2025-08-05 20:38:19,031 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:19,031 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:19,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:38:19,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:19,388 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:38:19,389 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:38:19,391 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:38:19,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:38:19,394 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:19,444 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:38:48 CST)" executed successfully
2025-08-05 20:38:19,531 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:19,532 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:38:19,534 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:19,540 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:19,603 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:28 CST)" executed successfully
2025-08-05 20:38:28,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:38 CST)" (scheduled at 2025-08-05 20:38:28.984488+08:00)
2025-08-05 20:38:29,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:29,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:29,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:29,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:29,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:38:29,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:29,520 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:29,564 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:38 CST)" executed successfully
2025-08-05 20:38:30,481 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:38:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:48 CST)" (scheduled at 2025-08-05 20:38:38.984488+08:00)
2025-08-05 20:38:39,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:39,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:39,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:39,519 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:39,520 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:38:39,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:39,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:39,576 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:48 CST)" executed successfully
2025-08-05 20:38:43,507 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:38:47,715 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:38:47,716 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:38:48,719 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:39:48 CST)" (scheduled at 2025-08-05 20:38:48.715924+08:00)
2025-08-05 20:38:48,764 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:38:48,765 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:48,766 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:38:48,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:39:18 CST)" (scheduled at 2025-08-05 20:38:48.850591+08:00)
2025-08-05 20:38:48,903 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:38:48,904 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:48,974 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:38:48,974 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:38:48,994 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:58 CST)" (scheduled at 2025-08-05 20:38:48.984488+08:00)
2025-08-05 20:38:49,037 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:49,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:49,038 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:38:49,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:49,386 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:38:49,386 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:38:49,387 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:38:49,388 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:38:49,388 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:49,432 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:39:18 CST)" executed successfully
2025-08-05 20:38:49,515 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:49,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:38:49,516 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:49,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:49,560 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:38:58 CST)" executed successfully
2025-08-05 20:38:50,434 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:38:50,435 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.67秒
2025-08-05 20:38:50,440 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:38:50,440 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:50,483 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:39:48 CST)" executed successfully
2025-08-05 20:38:58,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:08 CST)" (scheduled at 2025-08-05 20:38:58.984488+08:00)
2025-08-05 20:38:59,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:38:59,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:38:59,171 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:38:59,780 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:38:59,781 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.61秒
2025-08-05 20:38:59,782 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:38:59,782 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:38:59,826 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:08 CST)" executed successfully
2025-08-05 20:39:00,710 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:39:08,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:18 CST)" (scheduled at 2025-08-05 20:39:08.984488+08:00)
2025-08-05 20:39:09,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:09,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:09,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:09,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:09,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:39:09,519 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:09,519 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:09,566 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:18 CST)" executed successfully
2025-08-05 20:39:13,509 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:39:18,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:39:48 CST)" (scheduled at 2025-08-05 20:39:18.850591+08:00)
2025-08-05 20:39:18,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:28 CST)" (scheduled at 2025-08-05 20:39:18.984488+08:00)
2025-08-05 20:39:19,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:39:19,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:19,134 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:19,134 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:19,336 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:39:19,734 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 20:39:19,734 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 20:39:19,735 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 20:39:20,434 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:39:20,434 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:39:20,435 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 1.10秒
2025-08-05 20:39:20,436 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:39:20,437 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:20,533 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:39:48 CST)" executed successfully
2025-08-05 20:39:22,034 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:25,833 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:25,834 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 3.80秒
2025-08-05 20:39:25,837 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:25,839 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:25,934 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:28 CST)" executed successfully
2025-08-05 20:39:28,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:38 CST)" (scheduled at 2025-08-05 20:39:28.984488+08:00)
2025-08-05 20:39:29,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:29,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:29,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:29,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:29,519 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:39:29,519 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:29,520 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:29,567 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:38 CST)" executed successfully
2025-08-05 20:39:30,916 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:39:38,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:48 CST)" (scheduled at 2025-08-05 20:39:38.984488+08:00)
2025-08-05 20:39:39,031 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:39,031 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:39,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:39,508 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:39,508 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:39:39,509 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:39,509 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:39,555 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:48 CST)" executed successfully
2025-08-05 20:39:43,513 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:39:47,717 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:39:47,718 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:39:48,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:40:48 CST)" (scheduled at 2025-08-05 20:39:48.715924+08:00)
2025-08-05 20:39:48,777 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:39:48,778 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:48,778 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:39:48,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:40:18 CST)" (scheduled at 2025-08-05 20:39:48.850591+08:00)
2025-08-05 20:39:48,898 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:39:48,898 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:48,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:58 CST)" (scheduled at 2025-08-05 20:39:48.984488+08:00)
2025-08-05 20:39:49,025 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:39:49,026 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:39:49,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:49,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:49,033 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:39:49,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:49,385 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:39:49,386 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:39:49,388 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:39:49,390 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:39:49,392 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:49,438 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:40:18 CST)" executed successfully
2025-08-05 20:39:49,513 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:49,514 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:39:49,514 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:49,514 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:49,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:39:58 CST)" executed successfully
2025-08-05 20:39:49,874 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:39:49,874 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.10秒
2025-08-05 20:39:49,876 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:39:49,877 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:49,922 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:40:48 CST)" executed successfully
2025-08-05 20:39:58,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:08 CST)" (scheduled at 2025-08-05 20:39:58.984488+08:00)
2025-08-05 20:39:59,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:39:59,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:39:59,171 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:39:59,526 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:39:59,526 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:39:59,527 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:39:59,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:39:59,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:08 CST)" executed successfully
2025-08-05 20:40:00,131 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:40:08,993 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:18 CST)" (scheduled at 2025-08-05 20:40:08.984488+08:00)
2025-08-05 20:40:09,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:09,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:09,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:09,522 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:09,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:40:09,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:09,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:09,568 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:18 CST)" executed successfully
2025-08-05 20:40:13,528 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:40:18,861 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:40:48 CST)" (scheduled at 2025-08-05 20:40:18.850591+08:00)
2025-08-05 20:40:18,906 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:40:18,906 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:18,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:28 CST)" (scheduled at 2025-08-05 20:40:18.984488+08:00)
2025-08-05 20:40:19,049 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:40:19,049 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:19,050 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:19,182 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:19,396 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:40:19,396 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:40:19,397 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:40:19,397 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:40:19,398 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:19,441 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:40:48 CST)" executed successfully
2025-08-05 20:40:19,540 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:19,540 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:40:19,541 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:19,542 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:19,589 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:28 CST)" executed successfully
2025-08-05 20:40:28,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:38 CST)" (scheduled at 2025-08-05 20:40:28.984488+08:00)
2025-08-05 20:40:29,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:29,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:29,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:29,522 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:29,522 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:40:29,523 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:29,523 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:29,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:38 CST)" executed successfully
2025-08-05 20:40:30,836 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:40:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:48 CST)" (scheduled at 2025-08-05 20:40:38.984488+08:00)
2025-08-05 20:40:39,031 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:39,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:39,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:39,519 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:39,519 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:40:39,520 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:39,521 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:39,567 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:48 CST)" executed successfully
2025-08-05 20:40:43,539 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:40:47,729 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:40:47,731 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:40:48,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:41:48 CST)" (scheduled at 2025-08-05 20:40:48.715924+08:00)
2025-08-05 20:40:48,776 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:40:48,776 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:48,777 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:40:48,865 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:41:18 CST)" (scheduled at 2025-08-05 20:40:48.850591+08:00)
2025-08-05 20:40:48,909 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:40:48,909 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:48,982 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:40:48,982 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:40:48,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:58 CST)" (scheduled at 2025-08-05 20:40:48.984488+08:00)
2025-08-05 20:40:49,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:49,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:49,040 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:40:49,171 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:49,392 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:40:49,393 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:40:49,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:40:49,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:40:49,394 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:49,439 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:41:18 CST)" executed successfully
2025-08-05 20:40:49,520 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:49,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:40:49,523 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:49,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:49,574 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:40:58 CST)" executed successfully
2025-08-05 20:40:50,472 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:40:50,472 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.70秒
2025-08-05 20:40:50,472 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:40:50,473 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:50,523 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:41:48 CST)" executed successfully
2025-08-05 20:40:58,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:08 CST)" (scheduled at 2025-08-05 20:40:58.984488+08:00)
2025-08-05 20:40:59,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:40:59,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:40:59,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:40:59,526 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:40:59,526 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:40:59,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:40:59,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:40:59,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:08 CST)" executed successfully
2025-08-05 20:41:00,020 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:41:08,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:18 CST)" (scheduled at 2025-08-05 20:41:08.984488+08:00)
2025-08-05 20:41:09,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:09,038 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:09,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:09,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:09,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:41:09,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:09,522 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:09,567 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:18 CST)" executed successfully
2025-08-05 20:41:13,542 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:41:18,865 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:41:48 CST)" (scheduled at 2025-08-05 20:41:18.850591+08:00)
2025-08-05 20:41:18,909 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:41:18,910 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:18,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:28 CST)" (scheduled at 2025-08-05 20:41:18.984488+08:00)
2025-08-05 20:41:19,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:19,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:19,040 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:41:19,166 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:19,393 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:41:19,393 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:41:19,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:41:19,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:41:19,394 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:19,441 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:41:48 CST)" executed successfully
2025-08-05 20:41:19,516 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:19,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:41:19,517 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:19,518 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:19,562 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:28 CST)" executed successfully
2025-08-05 20:41:28,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:38 CST)" (scheduled at 2025-08-05 20:41:28.984488+08:00)
2025-08-05 20:41:29,041 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:29,043 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:29,178 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:29,546 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:29,546 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:41:29,547 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:29,547 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:29,591 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:38 CST)" executed successfully
2025-08-05 20:41:30,239 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:41:34,180 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:41:34,889 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:41:38,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:48 CST)" (scheduled at 2025-08-05 20:41:38.984488+08:00)
2025-08-05 20:41:39,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:39,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:39,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:39,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:39,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:41:39,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:39,523 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:39,571 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:48 CST)" executed successfully
2025-08-05 20:41:43,546 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:41:47,745 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:41:47,748 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:41:48,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:42:48 CST)" (scheduled at 2025-08-05 20:41:48.715924+08:00)
2025-08-05 20:41:48,768 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:41:48,769 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:48,769 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:41:48,860 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:42:18 CST)" (scheduled at 2025-08-05 20:41:48.850591+08:00)
2025-08-05 20:41:48,905 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:41:48,906 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:48,998 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:58 CST)" (scheduled at 2025-08-05 20:41:48.984488+08:00)
2025-08-05 20:41:48,999 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:41:49,002 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:41:49,042 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:41:49,043 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:49,043 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:49,173 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:49,395 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:41:49,396 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:41:49,396 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:41:49,397 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:41:49,397 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:49,441 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:42:18 CST)" executed successfully
2025-08-05 20:41:49,526 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:49,526 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:41:49,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:49,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:49,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:41:58 CST)" executed successfully
2025-08-05 20:41:50,164 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:41:50,164 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.39秒
2025-08-05 20:41:50,164 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:41:50,165 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:50,208 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:42:48 CST)" executed successfully
2025-08-05 20:41:58,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:08 CST)" (scheduled at 2025-08-05 20:41:58.984488+08:00)
2025-08-05 20:41:59,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:41:59,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:41:59,177 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:41:59,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:41:59,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:41:59,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:41:59,522 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:41:59,567 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:08 CST)" executed successfully
2025-08-05 20:42:00,452 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:42:08,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:18 CST)" (scheduled at 2025-08-05 20:42:08.984488+08:00)
2025-08-05 20:42:09,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:09,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:09,388 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:10,126 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:10,127 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.74秒
2025-08-05 20:42:10,127 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:10,128 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:10,174 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:18 CST)" executed successfully
2025-08-05 20:42:13,552 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:42:18,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:42:48 CST)" (scheduled at 2025-08-05 20:42:18.850591+08:00)
2025-08-05 20:42:18,900 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:42:18,901 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:18,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:28 CST)" (scheduled at 2025-08-05 20:42:18.984488+08:00)
2025-08-05 20:42:19,039 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:42:19,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:19,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:19,172 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:19,382 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:42:19,382 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:42:19,383 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 20:42:19,383 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:42:19,384 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:19,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:42:48 CST)" executed successfully
2025-08-05 20:42:19,525 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:19,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:42:19,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:19,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:19,575 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:28 CST)" executed successfully
2025-08-05 20:42:28,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:38 CST)" (scheduled at 2025-08-05 20:42:28.984488+08:00)
2025-08-05 20:42:29,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:29,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:29,171 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:29,527 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:29,527 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:42:29,528 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:29,528 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:29,573 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:38 CST)" executed successfully
2025-08-05 20:42:30,641 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:42:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:48 CST)" (scheduled at 2025-08-05 20:42:38.984488+08:00)
2025-08-05 20:42:39,031 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:39,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:39,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:39,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:39,519 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:42:39,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:39,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:39,573 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:48 CST)" executed successfully
2025-08-05 20:42:43,557 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:42:47,764 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:42:47,765 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:42:48,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:43:48 CST)" (scheduled at 2025-08-05 20:42:48.715924+08:00)
2025-08-05 20:42:48,770 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:42:48,770 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:48,770 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:42:48,865 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:43:18 CST)" (scheduled at 2025-08-05 20:42:48.850591+08:00)
2025-08-05 20:42:48,910 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:42:48,912 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:48,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:58 CST)" (scheduled at 2025-08-05 20:42:48.984488+08:00)
2025-08-05 20:42:49,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:49,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:49,047 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:42:49,053 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:42:49,054 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:42:49,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:49,406 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:42:49,406 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:42:49,407 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:42:49,407 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:42:49,408 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:49,452 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:43:18 CST)" executed successfully
2025-08-05 20:42:49,516 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:49,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:42:49,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:49,519 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:49,562 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:42:58 CST)" executed successfully
2025-08-05 20:42:49,681 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:42:49,681 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.91秒
2025-08-05 20:42:49,681 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:42:49,682 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:49,725 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:43:48 CST)" executed successfully
2025-08-05 20:42:53,000 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:42:58,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:08 CST)" (scheduled at 2025-08-05 20:42:58.984488+08:00)
2025-08-05 20:42:59,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:42:59,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:42:59,174 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:42:59,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:42:59,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:42:59,523 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:42:59,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:42:59,569 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:08 CST)" executed successfully
2025-08-05 20:43:00,856 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:43:08,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:18 CST)" (scheduled at 2025-08-05 20:43:08.984488+08:00)
2025-08-05 20:43:09,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:09,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:09,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:09,510 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:09,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:43:09,511 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:09,511 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:09,555 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:18 CST)" executed successfully
2025-08-05 20:43:13,564 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:43:18,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:43:48 CST)" (scheduled at 2025-08-05 20:43:18.850591+08:00)
2025-08-05 20:43:18,900 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:43:18,901 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:18,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:28 CST)" (scheduled at 2025-08-05 20:43:18.984488+08:00)
2025-08-05 20:43:19,029 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:43:19,030 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:19,030 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:19,160 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:19,372 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:43:19,373 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:43:19,374 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:43:19,378 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:43:19,382 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:19,427 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:43:48 CST)" executed successfully
2025-08-05 20:43:19,519 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:19,520 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:43:19,520 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:19,521 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:19,564 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:28 CST)" executed successfully
2025-08-05 20:43:28,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:38 CST)" (scheduled at 2025-08-05 20:43:28.984488+08:00)
2025-08-05 20:43:29,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:29,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:29,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:29,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:29,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:43:29,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:29,518 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:29,565 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:38 CST)" executed successfully
2025-08-05 20:43:30,026 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:43:38,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:48 CST)" (scheduled at 2025-08-05 20:43:38.984488+08:00)
2025-08-05 20:43:39,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:39,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:39,175 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:39,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:39,524 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:43:39,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:39,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:39,569 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:48 CST)" executed successfully
2025-08-05 20:43:43,573 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:43:47,778 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:43:47,780 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:43:48,721 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:44:48 CST)" (scheduled at 2025-08-05 20:43:48.715924+08:00)
2025-08-05 20:43:48,768 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:43:48,768 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:48,768 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:43:48,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:44:18 CST)" (scheduled at 2025-08-05 20:43:48.850591+08:00)
2025-08-05 20:43:48,905 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:43:48,905 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:48,971 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:43:48,971 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:43:48,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:58 CST)" (scheduled at 2025-08-05 20:43:48.984488+08:00)
2025-08-05 20:43:49,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:49,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:49,037 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:43:49,170 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:49,388 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:43:49,390 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:43:49,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:43:49,397 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:43:49,398 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:49,445 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:44:18 CST)" executed successfully
2025-08-05 20:43:49,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:49,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:43:49,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:49,519 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:49,564 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:43:58 CST)" executed successfully
2025-08-05 20:43:50,600 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:43:50,600 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.83秒
2025-08-05 20:43:50,601 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:43:50,602 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:50,647 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:44:48 CST)" executed successfully
2025-08-05 20:43:59,000 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:08 CST)" (scheduled at 2025-08-05 20:43:58.984488+08:00)
2025-08-05 20:43:59,045 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:43:59,045 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:43:59,182 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:43:59,539 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:43:59,540 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:43:59,542 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:43:59,542 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:43:59,587 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:08 CST)" executed successfully
2025-08-05 20:44:00,217 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:44:08,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:18 CST)" (scheduled at 2025-08-05 20:44:08.984488+08:00)
2025-08-05 20:44:09,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:09,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:09,170 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:09,527 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:09,528 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:44:09,528 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:09,528 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:09,575 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:18 CST)" executed successfully
2025-08-05 20:44:13,588 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:44:18,864 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:44:48 CST)" (scheduled at 2025-08-05 20:44:18.850591+08:00)
2025-08-05 20:44:18,910 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:44:18,910 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:28 CST)" (scheduled at 2025-08-05 20:44:18.984488+08:00)
2025-08-05 20:44:19,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:19,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:19,045 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:44:19,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:19,398 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:44:19,399 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:44:19,399 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:44:19,399 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:44:19,400 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:19,445 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:44:48 CST)" executed successfully
2025-08-05 20:44:19,525 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:19,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:44:19,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:19,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:19,574 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:28 CST)" executed successfully
2025-08-05 20:44:28,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:38 CST)" (scheduled at 2025-08-05 20:44:28.984488+08:00)
2025-08-05 20:44:29,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:29,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:29,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:29,530 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:29,530 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:44:29,531 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:29,531 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:29,576 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:38 CST)" executed successfully
2025-08-05 20:44:30,450 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:44:38,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:48 CST)" (scheduled at 2025-08-05 20:44:38.984488+08:00)
2025-08-05 20:44:39,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:39,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:39,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:39,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:39,522 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:44:39,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:39,523 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:39,569 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:48 CST)" executed successfully
2025-08-05 20:44:43,591 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:44:47,782 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:44:47,783 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:44:48,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:45:48 CST)" (scheduled at 2025-08-05 20:44:48.715924+08:00)
2025-08-05 20:44:48,775 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:44:48,778 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:48,784 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:44:48,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:45:18 CST)" (scheduled at 2025-08-05 20:44:48.850591+08:00)
2025-08-05 20:44:48,898 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:44:48,899 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:48,994 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:58 CST)" (scheduled at 2025-08-05 20:44:48.984488+08:00)
2025-08-05 20:44:49,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:49,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:49,040 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:44:49,046 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:44:49,047 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:44:49,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:49,396 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:44:49,397 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:44:49,399 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:44:49,400 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:44:49,402 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:49,447 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:45:18 CST)" executed successfully
2025-08-05 20:44:49,522 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:49,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:44:49,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:49,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:49,574 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:44:58 CST)" executed successfully
2025-08-05 20:44:49,687 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:44:49,688 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.90秒
2025-08-05 20:44:49,689 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:44:49,689 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:49,736 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:45:48 CST)" executed successfully
2025-08-05 20:44:58,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:08 CST)" (scheduled at 2025-08-05 20:44:58.984488+08:00)
2025-08-05 20:44:59,047 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:44:59,048 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:44:59,180 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:44:59,528 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:44:59,529 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:44:59,531 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:44:59,533 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:44:59,579 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:08 CST)" executed successfully
2025-08-05 20:45:00,638 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:45:08,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:18 CST)" (scheduled at 2025-08-05 20:45:08.984488+08:00)
2025-08-05 20:45:09,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:09,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:09,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:09,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:09,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:45:09,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:09,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:09,571 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:18 CST)" executed successfully
2025-08-05 20:45:13,593 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:45:18,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:45:48 CST)" (scheduled at 2025-08-05 20:45:18.850591+08:00)
2025-08-05 20:45:18,896 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:45:18,897 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:28 CST)" (scheduled at 2025-08-05 20:45:18.984488+08:00)
2025-08-05 20:45:19,032 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:45:19,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:19,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:19,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:19,386 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:45:19,387 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:45:19,389 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:45:19,392 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:45:19,398 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:19,446 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:45:48 CST)" executed successfully
2025-08-05 20:45:19,536 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:19,537 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:45:19,539 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:19,540 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:19,585 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:28 CST)" executed successfully
2025-08-05 20:45:28,993 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:38 CST)" (scheduled at 2025-08-05 20:45:28.984488+08:00)
2025-08-05 20:45:29,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:29,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:29,173 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:29,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:29,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:45:29,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:29,522 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:29,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:38 CST)" executed successfully
2025-08-05 20:45:30,826 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:45:38,993 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:48 CST)" (scheduled at 2025-08-05 20:45:38.984488+08:00)
2025-08-05 20:45:39,038 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:39,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:39,177 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:39,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:39,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:45:39,527 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:39,529 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:39,577 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:48 CST)" executed successfully
2025-08-05 20:45:43,609 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:45:47,796 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:45:47,798 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:45:48,726 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:46:48 CST)" (scheduled at 2025-08-05 20:45:48.715924+08:00)
2025-08-05 20:45:48,770 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:45:48,770 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:48,771 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:45:48,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:46:18 CST)" (scheduled at 2025-08-05 20:45:48.850591+08:00)
2025-08-05 20:45:48,897 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:45:48,897 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:48,972 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:45:48,973 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:45:48,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:58 CST)" (scheduled at 2025-08-05 20:45:48.984488+08:00)
2025-08-05 20:45:49,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:49,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:49,035 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:45:49,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:49,396 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:45:49,397 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:45:49,397 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:45:49,398 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:45:49,399 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:49,444 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:46:18 CST)" executed successfully
2025-08-05 20:45:49,511 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:49,511 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:45:49,512 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:49,512 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:49,556 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:45:58 CST)" executed successfully
2025-08-05 20:45:50,171 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:45:50,172 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.40秒
2025-08-05 20:45:50,175 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:45:50,177 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:50,223 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:46:48 CST)" executed successfully
2025-08-05 20:45:58,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:08 CST)" (scheduled at 2025-08-05 20:45:58.984488+08:00)
2025-08-05 20:45:59,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:45:59,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:45:59,178 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:45:59,528 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:45:59,529 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:45:59,530 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:45:59,531 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:45:59,575 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:08 CST)" executed successfully
2025-08-05 20:46:00,005 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:46:08,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:18 CST)" (scheduled at 2025-08-05 20:46:08.984488+08:00)
2025-08-05 20:46:09,041 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:09,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:09,176 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:09,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:09,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:46:09,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:09,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:09,570 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:18 CST)" executed successfully
2025-08-05 20:46:13,625 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:46:18,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:46:48 CST)" (scheduled at 2025-08-05 20:46:18.850591+08:00)
2025-08-05 20:46:18,899 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:46:18,900 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:28 CST)" (scheduled at 2025-08-05 20:46:18.984488+08:00)
2025-08-05 20:46:19,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:46:19,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:19,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:19,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:19,383 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:46:19,384 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:46:19,384 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:46:19,385 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:46:19,385 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:19,431 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:46:48 CST)" executed successfully
2025-08-05 20:46:19,520 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:19,521 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:46:19,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:19,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:19,573 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:28 CST)" executed successfully
2025-08-05 20:46:28,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:38 CST)" (scheduled at 2025-08-05 20:46:28.984488+08:00)
2025-08-05 20:46:29,041 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:29,041 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:29,171 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:29,562 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:29,563 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 20:46:29,571 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:29,572 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:29,623 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:38 CST)" executed successfully
2025-08-05 20:46:30,204 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:46:38,993 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:48 CST)" (scheduled at 2025-08-05 20:46:38.984488+08:00)
2025-08-05 20:46:39,038 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:39,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:39,177 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:39,532 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:39,533 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:46:39,533 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:39,533 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:39,577 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:48 CST)" executed successfully
2025-08-05 20:46:43,631 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:46:47,803 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:46:47,804 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:46:48,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:47:48 CST)" (scheduled at 2025-08-05 20:46:48.715924+08:00)
2025-08-05 20:46:48,775 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:46:48,778 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:48,779 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:46:48,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:47:18 CST)" (scheduled at 2025-08-05 20:46:48.850591+08:00)
2025-08-05 20:46:48,896 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:46:48,896 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:48,984 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:58 CST)" (scheduled at 2025-08-05 20:46:48.984488+08:00)
2025-08-05 20:46:49,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:49,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:49,033 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:46:49,047 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:46:49,047 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:46:49,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:49,383 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:46:49,383 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:46:49,384 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:46:49,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:46:49,385 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:49,429 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:47:18 CST)" executed successfully
2025-08-05 20:46:49,515 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:49,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:46:49,516 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:49,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:49,560 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:46:58 CST)" executed successfully
2025-08-05 20:46:49,646 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:46:49,647 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.87秒
2025-08-05 20:46:49,647 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:46:49,648 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:49,691 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:47:48 CST)" executed successfully
2025-08-05 20:46:58,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:08 CST)" (scheduled at 2025-08-05 20:46:58.984488+08:00)
2025-08-05 20:46:59,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:46:59,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:46:59,170 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:46:59,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:46:59,519 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:46:59,520 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:46:59,522 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:46:59,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:08 CST)" executed successfully
2025-08-05 20:47:00,385 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:47:08,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:18 CST)" (scheduled at 2025-08-05 20:47:08.984488+08:00)
2025-08-05 20:47:09,043 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:09,044 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:09,176 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:09,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:09,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:47:09,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:09,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:09,568 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:18 CST)" executed successfully
2025-08-05 20:47:13,637 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:47:18,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:47:48 CST)" (scheduled at 2025-08-05 20:47:18.850591+08:00)
2025-08-05 20:47:18,895 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:47:18,895 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:28 CST)" (scheduled at 2025-08-05 20:47:18.984488+08:00)
2025-08-05 20:47:19,023 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:47:19,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:19,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:19,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:19,366 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:47:19,367 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:47:19,367 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 20:47:19,367 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:47:19,368 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:19,414 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:47:48 CST)" executed successfully
2025-08-05 20:47:19,520 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:19,520 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:47:19,521 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:19,522 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:19,567 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:28 CST)" executed successfully
2025-08-05 20:47:28,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:38 CST)" (scheduled at 2025-08-05 20:47:28.984488+08:00)
2025-08-05 20:47:29,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:29,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:29,175 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:29,526 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:29,526 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:47:29,527 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:29,527 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:29,576 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:38 CST)" executed successfully
2025-08-05 20:47:30,609 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:47:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:48 CST)" (scheduled at 2025-08-05 20:47:38.984488+08:00)
2025-08-05 20:47:39,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:39,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:39,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:39,517 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:39,517 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:47:39,518 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:39,518 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:39,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:48 CST)" executed successfully
2025-08-05 20:47:43,648 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:47:47,817 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:47:47,818 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:47:48,731 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:48:48 CST)" (scheduled at 2025-08-05 20:47:48.715924+08:00)
2025-08-05 20:47:48,776 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:47:48,777 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:48,777 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:47:48,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:48:18 CST)" (scheduled at 2025-08-05 20:47:48.850591+08:00)
2025-08-05 20:47:48,900 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:47:48,900 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:48,973 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:47:48,974 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:47:48,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:58 CST)" (scheduled at 2025-08-05 20:47:48.984488+08:00)
2025-08-05 20:47:49,033 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:47:49,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:49,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:49,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:49,403 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:47:49,403 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:47:49,405 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 20:47:49,405 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:47:49,406 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:49,450 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:48:18 CST)" executed successfully
2025-08-05 20:47:49,514 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:49,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:47:49,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:49,517 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:49,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:47:58 CST)" executed successfully
2025-08-05 20:47:50,556 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:47:50,556 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.78秒
2025-08-05 20:47:50,557 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:47:50,557 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:50,604 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:48:48 CST)" executed successfully
2025-08-05 20:47:59,000 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:08 CST)" (scheduled at 2025-08-05 20:47:58.984488+08:00)
2025-08-05 20:47:59,046 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:47:59,047 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:47:59,178 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:47:59,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:47:59,522 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:47:59,522 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:47:59,523 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:47:59,568 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:08 CST)" executed successfully
2025-08-05 20:48:00,779 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:48:08,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:18 CST)" (scheduled at 2025-08-05 20:48:08.984488+08:00)
2025-08-05 20:48:09,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:09,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:09,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:09,521 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:09,522 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:48:09,523 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:09,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:09,571 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:18 CST)" executed successfully
2025-08-05 20:48:13,652 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:48:18,862 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:48:48 CST)" (scheduled at 2025-08-05 20:48:18.850591+08:00)
2025-08-05 20:48:18,908 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:48:18,908 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:18,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:28 CST)" (scheduled at 2025-08-05 20:48:18.984488+08:00)
2025-08-05 20:48:19,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:19,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:19,038 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:48:19,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:19,389 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:48:19,389 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:48:19,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:48:19,390 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:48:19,390 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:19,434 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:48:48 CST)" executed successfully
2025-08-05 20:48:19,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:19,524 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:48:19,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:19,528 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:19,573 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:28 CST)" executed successfully
2025-08-05 20:48:28,998 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:38 CST)" (scheduled at 2025-08-05 20:48:28.984488+08:00)
2025-08-05 20:48:29,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:29,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:29,175 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:29,536 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:29,536 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:48:29,537 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:29,537 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:29,584 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:38 CST)" executed successfully
2025-08-05 20:48:30,979 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:48:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:48 CST)" (scheduled at 2025-08-05 20:48:38.984488+08:00)
2025-08-05 20:48:39,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:39,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:39,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:39,551 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:39,552 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 20:48:39,552 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:39,552 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:39,600 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:48 CST)" executed successfully
2025-08-05 20:48:43,661 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:48:47,824 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=2, 总计=4
2025-08-05 20:48:47,825 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:48:48,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:49:48 CST)" (scheduled at 2025-08-05 20:48:48.715924+08:00)
2025-08-05 20:48:48,768 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:48:48,769 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:48,771 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:48:48,863 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:49:18 CST)" (scheduled at 2025-08-05 20:48:48.850591+08:00)
2025-08-05 20:48:48,908 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:48:48,908 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:48,962 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:48:48,962 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:48:48,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:58 CST)" (scheduled at 2025-08-05 20:48:48.984488+08:00)
2025-08-05 20:48:49,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:57,261 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:57,262 - scheduler_health_checks - ERROR - 怪物冷却管理器健康检查失败: Timeout reading from *************:6379
2025-08-05 20:48:57,267 - scheduler_health_checks - ERROR - Traceback (most recent call last):
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 534, in read_response
    response = await self._parser.read_response(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        disable_decoding=disable_decoding
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\python_evns\hj\Lib\site-packages\redis\_parsers\hiredis.py", line 213, in read_response
    await self.read_from_socket()
  File "D:\python_evns\hj\Lib\site-packages\redis\_parsers\hiredis.py", line 191, in read_from_socket
    buffer = await self._stream.read(self._read_size)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 730, in read
    await self._wait_for_data('read')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 539, in _wait_for_data
    await self._waiter
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 533, in read_response
    async with async_timeout(read_timeout):
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\timeouts.py", line 116, in __aexit__
    raise TimeoutError from exc_val
TimeoutError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\python_evns\newServer\scheduler_health_checks.py", line 100, in check_monster_cooldown_manager_health
    await redis_client.delete(test_key)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 615, in execute_command
    return await conn.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 62, in call_with_retry
    await fail(error)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 602, in _disconnect_raise
    raise error
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 59, in call_with_retry
    return await do()
           ^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 589, in _send_command_parse_response
    return await self.parse_response(conn, command_name, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 636, in parse_response
    response = await connection.read_response()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 552, in read_response
    raise TimeoutError(f"Timeout reading from {host_error}")
redis.exceptions.TimeoutError: Timeout reading from *************:6379

2025-08-05 20:48:57,267 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_persist 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 20:48:57,268 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_persist 执行失败，第 1 次重试: 任务 monster_cooldown_persist 依赖验证失败
2025-08-05 20:48:57,269 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:48:57,269 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 8.50秒
2025-08-05 20:48:57,270 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:48:57,270 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:57,392 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:57,445 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:49:48 CST)" executed successfully
2025-08-05 20:48:57,740 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:57,740 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:48:57,741 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:57,741 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:57,788 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:48:58 CST)" executed successfully
2025-08-05 20:48:58,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:08 CST)" (scheduled at 2025-08-05 20:48:58.984488+08:00)
2025-08-05 20:48:59,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:48:59,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:48:59,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:48:59,498 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:48:59,498 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 20:48:59,499 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:48:59,499 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:48:59,543 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:08 CST)" executed successfully
2025-08-05 20:49:00,292 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:49:02,404 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:49:02,748 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:49:02,748 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:49:02,749 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 20:49:02,749 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:49:02,749 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:02,793 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:49:18 CST)" executed successfully
2025-08-05 20:49:08,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:18 CST)" (scheduled at 2025-08-05 20:49:08.984488+08:00)
2025-08-05 20:49:09,041 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:09,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:09,166 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:09,503 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:09,504 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:49:09,505 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:09,506 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:09,549 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:18 CST)" executed successfully
2025-08-05 20:49:13,662 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:49:18,862 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:49:48 CST)" (scheduled at 2025-08-05 20:49:18.850591+08:00)
2025-08-05 20:49:18,906 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:49:18,906 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:18,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:28 CST)" (scheduled at 2025-08-05 20:49:18.984488+08:00)
2025-08-05 20:49:19,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:19,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:19,039 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:49:19,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:19,379 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:49:19,379 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:49:19,379 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 20:49:19,380 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:49:19,380 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:19,423 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:49:48 CST)" executed successfully
2025-08-05 20:49:19,510 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:19,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:49:19,511 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:19,511 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:19,557 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:28 CST)" executed successfully
2025-08-05 20:49:24,898 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:49:28,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:38 CST)" (scheduled at 2025-08-05 20:49:28.984488+08:00)
2025-08-05 20:49:29,030 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:29,030 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:29,158 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:29,501 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:29,502 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:49:29,502 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:29,503 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:29,550 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:38 CST)" executed successfully
2025-08-05 20:49:30,593 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:49:38,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:48 CST)" (scheduled at 2025-08-05 20:49:38.984488+08:00)
2025-08-05 20:49:42,345 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:42,346 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:42,483 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:42,837 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:42,837 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:49:42,838 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:42,838 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:42,884 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:48 CST)" executed successfully
2025-08-05 20:49:44,490 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:49:47,837 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:49:47,838 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:49:48,718 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:50:48 CST)" (scheduled at 2025-08-05 20:49:48.715924+08:00)
2025-08-05 20:49:48,767 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:49:48,767 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:48,768 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:49:48,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:50:18 CST)" (scheduled at 2025-08-05 20:49:48.850591+08:00)
2025-08-05 20:49:48,900 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:49:48,900 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:48,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:58 CST)" (scheduled at 2025-08-05 20:49:48.984488+08:00)
2025-08-05 20:49:49,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:49:49,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:49,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:49,105 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:49:49,105 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:49:49,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:49,384 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:49:49,384 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:49:49,385 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:49:49,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:49:49,387 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:49,431 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:50:18 CST)" executed successfully
2025-08-05 20:49:49,507 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:49,508 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:49:49,508 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:49,509 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:49,552 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:49:58 CST)" executed successfully
2025-08-05 20:49:49,737 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:49:49,737 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.97秒
2025-08-05 20:49:49,738 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:49:49,738 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:49,784 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:50:48 CST)" executed successfully
2025-08-05 20:49:58,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:08 CST)" (scheduled at 2025-08-05 20:49:58.984488+08:00)
2025-08-05 20:49:59,029 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:49:59,029 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:49:59,153 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:49:59,489 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:49:59,490 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:49:59,490 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:49:59,491 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:49:59,536 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:08 CST)" executed successfully
2025-08-05 20:50:00,371 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:50:08,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:18 CST)" (scheduled at 2025-08-05 20:50:08.984488+08:00)
2025-08-05 20:50:09,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:09,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:09,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:09,501 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:09,502 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:50:09,502 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:09,503 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:09,546 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:18 CST)" executed successfully
2025-08-05 20:50:14,502 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:50:18,855 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:50:48 CST)" (scheduled at 2025-08-05 20:50:18.850591+08:00)
2025-08-05 20:50:18,900 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:50:18,901 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:18,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:28 CST)" (scheduled at 2025-08-05 20:50:18.984488+08:00)
2025-08-05 20:50:19,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:50:19,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:19,037 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:19,191 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:19,404 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:50:19,404 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:50:19,404 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 20:50:19,405 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:50:19,405 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:19,447 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:50:48 CST)" executed successfully
2025-08-05 20:50:19,534 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:19,534 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:50:19,535 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:19,535 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:19,579 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:28 CST)" executed successfully
2025-08-05 20:50:28,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:38 CST)" (scheduled at 2025-08-05 20:50:28.984488+08:00)
2025-08-05 20:50:29,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:29,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:29,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:30,020 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:30,020 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.86秒
2025-08-05 20:50:30,021 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:30,022 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:30,069 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:38 CST)" executed successfully
2025-08-05 20:50:30,069 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:50:38,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:48 CST)" (scheduled at 2025-08-05 20:50:38.984488+08:00)
2025-08-05 20:50:39,047 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:39,048 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:39,178 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:39,527 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:39,528 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:50:39,528 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:39,529 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:39,577 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:48 CST)" executed successfully
2025-08-05 20:50:44,526 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:50:47,852 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:50:47,852 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:50:48,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:51:48 CST)" (scheduled at 2025-08-05 20:50:48.715924+08:00)
2025-08-05 20:50:48,775 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:50:48,778 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:48,783 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:50:48,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:51:18 CST)" (scheduled at 2025-08-05 20:50:48.850591+08:00)
2025-08-05 20:50:48,901 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:50:48,902 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:48,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:58 CST)" (scheduled at 2025-08-05 20:50:48.984488+08:00)
2025-08-05 20:50:48,994 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:50:48,995 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:50:49,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:49,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:49,035 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:50:49,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:49,381 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:50:49,381 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:50:49,382 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:50:49,383 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:50:49,383 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:49,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:51:18 CST)" executed successfully
2025-08-05 20:50:49,498 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:49,499 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:50:49,501 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:49,502 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:49,545 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:50:58 CST)" executed successfully
2025-08-05 20:50:50,304 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:50:50,305 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.52秒
2025-08-05 20:50:50,305 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:50:50,306 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:50,352 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:51:48 CST)" executed successfully
2025-08-05 20:50:58,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:08 CST)" (scheduled at 2025-08-05 20:50:58.984488+08:00)
2025-08-05 20:50:59,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:50:59,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:50:59,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:50:59,505 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:50:59,505 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:50:59,506 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:50:59,506 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:50:59,550 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:08 CST)" executed successfully
2025-08-05 20:51:00,250 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:51:08,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:18 CST)" (scheduled at 2025-08-05 20:51:08.984488+08:00)
2025-08-05 20:51:09,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:09,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:09,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:09,519 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:09,519 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:51:09,520 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:09,521 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:09,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:18 CST)" executed successfully
2025-08-05 20:51:14,528 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:51:18,866 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:51:48 CST)" (scheduled at 2025-08-05 20:51:18.850591+08:00)
2025-08-05 20:51:18,914 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:51:18,915 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:28 CST)" (scheduled at 2025-08-05 20:51:18.984488+08:00)
2025-08-05 20:51:19,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:19,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:19,044 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:51:19,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:19,392 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:51:19,392 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:51:19,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:51:19,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:51:19,394 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:19,448 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:51:48 CST)" executed successfully
2025-08-05 20:51:19,513 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:19,513 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:51:19,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:19,516 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:19,562 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:28 CST)" executed successfully
2025-08-05 20:51:28,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:38 CST)" (scheduled at 2025-08-05 20:51:28.984488+08:00)
2025-08-05 20:51:29,036 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:29,036 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:29,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:29,514 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:29,514 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:51:29,516 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:29,517 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:29,560 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:38 CST)" executed successfully
2025-08-05 20:51:30,461 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:51:38,987 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:48 CST)" (scheduled at 2025-08-05 20:51:38.984488+08:00)
2025-08-05 20:51:39,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:39,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:39,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:39,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:39,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:51:39,525 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:39,525 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:39,569 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:48 CST)" executed successfully
2025-08-05 20:51:44,544 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:51:47,865 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:51:47,865 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:51:48,717 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:52:48 CST)" (scheduled at 2025-08-05 20:51:48.715924+08:00)
2025-08-05 20:51:48,766 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:51:48,766 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:48,767 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:51:48,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:52:18 CST)" (scheduled at 2025-08-05 20:51:48.850591+08:00)
2025-08-05 20:51:48,906 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:51:48,907 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:48,958 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:51:48,958 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:51:48,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:58 CST)" (scheduled at 2025-08-05 20:51:48.984488+08:00)
2025-08-05 20:51:49,040 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:49,040 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:49,041 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:51:49,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:49,390 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:51:49,390 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:51:49,391 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:51:49,391 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:51:49,392 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:49,435 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:52:18 CST)" executed successfully
2025-08-05 20:51:49,516 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:49,516 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:51:49,517 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:49,517 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:49,559 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:51:58 CST)" executed successfully
2025-08-05 20:51:50,953 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:51:50,955 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.19秒
2025-08-05 20:51:50,956 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:51:50,959 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:51,014 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:52:48 CST)" executed successfully
2025-08-05 20:51:58,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:08 CST)" (scheduled at 2025-08-05 20:51:58.984488+08:00)
2025-08-05 20:51:59,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:51:59,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:51:59,163 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:51:59,509 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:51:59,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:51:59,510 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:51:59,511 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:51:59,553 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:08 CST)" executed successfully
2025-08-05 20:52:00,659 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:52:08,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:18 CST)" (scheduled at 2025-08-05 20:52:08.984488+08:00)
2025-08-05 20:52:09,039 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:09,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:09,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:09,508 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:09,509 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:52:09,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:09,517 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:09,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:18 CST)" executed successfully
2025-08-05 20:52:14,551 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:52:18,865 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:52:48 CST)" (scheduled at 2025-08-05 20:52:18.850591+08:00)
2025-08-05 20:52:18,970 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:52:18,970 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:18,989 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:28 CST)" (scheduled at 2025-08-05 20:52:18.984488+08:00)
2025-08-05 20:52:19,294 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:19,294 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:19,358 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:52:19,508 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 20:52:19,508 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 20:52:19,509 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 20:52:20,021 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:52:20,021 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:52:20,021 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.66秒
2025-08-05 20:52:20,022 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:52:20,022 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:20,070 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:52:48 CST)" executed successfully
2025-08-05 20:52:21,748 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:22,539 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:22,539 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.79秒
2025-08-05 20:52:22,540 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:22,540 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:22,598 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:28 CST)" executed successfully
2025-08-05 20:52:28,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:38 CST)" (scheduled at 2025-08-05 20:52:28.984488+08:00)
2025-08-05 20:52:29,044 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:29,044 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:29,176 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:29,529 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:29,530 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:52:29,530 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:29,531 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:29,574 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:38 CST)" executed successfully
2025-08-05 20:52:30,877 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:52:38,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:48 CST)" (scheduled at 2025-08-05 20:52:38.984488+08:00)
2025-08-05 20:52:39,037 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:39,038 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:39,175 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:39,524 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:39,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:52:39,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:39,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:39,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:48 CST)" executed successfully
2025-08-05 20:52:44,555 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:52:47,875 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:52:47,875 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:52:48,722 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:53:48 CST)" (scheduled at 2025-08-05 20:52:48.715924+08:00)
2025-08-05 20:52:48,767 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:52:48,767 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:48,767 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:52:48,864 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:53:18 CST)" (scheduled at 2025-08-05 20:52:48.850591+08:00)
2025-08-05 20:52:48,911 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:52:48,911 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:48,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:58 CST)" (scheduled at 2025-08-05 20:52:48.984488+08:00)
2025-08-05 20:52:49,032 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:52:49,032 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:52:49,237 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:49,237 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:49,238 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:52:49,464 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:49,693 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:52:49,693 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:52:49,693 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.46秒
2025-08-05 20:52:49,694 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:52:49,694 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:49,737 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:52:49,737 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.97秒
2025-08-05 20:52:49,738 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:52:49,738 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:49,741 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:53:18 CST)" executed successfully
2025-08-05 20:52:49,814 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:49,815 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:52:49,815 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:49,816 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:49,831 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:53:48 CST)" executed successfully
2025-08-05 20:52:49,861 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:52:58 CST)" executed successfully
2025-08-05 20:52:58,986 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:08 CST)" (scheduled at 2025-08-05 20:52:58.984488+08:00)
2025-08-05 20:52:59,029 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:52:59,029 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:52:59,156 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:52:59,498 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:52:59,498 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:52:59,498 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:52:59,499 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:52:59,543 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:08 CST)" executed successfully
2025-08-05 20:53:00,094 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:53:08,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:18 CST)" (scheduled at 2025-08-05 20:53:08.984488+08:00)
2025-08-05 20:53:09,033 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:09,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:09,160 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:09,511 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:09,511 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:53:09,512 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:09,513 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:09,556 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:18 CST)" executed successfully
2025-08-05 20:53:14,571 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:53:18,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:53:48 CST)" (scheduled at 2025-08-05 20:53:18.850591+08:00)
2025-08-05 20:53:18,899 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:53:18,899 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:18,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:28 CST)" (scheduled at 2025-08-05 20:53:18.984488+08:00)
2025-08-05 20:53:19,030 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:53:19,031 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:19,032 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:19,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:19,372 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:53:19,373 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:53:19,374 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 20:53:19,375 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:53:19,375 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:19,418 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:53:48 CST)" executed successfully
2025-08-05 20:53:19,505 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:19,505 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:53:19,505 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:19,506 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:19,549 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:28 CST)" executed successfully
2025-08-05 20:53:28,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:38 CST)" (scheduled at 2025-08-05 20:53:28.984488+08:00)
2025-08-05 20:53:29,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:29,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:29,172 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:29,533 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:29,533 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:53:29,534 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:29,534 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:29,580 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:38 CST)" executed successfully
2025-08-05 20:53:30,322 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:53:38,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:48 CST)" (scheduled at 2025-08-05 20:53:38.984488+08:00)
2025-08-05 20:53:39,035 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:39,035 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:39,164 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:39,508 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:39,509 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:53:39,510 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:39,511 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:39,557 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:48 CST)" executed successfully
2025-08-05 20:53:44,587 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:53:47,877 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:53:47,878 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:53:48,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:54:48 CST)" (scheduled at 2025-08-05 20:53:48.715924+08:00)
2025-08-05 20:53:48,775 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:53:48,775 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:48,776 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:53:48,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:54:18 CST)" (scheduled at 2025-08-05 20:53:48.850591+08:00)
2025-08-05 20:53:48,895 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:53:48,896 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:48,995 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:58 CST)" (scheduled at 2025-08-05 20:53:48.984488+08:00)
2025-08-05 20:53:49,009 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:53:49,009 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:53:49,025 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:53:49,041 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:49,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:49,169 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:49,375 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:53:49,375 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:53:49,375 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:53:49,376 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:53:49,376 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:49,424 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:54:18 CST)" executed successfully
2025-08-05 20:53:49,525 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:49,525 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:53:49,526 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:49,526 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:49,575 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:53:58 CST)" executed successfully
2025-08-05 20:53:50,197 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:53:50,197 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.42秒
2025-08-05 20:53:50,197 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:53:50,198 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:50,241 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:54:48 CST)" executed successfully
2025-08-05 20:53:58,988 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:08 CST)" (scheduled at 2025-08-05 20:53:58.984488+08:00)
2025-08-05 20:53:59,032 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:53:59,033 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:53:59,161 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:53:59,509 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:53:59,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:53:59,512 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:53:59,513 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:53:59,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:08 CST)" executed successfully
2025-08-05 20:54:00,537 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:54:08,996 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:18 CST)" (scheduled at 2025-08-05 20:54:08.984488+08:00)
2025-08-05 20:54:09,043 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:09,046 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:09,181 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:09,803 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:09,803 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.62秒
2025-08-05 20:54:09,804 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:09,804 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:09,849 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:18 CST)" executed successfully
2025-08-05 20:54:14,596 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:54:18,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:54:48 CST)" (scheduled at 2025-08-05 20:54:18.850591+08:00)
2025-08-05 20:54:18,953 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:54:18,953 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:18,991 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:28 CST)" (scheduled at 2025-08-05 20:54:18.984488+08:00)
2025-08-05 20:54:19,153 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:19,154 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:19,253 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:54:19,452 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 20:54:19,452 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 20:54:19,454 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 20:54:20,338 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:54:20,338 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:54:20,339 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 1.09秒
2025-08-05 20:54:20,340 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:54:20,340 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:20,390 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:54:48 CST)" executed successfully
2025-08-05 20:54:21,770 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:22,593 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:22,593 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.82秒
2025-08-05 20:54:22,593 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:22,594 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:22,637 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:28 CST)" executed successfully
2025-08-05 20:54:28,992 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:38 CST)" (scheduled at 2025-08-05 20:54:28.984488+08:00)
2025-08-05 20:54:29,038 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:29,039 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:29,167 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:29,504 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:29,504 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 20:54:29,505 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:29,505 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:29,548 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:38 CST)" executed successfully
2025-08-05 20:54:30,726 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:54:38,999 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:48 CST)" (scheduled at 2025-08-05 20:54:38.984488+08:00)
2025-08-05 20:54:39,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:39,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:39,168 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:39,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:39,524 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:54:39,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:39,524 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:39,568 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:48 CST)" executed successfully
2025-08-05 20:54:44,610 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:54:47,882 - ConnectionManager - INFO - Redis连接池状态 (Worker 41720): 使用中=2, 可用=3, 总计=5
2025-08-05 20:54:47,882 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 41720): 连接中
2025-08-05 20:54:48,721 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:55:48 CST)" (scheduled at 2025-08-05 20:54:48.715924+08:00)
2025-08-05 20:54:48,766 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:push_online
2025-08-05 20:54:48,767 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:48,769 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行推送在线人数任务
2025-08-05 20:54:48,860 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:55:18 CST)" (scheduled at 2025-08-05 20:54:48.850591+08:00)
2025-08-05 20:54:48,904 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:54:48,905 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:48,967 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:54:48,968 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:54:48,990 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:58 CST)" (scheduled at 2025-08-05 20:54:48.984488+08:00)
2025-08-05 20:54:49,033 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 开始执行怪物冷却持久化任务
2025-08-05 20:54:49,034 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:49,034 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:49,166 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:49,378 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:54:49,378 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 怪物冷却持久化完成
2025-08-05 20:54:49,379 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:54:49,379 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:54:49,380 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:49,424 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:55:18 CST)" executed successfully
2025-08-05 20:54:49,512 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:49,512 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:54:49,513 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:49,513 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:49,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:54:58 CST)" executed successfully
2025-08-05 20:54:50,649 - scheduler_tasks_unified - INFO - [定时任务] Worker 41720 在线人数推送完成，当前在线: 0
2025-08-05 20:54:50,650 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.88秒
2025-08-05 20:54:50,651 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:54:50,651 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:50,707 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:55:48 CST)" executed successfully
2025-08-05 20:54:58,997 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:55:08 CST)" (scheduled at 2025-08-05 20:54:58.984488+08:00)
2025-08-05 20:54:59,042 - distributed_lock - INFO - Worker 41720 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:54:59,042 - distributed_task - INFO - Worker 41720 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:54:59,173 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:54:59,518 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:54:59,518 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:54:59,519 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:54:59,519 - distributed_task - INFO - Worker 41720 任务执行完成: direct_wrapper
2025-08-05 20:54:59,563 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:55:08 CST)" executed successfully
2025-08-05 20:55:00,933 - ConnectionManager - INFO - 连接状态 (Worker 41720): 活跃连接数=0, 用户数=0
2025-08-05 20:55:04,435 - game_server - INFO - 关闭服务器... (Worker: 41720)
2025-08-05 20:55:04,437 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 20:55:04,798 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:55:04,799 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 20:55:04,799 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:04,800 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 20:55:04,800 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 20:55:04,801 - game_server - INFO - 日志清理管理器已停止
2025-08-05 20:55:04,801 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 20:55:04,801 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 20:55:04,802 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 20:55:04,802 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 20:55:04,803 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 20:55:04,804 - game_server - INFO - 统一调度器已关闭
2025-08-05 20:55:04,804 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 20:55:04,807 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 41720)
2025-08-05 20:55:04,850 - game_server - INFO - 服务器资源已清理 (Worker: 41720)
