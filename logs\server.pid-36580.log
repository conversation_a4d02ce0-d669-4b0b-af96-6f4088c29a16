2025-08-05 18:09:25,342 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,196 - models - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,221 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,685 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,694 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,703 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,715 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,729 - auth - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,309 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,355 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,384 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,396 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,408 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,408 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 6be4608c)
2025-08-05 18:09:29,415 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,450 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,460 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,469 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,478 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,554 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,562 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,569 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,575 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,582 - game_server - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,584 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:09:29,584 - msgManager - INFO - Monster handlers registered
2025-08-05 18:09:29,584 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:09:29,586 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:09:29,594 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:09:29,595 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:09:29,595 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52320001)
2025-08-05 18:09:29,633 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:09:29,678 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:09:29,678 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:09:29,680 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:09:29,681 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 36580)
2025-08-05 18:09:29,681 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:09:29,857 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:09:29,858 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:09:30,392 - ConnectionManager - INFO - 后台任务已启动 (Worker 36580)
2025-08-05 18:09:30,393 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 36580)
2025-08-05 18:09:30,393 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:09:30,394 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:09:30,394 - game_server - INFO - 游戏配置加载完成 (Worker: 36580)
2025-08-05 18:09:30,395 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:09:33,541 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:09:33,541 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=0, 总计=2
2025-08-05 18:09:33,542 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 36580): 2/2 (100.0%)
2025-08-05 18:09:33,543 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:09:33,586 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 52320001)
2025-08-05 18:09:33,586 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52320001)
2025-08-05 18:09:33,594 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:33,595 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:09:33,597 - ConnectionManager - INFO - Worker 36580 开始消费广播消息，消费者标签: ctag1.520b9f08d582498e9e02287cc59a10f7
2025-08-05 18:09:33,641 - ConnectionManager - INFO - Worker 36580 开始消费个人消息，消费者标签: ctag1.54cc29bb62c549f3b3317d1190460a0c
2025-08-05 18:09:33,712 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 36580)
2025-08-05 18:09:33,808 - distributed_lock - INFO - Worker 36580 成功获取锁: scheduler_initialization
2025-08-05 18:09:33,808 - game_server_scheduler_integration - INFO - Worker 36580 获得调度器初始化权限
2025-08-05 18:09:33,812 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:09:33,813 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:09:33,814 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:09:33,814 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:09:33,815 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:09:33,815 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:09:33,817 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:09:33,817 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:09:33,818 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:09:33,818 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:09:33,818 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:09:33,819 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:09:33,819 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:09:33,906 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:09:33,907 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:09:33,908 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:09:33,909 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:09:34,230 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:09:34,319 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:09:34,320 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:09:34,320 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:09:34,321 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:09:34,321 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:09:34,321 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:09:34,409 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:09:34,539 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:09:34,539 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:09:34,540 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:09:34,541 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:09:34,541 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:09:34,542 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:09:34,547 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,548 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:09:34,548 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:09:34,551 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,551 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:09:34,551 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:09:34,679 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,679 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:09:34,679 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:09:34,812 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,812 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:09:34,813 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:09:34,816 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,817 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:09:34,817 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:09:34,818 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,818 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:09:34,819 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:09:34,820 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:09:34,821 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,822 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,823 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,824 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,824 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,825 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,826 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:09:34,826 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:09:34,826 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:09:34,827 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:09:34,827 - game_server_scheduler_integration - INFO - Worker 36580 调度器初始化成功
2025-08-05 18:09:34,872 - game_server - INFO - 统一调度器初始化成功 (Worker: 36580)
2025-08-05 18:09:34,878 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:34,879 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:09:34,879 - game_server - INFO - 日志清理管理器已启动 (Worker: 36580)
2025-08-05 18:09:34,880 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:09:34,880 - game_server - INFO - Monster cooldown manager initialized (Worker: 36580)
2025-08-05 18:09:35,027 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:09:35,027 - game_server - INFO - 公会系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,028 - game_server - INFO - 邮件系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,032 - game_server - INFO - 商店系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,032 - game_server - INFO - 初始化完成 (Worker: 36580)
2025-08-05 18:09:44,816 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:54 CST)" (scheduled at 2025-08-05 18:09:44.812331+08:00)
2025-08-05 18:09:44,862 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:09:44,864 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:09:44,994 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:09:45,327 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:09:45,328 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 18:09:45,328 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:09:45,329 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:09:45,371 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:09:54 CST)" executed successfully
2025-08-05 18:09:54,307 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 18:09:54,354 - shop_database_manager - INFO - 商店创建成功: shop_b17163c30fa9
2025-08-05 18:09:54,440 - shop_service - INFO - 商店创建成功: shop_b17163c30fa9
2025-08-05 18:09:54,569 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:09:54,822 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:04 CST)" (scheduled at 2025-08-05 18:09:54.812331+08:00)
2025-08-05 18:09:54,874 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:09:54,874 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:09:55,001 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:09:55,342 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:09:55,342 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:09:55,343 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:09:55,344 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:09:55,386 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:04 CST)" executed successfully
2025-08-05 18:10:00,401 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:10:00,784 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:10:04,681 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:10:34 CST)" (scheduled at 2025-08-05 18:10:04.678985+08:00)
2025-08-05 18:10:04,724 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:10:04,725 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:04,819 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:14 CST)" (scheduled at 2025-08-05 18:10:04.812331+08:00)
2025-08-05 18:10:04,848 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:10:04,863 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:04,863 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:04,989 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:05,199 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:10:05,200 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:10:05,200 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:10:05,201 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:10:05,201 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:05,243 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:10:34 CST)" executed successfully
2025-08-05 18:10:05,328 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:05,328 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:10:05,329 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:05,330 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:05,375 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:14 CST)" executed successfully
2025-08-05 18:10:14,814 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:24 CST)" (scheduled at 2025-08-05 18:10:14.812331+08:00)
2025-08-05 18:10:14,860 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:14,861 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:14,988 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:15,335 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:15,335 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:10:15,335 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:15,336 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:15,381 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:24 CST)" executed successfully
2025-08-05 18:10:24,824 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:34 CST)" (scheduled at 2025-08-05 18:10:24.812331+08:00)
2025-08-05 18:10:24,868 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:24,870 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:25,004 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:25,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:25,352 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:10:25,352 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:25,353 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:25,395 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:34 CST)" executed successfully
2025-08-05 18:10:30,411 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:10:30,489 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:10:33,551 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:10:33,552 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:10:34,561 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:11:34 CST)" (scheduled at 2025-08-05 18:10:34.551094+08:00)
2025-08-05 18:10:34,608 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:10:34,610 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:34,611 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:10:34,612 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 18:10:34,686 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:11:04 CST)" (scheduled at 2025-08-05 18:10:34.678985+08:00)
2025-08-05 18:10:34,729 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:10:34,729 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:34,815 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:44 CST)" (scheduled at 2025-08-05 18:10:34.812331+08:00)
2025-08-05 18:10:34,856 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:10:34,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:34,857 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:34,983 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:35,198 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:10:35,198 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:10:35,199 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:10:35,199 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:10:35,199 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:35,243 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:11:04 CST)" executed successfully
2025-08-05 18:10:35,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:35,331 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:10:35,331 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:35,332 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:35,375 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:44 CST)" executed successfully
2025-08-05 18:10:35,709 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:10:35,710 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:10:35,713 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:10:35,713 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.10秒
2025-08-05 18:10:35,714 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:10:35,714 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:35,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:11:34 CST)" executed successfully
2025-08-05 18:10:44,813 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:54 CST)" (scheduled at 2025-08-05 18:10:44.812331+08:00)
2025-08-05 18:10:44,855 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:44,856 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:44,986 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:45,320 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:45,321 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:10:45,321 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:45,322 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:45,369 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:10:54 CST)" executed successfully
2025-08-05 18:10:54,826 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:04 CST)" (scheduled at 2025-08-05 18:10:54.812331+08:00)
2025-08-05 18:10:54,870 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:10:54,871 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:10:55,006 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:10:55,343 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:10:55,343 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:10:55,344 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:10:55,344 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:10:55,386 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:04 CST)" executed successfully
2025-08-05 18:11:00,426 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:11:00,690 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:11:04,690 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:11:34 CST)" (scheduled at 2025-08-05 18:11:04.678985+08:00)
2025-08-05 18:11:04,732 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:11:04,733 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:04,813 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:14 CST)" (scheduled at 2025-08-05 18:11:04.812331+08:00)
2025-08-05 18:11:04,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:04,857 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:04,858 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:11:04,984 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:05,195 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:11:05,196 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:11:05,196 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:11:05,196 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:11:05,197 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:05,241 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:11:34 CST)" executed successfully
2025-08-05 18:11:05,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:05,325 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:11:05,326 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:05,326 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:05,370 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:14 CST)" executed successfully
2025-08-05 18:11:14,824 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:24 CST)" (scheduled at 2025-08-05 18:11:14.812331+08:00)
2025-08-05 18:11:14,869 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:14,870 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:15,000 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:15,346 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:15,347 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:11:15,349 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:15,351 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:15,400 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:24 CST)" executed successfully
2025-08-05 18:11:24,814 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:34 CST)" (scheduled at 2025-08-05 18:11:24.812331+08:00)
2025-08-05 18:11:24,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:24,857 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:24,985 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:25,320 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:25,320 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:11:25,321 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:25,322 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:25,366 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:34 CST)" executed successfully
2025-08-05 18:11:30,443 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:11:30,892 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:11:33,559 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:11:33,561 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:11:34,566 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:12:34 CST)" (scheduled at 2025-08-05 18:11:34.551094+08:00)
2025-08-05 18:11:34,613 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:11:34,614 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:34,614 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:11:34,689 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:12:04 CST)" (scheduled at 2025-08-05 18:11:34.678985+08:00)
2025-08-05 18:11:34,734 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:11:34,734 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:34,813 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:44 CST)" (scheduled at 2025-08-05 18:11:34.812331+08:00)
2025-08-05 18:11:34,815 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:11:34,815 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:11:34,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:34,858 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:34,867 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:11:34,991 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:35,219 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:11:35,220 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:11:35,220 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:11:35,221 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:11:35,221 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:35,267 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:12:04 CST)" executed successfully
2025-08-05 18:11:35,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:35,331 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:11:35,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:35,332 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:35,376 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:44 CST)" executed successfully
2025-08-05 18:11:36,526 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:11:36,527 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.91秒
2025-08-05 18:11:36,527 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:11:36,528 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:36,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:12:34 CST)" executed successfully
2025-08-05 18:11:44,823 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:54 CST)" (scheduled at 2025-08-05 18:11:44.812331+08:00)
2025-08-05 18:11:44,866 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:44,866 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:44,992 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:45,334 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:45,335 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:11:45,337 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:45,346 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:45,393 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:11:54 CST)" executed successfully
2025-08-05 18:11:54,816 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:04 CST)" (scheduled at 2025-08-05 18:11:54.812331+08:00)
2025-08-05 18:11:54,858 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:11:54,859 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:11:54,983 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:11:55,319 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:11:55,320 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:11:55,320 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:11:55,321 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:11:55,367 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:04 CST)" executed successfully
2025-08-05 18:12:00,066 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:12:00,452 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:12:04,694 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:12:34 CST)" (scheduled at 2025-08-05 18:12:04.678985+08:00)
2025-08-05 18:12:04,736 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:12:04,736 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:04,816 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:14 CST)" (scheduled at 2025-08-05 18:12:04.812331+08:00)
2025-08-05 18:12:04,860 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:12:04,860 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:04,861 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:04,994 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:05,208 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:12:05,208 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:12:05,209 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:12:05,209 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:12:05,209 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:05,252 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:12:34 CST)" executed successfully
2025-08-05 18:12:05,338 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:05,339 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:05,340 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:05,345 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:05,388 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:14 CST)" executed successfully
2025-08-05 18:12:14,819 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:24 CST)" (scheduled at 2025-08-05 18:12:14.812331+08:00)
2025-08-05 18:12:14,862 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:14,863 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:14,988 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:15,327 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:15,327 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:15,328 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:15,328 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:15,372 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:24 CST)" executed successfully
2025-08-05 18:12:24,818 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:34 CST)" (scheduled at 2025-08-05 18:12:24.812331+08:00)
2025-08-05 18:12:24,861 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:24,861 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:24,986 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:25,324 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:25,324 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:25,324 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:25,325 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:25,367 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:34 CST)" executed successfully
2025-08-05 18:12:30,297 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:12:30,467 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:12:33,574 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:12:33,575 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:12:34,561 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:13:34 CST)" (scheduled at 2025-08-05 18:12:34.551094+08:00)
2025-08-05 18:12:34,604 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:12:34,604 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:34,607 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:12:34,682 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:13:04 CST)" (scheduled at 2025-08-05 18:12:34.678985+08:00)
2025-08-05 18:12:34,725 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:12:34,726 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:34,820 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:44 CST)" (scheduled at 2025-08-05 18:12:34.812331+08:00)
2025-08-05 18:12:34,826 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:12:34,826 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:12:34,852 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:12:34,863 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:34,863 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:34,988 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:35,194 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:12:35,194 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:12:35,197 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:12:35,198 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:12:35,198 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:35,241 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:13:04 CST)" executed successfully
2025-08-05 18:12:35,329 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:35,329 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:35,330 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:35,330 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:35,375 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:44 CST)" executed successfully
2025-08-05 18:12:36,112 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:12:36,112 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.50秒
2025-08-05 18:12:36,113 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:12:36,114 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:36,159 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:13:34 CST)" executed successfully
2025-08-05 18:12:44,818 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:54 CST)" (scheduled at 2025-08-05 18:12:44.812331+08:00)
2025-08-05 18:12:44,860 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:44,861 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:44,986 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:45,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:45,325 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:45,325 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:45,326 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:45,368 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:12:54 CST)" executed successfully
2025-08-05 18:12:54,826 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:04 CST)" (scheduled at 2025-08-05 18:12:54.812331+08:00)
2025-08-05 18:12:54,869 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:12:54,869 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:12:54,996 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:12:55,331 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:12:55,331 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:12:55,332 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:12:55,332 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:12:55,377 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:04 CST)" executed successfully
2025-08-05 18:13:00,475 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:13:00,491 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:13:04,688 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:13:34 CST)" (scheduled at 2025-08-05 18:13:04.678985+08:00)
2025-08-05 18:13:04,733 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:13:04,733 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:04,819 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:14 CST)" (scheduled at 2025-08-05 18:13:04.812331+08:00)
2025-08-05 18:13:04,861 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:13:04,863 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:04,863 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:04,992 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:05,196 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:13:05,196 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:13:05,197 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:13:05,197 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:13:05,198 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:05,242 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:13:34 CST)" executed successfully
2025-08-05 18:13:05,337 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:05,338 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:13:05,347 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:05,349 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:05,398 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:14 CST)" executed successfully
2025-08-05 18:13:14,823 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:24 CST)" (scheduled at 2025-08-05 18:13:14.812331+08:00)
2025-08-05 18:13:14,866 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:14,867 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:14,994 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:15,338 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:15,338 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:13:15,338 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:15,339 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:15,382 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:24 CST)" executed successfully
2025-08-05 18:13:24,816 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:34 CST)" (scheduled at 2025-08-05 18:13:24.812331+08:00)
2025-08-05 18:13:24,860 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:24,860 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:24,985 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:25,328 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:25,328 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:13:25,330 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:25,331 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:25,374 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:34 CST)" executed successfully
2025-08-05 18:13:30,487 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:13:30,721 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:13:33,582 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:13:33,583 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:13:34,554 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:14:34 CST)" (scheduled at 2025-08-05 18:13:34.551094+08:00)
2025-08-05 18:13:34,601 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:13:34,601 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:34,602 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:13:34,692 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:14:04 CST)" (scheduled at 2025-08-05 18:13:34.678985+08:00)
2025-08-05 18:13:34,736 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:13:34,738 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:34,802 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:13:34,803 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:13:34,817 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:44 CST)" (scheduled at 2025-08-05 18:13:34.812331+08:00)
2025-08-05 18:13:34,859 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:34,860 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:34,872 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:13:34,988 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:35,211 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:13:35,211 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:13:35,211 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:13:35,212 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:13:35,212 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:35,254 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:14:04 CST)" executed successfully
2025-08-05 18:13:35,325 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:35,326 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:13:35,326 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:35,327 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:35,371 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:44 CST)" executed successfully
2025-08-05 18:13:36,803 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:13:36,806 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.20秒
2025-08-05 18:13:36,815 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:13:36,819 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:36,863 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:14:34 CST)" executed successfully
2025-08-05 18:13:44,821 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:54 CST)" (scheduled at 2025-08-05 18:13:44.812331+08:00)
2025-08-05 18:13:44,863 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:44,863 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:44,993 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:45,332 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:45,333 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:13:45,333 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:45,334 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:45,377 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:13:54 CST)" executed successfully
2025-08-05 18:13:54,825 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:04 CST)" (scheduled at 2025-08-05 18:13:54.812331+08:00)
2025-08-05 18:13:54,870 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:13:54,870 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:13:54,998 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:13:55,337 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:13:55,340 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:13:55,341 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:13:55,346 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:13:55,393 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:04 CST)" executed successfully
2025-08-05 18:14:00,492 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:14:00,955 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:14:04,690 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:14:34 CST)" (scheduled at 2025-08-05 18:14:04.678985+08:00)
2025-08-05 18:14:04,733 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:14:04,734 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:04,819 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:14 CST)" (scheduled at 2025-08-05 18:14:04.812331+08:00)
2025-08-05 18:14:04,862 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:14:04,865 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:04,867 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:04,997 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:05,202 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:14:05,202 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:14:05,203 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:14:05,203 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:14:05,204 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:05,246 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:14:34 CST)" executed successfully
2025-08-05 18:14:05,336 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:05,337 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:14:05,337 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:05,339 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:05,383 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:14 CST)" executed successfully
2025-08-05 18:14:14,814 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:24 CST)" (scheduled at 2025-08-05 18:14:14.812331+08:00)
2025-08-05 18:14:14,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:14,857 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:14,984 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:15,324 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:15,326 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:14:15,330 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:15,331 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:15,376 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:24 CST)" executed successfully
2025-08-05 18:14:24,823 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:34 CST)" (scheduled at 2025-08-05 18:14:24.812331+08:00)
2025-08-05 18:14:24,866 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:24,866 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:24,994 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:25,336 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:25,336 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:14:25,337 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:25,337 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:25,383 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:34 CST)" executed successfully
2025-08-05 18:14:30,172 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:14:30,498 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:14:33,587 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:14:33,588 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:14:34,564 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:15:34 CST)" (scheduled at 2025-08-05 18:14:34.551094+08:00)
2025-08-05 18:14:34,609 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:14:34,610 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:34,612 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:14:34,687 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:15:04 CST)" (scheduled at 2025-08-05 18:14:34.678985+08:00)
2025-08-05 18:14:34,730 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:14:34,731 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:34,814 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:44 CST)" (scheduled at 2025-08-05 18:14:34.812331+08:00)
2025-08-05 18:14:34,857 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:14:34,857 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:14:34,860 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:14:34,861 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:34,862 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:34,988 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:35,201 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:14:35,202 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:14:35,202 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:14:35,202 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:14:35,203 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:35,245 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:15:04 CST)" executed successfully
2025-08-05 18:14:35,323 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:35,324 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:14:35,324 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:35,325 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:35,367 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:44 CST)" executed successfully
2025-08-05 18:14:35,813 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:14:35,814 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.20秒
2025-08-05 18:14:35,814 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:14:35,815 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:35,856 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:15:34 CST)" executed successfully
2025-08-05 18:14:44,822 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:54 CST)" (scheduled at 2025-08-05 18:14:44.812331+08:00)
2025-08-05 18:14:44,868 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:44,871 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:45,000 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:45,334 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:45,334 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 18:14:45,334 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:45,335 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:45,377 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:14:54 CST)" executed successfully
2025-08-05 18:14:54,818 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:04 CST)" (scheduled at 2025-08-05 18:14:54.812331+08:00)
2025-08-05 18:14:54,862 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:14:54,862 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:14:54,985 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:14:55,320 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:14:55,321 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:14:55,321 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:14:55,321 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:14:55,366 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:04 CST)" executed successfully
2025-08-05 18:15:00,421 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:15:00,514 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:15:04,691 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:15:34 CST)" (scheduled at 2025-08-05 18:15:04.678985+08:00)
2025-08-05 18:15:04,734 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:15:04,734 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:04,817 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:14 CST)" (scheduled at 2025-08-05 18:15:04.812331+08:00)
2025-08-05 18:15:04,863 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:15:04,864 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:04,866 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:05,010 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:05,222 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:15:05,222 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:15:05,223 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:15:05,223 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:15:05,224 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:05,265 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:15:34 CST)" executed successfully
2025-08-05 18:15:05,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:15:05,353 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:15:05,354 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:15:05,354 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:05,399 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:14 CST)" executed successfully
2025-08-05 18:15:14,819 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:24 CST)" (scheduled at 2025-08-05 18:15:14.812331+08:00)
2025-08-05 18:15:14,866 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:14,866 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:14,990 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:15,335 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:15:15,336 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:15:15,336 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:15:15,337 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:15,379 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:24 CST)" executed successfully
2025-08-05 18:15:24,826 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:34 CST)" (scheduled at 2025-08-05 18:15:24.812331+08:00)
2025-08-05 18:15:24,868 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:24,868 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:24,995 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:25,339 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:15:25,340 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:15:25,340 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:15:25,340 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:25,386 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:34 CST)" executed successfully
2025-08-05 18:15:30,519 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:15:30,627 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:15:33,590 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=3, 总计=5
2025-08-05 18:15:33,591 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:15:34,552 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:16:34 CST)" (scheduled at 2025-08-05 18:15:34.551094+08:00)
2025-08-05 18:15:34,596 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:push_online
2025-08-05 18:15:34,596 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:34,597 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行推送在线人数任务
2025-08-05 18:15:34,690 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:16:04 CST)" (scheduled at 2025-08-05 18:15:34.678985+08:00)
2025-08-05 18:15:34,734 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:15:34,735 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:34,799 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:15:34,800 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:15:34,813 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:44 CST)" (scheduled at 2025-08-05 18:15:34.812331+08:00)
2025-08-05 18:15:34,857 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:34,858 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:34,861 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 开始执行怪物冷却持久化任务
2025-08-05 18:15:34,984 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:35,202 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:15:35,203 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 怪物冷却持久化完成
2025-08-05 18:15:35,203 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:15:35,203 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:15:35,204 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:35,246 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:16:04 CST)" executed successfully
2025-08-05 18:15:35,329 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:15:35,329 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:15:35,329 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:15:35,330 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:35,373 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:44 CST)" executed successfully
2025-08-05 18:15:36,455 - scheduler_tasks_unified - INFO - [定时任务] Worker 36580 在线人数推送完成，当前在线: 0
2025-08-05 18:15:36,455 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.86秒
2025-08-05 18:15:36,456 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:15:36,456 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:36,502 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:16:34 CST)" executed successfully
2025-08-05 18:15:44,816 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:54 CST)" (scheduled at 2025-08-05 18:15:44.812331+08:00)
2025-08-05 18:15:44,864 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:44,864 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:44,990 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:45,326 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:15:45,327 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:15:45,328 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:15:45,329 - distributed_task - INFO - Worker 36580 任务执行完成: direct_wrapper
2025-08-05 18:15:45,378 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:15:54 CST)" executed successfully
2025-08-05 18:15:54,824 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:04 CST)" (scheduled at 2025-08-05 18:15:54.812331+08:00)
2025-08-05 18:15:54,872 - game_server - INFO - 关闭服务器... (Worker: 36580)
2025-08-05 18:15:54,873 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 18:15:54,874 - distributed_lock - INFO - Worker 36580 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:15:54,874 - distributed_task - INFO - Worker 36580 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:15:55,001 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:15:55,208 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:15:55,209 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 18:15:55,209 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 18:15:55,210 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 18:15:55,211 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 18:15:55,211 - game_server - INFO - 日志清理管理器已停止
2025-08-05 18:15:55,212 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 18:15:55,214 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 18:15:55,214 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 18:15:55,215 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 18:15:55,215 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 18:15:55,215 - game_server - INFO - 统一调度器已关闭
2025-08-05 18:15:55,216 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 18:15:55,218 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 36580)
2025-08-05 18:15:55,263 - game_server - INFO - 服务器资源已清理 (Worker: 36580)
