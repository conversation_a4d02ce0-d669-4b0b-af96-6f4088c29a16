2025-08-05 18:09:25,342 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,196 - models - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,221 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,685 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,694 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,703 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,715 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:26,729 - auth - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,309 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,355 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,384 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,396 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,408 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,408 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 6be4608c)
2025-08-05 18:09:29,415 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,450 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,460 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,469 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,478 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,554 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,562 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,569 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,575 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,582 - game_server - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:29,584 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:09:29,584 - msgManager - INFO - Monster handlers registered
2025-08-05 18:09:29,584 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:09:29,586 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:09:29,594 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:09:29,595 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:09:29,595 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52320001)
2025-08-05 18:09:29,633 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:09:29,678 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:09:29,678 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:09:29,680 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:09:29,681 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 36580)
2025-08-05 18:09:29,681 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:09:29,857 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:09:29,858 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:09:30,392 - ConnectionManager - INFO - 后台任务已启动 (Worker 36580)
2025-08-05 18:09:30,393 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 36580)
2025-08-05 18:09:30,393 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:09:30,394 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:09:30,394 - game_server - INFO - 游戏配置加载完成 (Worker: 36580)
2025-08-05 18:09:30,395 - ConnectionManager - INFO - 连接状态 (Worker 36580): 活跃连接数=0, 用户数=0
2025-08-05 18:09:33,541 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:09:33,541 - ConnectionManager - INFO - Redis连接池状态 (Worker 36580): 使用中=2, 可用=0, 总计=2
2025-08-05 18:09:33,542 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 36580): 2/2 (100.0%)
2025-08-05 18:09:33,543 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 36580): 连接中
2025-08-05 18:09:33,586 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 52320001)
2025-08-05 18:09:33,586 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 52320001)
2025-08-05 18:09:33,594 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:33,595 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:09:33,597 - ConnectionManager - INFO - Worker 36580 开始消费广播消息，消费者标签: ctag1.520b9f08d582498e9e02287cc59a10f7
2025-08-05 18:09:33,641 - ConnectionManager - INFO - Worker 36580 开始消费个人消息，消费者标签: ctag1.54cc29bb62c549f3b3317d1190460a0c
2025-08-05 18:09:33,712 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 36580)
2025-08-05 18:09:33,808 - distributed_lock - INFO - Worker 36580 成功获取锁: scheduler_initialization
2025-08-05 18:09:33,808 - game_server_scheduler_integration - INFO - Worker 36580 获得调度器初始化权限
2025-08-05 18:09:33,812 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:09:33,813 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:09:33,814 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:09:33,814 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:09:33,815 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:09:33,815 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:09:33,816 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:09:33,817 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:09:33,817 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:09:33,818 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:09:33,818 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:09:33,818 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:09:33,819 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:09:33,819 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:09:33,906 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:09:33,907 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:09:33,908 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:09:33,909 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:09:34,230 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:09:34,319 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:09:34,320 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:09:34,320 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:09:34,321 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:09:34,321 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:09:34,321 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:09:34,409 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:09:34,539 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:09:34,539 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:09:34,540 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:09:34,541 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:09:34,541 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:09:34,542 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:09:34,547 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,548 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:09:34,548 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:09:34,551 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,551 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:09:34,551 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:09:34,679 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,679 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:09:34,679 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:09:34,812 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,812 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:09:34,813 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:09:34,816 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,817 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:09:34,817 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:09:34,818 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:09:34,818 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:09:34,819 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:09:34,820 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:09:34,821 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,822 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,823 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,824 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,824 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,825 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:09:34,826 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:09:34,826 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:09:34,826 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:09:34,827 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:09:34,827 - game_server_scheduler_integration - INFO - Worker 36580 调度器初始化成功
2025-08-05 18:09:34,872 - game_server - INFO - 统一调度器初始化成功 (Worker: 36580)
2025-08-05 18:09:34,878 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 36580)
2025-08-05 18:09:34,879 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:09:34,879 - game_server - INFO - 日志清理管理器已启动 (Worker: 36580)
2025-08-05 18:09:34,880 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:09:34,880 - game_server - INFO - Monster cooldown manager initialized (Worker: 36580)
2025-08-05 18:09:35,027 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:09:35,027 - game_server - INFO - 公会系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,028 - game_server - INFO - 邮件系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,032 - game_server - INFO - 商店系统初始化成功 (Worker: 36580)
2025-08-05 18:09:35,032 - game_server - INFO - 初始化完成 (Worker: 36580)
