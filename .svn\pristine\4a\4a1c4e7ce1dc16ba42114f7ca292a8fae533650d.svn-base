"""
商店系统启动和测试脚本
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ShopSystemStarter:
    """商店系统启动器"""
    
    def __init__(self):
        self.shop_service = None
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，准备关闭...")
        self.running = False
    
    async def initialize_shop_system(self):
        """初始化商店系统"""
        try:
            logger.info("开始初始化商店系统...")
            
            # 1. 初始化服务
            from shop_service import ShopService
            from currency_service import CurrencyService
            from player_service import PlayerService

            # 创建服务实例
            self.shop_service = ShopService()
            currency_service = CurrencyService()
            player_service = PlayerService()

            # 设置外部服务依赖（不传递item_service，使用内置的ItemCacheManager）
            self.shop_service.set_external_services(currency_service, None, player_service)
            
            logger.info("商店服务初始化完成")
            
            # 2. 测试Redis连接
            await self._test_redis_connection()
            
            # 3. 测试数据库连接
            await self._test_database_connection()
            
            # 4. 初始化示例数据（如果需要）
            await self._initialize_sample_data()
            
            logger.info("商店系统初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"初始化商店系统失败: {str(e)}")
            return False
    
    async def _test_redis_connection(self):
        """测试Redis连接"""
        try:
            from shop_redis_manager import get_shop_redis
            
            redis = await get_shop_redis()
            await redis.ping()
            logger.info("✅ Redis连接测试成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接测试失败: {str(e)}")
            raise
    
    async def _test_database_connection(self):
        """测试数据库连接"""
        try:
            from shop_database_manager import ShopDatabaseManager
            
            db_manager = ShopDatabaseManager()
            # 简单的数据库连接测试
            db = await db_manager._get_db()
            
            # 测试查询
            collections = await db.list_collection_names()
            logger.info(f"✅ 数据库连接测试成功，发现 {len(collections)} 个集合")
            
        except Exception as e:
            logger.error(f"❌ 数据库连接测试失败: {str(e)}")
            raise
    
    async def _initialize_sample_data(self):
        """初始化示例数据"""
        try:
            # 检查是否已有商店数据
            shops = await self.shop_service.get_available_shops("system")
            
            if not shops:
                logger.info("未发现商店数据，开始创建示例数据...")
                
                from shop_init import ShopSystemInitializer
                initializer = ShopSystemInitializer()
                await initializer.initialize_system()
                
                logger.info("✅ 示例数据创建完成")
            else:
                logger.info(f"✅ 发现 {len(shops)} 个现有商店，跳过示例数据创建")
                
        except Exception as e:
            logger.error(f"❌ 初始化示例数据失败: {str(e)}")
            # 不抛出异常，因为这不是致命错误
    
    async def run_basic_tests(self):
        """运行基础功能测试"""
        try:
            logger.info("开始运行基础功能测试...")
            
            test_player = "test_player_startup"
            
            # 1. 测试获取商店列表
            shops = await self.shop_service.get_available_shops(test_player)
            logger.info(f"✅ 获取商店列表成功: {len(shops)} 个商店")
            
            if shops:
                # 2. 测试获取商店商品
                first_shop = shops[0]
                items = await self.shop_service.get_shop_items(first_shop.shop_id, test_player)
                logger.info(f"✅ 获取商店商品成功: {len(items)} 个商品")
                
                if items:
                    # 3. 测试获取商品详情
                    first_item = items[0]
                    detail = await self.shop_service.get_item_detail(first_item.config_id, test_player)
                    if detail:
                        logger.info(f"✅ 获取商品详情成功: {detail['config']['item_template_id']}")
                    
                    # 4. 测试预览购买
                    preview = await self.shop_service.preview_purchase(test_player, first_item.config_id, 1)
                    logger.info(f"✅ 预览购买成功: 可购买={preview['can_purchase']}")
            
            # 5. 测试限购状态
            limits = await self.shop_service.get_player_limits(test_player)
            logger.info(f"✅ 获取限购状态成功")
            
            logger.info("🎉 基础功能测试全部通过！")
            
        except Exception as e:
            logger.error(f"❌ 基础功能测试失败: {str(e)}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("开始清理资源...")
            
            # 关闭Redis连接
            from shop_redis_manager import close_shop_redis
            await close_shop_redis()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {str(e)}")
    
    async def run(self):
        """运行商店系统"""
        try:
            # 初始化系统
            if not await self.initialize_shop_system():
                logger.error("系统初始化失败，退出")
                return False
            
            # 运行基础测试
            await self.run_basic_tests()
            
            # 显示系统状态
            logger.info("🚀 商店系统启动成功！")
            logger.info("系统正在运行，按 Ctrl+C 退出...")
            
            # 保持运行状态
            while self.running:
                await asyncio.sleep(1)
            
            return True
            
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
            return True
        except Exception as e:
            logger.error(f"运行时发生错误: {str(e)}")
            return False
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("🏪 商店系统启动器")
    print("=" * 50)
    
    starter = ShopSystemStarter()
    success = await starter.run()
    
    if success:
        print("\n✅ 商店系统运行完成")
    else:
        print("\n❌ 商店系统运行失败")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n💥 启动失败: {str(e)}")
        sys.exit(1)
