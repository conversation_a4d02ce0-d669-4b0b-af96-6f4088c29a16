from pydantic import BaseModel, ConfigDict
from abc import ABC
from datetime import datetime
from typing import Optional, Dict, Any, Tuple
import logging
# 初始化日志系统
logger = logging.getLogger(__name__)

class BaseModelORM(BaseModel, ABC):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def serialize(self, exclude_fields=None) -> dict:
        """
        序列化模型数据，支持排除指定字段

        Args:
            exclude_fields: 要排除的字段列表，如 ['password', 'token']

        Returns:
            dict: 序列化后的字典
        """
        try:
            # 构建排除字段集合
            exclude_set = set()
            if exclude_fields:
                if isinstance(exclude_fields, (list, tuple, set)):
                    exclude_set = set(exclude_fields)
                else:
                    exclude_set = {exclude_fields}

            # 使用Pydantic v2的API - 修改为model_dump
            try:
                data = self.model_dump(exclude_unset=False)
            except AttributeError:
                # 兼容Pydantic v1
                data = self.dict(exclude_unset=False)

            # 手动排除字段和None值
            for field, value in list(data.items()):
                if field in exclude_set or value is None:
                    del data[field]

            # 处理特殊类型
            data = self._process_special_types(data)

            return data
        except Exception as e:
            logger.error(f"序列化失败: {str(e)}, 排除字段: {exclude_fields}")
            return {}

    def _process_special_types(self, data: dict) -> dict:
        """处理特殊类型的序列化"""
        try:
            from bson import ObjectId
        except ImportError:
            ObjectId = None

        for key, value in list(data.items()):
            if ObjectId and isinstance(value, ObjectId):
                data[key] = str(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, bytes):
                data[key] = value.decode('utf-8')

        # 移除MongoDB的_id字段
        data.pop("_id", None)

        return data

    @classmethod
    def deserialize(cls, data: dict) -> 'BaseModelORM':
        try:
            clean_data = {k: v for k, v in data.items() if k != "_id"}
            if "created_at" in clean_data and isinstance(clean_data["created_at"], str):
                clean_data["created_at"] = datetime.fromisoformat(clean_data["created_at"])
            if "password" in clean_data and isinstance(clean_data["password"], str):
                clean_data["password"] = clean_data["password"].encode('utf-8')
            return cls(**clean_data)
        except Exception as e:
            logger.error(f"反序列化失败: {str(e)}，数据: {data}")
            raise
