from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.websockets import WebSocketState
from typing import Dict, List, Callable
import logging
import json
import asyncio
import traceback
import os
from logger_config import setup_logger
from auth import UserRegister, UserLogin, register_user, login_user, logout_user, get_current_user, router as auth_router
from models import ResponseModel, MessageModel
from game_database import DatabaseManager
from msgManager import MessageManager
from ConnectionManager import ConnectionManager
from config import config
from utils import handle_error
from enums import MessageId
import websocket_handlers  # 导入 WebSocket 处理器
from datetime import datetime
from service_locator import ServiceLocator
from task_queue import TaskQueue
from distributed_task import distributed_task
from scheduler_manager import SchedulerManager

# 初始化日志系统
logger = setup_logger(__name__)

# 创建应用
app = FastAPI(title="Game Server API", version="2.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 实例化数据库管理器
db_manager = DatabaseManager()

# 实例化连接管理器
connection_manager = ConnectionManager(db_manager)

# 实例化消息管理器
msg_manager = MessageManager()

# 实例化任务队列
task_queue = TaskQueue()

# 注册服务
ServiceLocator.register("db_manager", db_manager)
ServiceLocator.register("conn_manager", connection_manager)
ServiceLocator.register("task_queue", task_queue)

# 路由器
app.include_router(auth_router)


@app.get("/userinfo")
async def get_user_info(request: Request):
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="缺少或无效的 Authorization 头")
        token = auth_header.split(" ")[1]
        username = await get_current_user(token)
        user = await db_manager.get_user_by_username(username)
        if not user:
            raise HTTPException(status_code=404, detail="用户未找到")        
        user_data = user.serialize(exclude_fields=["password"])
        user_data.pop("_id", None)        
        logger.info(f"获取用户信息，用户 {username}，返回字段: {list(user_data.keys())}")
        return JSONResponse(
            status_code=200,
            content=ResponseModel(
                success=True,
                code=200,
                data=user_data
            ).model_dump(),
            headers={"Content-Type": "application/json"}
        )
    except HTTPException:
        raise
    except Exception as e:
        return await handle_error("获取用户信息失败", 500, exception=e)

# WebSocket 事件处理
@app.websocket("/ws/{token}")
async def websocket_endpoint(websocket: WebSocket, token: str):
    logger.info(f"WebSocket 连接尝试，token: {token[:10]}..., worker: {os.getpid()}")
    username = None
    try:
        try:
            username = await get_current_user(token)
        except HTTPException as e:
            logger.error(f"Token 验证失败，token: {token[:10]}...: {e.detail}")
            if websocket.application_state != WebSocketState.DISCONNECTED:
                try:
                    await websocket.close(code=1008, reason=f"认证失败: {e.detail}")
                except Exception as close_e:
                    logger.warning(f"关闭 WebSocket 失败，token: {token[:10]}..., 错误: {str(close_e)}")
            return
        
        # 连接到WebSocket
        await connection_manager.connect(websocket, token, username)

        try:
            while True:
                if websocket.application_state == WebSocketState.CONNECTED:
                    data = await websocket.receive_text()
                    logger.debug(f"收到消息，用户: {username}, token: {token[:10]}, 数据: {data}")
                    try:
                        message = json.loads(data)
                        msgId = message.get("msgId")
                        message_data = message.get("data", {})
                        if msgId is not None and isinstance(msgId, int):
                            start_time = asyncio.get_event_loop().time()      
                            await msg_manager.dispatch_message(msgId, message_data, websocket, username, token, connection_manager)
                            end_time = asyncio.get_event_loop().time()
                            logger.debug(f"处理 msgId {msgId} 耗时: {(end_time - start_time) * 1000:.2f} 毫秒，用户: {username}, worker: {os.getpid()}")
                        else:
                            await connection_manager.send_personal_message(MessageModel(
                                msgId=MessageId.ERROR,
                                data={"error": "无效的消息 ID"}
                            ).model_dump(), token)
                    except json.JSONDecodeError as e:
                        logger.error(f"消息格式无效: {str(e)}")
                        await connection_manager.send_personal_message(MessageModel(
                            msgId=MessageId.ERROR,
                            data={"error": "消息格式无效"}
                        ).model_dump(), token)
                else:
                    logger.warning(f"WebSocket 状态异常，用户: {username}, token: {token[:10]}...，状态: {websocket.application_state}")
                    await connection_manager.disconnect(token)
                    break
        except WebSocketDisconnect as e:
            logger.info(f"WebSocket 断开，用户: {username}, token: {token[:10]}..., 代码: {e.code}, 原因: {e.reason}, worker: {os.getpid()}")
            await connection_manager.disconnect(token)
        except RuntimeError as e:
            logger.error(f"WebSocket 运行时错误，用户: {username}, token: {token[:10]}...: {str(e)}")
            await connection_manager.disconnect(token)
    except Exception as e:
        logger.error(f"意外的 WebSocket 错误，用户: {username or '未知'}, token: {token[:10]}...: {str(e)}, 类型: {type(e).__name__}")
        logger.error(traceback.format_exc())
        if username and token in connection_manager.active_connections:
            await connection_manager.disconnect(token)
        if websocket.application_state != WebSocketState.DISCONNECTED:
            try:
                await websocket.close(code=1008, reason=f"内部服务器错误: {str(e)}")
            except Exception as close_e:
                logger.warning(f"关闭 WebSocket 失败，token: {token[:10]}..., 错误: {str(close_e)}")
    finally:
        if username and token in connection_manager.active_connections:
            try:
                await connection_manager.disconnect(token)
                logger.info(f"确保用户 {username} 断开连接，token: {token[:10]}..., worker: {os.getpid()}")
            except Exception as e:
                logger.error(f"最终断开连接失败，用户: {username}, token: {token[:10]}..., 错误: {str(e)}")
# 启动事件
@app.on_event("startup")
async def startup_event():
    try:
        logger.info(f"启动服务器，初始化数据库和连接管理器... (Worker: {os.getpid()})")
        await db_manager.startup()
        await connection_manager.initialize()
        
        # 加载游戏配置
        config.get_item_config()
        logger.info(f"游戏配置加载完成 (Worker: {os.getpid()})")
        
        # 启动配置监控
        asyncio.create_task(config.monitor_game_configs())

        
        handlers = {
            "reset_daily": do_reset_daily,
            "push_online_count": do_push_online_count
        }
        asyncio.create_task(task_queue.consume_tasks(handlers))

        logger.info(f"初始化完成 (Worker: {os.getpid()})")
    except Exception as e:
        logger.error(f"启动时初始化失败: {str(e)}")
        logger.error(traceback.format_exc())
        logger.warning("初始化失败，服务器将继续运行，但可能影响功能")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info(f"关闭服务器... (Worker: {os.getpid()})")
    try:
        await connection_manager.cleanup()
        await db_manager.shutdown()
        await task_queue.close()
        logger.info(f"服务器资源已清理 (Worker: {os.getpid()})")
    except Exception as e:
        logger.error(f"关闭时清理资源失败: {str(e)}")
        logger.error(traceback.format_exc())
#---------------------------------task---------------------------------
async def do_reset_daily(params):
    print(f"[worker] 执行每日重置")
    await connection_manager.broadcast(MessageModel(
        msgId=MessageId.CHAT,
        data={"content": "每日重置"}
    ).model_dump(), 0)
    print("[worker] 每日重置完成")

async def do_push_online_count(params):
    print("[worker] 执行推送在线人数")
    await connection_manager.broadcast(MessageModel(
        msgId=MessageId.CHAT,
        data={"content": "每日重置"}
    ).model_dump(), 0)
    print("[worker] 在线人数推送完成")
        #---------------------------------task---------------------------------
