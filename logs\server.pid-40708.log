2025-08-05 21:04:27,847 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,557 - models - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,589 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,946 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,960 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,973 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:28,988 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:29,001 - auth - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:33,676 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:33,860 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:33,925 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:33,970 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,004 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,005 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 8f4da9eb)
2025-08-05 21:04:34,028 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,104 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,131 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,148 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,179 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,337 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,371 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,393 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,407 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,425 - game_server - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:34,426 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 21:04:34,429 - msgManager - INFO - Monster handlers registered
2025-08-05 21:04:34,430 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 21:04:34,438 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 21:04:34,470 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 21:04:34,472 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 21:04:34,476 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: ed0cd8c6)
2025-08-05 21:04:34,551 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 21:04:34,623 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 21:04:34,624 - game_server - INFO - 模板引擎初始化成功
2025-08-05 21:04:34,629 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:04:34,631 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 40708)
2025-08-05 21:04:34,635 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 21:04:34,969 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:04:35,067 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 21:04:36,466 - ConnectionManager - INFO - 后台任务已启动 (Worker 40708)
2025-08-05 21:04:36,467 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 40708)
2025-08-05 21:04:36,478 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:04:36,479 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:04:36,479 - game_server - INFO - 游戏配置加载完成 (Worker: 40708)
2025-08-05 21:04:36,480 - ConnectionManager - INFO - 连接状态 (Worker 40708): 活跃连接数=0, 用户数=0
2025-08-05 21:04:41,316 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 21:04:41,317 - ConnectionManager - INFO - Redis连接池状态 (Worker 40708): 使用中=2, 可用=0, 总计=2
2025-08-05 21:04:41,317 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 40708): 2/2 (100.0%)
2025-08-05 21:04:41,318 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40708): 连接中
2025-08-05 21:04:41,468 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: ed0cd8c6)
2025-08-05 21:04:41,468 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: ed0cd8c6)
2025-08-05 21:04:41,475 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:41,477 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 21:04:41,478 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 21:04:41,486 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:04:41,487 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 21:04:41,488 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:04:41,488 - ConnectionManager - INFO - Worker 40708 开始消费广播消息，消费者标签: ctag1.51a85a5d72ab47fb90c1d60b97a97ec0
2025-08-05 21:04:41,670 - ConnectionManager - INFO - Worker 40708 开始消费个人消息，消费者标签: ctag1.736ac8fe9aba4eabbcc26c2858cea1d6
2025-08-05 21:04:41,867 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 40708)
2025-08-05 21:04:41,965 - distributed_lock - INFO - Worker 40708 成功获取锁: scheduler_initialization
2025-08-05 21:04:41,965 - game_server_scheduler_integration - INFO - Worker 40708 获得调度器初始化权限
2025-08-05 21:04:41,971 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 21:04:41,971 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 21:04:41,972 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 21:04:41,973 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 21:04:41,974 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 21:04:41,974 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 21:04:41,975 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 21:04:41,976 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 21:04:41,976 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 21:04:41,977 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 21:04:41,977 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 21:04:41,978 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 21:04:41,979 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 21:04:41,980 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 21:04:41,980 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 21:04:41,981 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 21:04:42,265 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 21:04:42,265 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 21:04:42,266 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 21:04:42,267 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 21:04:43,070 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 21:04:43,570 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 21:04:43,572 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 21:04:43,573 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 21:04:43,573 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 21:04:43,574 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 21:04:43,574 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:04:43,772 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 21:04:44,002 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:04:44,003 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 21:04:44,003 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 21:04:44,012 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 21:04:44,014 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 21:04:44,015 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 21:04:44,021 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,025 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:04:44,026 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:04:44,028 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,028 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:04:44,029 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:04:44,162 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,162 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:04:44,163 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:04:44,288 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,288 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:04:44,289 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:04:44,289 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,290 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:04:44,290 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:04:44,291 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:04:44,291 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:04:44,291 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:04:44,292 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 21:04:44,293 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,294 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,301 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,304 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,308 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,310 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:04:44,313 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 21:04:44,314 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 21:04:44,315 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 21:04:44,315 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 21:04:44,316 - game_server_scheduler_integration - INFO - Worker 40708 调度器初始化成功
2025-08-05 21:04:44,360 - game_server - INFO - 统一调度器初始化成功 (Worker: 40708)
2025-08-05 21:04:44,368 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 40708)
2025-08-05 21:04:44,369 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 21:04:44,369 - game_server - INFO - 日志清理管理器已启动 (Worker: 40708)
2025-08-05 21:04:44,369 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:04:44,370 - game_server - INFO - Monster cooldown manager initialized (Worker: 40708)
2025-08-05 21:04:44,513 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:04:44,513 - game_server - INFO - 公会系统初始化成功 (Worker: 40708)
2025-08-05 21:04:44,514 - game_server - INFO - 邮件系统初始化成功 (Worker: 40708)
2025-08-05 21:04:44,517 - game_server - INFO - 商店系统初始化成功 (Worker: 40708)
2025-08-05 21:04:44,517 - game_server - INFO - 初始化完成 (Worker: 40708)
2025-08-05 21:04:54,291 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:04 CST)" (scheduled at 2025-08-05 21:04:54.288458+08:00)
2025-08-05 21:04:54,335 - distributed_lock - INFO - Worker 40708 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:04:54,335 - distributed_task - INFO - Worker 40708 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:04:54,465 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:04:54,818 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:04:54,818 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:04:54,819 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:04:54,819 - distributed_task - INFO - Worker 40708 任务执行完成: direct_wrapper
2025-08-05 21:04:54,871 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:04 CST)" executed successfully
2025-08-05 21:05:00,498 - ConnectionManager - INFO - 连接状态 (Worker 40708): 活跃连接数=0, 用户数=0
2025-08-05 21:05:04,303 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:14 CST)" (scheduled at 2025-08-05 21:05:04.288458+08:00)
2025-08-05 21:05:04,348 - distributed_lock - INFO - Worker 40708 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:05:04,348 - distributed_task - INFO - Worker 40708 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:05:04,479 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:05:04,840 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:05:04,840 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:05:04,840 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:05:04,841 - distributed_task - INFO - Worker 40708 任务执行完成: direct_wrapper
2025-08-05 21:05:04,885 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:14 CST)" executed successfully
2025-08-05 21:05:06,492 - ConnectionManager - INFO - 连接状态 (Worker 40708): 活跃连接数=0, 用户数=0
2025-08-05 21:05:14,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:05:44 CST)" (scheduled at 2025-08-05 21:05:14.162526+08:00)
2025-08-05 21:05:17,787 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:24 CST)" was missed by 0:00:03.498635
2025-08-05 21:05:17,829 - distributed_lock - INFO - Worker 40708 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:05:17,829 - distributed_task - INFO - Worker 40708 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:05:17,962 - scheduler_tasks_unified - INFO - [定时任务] Worker 40708 开始执行怪物冷却持久化任务
2025-08-05 21:05:18,326 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:05:18,327 - scheduler_tasks_unified - INFO - [定时任务] Worker 40708 怪物冷却持久化完成
2025-08-05 21:05:18,327 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 21:05:18,328 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:05:18,328 - distributed_task - INFO - Worker 40708 任务执行完成: direct_wrapper
2025-08-05 21:05:18,375 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:05:44 CST)" executed successfully
2025-08-05 21:05:24,301 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:34 CST)" (scheduled at 2025-08-05 21:05:24.288458+08:00)
2025-08-05 21:05:24,346 - distributed_lock - INFO - Worker 40708 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:05:24,346 - distributed_task - INFO - Worker 40708 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:05:24,480 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:05:24,847 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:05:24,847 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:05:24,848 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:05:24,848 - distributed_task - INFO - Worker 40708 任务执行完成: direct_wrapper
2025-08-05 21:05:24,895 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:05:34 CST)" executed successfully
2025-08-05 21:05:30,934 - ConnectionManager - INFO - 连接状态 (Worker 40708): 活跃连接数=0, 用户数=0
2025-08-05 21:06:20,741 - ConnectionManager - INFO - 连接状态 (Worker 40708): 活跃连接数=0, 用户数=0
2025-08-05 21:06:20,742 - ConnectionManager - INFO - Redis连接池状态 (Worker 40708): 使用中=2, 可用=2, 总计=4
2025-08-05 21:06:20,743 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 40708): 连接中
2025-08-05 21:06:20,744 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:06:24 CST)" was missed by 0:00:06.455860
2025-08-05 21:06:20,744 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:06:44 CST)" was missed by 0:00:36.716435
2025-08-05 21:06:20,744 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:06:44 CST)" was missed by 0:00:06.582437
2025-08-05 21:06:22,350 - game_server - INFO - 关闭服务器... (Worker: 40708)
2025-08-05 21:06:22,352 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 21:06:22,705 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:06:22,706 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 21:06:22,706 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 21:06:22,706 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 21:06:22,707 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 21:06:22,707 - game_server - INFO - 日志清理管理器已停止
2025-08-05 21:06:22,708 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 21:06:22,708 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 21:06:22,709 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 21:06:22,709 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 21:06:22,709 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 21:06:22,710 - game_server - INFO - 统一调度器已关闭
2025-08-05 21:06:22,710 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 21:06:22,711 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 40708)
2025-08-05 21:06:22,753 - game_server - INFO - 服务器资源已清理 (Worker: 40708)
