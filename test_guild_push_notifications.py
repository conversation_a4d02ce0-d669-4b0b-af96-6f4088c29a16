# -*- coding: utf-8 -*-
"""
测试公会推送通知功能
验证公会创建、解散、成员变动时的推送是否正常工作
"""

import asyncio
import logging
from datetime import datetime

# 模拟测试数据
class MockGuild:
    def __init__(self, guild_id, name, member_count=1):
        self.guild_id = guild_id
        self.name = name
        self.level = 1
        self.member_count = member_count
        self.max_members = 50
        self.created_at = datetime.now()

class MockGuildMember:
    def __init__(self, guild_id, player_id, position="member"):
        self.guild_id = guild_id
        self.player_id = player_id
        self.position = position
        self.joined_at = datetime.now()

class MockNotificationService:
    """模拟通知服务"""
    
    def __init__(self):
        self.notifications = []
    
    async def notify_guild_join(self, player_id, guild, member):
        """模拟新成员加入通知"""
        notification = {
            "type": "guild_join",
            "player_id": player_id,
            "guild_name": guild.name,
            "guild_id": guild.guild_id,
            "timestamp": datetime.now().isoformat()
        }
        self.notifications.append(notification)
        print(f"✅ 推送公会加入通知: {player_id} 加入公会 {guild.name}")
        return True
    
    async def notify_guild_leave(self, player_id, guild_name):
        """模拟成员离开通知"""
        notification = {
            "type": "guild_leave",
            "player_id": player_id,
            "guild_name": guild_name,
            "timestamp": datetime.now().isoformat()
        }
        self.notifications.append(notification)
        print(f"✅ 推送公会离开通知: {player_id} 离开公会 {guild_name}")
        return True
    
    async def notify_guild_info_change(self, guild, member_ids):
        """模拟公会信息变更通知"""
        notification = {
            "type": "guild_info_change",
            "guild_id": guild.guild_id,
            "guild_name": guild.name,
            "member_count": guild.member_count,
            "member_ids": member_ids,
            "timestamp": datetime.now().isoformat()
        }
        self.notifications.append(notification)
        print(f"✅ 推送公会信息变更通知: 公会 {guild.name} 给 {len(member_ids)} 名成员")
        return {member_id: True for member_id in member_ids}

class GuildPushTestSuite:
    """公会推送测试套件"""
    
    def __init__(self):
        self.notification_service = MockNotificationService()
    
    async def test_guild_creation_push(self):
        """测试公会创建时的推送"""
        print("\n=== 测试公会创建推送 ===")
        
        # 模拟创建公会
        guild = MockGuild("guild_001", "测试公会")
        leader = MockGuildMember("guild_001", "player_001", "leader")
        
        # 推送给会长
        await self.notification_service.notify_guild_join("player_001", guild, leader)
        
        print("✓ 公会创建推送测试通过")
        return True
    
    async def test_guild_disband_push(self):
        """测试公会解散时的推送"""
        print("\n=== 测试公会解散推送 ===")
        
        # 模拟公会成员
        members = [
            MockGuildMember("guild_002", "player_001", "leader"),
            MockGuildMember("guild_002", "player_002", "member"),
            MockGuildMember("guild_002", "player_003", "member")
        ]
        
        guild_name = "即将解散的公会"
        
        # 推送给所有成员
        for member in members:
            await self.notification_service.notify_guild_leave(member.player_id, guild_name)
        
        print("✓ 公会解散推送测试通过")
        return True
    
    async def test_member_join_push(self):
        """测试成员加入时的推送"""
        print("\n=== 测试成员加入推送 ===")
        
        guild = MockGuild("guild_003", "活跃公会", member_count=3)
        new_member = MockGuildMember("guild_003", "player_004", "member")
        existing_member_ids = ["player_001", "player_002", "player_003"]
        
        # 推送给新成员
        await self.notification_service.notify_guild_join("player_004", guild, new_member)
        
        # 推送给其他成员
        guild.member_count = 4  # 更新成员数量
        await self.notification_service.notify_guild_info_change(guild, existing_member_ids)
        
        print("✓ 成员加入推送测试通过")
        return True
    
    async def test_member_leave_push(self):
        """测试成员离开时的推送"""
        print("\n=== 测试成员离开推送 ===")
        
        guild = MockGuild("guild_004", "减员公会", member_count=3)
        leaving_player = "player_002"
        remaining_member_ids = ["player_001", "player_003"]
        
        # 推送给离开的成员
        await self.notification_service.notify_guild_leave(leaving_player, guild.name)
        
        # 推送给剩余成员
        guild.member_count = 2  # 更新成员数量
        await self.notification_service.notify_guild_info_change(guild, remaining_member_ids)
        
        print("✓ 成员离开推送测试通过")
        return True
    
    async def test_member_removal_push(self):
        """测试成员被移除时的推送"""
        print("\n=== 测试成员移除推送 ===")
        
        guild = MockGuild("guild_005", "管理严格的公会", member_count=4)
        removed_player = "player_004"
        remaining_member_ids = ["player_001", "player_002", "player_003"]
        
        # 推送给被移除的成员
        await self.notification_service.notify_guild_leave(removed_player, guild.name)
        
        # 推送给剩余成员
        guild.member_count = 3  # 更新成员数量
        await self.notification_service.notify_guild_info_change(guild, remaining_member_ids)
        
        print("✓ 成员移除推送测试通过")
        return True
    
    async def test_guild_info_update_push(self):
        """测试公会信息更新时的推送"""
        print("\n=== 测试公会信息更新推送 ===")
        
        guild = MockGuild("guild_006", "更新信息的公会", member_count=5)
        all_member_ids = ["player_001", "player_002", "player_003", "player_004", "player_005"]
        
        # 模拟公会信息更新
        guild.name = "新名称的公会"
        guild.level = 2
        
        # 推送给所有成员
        await self.notification_service.notify_guild_info_change(guild, all_member_ids)
        
        print("✓ 公会信息更新推送测试通过")
        return True
    
    def print_notification_summary(self):
        """打印通知摘要"""
        print("\n=== 推送通知摘要 ===")
        
        notification_types = {}
        for notification in self.notification_service.notifications:
            ntype = notification["type"]
            notification_types[ntype] = notification_types.get(ntype, 0) + 1
        
        for ntype, count in notification_types.items():
            print(f"- {ntype}: {count} 次")
        
        print(f"总推送次数: {len(self.notification_service.notifications)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始公会推送通知测试...")
        
        tests = [
            self.test_guild_creation_push,
            self.test_guild_disband_push,
            self.test_member_join_push,
            self.test_member_leave_push,
            self.test_member_removal_push,
            self.test_guild_info_update_push
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if await test():
                    passed += 1
                else:
                    failed += 1
                    print(f"✗ {test.__name__} 测试失败")
            except Exception as e:
                failed += 1
                print(f"✗ {test.__name__} 测试异常: {str(e)}")
        
        self.print_notification_summary()
        
        print(f"\n=== 测试结果 ===")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"总计: {passed + failed}")
        
        if failed == 0:
            print("🎉 所有推送通知测试都通过了！")
            return True
        else:
            print("❌ 有测试失败，需要检查推送逻辑。")
            return False

async def main():
    """主函数"""
    test_suite = GuildPushTestSuite()
    success = await test_suite.run_all_tests()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
