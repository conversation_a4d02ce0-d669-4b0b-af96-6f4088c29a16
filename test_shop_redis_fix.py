"""
测试商店系统Redis连接修复
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_redis_connection():
    """测试Redis连接"""
    print("🔧 测试Redis连接修复...")
    
    try:
        # 测试新的Redis管理器
        from shop_redis_manager import get_shop_redis, close_shop_redis
        
        print("1. 获取Redis连接...")
        redis = await get_shop_redis()
        
        print("2. 测试Redis ping...")
        await redis.ping()
        print("✅ Redis连接正常")
        
        print("3. 测试基本操作...")
        await redis.set("test_key", "test_value", ex=10)
        value = await redis.get("test_key")
        print(f"✅ Redis操作正常: {value}")
        
        print("4. 清理测试数据...")
        await redis.delete("test_key")
        
        print("5. 关闭Redis连接...")
        await close_shop_redis()
        print("✅ Redis连接已正确关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {str(e)}")
        return False


async def test_shop_cache_service():
    """测试商店缓存服务"""
    print("\n🏪 测试商店缓存服务...")
    
    try:
        from shop_cache_service import ShopCacheService
        from shop_models import Shop
        from datetime import datetime
        
        cache_service = ShopCacheService()
        
        print("1. 创建测试商店数据...")
        test_shop = Shop(
            shop_id="test_shop_redis",
            shop_name="测试商店Redis",
            shop_type="normal",
            description="Redis连接测试商店",
            icon="test.png",
            is_active=True,
            access_conditions={},
            refresh_config=None,
            sort_order=1,
            ui_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        print("2. 测试缓存商店配置...")
        success = await cache_service.cache_shop_config(test_shop)
        if success:
            print("✅ 缓存商店配置成功")
        else:
            print("❌ 缓存商店配置失败")
            return False
        
        print("3. 测试获取缓存的商店配置...")
        cached_shop = await cache_service.get_cached_shop_config("test_shop_redis")
        if cached_shop and cached_shop.shop_name == "测试商店Redis":
            print("✅ 获取缓存商店配置成功")
        else:
            print("❌ 获取缓存商店配置失败")
            return False
        
        print("4. 测试清除缓存...")
        success = await cache_service.invalidate_shop_config("test_shop_redis")
        if success:
            print("✅ 清除缓存成功")
        else:
            print("❌ 清除缓存失败")
            return False
        
        print("5. 验证缓存已清除...")
        cached_shop = await cache_service.get_cached_shop_config("test_shop_redis")
        if cached_shop is None:
            print("✅ 缓存已正确清除")
        else:
            print("❌ 缓存清除验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 商店缓存服务测试失败: {str(e)}")
        return False


async def test_limit_service():
    """测试限购服务"""
    print("\n📊 测试限购服务...")
    
    try:
        from shop_limit_service import ShopLimitService
        
        limit_service = ShopLimitService()
        
        print("1. 测试获取玩家限购状态...")
        status = await limit_service.get_player_limit_status("test_player", "daily")
        print(f"✅ 获取限购状态成功: {len(status.get('counters', {}))} 个计数器")
        
        print("2. 测试限购统计...")
        stats = await limit_service.get_limit_statistics("daily")
        print(f"✅ 获取限购统计成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 限购服务测试失败: {str(e)}")
        return False


async def test_multiple_connections():
    """测试多个连接的情况"""
    print("\n🔄 测试多个Redis连接...")
    
    try:
        from shop_redis_manager import get_shop_redis
        
        # 创建多个连接
        connections = []
        for i in range(5):
            redis = await get_shop_redis()
            connections.append(redis)
            await redis.set(f"test_multi_{i}", f"value_{i}", ex=10)
            print(f"✅ 连接 {i+1} 创建成功")
        
        # 验证所有连接都能正常工作
        for i, redis in enumerate(connections):
            value = await redis.get(f"test_multi_{i}")
            if value == f"value_{i}".encode():
                print(f"✅ 连接 {i+1} 验证成功")
            else:
                print(f"❌ 连接 {i+1} 验证失败")
                return False
        
        # 清理测试数据
        for i in range(5):
            await connections[0].delete(f"test_multi_{i}")
        
        print("✅ 多连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 多连接测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🧪 商店系统Redis连接修复测试")
    print("=" * 50)
    
    tests = [
        ("Redis连接基础测试", test_redis_connection),
        ("商店缓存服务测试", test_shop_cache_service),
        ("限购服务测试", test_limit_service),
        ("多连接测试", test_multiple_connections),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            if await test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Redis连接问题已修复")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    # 最终清理
    try:
        from shop_redis_manager import close_shop_redis
        await close_shop_redis()
        print("🧹 资源清理完成")
    except Exception as e:
        print(f"⚠️  清理时发生错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
