"""
Monster Cooldown System Package

This package implements a comprehensive monster cooldown system for the game server,
supporting personal, guild, and global cooldown types.

Key components:
- monster_cooldown.py: Core cooldown data structure and operations
- monster_cooldown_service.py: Service for managing cooldowns and notifications
- monster_handlers.py: WebSocket handlers for cooldown-related operations
"""

__version__ = "1.0.0" 