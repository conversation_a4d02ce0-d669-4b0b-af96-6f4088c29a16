"""
公会系统工具函数
提供公会系统的各种工具和辅助函数
"""

import uuid
import re
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from guild_models import Guild, GuildMember, GuildPosition
from guild_config import guild_config

logger = logging.getLogger(__name__)


class GuildUtils:
    """公会工具类"""
    
    @staticmethod
    def generate_guild_id() -> str:
        """生成唯一的公会ID"""
        return f"guild_{uuid.uuid4().hex[:12]}"
    
    @staticmethod
    def generate_application_id() -> str:
        """生成唯一的申请ID"""
        return f"app_{uuid.uuid4().hex[:12]}"
    
    @staticmethod
    def validate_guild_name(name: str) -> Tu<PERSON>[bool, str]:
        """验证公会名称"""
        return guild_config.validate_guild_name(name)
    
    @staticmethod
    def validate_guild_description(description: str) -> <PERSON><PERSON>[bool, str]:
        """验证公会简介"""
        return guild_config.validate_guild_description(description)
    
    @staticmethod
    def format_guild_info(guild: Guild, include_sensitive: bool = False) -> Dict[str, Any]:
        """格式化公会信息用于显示"""
        guild_data = guild.to_dict()
        
        # 添加格式化的时间
        guild_data["created_at_formatted"] = guild.created_at.strftime("%Y-%m-%d %H:%M:%S")
        guild_data["updated_at_formatted"] = guild.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # 添加等级信息
        guild_data["level_info"] = {
            "current_level": guild.level,
            "current_exp": guild.exp,
            "next_level_exp": guild_config.get_exp_requirement_for_level(guild.level + 1),
            "max_members": guild_config.get_max_members_for_level(guild.level)
        }
        
        # 如果不包含敏感信息，移除某些字段
        if not include_sensitive:
            guild_data.pop("join_condition", None)
        
        return guild_data
    
    @staticmethod
    def format_member_info(member: GuildMember, is_online: bool = False) -> Dict[str, Any]:
        """格式化成员信息用于显示"""
        member_data = member.to_dict()
        
        # 添加格式化的时间
        member_data["joined_at_formatted"] = member.joined_at.strftime("%Y-%m-%d %H:%M:%S")
        member_data["last_active_formatted"] = member.last_active.strftime("%Y-%m-%d %H:%M:%S")
        
        # 添加在线状态
        member_data["is_online"] = is_online
        
        # 添加职位信息
        member_data["position_info"] = {
            "position": member.position,
            "position_name": guild_config.get_position_name(member.position),
            "position_color": guild_config.get_position_color(member.position)
        }
        
        # 计算加入天数
        days_joined = (datetime.now() - member.joined_at).days
        member_data["days_joined"] = days_joined
        
        return member_data
    
    @staticmethod
    def calculate_guild_level(exp: int) -> int:
        """根据经验计算公会等级"""
        for level in range(10, 0, -1):  # 从高到低检查
            required_exp = guild_config.get_exp_requirement_for_level(level)
            if exp >= required_exp:
                return level
        return 1
    
    @staticmethod
    def get_level_progress(current_exp: int, current_level: int) -> Dict[str, Any]:
        """获取等级进度信息"""
        current_level_exp = guild_config.get_exp_requirement_for_level(current_level)
        next_level_exp = guild_config.get_exp_requirement_for_level(current_level + 1)
        
        if next_level_exp == 0:  # 已达到最高等级
            return {
                "current_level": current_level,
                "current_exp": current_exp,
                "level_exp": current_level_exp,
                "next_level_exp": 0,
                "progress_exp": 0,
                "progress_percentage": 100.0,
                "is_max_level": True
            }
        
        progress_exp = current_exp - current_level_exp
        total_exp_needed = next_level_exp - current_level_exp
        progress_percentage = (progress_exp / total_exp_needed) * 100 if total_exp_needed > 0 else 0
        
        return {
            "current_level": current_level,
            "current_exp": current_exp,
            "level_exp": current_level_exp,
            "next_level_exp": next_level_exp,
            "progress_exp": progress_exp,
            "progress_percentage": min(progress_percentage, 100.0),
            "is_max_level": False
        }
    
    @staticmethod
    def check_join_conditions(guild: Guild, player_level: int, player_power: int) -> Tuple[bool, str]:
        """检查玩家是否满足公会加入条件"""
        conditions = guild.join_condition
        
        # 检查等级要求
        required_level = conditions.get("level_required", 1)
        if player_level < required_level:
            return False, f"需要达到{required_level}级才能加入"
        
        # 检查战力要求
        required_power = conditions.get("power_required", 0)
        if player_power < required_power:
            return False, f"需要战力达到{required_power}才能加入"
        
        return True, ""
    
    @staticmethod
    def can_promote_to_position(guild_members: List[GuildMember], target_position: GuildPosition) -> Tuple[bool, str]:
        """检查是否可以提升到指定职位"""
        # 统计当前职位数量
        position_count = sum(1 for member in guild_members if member.position == target_position)
        max_count = guild_config.get_max_position_count(target_position)
        
        if position_count >= max_count:
            position_name = guild_config.get_position_name(target_position)
            return False, f"{position_name}职位已达到上限({max_count}个)"
        
        return True, ""
    
    @staticmethod
    def sort_members_by_hierarchy(members: List[GuildMember]) -> List[GuildMember]:
        """按职位等级排序成员列表"""
        position_order = {
            GuildPosition.LEADER: 1,
            GuildPosition.VICE_LEADER: 2,
            GuildPosition.ELDER: 3,
            GuildPosition.MEMBER: 4
        }
        
        return sorted(members, key=lambda m: (
            position_order.get(m.position, 999),  # 职位优先级
            -m.total_contribution,                # 贡献度降序
            m.joined_at                          # 加入时间升序
        ))
    
    @staticmethod
    def filter_guilds_by_criteria(guilds: List[Dict[str, Any]], 
                                 min_level: int = None,
                                 max_level: int = None,
                                 has_space: bool = None,
                                 keyword: str = None) -> List[Dict[str, Any]]:
        """根据条件过滤公会列表"""
        filtered = guilds
        
        # 按等级过滤
        if min_level is not None:
            filtered = [g for g in filtered if g.get("level", 1) >= min_level]
        
        if max_level is not None:
            filtered = [g for g in filtered if g.get("level", 1) <= max_level]
        
        # 按是否有空位过滤
        if has_space is not None:
            if has_space:
                filtered = [g for g in filtered if g.get("member_count", 0) < g.get("max_members", 30)]
            else:
                filtered = [g for g in filtered if g.get("member_count", 0) >= g.get("max_members", 30)]
        
        # 按关键词过滤
        if keyword:
            keyword_lower = keyword.lower()
            filtered = [g for g in filtered if 
                       keyword_lower in g.get("name", "").lower() or 
                       keyword_lower in g.get("description", "").lower()]
        
        return filtered
    
    @staticmethod
    def calculate_member_activity_score(member: GuildMember) -> float:
        """计算成员活跃度分数"""
        now = datetime.now()
        
        # 基础分数
        base_score = 50.0
        
        # 根据最后活跃时间计算分数
        days_inactive = (now - member.last_active).days
        if days_inactive == 0:
            activity_score = 50.0  # 今天活跃
        elif days_inactive <= 3:
            activity_score = 40.0  # 3天内活跃
        elif days_inactive <= 7:
            activity_score = 30.0  # 一周内活跃
        elif days_inactive <= 14:
            activity_score = 20.0  # 两周内活跃
        elif days_inactive <= 30:
            activity_score = 10.0  # 一月内活跃
        else:
            activity_score = 0.0   # 超过一月未活跃
        
        # 根据贡献度加分
        contribution_score = min(member.total_contribution / 1000 * 10, 30.0)  # 最多30分
        
        # 根据职位加分
        position_bonus = {
            GuildPosition.LEADER: 20.0,
            GuildPosition.VICE_LEADER: 15.0,
            GuildPosition.ELDER: 10.0,
            GuildPosition.MEMBER: 0.0
        }.get(member.position, 0.0)
        
        total_score = base_score + activity_score + contribution_score + position_bonus
        return min(total_score, 100.0)  # 最高100分
    
    @staticmethod
    def get_inactive_members(members: List[GuildMember], days: int = 7) -> List[GuildMember]:
        """获取不活跃的成员列表"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [member for member in members if member.last_active < cutoff_date]
    
    @staticmethod
    def format_time_ago(dt: datetime) -> str:
        """格式化时间为"多久之前"的形式"""
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """清理用户输入，移除危险字符"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除特殊字符
        text = re.sub(r'[<>&"\'\\]', '', text)
        
        # 限制长度
        return text[:200]
    
    @staticmethod
    def validate_player_eligibility(player_id: str, player_level: int) -> Tuple[bool, str]:
        """验证玩家是否有资格进行公会操作"""
        # 这里可以添加更多的验证逻辑
        # 比如检查玩家是否被封禁、是否在冷却期等
        
        if player_level < 1:
            return False, "玩家等级无效"
        
        return True, ""


# 便捷函数
def generate_guild_id() -> str:
    """生成公会ID的便捷函数"""
    return GuildUtils.generate_guild_id()


def generate_application_id() -> str:
    """生成申请ID的便捷函数"""
    return GuildUtils.generate_application_id()


def validate_guild_name(name: str) -> Tuple[bool, str]:
    """验证公会名称的便捷函数"""
    return GuildUtils.validate_guild_name(name)


def format_guild_info(guild: Guild, include_sensitive: bool = False) -> Dict[str, Any]:
    """格式化公会信息的便捷函数"""
    return GuildUtils.format_guild_info(guild, include_sensitive)


def format_member_info(member: GuildMember, is_online: bool = False) -> Dict[str, Any]:
    """格式化成员信息的便捷函数"""
    return GuildUtils.format_member_info(member, is_online)
