/* 商店管理系统 - 组件样式文件 */

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    min-height: 36px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--bg-primary);
    color: var(--gray-700);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-300);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: #047857;
    border-color: #047857;
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    min-height: 28px;
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    min-height: 44px;
}

/* 图标 */
.icon-plus::before { content: "➕"; }
.icon-refresh::before { content: "🔄"; }
.icon-edit::before { content: "✏️"; }
.icon-delete::before { content: "🗑️"; }
.icon-view::before { content: "👁️"; }
.icon-settings::before { content: "⚙️"; }

/* 商店卡片 */
.shop-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
}

.shop-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.shop-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
}

.shop-info {
    flex: 1;
}

.shop-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.shop-id {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-family: monospace;
}

.shop-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.status-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-badge.active {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background-color: #fef2f2;
    color: #991b1b;
}

.shop-type {
    padding: var(--spacing-1) var(--spacing-2);
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.shop-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    line-height: 1.5;
}

.shop-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.shop-actions {
    display: flex;
    gap: var(--spacing-2);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.2s ease-out;
}

.modal-small .modal-content {
    max-width: 400px;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

/* 优化关闭按钮样式 - 更明显易用，避免误操作 */
.modal-close {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: 50%;
    transition: var(--transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.modal-close:hover {
    color: var(--red-600);
    background-color: var(--red-50);
    border-color: var(--red-300);
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(220,53,69,0.2);
}

.modal-close:active {
    transform: scale(0.95);
}

/* 为关闭按钮添加悬停提示 */
.modal-close:hover::after {
    content: "关闭 (ESC)";
    position: absolute;
    top: -35px;
    right: 0;
    background: var(--gray-900);
    color: white;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1000;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 模态框提示信息 */
.modal-hint {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-style: italic;
}

/* 按钮组 */
.modal-buttons {
    display: flex;
    gap: var(--spacing-3);
}

/* 表单组件 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-section {
    margin-top: var(--spacing-6);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--border-color);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

/* 复选框 */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.checkmark {
    font-size: var(--font-size-sm);
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 80px;
    right: var(--spacing-6);
    z-index: 1100;
    max-width: 400px;
}

.message {
    padding: var(--spacing-4);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
    box-shadow: var(--shadow-md);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message.success {
    background-color: #dcfce7;
    color: #166534;
    border-left: 4px solid var(--success-color);
}

.message.error {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.message.warning {
    background-color: #fefce8;
    color: #a16207;
    border-left: 4px solid var(--warning-color);
}

.message.info {
    background-color: #f0f9ff;
    color: #0c4a6e;
    border-left: 4px solid var(--info-color);
}

.warning-text {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--primary-hover);
}

.breadcrumb-separator {
    color: var(--gray-400);
}

/* 商品卡片 */
.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-4);
}

.item-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
}

.item-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.item-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-3);
}

.item-info {
    flex: 1;
}

.item-template-id {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.item-config-id {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-family: monospace;
}

.item-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.item-details {
    margin-bottom: var(--spacing-3);
}

.item-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-1) 0;
    font-size: var(--font-size-sm);
}

.item-detail-label {
    color: var(--gray-600);
    font-weight: 500;
}

.item-detail-value {
    color: var(--gray-900);
}

.item-price {
    background-color: var(--gray-50);
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
}

.item-price-label {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.item-price-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
}

.item-actions {
    display: flex;
    gap: var(--spacing-2);
    justify-content: flex-end;
}

/* 品质颜色 */
.quality-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.quality-1 { background-color: #f3f4f6; color: #374151; } /* 白色 */
.quality-2 { background-color: #dcfce7; color: #166534; } /* 绿色 */
.quality-3 { background-color: #dbeafe; color: #1e40af; } /* 蓝色 */
.quality-4 { background-color: #e9d5ff; color: #7c3aed; } /* 紫色 */
.quality-5 { background-color: #fed7aa; color: #ea580c; } /* 橙色 */
.quality-6 { background-color: #fecaca; color: #dc2626; } /* 红色 */

/* 表单帮助文本 */
.form-help {
    display: block;
    margin-top: var(--spacing-1);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.4;
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: var(--spacing-4);
        max-height: calc(100vh - 2rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }

    .item-grid {
        grid-template-columns: 1fr;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }
}
