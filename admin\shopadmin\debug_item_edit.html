<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品编辑调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 商品编辑功能调试页面</h1>
        
        <div class="debug-section">
            <div class="debug-title">📊 当前状态</div>
            <div id="statusInfo" class="debug-info">等待初始化...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🧪 测试操作</div>
            <button class="btn btn-primary" onclick="testLoadItems()">加载商品列表</button>
            <button class="btn btn-secondary" onclick="testEditItem()">模拟编辑商品</button>
            <button class="btn btn-success" onclick="testFormSubmit()">模拟表单提交</button>
            <button class="btn btn-danger" onclick="clearLogs()">清除日志</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📝 操作日志</div>
            <div id="debugLogs" class="debug-info">日志将显示在这里...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">⚠️ 错误信息</div>
            <div id="errorLogs" class="debug-info">错误信息将显示在这里...</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/item-manager.js"></script>

    <script>
        // 调试工具
        class DebugTool {
            constructor() {
                this.logs = [];
                this.errors = [];
                this.init();
            }

            init() {
                // 拦截console.log和console.error
                this.originalLog = console.log;
                this.originalError = console.error;
                
                console.log = (...args) => {
                    this.originalLog(...args);
                    this.addLog('LOG', args.join(' '));
                };
                
                console.error = (...args) => {
                    this.originalError(...args);
                    this.addError('ERROR', args.join(' '));
                };

                this.updateStatus();
                this.updateLogs();
            }

            addLog(type, message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logs.push(`[${timestamp}] ${type}: ${message}`);
                this.updateLogs();
            }

            addError(type, message) {
                const timestamp = new Date().toLocaleTimeString();
                this.errors.push(`[${timestamp}] ${type}: ${message}`);
                this.updateErrors();
            }

            updateStatus() {
                const statusEl = document.getElementById('statusInfo');
                if (statusEl) {
                    const status = {
                        itemManager: window.itemManager ? '✅ 已初始化' : '❌ 未初始化',
                        shopAPI: window.shopAPI ? '✅ 已初始化' : '❌ 未初始化',
                        currentItem: window.itemManager?.currentItem ? 
                            `✅ ${window.itemManager.currentItem.config_id}` : '❌ 无',
                        isSubmitting: window.itemManager?.isSubmitting ? '⚠️ 提交中' : '✅ 空闲',
                        shopId: window.itemManager?.shopId || '❌ 未设置'
                    };

                    statusEl.textContent = Object.entries(status)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\n');
                }
            }

            updateLogs() {
                const logsEl = document.getElementById('debugLogs');
                if (logsEl) {
                    logsEl.textContent = this.logs.slice(-20).join('\n');
                }
            }

            updateErrors() {
                const errorsEl = document.getElementById('errorLogs');
                if (errorsEl) {
                    errorsEl.textContent = this.errors.slice(-10).join('\n');
                }
            }

            clearLogs() {
                this.logs = [];
                this.errors = [];
                this.updateLogs();
                this.updateErrors();
            }
        }

        // 测试函数
        async function testLoadItems() {
            try {
                if (!window.itemManager) {
                    throw new Error('ItemManager未初始化');
                }

                // 设置测试商店ID
                window.itemManager.shopId = 'shop_026666601acb';
                
                console.log('开始测试加载商品列表...');
                await window.itemManager.loadItems();
                console.log('商品列表加载完成');
                
                debugTool.updateStatus();
            } catch (error) {
                console.error('测试加载商品失败:', error);
            }
        }

        async function testEditItem() {
            try {
                if (!window.itemManager) {
                    throw new Error('ItemManager未初始化');
                }

                // 确保有商品数据
                if (window.itemManager.items.length === 0) {
                    console.log('先加载商品列表...');
                    await testLoadItems();
                }

                if (window.itemManager.items.length > 0) {
                    const firstItem = window.itemManager.items[0];
                    console.log('开始测试编辑商品:', firstItem.config_id);
                    await window.itemManager.editItem(firstItem.config_id);
                    console.log('编辑商品测试完成');
                } else {
                    console.log('没有可编辑的商品');
                }
                
                debugTool.updateStatus();
            } catch (error) {
                console.error('测试编辑商品失败:', error);
            }
        }

        async function testFormSubmit() {
            try {
                if (!window.itemManager) {
                    throw new Error('ItemManager未初始化');
                }

                if (!window.itemManager.currentItem) {
                    console.log('先打开编辑表单...');
                    await testEditItem();
                }

                if (window.itemManager.currentItem) {
                    console.log('开始测试表单提交...');
                    
                    // 创建模拟事件
                    const mockEvent = {
                        preventDefault: () => console.log('preventDefault called'),
                        target: document.getElementById('itemForm')
                    };

                    await window.itemManager.handleFormSubmit(mockEvent);
                    console.log('表单提交测试完成');
                } else {
                    console.log('没有当前编辑的商品');
                }
                
                debugTool.updateStatus();
            } catch (error) {
                console.error('测试表单提交失败:', error);
            }
        }

        function clearLogs() {
            debugTool.clearLogs();
        }

        // 初始化调试工具
        let debugTool;
        
        window.addEventListener('load', () => {
            debugTool = new DebugTool();
            
            // 定期更新状态
            setInterval(() => {
                debugTool.updateStatus();
            }, 1000);
            
            console.log('调试页面已加载');
        });
    </script>
</body>
</html>
