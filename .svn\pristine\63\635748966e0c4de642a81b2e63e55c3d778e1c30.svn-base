"""
统一调度器管理器
提供统一的调度器架构，支持多种启动模式和完整的服务依赖管理
"""

import asyncio
import time
import traceback
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from logger_config import setup_logger
from scheduler_manager import SchedulerManager
from service_locator import ServiceLocator
from task_queue import TaskQueue
from mongodb_manager import MongoDBManager
from ConnectionManager import ConnectionManager
from distributed_task import distributed_task
from config import config

logger = setup_logger(__name__)

class SchedulerMode(Enum):
    """调度器运行模式"""
    STANDALONE = "standalone"  # 独立调度器模式
    INTEGRATED = "integrated"  # 集成到游戏服务器模式

@dataclass
class ServiceDependency:
    """服务依赖定义"""
    name: str
    service_key: str
    required: bool = True
    health_check: Optional[Callable] = None
    initialization: Optional[Callable] = None

class ExecutionMode(Enum):
    """任务执行模式"""
    DIRECT = "direct"      # 直接调用模式
    QUEUE = "queue"        # 队列模式
    HYBRID = "hybrid"      # 混合模式

@dataclass
class TaskDefinition:
    """任务定义"""
    name: str
    func: Callable
    trigger: str
    trigger_args: Dict[str, Any]
    dependencies: List[str]
    lock_key: Optional[str] = None
    lock_ttl: int = 60
    enabled: bool = True
    execution_mode: ExecutionMode = ExecutionMode.DIRECT
    max_retries: int = 3
    retry_delay: int = 5

class UnifiedSchedulerManager:
    """统一调度器管理器"""
    
    def __init__(self, mode: SchedulerMode = SchedulerMode.STANDALONE):
        self.mode = mode
        self.scheduler_manager = SchedulerManager()
        self.task_queue = TaskQueue()
        self.services: Dict[str, Any] = {}
        self.service_dependencies: Dict[str, ServiceDependency] = {}
        self.task_definitions: Dict[str, TaskDefinition] = {}
        self.health_check_interval = 60  # 健康检查间隔（秒）
        self.health_check_task = None
        self.is_running = False
        
        logger.info(f"统一调度器管理器初始化，模式: {mode.value}")
    
    def register_service_dependency(self, dependency: ServiceDependency):
        """注册服务依赖"""
        self.service_dependencies[dependency.service_key] = dependency
        logger.info(f"注册服务依赖: {dependency.name} ({dependency.service_key})")
    
    def register_task(self, task_def: TaskDefinition):
        """注册任务定义"""
        self.task_definitions[task_def.name] = task_def
        logger.info(f"注册任务定义: {task_def.name}")
    
    async def initialize_services(self) -> bool:
        """初始化所有服务"""
        logger.info("开始初始化服务...")

        try:
            # 按依赖顺序初始化服务
            for service_key, dependency in self.service_dependencies.items():
                # 检查服务是否已经存在
                existing_service = ServiceLocator.get(service_key)
                if existing_service is not None:
                    logger.info(f"服务 {dependency.name} 已存在，跳过初始化")
                    self.services[service_key] = existing_service
                    continue

                if dependency.initialization:
                    logger.info(f"初始化服务: {dependency.name}")
                    service_instance = await dependency.initialization()
                    self.services[service_key] = service_instance
                    ServiceLocator.register(service_key, service_instance)
                    logger.info(f"服务 {dependency.name} 初始化完成")
                else:
                    logger.warning(f"服务 {dependency.name} 没有初始化函数")

            # 注册基础服务
            ServiceLocator.register("scheduler_manager", self.scheduler_manager)
            ServiceLocator.register("task_queue", self.task_queue)

            logger.info("所有服务初始化完成")
            return True

        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def check_service_health(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        health_status = {}
        
        for service_key, dependency in self.service_dependencies.items():
            try:
                service = ServiceLocator.get(service_key)
                if not service:
                    health_status[service_key] = False
                    continue
                
                if dependency.health_check:
                    is_healthy = await dependency.health_check(service)
                    health_status[service_key] = is_healthy
                    if not is_healthy:
                        logger.warning(f"服务 {dependency.name} 健康检查失败")
                else:
                    # 默认检查：服务存在即认为健康
                    health_status[service_key] = True
                    
            except Exception as e:
                logger.error(f"检查服务 {dependency.name} 健康状态失败: {str(e)}")
                health_status[service_key] = False
        
        return health_status
    
    async def validate_task_dependencies(self, task_name: str) -> bool:
        """验证任务依赖"""
        if task_name not in self.task_definitions:
            logger.error(f"任务 {task_name} 未定义")
            return False
        
        task_def = self.task_definitions[task_name]
        
        for dep_key in task_def.dependencies:
            if dep_key not in self.service_dependencies:
                logger.error(f"任务 {task_name} 依赖的服务 {dep_key} 未注册")
                return False
            
            service = ServiceLocator.get(dep_key)
            if not service:
                logger.error(f"任务 {task_name} 依赖的服务 {dep_key} 不可用")
                return False
            
            # 检查服务健康状态
            dependency = self.service_dependencies[dep_key]
            if dependency.health_check:
                try:
                    is_healthy = await dependency.health_check(service)
                    if not is_healthy:
                        logger.error(f"任务 {task_name} 依赖的服务 {dep_key} 不健康")
                        return False
                except Exception as e:
                    logger.error(f"检查服务 {dep_key} 健康状态失败: {str(e)}")
                    return False
        
        return True
    
    async def register_scheduled_tasks(self) -> bool:
        """注册所有调度任务"""
        logger.info("开始注册调度任务...")
        
        try:
            for task_name, task_def in self.task_definitions.items():
                if not task_def.enabled:
                    logger.info(f"跳过禁用的任务: {task_name}")
                    continue
                
                # 验证任务依赖
                if not await self.validate_task_dependencies(task_name):
                    logger.error(f"任务 {task_name} 依赖验证失败，跳过注册")
                    continue
                
                # 根据执行模式包装任务函数
                if task_def.execution_mode == ExecutionMode.DIRECT:
                    wrapped_func = self._wrap_direct_execution(task_def)
                elif task_def.execution_mode == ExecutionMode.QUEUE:
                    wrapped_func = self._wrap_queue_execution(task_def)
                else:  # HYBRID
                    wrapped_func = self._wrap_hybrid_execution(task_def)
                
                # 注册到调度器
                job = self.scheduler_manager.add_job(
                    wrapped_func,
                    trigger=task_def.trigger,
                    id=task_name,
                    **task_def.trigger_args
                )
                
                logger.info(f"任务 {task_name} 注册成功: {job}")
            
            logger.info("所有调度任务注册完成")
            return True
            
        except Exception as e:
            logger.error(f"注册调度任务失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def start_health_monitoring(self):
        """启动健康监控"""
        if self.health_check_task:
            return
        
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info("健康监控已启动")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                health_status = await self.check_service_health()
                
                # 记录不健康的服务
                unhealthy_services = [
                    self.service_dependencies[key].name 
                    for key, healthy in health_status.items() 
                    if not healthy
                ]
                
                if unhealthy_services:
                    logger.warning(f"检测到不健康的服务: {unhealthy_services}")
                else:
                    logger.debug("所有服务健康状态正常")
                
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环异常: {str(e)}")
                await asyncio.sleep(10)  # 出错后短暂等待
    
    async def start(self) -> bool:
        """启动统一调度器"""
        logger.info("启动统一调度器...")
        
        try:
            # 1. 初始化服务
            if not await self.initialize_services():
                return False
            
            # 2. 注册调度任务
            if not await self.register_scheduled_tasks():
                return False
            
            # 3. 启动调度器
            self.scheduler_manager.start()
            self.is_running = True
            
            # 4. 启动健康监控
            await self.start_health_monitoring()
            
            logger.info("统一调度器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动统一调度器失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def stop(self):
        """停止统一调度器"""
        logger.info("停止统一调度器...")
        
        self.is_running = False
        
        # 停止健康监控
        if self.health_check_task and not self.health_check_task.done():
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # 停止调度器
        self.scheduler_manager.shutdown(wait=True)
        
        # 关闭任务队列
        await self.task_queue.close()

        logger.info("统一调度器已停止")

    def _wrap_direct_execution(self, task_def: TaskDefinition):
        """包装直接执行模式的任务"""
        async def direct_wrapper():
            # 添加重试机制
            for attempt in range(task_def.max_retries + 1):
                try:
                    # 验证依赖
                    if not await self.validate_task_dependencies(task_def.name):
                        raise Exception(f"任务 {task_def.name} 依赖验证失败")

                    # 执行任务
                    try:
                        logger.debug(f"准备执行任务 {task_def.name}, 函数类型: {type(task_def.func)}")
                        result = await task_def.func()
                    except TypeError as te:
                        # 如果是参数错误，可能是未绑定方法
                        logger.error(f"任务 {task_def.name} 调用参数错误: {str(te)}")
                        logger.error(f"函数详情: {task_def.func}, 类型: {type(task_def.func)}")
                        raise Exception(f"任务函数调用失败: {str(te)}")
                    except Exception as e:
                        logger.error(f"任务 {task_def.name} 执行异常: {str(e)}")
                        raise
                    logger.info(f"任务 {task_def.name} 直接执行成功")
                    return result

                except Exception as e:
                    if attempt < task_def.max_retries:
                        logger.warning(f"任务 {task_def.name} 执行失败，第 {attempt + 1} 次重试: {str(e)}")
                        await asyncio.sleep(task_def.retry_delay)
                    else:
                        logger.error(f"任务 {task_def.name} 执行失败，已达最大重试次数: {str(e)}")
                        raise

        # 添加分布式锁
        if task_def.lock_key:
            return distributed_task(
                lock_key=task_def.lock_key,
                ttl=task_def.lock_ttl
            )(direct_wrapper)

        return direct_wrapper

    def _wrap_queue_execution(self, task_def: TaskDefinition):
        """包装队列执行模式的任务"""
        async def queue_wrapper():
            try:
                # 发布到队列
                await self.task_queue.publish_task(task_def.name, {})
                logger.info(f"任务 {task_def.name} 已发布到队列")

            except Exception as e:
                logger.error(f"任务 {task_def.name} 发布到队列失败: {str(e)}")
                raise

        return queue_wrapper

    def _wrap_hybrid_execution(self, task_def: TaskDefinition):
        """包装混合执行模式的任务"""
        async def hybrid_wrapper():
            try:
                # 首先尝试直接执行
                if await self.validate_task_dependencies(task_def.name):
                    # 执行任务
                    try:
                        result = await task_def.func()
                    except TypeError as te:
                        # 如果是参数错误，可能是未绑定方法
                        logger.error(f"任务 {task_def.name} 调用参数错误: {str(te)}")
                        raise Exception(f"任务函数调用失败: {str(te)}")
                    logger.info(f"任务 {task_def.name} 混合模式直接执行成功")
                    return result
                else:
                    # 依赖不可用时降级到队列模式
                    await self.task_queue.publish_task(task_def.name, {})
                    logger.info(f"任务 {task_def.name} 降级到队列执行")

            except Exception as e:
                logger.error(f"任务 {task_def.name} 混合模式执行失败: {str(e)}")
                # 失败时也尝试队列模式
                try:
                    await self.task_queue.publish_task(task_def.name, {})
                    logger.info(f"任务 {task_def.name} 失败后降级到队列执行")
                except Exception as queue_error:
                    logger.error(f"任务 {task_def.name} 队列降级也失败: {str(queue_error)}")
                    raise e

        # 添加分布式锁
        if task_def.lock_key:
            return distributed_task(
                lock_key=task_def.lock_key,
                ttl=task_def.lock_ttl
            )(hybrid_wrapper)

        return hybrid_wrapper
