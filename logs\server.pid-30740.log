2025-08-05 18:16:02,672 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,333 - models - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,357 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,693 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,700 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,708 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,718 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:03,728 - auth - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,486 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,535 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,560 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,572 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,585 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,586 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 8472e847)
2025-08-05 18:16:06,595 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,638 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,654 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,663 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,672 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,761 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,773 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,782 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,791 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,800 - game_server - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:06,801 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:16:06,801 - msgManager - INFO - Monster handlers registered
2025-08-05 18:16:06,801 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:16:06,803 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:16:06,813 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:16:06,814 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:16:06,815 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5948c439)
2025-08-05 18:16:06,867 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:16:06,910 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:16:06,910 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:16:06,912 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:16:06,914 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 30740)
2025-08-05 18:16:06,914 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:16:07,086 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:16:07,087 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:16:07,602 - ConnectionManager - INFO - 后台任务已启动 (Worker 30740)
2025-08-05 18:16:07,603 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 30740)
2025-08-05 18:16:07,603 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:16:07,604 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:16:07,605 - game_server - INFO - 游戏配置加载完成 (Worker: 30740)
2025-08-05 18:16:07,606 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:16:11,884 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:16:11,886 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=0, 总计=2
2025-08-05 18:16:11,887 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 30740): 2/2 (100.0%)
2025-08-05 18:16:11,888 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:16:11,930 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 5948c439)
2025-08-05 18:16:11,931 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5948c439)
2025-08-05 18:16:11,940 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:11,942 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:16:11,943 - ConnectionManager - INFO - Worker 30740 开始消费广播消息，消费者标签: ctag1.79ab541fd60849da907c4db8f06b63f8
2025-08-05 18:16:11,988 - ConnectionManager - INFO - Worker 30740 开始消费个人消息，消费者标签: ctag1.3e37119b77ee4eb4bb68ba1e0f5f4485
2025-08-05 18:16:12,057 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 30740)
2025-08-05 18:16:12,166 - distributed_lock - INFO - Worker 30740 获取锁超时: scheduler_initialization
2025-08-05 18:16:12,166 - game_server_scheduler_integration - INFO - Worker 30740 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 18:16:12,167 - game_server - INFO - 统一调度器初始化成功 (Worker: 30740)
2025-08-05 18:16:12,173 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 30740)
2025-08-05 18:16:12,174 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:16:12,174 - game_server - INFO - 日志清理管理器已启动 (Worker: 30740)
2025-08-05 18:16:12,175 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:16:12,176 - game_server - INFO - Monster cooldown manager initialized (Worker: 30740)
2025-08-05 18:16:12,533 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:16:12,619 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:16:12,619 - game_server - INFO - 公会系统初始化成功 (Worker: 30740)
2025-08-05 18:16:12,619 - game_server - INFO - 邮件系统初始化成功 (Worker: 30740)
2025-08-05 18:16:12,621 - game_server - INFO - 商店系统初始化成功 (Worker: 30740)
2025-08-05 18:16:12,621 - game_server - INFO - 初始化完成 (Worker: 30740)
2025-08-05 18:16:30,018 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:16:37,611 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:17:00,258 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:17:07,626 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:17:11,899 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:17:11,899 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:17:11,946 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 18:17:11,955 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 18:17:11,955 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 18:17:11,956 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 18:17:13,028 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:17:13,738 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 18:17:17,058 - shop_api - INFO - [ShopAPI] 删除商店: shop_b17163c30fa9
2025-08-05 18:17:17,277 - shop_database_manager - INFO - 删除商店 shop_b17163c30fa9 的 0 个商品配置
2025-08-05 18:17:17,360 - shop_database_manager - INFO - 商店删除成功: shop_b17163c30fa9
2025-08-05 18:17:17,455 - shop_service - INFO - 商店删除成功: shop_b17163c30fa9
2025-08-05 18:17:17,455 - shop_api - INFO - [ShopAPI] 商店删除成功: shop_b17163c30fa9
2025-08-05 18:17:17,461 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:17:19,922 - shop_api - INFO - [ShopAPI] 删除商店: shop_eba24fafed66
2025-08-05 18:17:20,054 - shop_database_manager - INFO - 删除商店 shop_eba24fafed66 的 0 个商品配置
2025-08-05 18:17:20,138 - shop_database_manager - INFO - 商店删除成功: shop_eba24fafed66
2025-08-05 18:17:20,230 - shop_service - INFO - 商店删除成功: shop_eba24fafed66
2025-08-05 18:17:20,230 - shop_api - INFO - [ShopAPI] 商店删除成功: shop_eba24fafed66
2025-08-05 18:17:20,238 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:17:30,370 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:17:30,370 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:17:30,468 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:17:37,642 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:18:00,847 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:18:11,911 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:18:11,911 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:18:13,165 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:18:13,165 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:18:26,811 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:18:30,039 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:18:56,829 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:19:00,240 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:19:11,925 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:19:11,926 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:19:13,268 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:19:13,269 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:19:26,837 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:19:30,509 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:19:56,841 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:20:00,795 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:20:11,931 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:20:11,932 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:20:13,160 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:20:13,161 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:20:26,846 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:20:30,334 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:20:56,857 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:21:00,566 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:21:11,943 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:21:11,944 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:21:13,139 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:21:13,139 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:21:26,871 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:21:30,765 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:21:56,880 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:22:00,982 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:22:11,960 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:22:11,961 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:22:13,166 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:22:13,166 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:22:26,883 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:22:30,209 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:22:56,896 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:23:00,411 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:23:11,968 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:23:11,970 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:23:13,214 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:23:13,214 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:23:26,904 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:23:30,637 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:23:56,906 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:24:00,823 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:24:11,984 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:24:11,986 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:24:13,263 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:24:13,264 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:24:26,921 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:24:56,936 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:25:00,183 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:25:11,989 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:25:11,992 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:25:13,152 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:25:13,152 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:25:26,952 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:25:30,393 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:25:56,961 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:26:00,622 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:26:12,017 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=2, 可用=1, 总计=3
2025-08-05 18:26:12,020 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:26:13,183 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:26:13,183 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:26:26,973 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:26:30,823 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:26:56,974 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:27:00,035 - ConnectionManager - INFO - 连接状态 (Worker 30740): 活跃连接数=0, 用户数=0
2025-08-05 18:27:08,842 - game_server - INFO - 关闭服务器... (Worker: 30740)
2025-08-05 18:27:08,843 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 18:27:12,024 - ConnectionManager - INFO - Redis连接池状态 (Worker 30740): 使用中=3, 可用=0, 总计=3
2025-08-05 18:27:12,024 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 30740): 3/3 (100.0%)
2025-08-05 18:27:12,026 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 30740): 连接中
2025-08-05 18:27:13,857 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 18:27:13,858 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:27:14,053 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:27:14,459 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:27:14,459 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 18:27:14,460 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 18:27:14,460 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 18:27:14,461 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 18:27:14,461 - game_server - INFO - 日志清理管理器已停止
2025-08-05 18:27:14,462 - game_server - INFO - 统一调度器已关闭
2025-08-05 18:27:14,464 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 30740)
2025-08-05 18:27:14,510 - game_server - INFO - 服务器资源已清理 (Worker: 30740)
