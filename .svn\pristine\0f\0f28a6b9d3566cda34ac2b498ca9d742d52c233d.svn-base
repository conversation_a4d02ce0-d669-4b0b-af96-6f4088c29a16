"""
分布式角色服务
支持多Worker环境，使用Redis进行分布式缓存和锁管理
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime
import uuid

from game_database import DatabaseManager, RoleCacheManager
from role_models import Role, RoleCreateRequest, RoleUpdateRequest, RoleState
from distributed_lock import DistributedLock
from distributed_task import distributed_task
from enums import MessageId

logger = logging.getLogger(__name__)


class DistributedRoleService:
    """分布式角色服务 - 支持多Worker环境"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.cache_manager = db_manager.role_cache
        self.redis_client = db_manager.redis_client
        
        # 分布式锁配置
        self.lock_timeout = 30  # 锁超时时间（秒）
        self.lock_retry_delay = 0.1  # 重试延迟（秒）
        self.lock_max_retries = 10  # 最大重试次数
    
    async def _get_role_lock(self, owner: str, role_id: str = None) -> DistributedLock:
        """获取角色操作锁"""
        lock_key = f"role_lock:{owner}:{role_id}" if role_id else f"role_lock:{owner}"
        return DistributedLock(
            redis_client=self.redis_client,
            lock_key=lock_key,
            timeout=self.lock_timeout,
            retry_delay=self.lock_retry_delay,
            max_retries=self.lock_max_retries
        )
    
    @distributed_task
    async def get_player_roles(self, owner: str) -> List[Dict[str, Any]]:
        """获取玩家角色列表"""
        try:
            # 1. 尝试从缓存获取
            roles = await self.cache_manager.get_player_roles(owner)
            if roles:
                logger.debug(f"从缓存获取玩家 {owner} 的角色列表: {len(roles)} 个角色")
                return roles
            
            # 2. 从数据库获取
            roles = await self._load_roles_from_db(owner)
            if roles:
                # 3. 同步到缓存
                await self.cache_manager.sync_roles_to_cache(owner, roles)
                logger.info(f"从数据库加载玩家 {owner} 的角色列表: {len(roles)} 个角色")
            
            return roles
            
        except Exception as e:
            logger.error(f"获取玩家 {owner} 角色列表失败: {str(e)}")
            return []
    
    @distributed_task
    async def get_role_details(self, owner: str, role_id: str) -> Optional[Dict[str, Any]]:
        """获取角色详情"""
        try:
            # 1. 尝试从缓存获取
            role_data = await self.cache_manager.get_role_details(owner, role_id)
            if role_data:
                logger.debug(f"从缓存获取角色详情: {owner}:{role_id}")
                return role_data
            
            # 2. 从数据库获取
            role_data = await self._load_role_from_db(owner, role_id)
            if role_data:
                # 3. 缓存角色详情
                await self.cache_manager.set_role_details(owner, role_id, role_data)
                logger.info(f"从数据库加载角色详情: {owner}:{role_id}")
            
            return role_data
            
        except Exception as e:
            logger.error(f"获取角色详情失败 {owner}:{role_id}: {str(e)}")
            return None
    
    @distributed_task
    async def create_role(self, owner: str, role_request: RoleCreateRequest) -> Dict[str, Any]:
        """创建角色"""
        async with await self._get_role_lock(owner) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 检查角色数量限制
                existing_roles = await self.get_player_roles(owner)
                if len(existing_roles) >= 100:  # 最大100个角色
                    return {"success": False, "error": "角色数量已达上限"}
                
                # 2. 检查角色名称是否重复
                for role in existing_roles:
                    if role.get("name") == role_request.name:
                        return {"success": False, "error": "角色名称已存在"}
                
                # 3. 创建角色
                role = Role(
                    owner=owner,
                    name=role_request.name,
                    avatar=role_request.avatar
                )
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                role_data = role.to_dict()
                await self.cache_manager.add_role(role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_player_roles_cache(owner)
                
                logger.info(f"创建角色成功: {owner}:{role.role_id}")
                return {
                    "success": True,
                    "data": role_data,
                    "message": "创建角色成功"
                }
                
            except Exception as e:
                logger.error(f"创建角色失败 {owner}: {str(e)}")
                return {"success": False, "error": f"创建角色失败: {str(e)}"}
    
    @distributed_task
    async def delete_role(self, owner: str, role_id: str) -> Dict[str, Any]:
        """删除角色"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 检查角色是否存在
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 检查角色状态
                if role_data.get("state") != RoleState.IDLE.value:
                    return {"success": False, "error": "角色正在使用中，无法删除"}
                
                # 3. 从数据库删除
                success = await self._delete_role_from_db(owner, role_id)
                if not success:
                    return {"success": False, "error": "从数据库删除角色失败"}
                
                # 4. 更新缓存
                await self.cache_manager.delete_role(role_id, owner)
                
                logger.info(f"删除角色成功: {owner}:{role_id}")
                return {
                    "success": True,
                    "message": "删除角色成功"
                }
                
            except Exception as e:
                logger.error(f"删除角色失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"删除角色失败: {str(e)}"}
    
    @distributed_task
    async def update_role(self, owner: str, role_id: str, update_data: RoleUpdateRequest) -> Dict[str, Any]:
        """更新角色"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 更新字段
                if update_data.name is not None:
                    role.name = update_data.name
                if update_data.avatar is not None:
                    role.avatar = update_data.avatar
                if update_data.level is not None:
                    role.level = update_data.level
                if update_data.exp is not None:
                    role.exp = update_data.exp
                if update_data.star is not None:
                    role.star = update_data.star
                if update_data.state is not None:
                    role.state = update_data.state
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"更新角色成功: {owner}:{role_id}")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "更新角色成功"
                }
                
            except Exception as e:
                logger.error(f"更新角色失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"更新角色失败: {str(e)}"}
    
    @distributed_task
    async def add_role_exp(self, owner: str, role_id: str, exp_amount: int) -> Dict[str, Any]:
        """给角色添加经验"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 添加经验
                exp_result = role.add_exp(exp_amount)
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 获得经验 {exp_amount}, 升级结果: {exp_result}")
                return {
                    "success": True,
                    "data": {
                        "role": updated_role_data,
                        "exp_result": exp_result
                    },
                    "message": "添加经验成功"
                }
                
            except Exception as e:
                logger.error(f"添加角色经验失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"添加经验失败: {str(e)}"}
    
    @distributed_task
    async def equip_weapon(self, owner: str, role_id: str, weapon_id: str, weapon_name: str, weapon_level: int = 0, weapon_star: int = 0) -> Dict[str, Any]:
        """装备武器"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 装备武器
                role.equip_weapon(weapon_id, weapon_name, weapon_level, weapon_star)
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 装备武器 {weapon_id}")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "装备武器成功"
                }
                
            except Exception as e:
                logger.error(f"装备武器失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"装备武器失败: {str(e)}"}
    
    @distributed_task
    async def unequip_weapon(self, owner: str, role_id: str) -> Dict[str, Any]:
        """卸下武器"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 卸下武器
                role.unequip_weapon()
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 卸下武器")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "卸下武器成功"
                }
                
            except Exception as e:
                logger.error(f"卸下武器失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"卸下武器失败: {str(e)}"}
    
    @distributed_task
    async def equip_armor(self, owner: str, role_id: str, armor_id: str, armor_name: str, armor_level: int = 0, armor_star: int = 0) -> Dict[str, Any]:
        """装备防具"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 装备防具
                role.equip_armor(armor_id, armor_name, armor_level, armor_star)
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 装备防具 {armor_id}")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "装备防具成功"
                }
                
            except Exception as e:
                logger.error(f"装备防具失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"装备防具失败: {str(e)}"}
    
    @distributed_task
    async def unequip_armor(self, owner: str, role_id: str) -> Dict[str, Any]:
        """卸下防具"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 卸下防具
                role.unequip_armor()
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 卸下防具")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "卸下防具成功"
                }
                
            except Exception as e:
                logger.error(f"卸下防具失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"卸下防具失败: {str(e)}"}
    
    @distributed_task
    async def change_role_state(self, owner: str, role_id: str, new_state: RoleState) -> Dict[str, Any]:
        """改变角色状态"""
        async with await self._get_role_lock(owner, role_id) as lock:
            if not lock.acquired:
                return {"success": False, "error": "获取锁失败"}
            
            try:
                # 1. 获取角色数据
                role_data = await self.get_role_details(owner, role_id)
                if not role_data:
                    return {"success": False, "error": "角色不存在"}
                
                # 2. 创建角色实例
                role = Role.from_dict(role_data)
                
                # 3. 改变状态
                role.change_state(new_state)
                
                # 4. 保存到数据库
                success = await self._save_role_to_db(role)
                if not success:
                    return {"success": False, "error": "保存角色到数据库失败"}
                
                # 5. 更新缓存
                updated_role_data = role.to_dict()
                await self.cache_manager.update_role(updated_role_data)
                
                # 6. 使相关缓存失效
                await self.cache_manager.invalidate_role_cache(owner, role_id)
                
                logger.info(f"角色 {owner}:{role_id} 状态改变为 {new_state.value}")
                return {
                    "success": True,
                    "data": updated_role_data,
                    "message": "改变角色状态成功"
                }
                
            except Exception as e:
                logger.error(f"改变角色状态失败 {owner}:{role_id}: {str(e)}")
                return {"success": False, "error": f"改变角色状态失败: {str(e)}"}
    
    # ============= 数据库操作方法 =============
    
    async def _load_roles_from_db(self, owner: str) -> List[Dict[str, Any]]:
        """从数据库加载玩家角色列表"""
        try:
            collection = self.db.db.roles
            cursor = collection.find({"owner": owner})
            roles = []
            
            async for doc in cursor:
                # 移除MongoDB的_id字段
                doc.pop("_id", None)
                roles.append(doc)
            
            return roles
            
        except Exception as e:
            logger.error(f"从数据库加载角色列表失败 {owner}: {str(e)}")
            return []
    
    async def _load_role_from_db(self, owner: str, role_id: str) -> Optional[Dict[str, Any]]:
        """从数据库加载角色详情"""
        try:
            collection = self.db.db.roles
            doc = await collection.find_one({"owner": owner, "role_id": role_id})
            
            if doc:
                # 移除MongoDB的_id字段
                doc.pop("_id", None)
                return doc
            
            return None
            
        except Exception as e:
            logger.error(f"从数据库加载角色详情失败 {owner}:{role_id}: {str(e)}")
            return None
    
    async def _save_role_to_db(self, role: Role) -> bool:
        """保存角色到数据库"""
        try:
            collection = self.db.db.roles
            
            # 使用upsert操作
            result = await collection.replace_one(
                {"owner": role.owner, "role_id": role.role_id},
                role.to_dict(),
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"保存角色到数据库失败 {role.owner}:{role.role_id}: {str(e)}")
            return False
    
    async def _delete_role_from_db(self, owner: str, role_id: str) -> bool:
        """从数据库删除角色"""
        try:
            collection = self.db.db.roles
            result = await collection.delete_one({"owner": owner, "role_id": role_id})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"从数据库删除角色失败 {owner}:{role_id}: {str(e)}")
            return False
    
    # ============= 缓存管理方法 =============
    
    async def invalidate_role_cache(self, owner: str, role_id: str):
        """使角色缓存失效"""
        await self.cache_manager.invalidate_role_cache(owner, role_id)
    
    async def invalidate_player_roles_cache(self, owner: str):
        """使玩家角色列表缓存失效"""
        await self.cache_manager.invalidate_player_roles_cache(owner)
    
    async def sync_roles_to_cache(self, owner: str, roles: List[Dict[str, Any]]):
        """同步角色列表到缓存"""
        await self.cache_manager.sync_roles_to_cache(owner, roles)


# 全局角色服务实例
_role_service = None

def get_role_service() -> DistributedRoleService:
    """获取全局角色服务实例"""
    global _role_service
    if _role_service is None:
        db_manager = DatabaseManager()
        _role_service = DistributedRoleService(db_manager)
    return _role_service 