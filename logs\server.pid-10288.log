2025-08-05 18:06:20,323 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,068 - models - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,091 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,500 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,509 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,521 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,534 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,545 - auth - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:24,227 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:24,270 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:02,651 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,353 - models - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,377 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,708 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,715 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,725 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,735 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,744 - auth - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,497 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,548 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,575 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,587 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,601 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,602 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5078c0e4)
2025-08-05 18:16:06,610 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,653 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,669 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,680 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,689 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,779 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,794 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,804 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,811 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,823 - game_server - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,823 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:16:06,824 - msgManager - INFO - Monster handlers registered
2025-08-05 18:16:06,824 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:16:06,826 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:16:06,839 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:16:06,839 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:16:06,840 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 725c7fad)
2025-08-05 18:16:06,885 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:16:06,929 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:16:06,929 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:16:06,932 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:16:06,933 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 10288)
2025-08-05 18:16:06,933 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:16:07,106 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:16:07,115 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:16:07,653 - ConnectionManager - INFO - 后台任务已启动 (Worker 10288)
2025-08-05 18:16:07,653 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 10288)
2025-08-05 18:16:07,654 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:16:07,654 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:16:07,655 - game_server - INFO - 游戏配置加载完成 (Worker: 10288)
2025-08-05 18:16:07,655 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:11,885 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:16:11,887 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=0, 总计=2
2025-08-05 18:16:11,888 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 10288): 2/2 (100.0%)
2025-08-05 18:16:11,889 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:16:11,931 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 725c7fad)
2025-08-05 18:16:11,934 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 725c7fad)
2025-08-05 18:16:11,941 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:11,942 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:16:11,944 - ConnectionManager - INFO - Worker 10288 开始消费广播消息，消费者标签: ctag1.fdfd596a728f4da1970506244104a45c
2025-08-05 18:16:11,989 - ConnectionManager - INFO - Worker 10288 开始消费个人消息，消费者标签: ctag1.523bab95b4f94381acacfcffadeaa7d3
2025-08-05 18:16:12,078 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 10288)
2025-08-05 18:16:12,165 - distributed_lock - INFO - Worker 10288 成功获取锁: scheduler_initialization
2025-08-05 18:16:12,165 - game_server_scheduler_integration - INFO - Worker 10288 获得调度器初始化权限
2025-08-05 18:16:12,169 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:16:12,169 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:16:12,170 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:16:12,170 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:16:12,173 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:16:12,173 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:16:12,174 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:16:12,175 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:16:12,175 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:16:12,176 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:16:12,264 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:16:12,265 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:16:12,265 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:16:12,266 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:16:12,582 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:16:12,669 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:16:12,670 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:16:12,670 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:16:12,671 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:16:12,671 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:16:12,671 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:16:12,762 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:16:12,889 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:16:12,889 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:16:12,890 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:16:12,890 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:16:12,891 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:16:12,891 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:16:12,895 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:12,895 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:16:12,899 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:16:12,900 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:12,900 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:16:12,901 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:16:13,034 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,035 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:16:13,037 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:16:13,170 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,170 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:16:13,170 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:16:13,171 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,172 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:16:13,173 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:16:13,173 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,174 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:16:13,174 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:16:13,174 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:16:13,176 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,177 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,179 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,179 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:16:13,180 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:16:13,180 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:16:13,180 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:16:13,181 - game_server_scheduler_integration - INFO - Worker 10288 调度器初始化成功
2025-08-05 18:16:13,227 - game_server - INFO - 统一调度器初始化成功 (Worker: 10288)
2025-08-05 18:16:13,233 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:13,234 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:16:13,234 - game_server - INFO - 日志清理管理器已启动 (Worker: 10288)
2025-08-05 18:16:13,234 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:16:13,235 - game_server - INFO - Monster cooldown manager initialized (Worker: 10288)
2025-08-05 18:16:13,374 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:16:13,374 - game_server - INFO - 公会系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,375 - game_server - INFO - 邮件系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,377 - game_server - INFO - 商店系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,377 - game_server - INFO - 初始化完成 (Worker: 10288)
2025-08-05 18:16:23,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:33 CST)" (scheduled at 2025-08-05 18:16:23.169939+08:00)
2025-08-05 18:16:24,927 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:24,927 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:25,059 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:25,406 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:25,406 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:25,407 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:25,407 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:25,455 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:33 CST)" executed successfully
2025-08-05 18:16:30,003 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:33,185 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:43 CST)" (scheduled at 2025-08-05 18:16:33.169939+08:00)
2025-08-05 18:16:33,229 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:33,230 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:33,359 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:33,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:33,722 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:16:33,723 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:33,723 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:33,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:43 CST)" executed successfully
2025-08-05 18:16:37,665 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:43,035 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:13 CST)" (scheduled at 2025-08-05 18:16:43.034854+08:00)
2025-08-05 18:16:43,078 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:16:43,078 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:43,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:53 CST)" (scheduled at 2025-08-05 18:16:43.169939+08:00)
2025-08-05 18:16:43,208 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:16:43,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:43,223 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:43,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:43,550 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:16:43,551 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:16:43,551 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:16:43,552 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:16:43,552 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:43,596 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:13 CST)" executed successfully
2025-08-05 18:16:43,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:43,706 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:43,707 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:43,707 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:43,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:53 CST)" executed successfully
2025-08-05 18:16:53,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:03 CST)" (scheduled at 2025-08-05 18:16:53.169939+08:00)
2025-08-05 18:16:53,221 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:53,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:53,360 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:53,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:53,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:53,713 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:53,714 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:53,760 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:03 CST)" executed successfully
2025-08-05 18:17:00,242 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:03,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:13 CST)" (scheduled at 2025-08-05 18:17:03.169939+08:00)
2025-08-05 18:17:03,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:17:03,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:03,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:17:03,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:17:03,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:17:03,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:17:03,717 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:03,761 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:13 CST)" executed successfully
2025-08-05 18:17:07,673 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:11,899 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:17:11,899 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:17:11,946 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 18:17:11,954 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 18:17:11,955 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 18:17:11,956 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 18:17:12,905 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:18:12 CST)" (scheduled at 2025-08-05 18:17:12.900596+08:00)
2025-08-05 18:17:12,949 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:17:12,950 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:12,950 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:17:12,953 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 18:17:29,358 - player_session_manager - ERROR - 获取在线玩家数量失败: Timeout reading from *************:6379
2025-08-05 18:17:29,359 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:43 CST)" was missed by 0:00:16.324994
2025-08-05 18:17:29,360 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:33 CST)" was missed by 0:00:06.190260
2025-08-05 18:17:30,074 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 18:17:30,122 - shop_database_manager - INFO - 商店创建成功: shop_7d7dfb0eaf8b
2025-08-05 18:17:30,212 - shop_service - INFO - 商店创建成功: shop_7d7dfb0eaf8b
2025-08-05 18:17:30,359 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:17:30,361 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:30,372 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:17:30,373 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:17:30,376 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:17:30,378 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 17.43秒
2025-08-05 18:17:30,378 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:17:30,379 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:30,430 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:18:12 CST)" executed successfully
2025-08-05 18:17:33,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:43 CST)" (scheduled at 2025-08-05 18:17:33.169939+08:00)
2025-08-05 18:17:33,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:17:33,227 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:33,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:17:33,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:17:56,806 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 23.44秒
2025-08-05 18:17:56,807 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:17:56,807 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:56,809 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:56,809 - apscheduler.scheduler - WARNING - Execution of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:43 CST)" skipped: maximum number of running instances reached (1)
2025-08-05 18:17:56,810 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:18:13 CST)" was missed by 0:00:13.775314
2025-08-05 18:17:56,852 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:03 CST)" executed successfully
2025-08-05 18:18:00,847 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
