2025-08-05 18:06:20,323 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,068 - models - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,091 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,500 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,509 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,521 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,534 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:21,545 - auth - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:24,227 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:06:24,270 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:02,651 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,353 - models - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,377 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,708 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,715 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,725 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,735 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:03,744 - auth - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,497 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,548 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,575 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,587 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,601 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,602 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 5078c0e4)
2025-08-05 18:16:06,610 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,653 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,669 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,680 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,689 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,779 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,794 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,804 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,811 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,823 - game_server - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:06,823 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:16:06,824 - msgManager - INFO - Monster handlers registered
2025-08-05 18:16:06,824 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:16:06,826 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:16:06,839 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:16:06,839 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:16:06,840 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 725c7fad)
2025-08-05 18:16:06,885 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:16:06,929 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:16:06,929 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:16:06,932 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:16:06,933 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 10288)
2025-08-05 18:16:06,933 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:16:07,106 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:16:07,115 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:16:07,653 - ConnectionManager - INFO - 后台任务已启动 (Worker 10288)
2025-08-05 18:16:07,653 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 10288)
2025-08-05 18:16:07,654 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 18:16:07,654 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 18:16:07,655 - game_server - INFO - 游戏配置加载完成 (Worker: 10288)
2025-08-05 18:16:07,655 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:11,885 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:16:11,887 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=0, 总计=2
2025-08-05 18:16:11,888 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 10288): 2/2 (100.0%)
2025-08-05 18:16:11,889 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:16:11,931 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 725c7fad)
2025-08-05 18:16:11,934 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 725c7fad)
2025-08-05 18:16:11,941 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:11,942 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:16:11,944 - ConnectionManager - INFO - Worker 10288 开始消费广播消息，消费者标签: ctag1.fdfd596a728f4da1970506244104a45c
2025-08-05 18:16:11,989 - ConnectionManager - INFO - Worker 10288 开始消费个人消息，消费者标签: ctag1.523bab95b4f94381acacfcffadeaa7d3
2025-08-05 18:16:12,078 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 10288)
2025-08-05 18:16:12,165 - distributed_lock - INFO - Worker 10288 成功获取锁: scheduler_initialization
2025-08-05 18:16:12,165 - game_server_scheduler_integration - INFO - Worker 10288 获得调度器初始化权限
2025-08-05 18:16:12,169 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:16:12,169 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:16:12,170 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:16:12,170 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:16:12,171 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:16:12,172 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:16:12,173 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:16:12,173 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:16:12,174 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:16:12,175 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:16:12,175 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:16:12,176 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:16:12,264 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:16:12,265 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:16:12,265 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:16:12,266 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:16:12,582 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:16:12,669 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:16:12,670 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:16:12,670 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:16:12,671 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:16:12,671 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:16:12,671 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:16:12,762 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:16:12,889 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:16:12,889 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:16:12,890 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:16:12,890 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:16:12,891 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:16:12,891 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:16:12,895 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:12,895 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:16:12,899 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:16:12,900 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:12,900 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:16:12,901 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:16:13,034 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,035 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:16:13,037 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:16:13,170 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,170 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:16:13,170 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:16:13,171 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,172 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:16:13,173 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:16:13,173 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:16:13,174 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:16:13,174 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:16:13,174 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:16:13,176 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,177 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,178 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,179 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:16:13,179 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:16:13,180 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:16:13,180 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:16:13,180 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:16:13,181 - game_server_scheduler_integration - INFO - Worker 10288 调度器初始化成功
2025-08-05 18:16:13,227 - game_server - INFO - 统一调度器初始化成功 (Worker: 10288)
2025-08-05 18:16:13,233 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 10288)
2025-08-05 18:16:13,234 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:16:13,234 - game_server - INFO - 日志清理管理器已启动 (Worker: 10288)
2025-08-05 18:16:13,234 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:16:13,235 - game_server - INFO - Monster cooldown manager initialized (Worker: 10288)
2025-08-05 18:16:13,374 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:16:13,374 - game_server - INFO - 公会系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,375 - game_server - INFO - 邮件系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,377 - game_server - INFO - 商店系统初始化成功 (Worker: 10288)
2025-08-05 18:16:13,377 - game_server - INFO - 初始化完成 (Worker: 10288)
2025-08-05 18:16:23,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:33 CST)" (scheduled at 2025-08-05 18:16:23.169939+08:00)
2025-08-05 18:16:24,927 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:24,927 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:25,059 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:25,406 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:25,406 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:25,407 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:25,407 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:25,455 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:33 CST)" executed successfully
2025-08-05 18:16:30,003 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:33,185 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:43 CST)" (scheduled at 2025-08-05 18:16:33.169939+08:00)
2025-08-05 18:16:33,229 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:33,230 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:33,359 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:33,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:33,722 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:16:33,723 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:33,723 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:33,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:43 CST)" executed successfully
2025-08-05 18:16:37,665 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:16:43,035 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:13 CST)" (scheduled at 2025-08-05 18:16:43.034854+08:00)
2025-08-05 18:16:43,078 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:16:43,078 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:43,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:53 CST)" (scheduled at 2025-08-05 18:16:43.169939+08:00)
2025-08-05 18:16:43,208 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:16:43,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:43,223 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:43,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:43,550 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:16:43,551 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:16:43,551 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:16:43,552 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:16:43,552 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:43,596 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:13 CST)" executed successfully
2025-08-05 18:16:43,705 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:43,706 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:43,707 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:43,707 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:43,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:16:53 CST)" executed successfully
2025-08-05 18:16:53,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:03 CST)" (scheduled at 2025-08-05 18:16:53.169939+08:00)
2025-08-05 18:16:53,221 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:16:53,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:16:53,360 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:16:53,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:16:53,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:16:53,713 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:16:53,714 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:16:53,760 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:03 CST)" executed successfully
2025-08-05 18:17:00,242 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:03,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:13 CST)" (scheduled at 2025-08-05 18:17:03.169939+08:00)
2025-08-05 18:17:03,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:17:03,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:03,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:17:03,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:17:03,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:17:03,715 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:17:03,717 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:03,761 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:13 CST)" executed successfully
2025-08-05 18:17:07,673 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:11,899 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:17:11,899 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:17:11,946 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 18:17:11,954 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 18:17:11,955 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 18:17:11,956 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 18:17:12,905 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:18:12 CST)" (scheduled at 2025-08-05 18:17:12.900596+08:00)
2025-08-05 18:17:12,949 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:17:12,950 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:12,950 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:17:12,953 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 18:17:29,358 - player_session_manager - ERROR - 获取在线玩家数量失败: Timeout reading from *************:6379
2025-08-05 18:17:29,359 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:17:43 CST)" was missed by 0:00:16.324994
2025-08-05 18:17:29,360 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:33 CST)" was missed by 0:00:06.190260
2025-08-05 18:17:30,074 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 18:17:30,122 - shop_database_manager - INFO - 商店创建成功: shop_7d7dfb0eaf8b
2025-08-05 18:17:30,212 - shop_service - INFO - 商店创建成功: shop_7d7dfb0eaf8b
2025-08-05 18:17:30,359 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:17:30,361 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:30,372 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:17:30,373 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:17:30,376 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:17:30,378 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 17.43秒
2025-08-05 18:17:30,378 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:17:30,379 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:30,430 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:18:12 CST)" executed successfully
2025-08-05 18:17:33,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:43 CST)" (scheduled at 2025-08-05 18:17:33.169939+08:00)
2025-08-05 18:17:33,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:17:33,227 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:17:33,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:17:33,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:17:56,806 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 23.44秒
2025-08-05 18:17:56,807 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:17:56,807 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:17:56,809 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:17:56,809 - apscheduler.scheduler - WARNING - Execution of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:17:43 CST)" skipped: maximum number of running instances reached (1)
2025-08-05 18:17:56,810 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:18:13 CST)" was missed by 0:00:13.775314
2025-08-05 18:17:56,852 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:03 CST)" executed successfully
2025-08-05 18:18:00,847 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:18:03,173 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:13 CST)" (scheduled at 2025-08-05 18:18:03.169939+08:00)
2025-08-05 18:18:03,218 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:03,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:03,358 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:03,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:03,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:18:03,722 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:03,722 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:03,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:13 CST)" executed successfully
2025-08-05 18:18:11,911 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:18:11,911 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:18:12,907 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:19:12 CST)" (scheduled at 2025-08-05 18:18:12.900596+08:00)
2025-08-05 18:18:12,954 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:18:12,956 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:12,959 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:18:13,046 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:18:43 CST)" (scheduled at 2025-08-05 18:18:13.034854+08:00)
2025-08-05 18:18:13,091 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:18:13,091 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:13,167 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:18:13,168 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:18:13,171 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:23 CST)" (scheduled at 2025-08-05 18:18:13.169939+08:00)
2025-08-05 18:18:13,223 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:18:13,265 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:13,266 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:13,396 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:13,579 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:18:13,580 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:18:13,581 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:18:13,582 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:18:13,583 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:13,630 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:18:43 CST)" executed successfully
2025-08-05 18:18:13,749 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:13,749 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:18:13,750 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:13,751 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:13,799 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:23 CST)" executed successfully
2025-08-05 18:18:14,166 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:18:14,167 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.21秒
2025-08-05 18:18:14,167 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:18:14,168 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:14,211 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:19:12 CST)" executed successfully
2025-08-05 18:18:23,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:33 CST)" (scheduled at 2025-08-05 18:18:23.169939+08:00)
2025-08-05 18:18:23,219 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:23,220 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:23,349 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:23,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:23,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:18:23,707 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:23,708 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:23,751 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:33 CST)" executed successfully
2025-08-05 18:18:26,811 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:18:30,027 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:18:33,181 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:43 CST)" (scheduled at 2025-08-05 18:18:33.169939+08:00)
2025-08-05 18:18:33,225 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:33,225 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:33,360 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:33,715 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:33,715 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:18:33,716 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:33,716 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:33,762 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:43 CST)" executed successfully
2025-08-05 18:18:43,045 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:19:13 CST)" (scheduled at 2025-08-05 18:18:43.034854+08:00)
2025-08-05 18:18:43,089 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:18:43,089 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:43,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:53 CST)" (scheduled at 2025-08-05 18:18:43.169939+08:00)
2025-08-05 18:18:43,220 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:18:43,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:43,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:43,355 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:43,571 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:18:43,571 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:18:43,572 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:18:43,572 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:18:43,573 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:43,619 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:19:13 CST)" executed successfully
2025-08-05 18:18:43,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:43,709 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:18:43,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:43,711 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:43,759 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:18:53 CST)" executed successfully
2025-08-05 18:18:53,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:03 CST)" (scheduled at 2025-08-05 18:18:53.169939+08:00)
2025-08-05 18:18:53,229 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:18:53,230 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:18:53,362 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:18:53,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:18:53,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:18:53,714 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:18:53,714 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:18:53,762 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:03 CST)" executed successfully
2025-08-05 18:18:56,813 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:19:00,224 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:19:03,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:13 CST)" (scheduled at 2025-08-05 18:19:03.169939+08:00)
2025-08-05 18:19:03,219 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:03,219 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:03,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:03,707 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:03,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:19:03,708 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:03,708 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:03,754 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:13 CST)" executed successfully
2025-08-05 18:19:11,925 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:19:11,926 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:19:12,911 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:20:12 CST)" (scheduled at 2025-08-05 18:19:12.900596+08:00)
2025-08-05 18:19:12,957 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:19:12,958 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:12,959 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:19:13,035 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:19:43 CST)" (scheduled at 2025-08-05 18:19:13.034854+08:00)
2025-08-05 18:19:13,082 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:19:13,083 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:13,171 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:23 CST)" (scheduled at 2025-08-05 18:19:13.169939+08:00)
2025-08-05 18:19:13,215 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:13,216 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:13,217 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:19:13,277 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:19:13,278 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:19:13,347 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:13,572 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:19:13,572 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:19:13,575 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:19:13,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:19:13,576 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:13,621 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:19:43 CST)" executed successfully
2025-08-05 18:19:13,695 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:13,695 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:19:13,696 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:13,696 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:13,742 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:23 CST)" executed successfully
2025-08-05 18:19:14,191 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:19:14,191 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.23秒
2025-08-05 18:19:14,192 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:19:14,192 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:14,239 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:20:12 CST)" executed successfully
2025-08-05 18:19:23,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:33 CST)" (scheduled at 2025-08-05 18:19:23.169939+08:00)
2025-08-05 18:19:23,218 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:23,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:23,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:23,698 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:23,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:19:23,701 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:23,703 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:23,762 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:33 CST)" executed successfully
2025-08-05 18:19:26,822 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:19:30,493 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:19:33,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:43 CST)" (scheduled at 2025-08-05 18:19:33.169939+08:00)
2025-08-05 18:19:33,352 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:33,352 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:34,455 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:36,953 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:36,953 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 2.50秒
2025-08-05 18:19:36,954 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:36,956 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:37,051 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:43 CST)" executed successfully
2025-08-05 18:19:43,049 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:20:13 CST)" (scheduled at 2025-08-05 18:19:43.034854+08:00)
2025-08-05 18:19:43,092 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:19:43,093 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:43,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:53 CST)" (scheduled at 2025-08-05 18:19:43.169939+08:00)
2025-08-05 18:19:43,218 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:43,219 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:43,221 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:19:43,357 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:43,577 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:19:43,578 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:19:43,579 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:19:43,579 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:19:43,579 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:43,623 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:20:13 CST)" executed successfully
2025-08-05 18:19:43,712 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:43,713 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:19:43,713 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:43,713 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:43,760 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:19:53 CST)" executed successfully
2025-08-05 18:19:53,181 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:03 CST)" (scheduled at 2025-08-05 18:19:53.169939+08:00)
2025-08-05 18:19:53,226 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:19:53,227 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:19:53,360 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:19:53,724 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:19:53,724 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:19:53,725 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:19:53,725 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:19:53,770 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:03 CST)" executed successfully
2025-08-05 18:19:56,825 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:20:00,719 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:20:03,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:13 CST)" (scheduled at 2025-08-05 18:20:03.169939+08:00)
2025-08-05 18:20:03,228 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:03,228 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:03,363 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:03,721 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:03,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:20:03,721 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:03,722 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:03,768 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:13 CST)" executed successfully
2025-08-05 18:20:11,931 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:20:11,932 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:20:12,913 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:21:12 CST)" (scheduled at 2025-08-05 18:20:12.900596+08:00)
2025-08-05 18:20:12,960 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:20:12,960 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:12,961 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:20:13,037 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:20:43 CST)" (scheduled at 2025-08-05 18:20:13.034854+08:00)
2025-08-05 18:20:13,083 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:20:13,084 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:13,162 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:20:13,162 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:20:13,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:23 CST)" (scheduled at 2025-08-05 18:20:13.169939+08:00)
2025-08-05 18:20:13,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:13,220 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:13,225 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:20:13,349 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:13,580 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:20:13,581 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:20:13,581 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:20:13,582 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:20:13,582 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:13,626 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:20:43 CST)" executed successfully
2025-08-05 18:20:13,697 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:13,697 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:20:13,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:13,698 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:13,748 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:23 CST)" executed successfully
2025-08-05 18:20:14,994 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:20:14,994 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.03秒
2025-08-05 18:20:14,995 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:20:14,995 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:15,043 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:21:12 CST)" executed successfully
2025-08-05 18:20:23,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:33 CST)" (scheduled at 2025-08-05 18:20:23.169939+08:00)
2025-08-05 18:20:23,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:23,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:23,350 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:23,700 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:23,700 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:20:23,700 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:23,701 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:23,746 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:33 CST)" executed successfully
2025-08-05 18:20:26,830 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:20:30,272 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:20:33,178 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:43 CST)" (scheduled at 2025-08-05 18:20:33.169939+08:00)
2025-08-05 18:20:33,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:33,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:33,352 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:33,703 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:33,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:20:33,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:33,705 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:33,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:43 CST)" executed successfully
2025-08-05 18:20:43,038 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:21:13 CST)" (scheduled at 2025-08-05 18:20:43.034854+08:00)
2025-08-05 18:20:43,082 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:20:43,082 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:43,178 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:53 CST)" (scheduled at 2025-08-05 18:20:43.169939+08:00)
2025-08-05 18:20:43,211 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:20:43,223 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:43,225 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:43,361 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:43,556 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:20:43,556 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:20:43,557 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:20:43,557 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:20:43,558 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:43,602 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:21:13 CST)" executed successfully
2025-08-05 18:20:43,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:43,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:20:43,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:43,711 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:43,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:20:53 CST)" executed successfully
2025-08-05 18:20:53,173 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:03 CST)" (scheduled at 2025-08-05 18:20:53.169939+08:00)
2025-08-05 18:20:53,218 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:20:53,220 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:20:53,357 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:20:53,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:20:53,715 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:20:53,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:20:53,719 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:20:53,769 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:03 CST)" executed successfully
2025-08-05 18:20:56,842 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:21:00,488 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:21:03,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:13 CST)" (scheduled at 2025-08-05 18:21:03.169939+08:00)
2025-08-05 18:21:03,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:03,229 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:03,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:03,714 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:03,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:21:03,714 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:03,715 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:03,763 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:13 CST)" executed successfully
2025-08-05 18:21:11,943 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:21:11,944 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:21:12,910 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:22:12 CST)" (scheduled at 2025-08-05 18:21:12.900596+08:00)
2025-08-05 18:21:12,955 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:21:12,955 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:12,958 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:21:13,050 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:21:43 CST)" (scheduled at 2025-08-05 18:21:13.034854+08:00)
2025-08-05 18:21:13,094 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:21:13,096 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:13,172 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:23 CST)" (scheduled at 2025-08-05 18:21:13.169939+08:00)
2025-08-05 18:21:13,182 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:21:13,183 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:21:13,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:13,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:13,243 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:21:13,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:13,595 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:21:13,595 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:21:13,596 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:21:13,596 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:21:13,597 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:13,643 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:21:43 CST)" executed successfully
2025-08-05 18:21:13,693 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:13,694 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:21:13,696 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:13,698 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:13,747 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:23 CST)" executed successfully
2025-08-05 18:21:15,241 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:21:15,242 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.28秒
2025-08-05 18:21:15,243 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:21:15,244 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:15,297 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:22:12 CST)" executed successfully
2025-08-05 18:21:23,180 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:33 CST)" (scheduled at 2025-08-05 18:21:23.169939+08:00)
2025-08-05 18:21:23,225 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:23,226 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:23,359 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:23,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:23,719 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:21:23,719 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:23,721 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:23,769 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:33 CST)" executed successfully
2025-08-05 18:21:26,855 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:21:30,673 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:21:33,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:43 CST)" (scheduled at 2025-08-05 18:21:33.169939+08:00)
2025-08-05 18:21:33,235 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:33,235 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:33,364 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:33,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:33,713 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:21:33,713 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:33,714 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:33,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:43 CST)" executed successfully
2025-08-05 18:21:43,042 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:22:13 CST)" (scheduled at 2025-08-05 18:21:43.034854+08:00)
2025-08-05 18:21:43,085 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:21:43,086 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:43,173 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:53 CST)" (scheduled at 2025-08-05 18:21:43.169939+08:00)
2025-08-05 18:21:43,216 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:21:43,217 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:43,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:43,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:43,562 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:21:43,563 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:21:43,563 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:21:43,564 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:21:43,564 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:43,609 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:22:13 CST)" executed successfully
2025-08-05 18:21:43,704 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:43,704 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:21:43,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:43,706 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:43,757 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:21:53 CST)" executed successfully
2025-08-05 18:21:53,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:03 CST)" (scheduled at 2025-08-05 18:21:53.169939+08:00)
2025-08-05 18:21:53,223 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:21:53,225 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:21:53,360 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:21:53,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:21:53,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:21:53,719 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:21:53,719 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:21:53,764 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:03 CST)" executed successfully
2025-08-05 18:21:56,864 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:22:00,888 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:22:03,173 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:13 CST)" (scheduled at 2025-08-05 18:22:03.169939+08:00)
2025-08-05 18:22:03,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:03,220 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:03,358 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:03,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:03,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:22:03,719 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:03,719 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:03,764 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:13 CST)" executed successfully
2025-08-05 18:22:11,960 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:22:11,960 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:22:12,907 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:23:12 CST)" (scheduled at 2025-08-05 18:22:12.900596+08:00)
2025-08-05 18:22:12,952 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:22:12,953 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:12,953 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:22:13,046 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:22:43 CST)" (scheduled at 2025-08-05 18:22:13.034854+08:00)
2025-08-05 18:22:13,090 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:22:13,091 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:13,179 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:23 CST)" (scheduled at 2025-08-05 18:22:13.169939+08:00)
2025-08-05 18:22:13,186 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:22:13,187 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:22:13,223 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:13,223 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:13,224 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:22:13,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:13,582 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:22:13,582 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:22:13,582 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:22:13,583 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:22:13,585 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:13,630 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:22:43 CST)" executed successfully
2025-08-05 18:22:13,703 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:13,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:22:13,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:13,705 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:13,753 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:23 CST)" executed successfully
2025-08-05 18:22:14,318 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:22:14,318 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.36秒
2025-08-05 18:22:14,318 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:22:14,319 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:14,363 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:23:12 CST)" executed successfully
2025-08-05 18:22:23,171 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:33 CST)" (scheduled at 2025-08-05 18:22:23.169939+08:00)
2025-08-05 18:22:23,216 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:23,217 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:23,352 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:23,706 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:23,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:22:23,707 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:23,708 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:23,755 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:33 CST)" executed successfully
2025-08-05 18:22:26,867 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:22:30,102 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:22:33,170 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:43 CST)" (scheduled at 2025-08-05 18:22:33.169939+08:00)
2025-08-05 18:22:33,217 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:33,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:33,347 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:33,697 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:33,698 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:22:33,698 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:33,698 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:33,742 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:43 CST)" executed successfully
2025-08-05 18:22:43,043 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:23:13 CST)" (scheduled at 2025-08-05 18:22:43.034854+08:00)
2025-08-05 18:22:43,100 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:22:43,101 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:43,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:53 CST)" (scheduled at 2025-08-05 18:22:43.169939+08:00)
2025-08-05 18:22:43,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:43,227 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:43,229 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:22:43,363 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:43,574 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:22:43,575 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:22:43,575 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:22:43,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:22:43,576 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:43,620 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:23:13 CST)" executed successfully
2025-08-05 18:22:43,717 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:43,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:22:43,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:43,719 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:43,767 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:22:53 CST)" executed successfully
2025-08-05 18:22:53,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:03 CST)" (scheduled at 2025-08-05 18:22:53.169939+08:00)
2025-08-05 18:22:53,219 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:22:53,219 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:22:53,352 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:22:53,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:22:53,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:22:53,711 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:22:53,712 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:22:53,756 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:03 CST)" executed successfully
2025-08-05 18:22:56,881 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:23:00,285 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:23:03,170 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:13 CST)" (scheduled at 2025-08-05 18:23:03.169939+08:00)
2025-08-05 18:23:03,216 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:03,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:03,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:03,707 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:03,707 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:03,707 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:03,708 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:03,765 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:13 CST)" executed successfully
2025-08-05 18:23:11,968 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:23:11,974 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:23:12,911 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:24:12 CST)" (scheduled at 2025-08-05 18:23:12.900596+08:00)
2025-08-05 18:23:12,956 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:23:12,956 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:12,958 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:23:13,049 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:23:43 CST)" (scheduled at 2025-08-05 18:23:13.034854+08:00)
2025-08-05 18:23:13,096 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:23:13,099 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:13,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:23 CST)" (scheduled at 2025-08-05 18:23:13.169939+08:00)
2025-08-05 18:23:13,218 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:13,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:13,224 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:23:13,224 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:23:13,246 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:23:13,347 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:13,596 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:23:13,596 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:23:13,596 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:23:13,597 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:23:13,597 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:13,645 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:23:43 CST)" executed successfully
2025-08-05 18:23:13,696 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:13,696 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:13,697 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:13,697 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:13,743 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:23 CST)" executed successfully
2025-08-05 18:23:13,887 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:23:13,888 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.93秒
2025-08-05 18:23:13,888 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:23:13,889 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:13,933 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:24:12 CST)" executed successfully
2025-08-05 18:23:23,180 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:33 CST)" (scheduled at 2025-08-05 18:23:23.169939+08:00)
2025-08-05 18:23:23,225 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:23,225 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:23,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:23,702 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:23,702 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:23,703 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:23,703 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:23,747 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:33 CST)" executed successfully
2025-08-05 18:23:26,888 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:23:30,512 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:23:33,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:43 CST)" (scheduled at 2025-08-05 18:23:33.169939+08:00)
2025-08-05 18:23:33,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:33,225 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:33,374 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:33,722 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:33,722 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:33,723 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:33,724 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:33,769 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:43 CST)" executed successfully
2025-08-05 18:23:43,047 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:24:13 CST)" (scheduled at 2025-08-05 18:23:43.034854+08:00)
2025-08-05 18:23:43,092 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:23:43,093 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:43,171 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:53 CST)" (scheduled at 2025-08-05 18:23:43.169939+08:00)
2025-08-05 18:23:43,216 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:43,218 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:43,233 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:23:43,351 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:43,584 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:23:43,585 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:23:43,585 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:23:43,586 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:23:43,586 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:43,633 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:24:13 CST)" executed successfully
2025-08-05 18:23:43,700 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:43,700 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:43,702 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:43,703 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:43,749 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:23:53 CST)" executed successfully
2025-08-05 18:23:53,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:03 CST)" (scheduled at 2025-08-05 18:23:53.169939+08:00)
2025-08-05 18:23:53,228 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:23:53,229 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:23:53,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:23:53,718 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:23:53,718 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:23:53,718 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:23:53,719 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:23:53,764 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:03 CST)" executed successfully
2025-08-05 18:23:56,891 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:24:00,683 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:24:03,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:13 CST)" (scheduled at 2025-08-05 18:24:03.169939+08:00)
2025-08-05 18:24:03,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:03,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:03,357 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:03,712 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:03,712 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:24:03,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:03,713 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:03,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:13 CST)" executed successfully
2025-08-05 18:24:11,984 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:24:11,986 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:24:12,910 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:25:12 CST)" (scheduled at 2025-08-05 18:24:12.900596+08:00)
2025-08-05 18:24:12,962 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:24:12,962 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:12,963 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:24:13,050 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:24:43 CST)" (scheduled at 2025-08-05 18:24:13.034854+08:00)
2025-08-05 18:24:13,095 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:24:13,096 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:13,174 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:23 CST)" (scheduled at 2025-08-05 18:24:13.169939+08:00)
2025-08-05 18:24:13,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:13,220 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:13,231 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:24:13,265 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:24:13,265 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:24:13,349 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:13,585 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:24:13,585 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:24:13,586 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:24:13,587 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:24:13,587 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:13,631 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:24:43 CST)" executed successfully
2025-08-05 18:24:13,701 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:13,702 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:24:13,703 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:13,704 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:13,749 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:23 CST)" executed successfully
2025-08-05 18:24:13,906 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:24:13,907 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.94秒
2025-08-05 18:24:13,908 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:24:13,909 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:13,953 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:25:12 CST)" executed successfully
2025-08-05 18:24:23,184 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:33 CST)" (scheduled at 2025-08-05 18:24:23.169939+08:00)
2025-08-05 18:24:23,356 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:23,357 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:23,657 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:24,457 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:24,458 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.80秒
2025-08-05 18:24:24,458 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:24,458 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:24,557 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:33 CST)" executed successfully
2025-08-05 18:24:26,906 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:24:30,851 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:24:33,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:43 CST)" (scheduled at 2025-08-05 18:24:33.169939+08:00)
2025-08-05 18:24:33,226 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:33,226 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:33,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:33,701 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:33,702 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:24:33,702 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:33,702 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:33,749 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:43 CST)" executed successfully
2025-08-05 18:24:43,037 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:25:13 CST)" (scheduled at 2025-08-05 18:24:43.034854+08:00)
2025-08-05 18:24:43,081 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:24:43,081 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:43,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:53 CST)" (scheduled at 2025-08-05 18:24:43.169939+08:00)
2025-08-05 18:24:43,211 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:24:43,221 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:43,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:43,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:43,563 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:24:43,564 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:24:43,565 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:24:43,566 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:24:43,567 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:43,610 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:25:13 CST)" executed successfully
2025-08-05 18:24:43,713 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:43,714 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:24:43,716 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:43,718 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:43,770 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:24:53 CST)" executed successfully
2025-08-05 18:24:53,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:03 CST)" (scheduled at 2025-08-05 18:24:53.169939+08:00)
2025-08-05 18:24:53,228 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:24:53,230 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:24:53,559 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:24:54,220 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:24:54,220 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.66秒
2025-08-05 18:24:54,221 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:24:54,225 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:24:54,380 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:03 CST)" executed successfully
2025-08-05 18:24:56,920 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:25:00,029 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:25:03,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:13 CST)" (scheduled at 2025-08-05 18:25:03.169939+08:00)
2025-08-05 18:25:03,231 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:03,232 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:03,368 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:03,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:03,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:25:03,721 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:03,722 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:03,770 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:13 CST)" executed successfully
2025-08-05 18:25:11,989 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:25:11,991 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:25:12,905 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:26:12 CST)" (scheduled at 2025-08-05 18:25:12.900596+08:00)
2025-08-05 18:25:12,950 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:25:12,950 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:12,951 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:25:13,048 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:25:43 CST)" (scheduled at 2025-08-05 18:25:13.034854+08:00)
2025-08-05 18:25:13,095 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:25:13,095 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:13,168 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:25:13,169 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:25:13,172 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:23 CST)" (scheduled at 2025-08-05 18:25:13.169939+08:00)
2025-08-05 18:25:13,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:13,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:13,229 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:25:13,350 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:13,581 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:25:13,582 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:25:13,582 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:25:13,583 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:25:13,583 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:13,628 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:25:43 CST)" executed successfully
2025-08-05 18:25:13,699 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:13,699 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:25:13,700 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:13,700 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:13,744 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:23 CST)" executed successfully
2025-08-05 18:25:15,074 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:25:15,074 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.12秒
2025-08-05 18:25:15,075 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:25:15,075 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:15,122 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:26:12 CST)" executed successfully
2025-08-05 18:25:23,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:33 CST)" (scheduled at 2025-08-05 18:25:23.169939+08:00)
2025-08-05 18:25:23,232 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:23,233 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:23,363 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:23,719 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:23,720 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:25:23,722 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:23,724 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:23,773 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:33 CST)" executed successfully
2025-08-05 18:25:26,921 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:25:30,256 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:25:33,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:43 CST)" (scheduled at 2025-08-05 18:25:33.169939+08:00)
2025-08-05 18:25:33,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:33,222 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:33,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:33,710 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:33,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:25:33,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:33,711 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:33,766 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:43 CST)" executed successfully
2025-08-05 18:25:43,046 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:26:13 CST)" (scheduled at 2025-08-05 18:25:43.034854+08:00)
2025-08-05 18:25:43,090 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:25:43,090 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:43,176 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:53 CST)" (scheduled at 2025-08-05 18:25:43.169939+08:00)
2025-08-05 18:25:43,220 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:25:43,222 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:43,223 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:43,356 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:43,585 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:25:43,585 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:25:43,586 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 18:25:43,586 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:25:43,586 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:43,632 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:26:13 CST)" executed successfully
2025-08-05 18:25:43,725 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:43,725 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 18:25:43,726 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:43,727 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:43,774 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:25:53 CST)" executed successfully
2025-08-05 18:25:53,183 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:03 CST)" (scheduled at 2025-08-05 18:25:53.169939+08:00)
2025-08-05 18:25:53,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:25:53,228 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:25:53,359 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:25:53,712 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:25:53,712 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:25:53,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:25:53,713 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:25:53,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:03 CST)" executed successfully
2025-08-05 18:25:56,930 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:26:00,482 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:26:03,180 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:13 CST)" (scheduled at 2025-08-05 18:26:03.169939+08:00)
2025-08-05 18:26:03,227 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:03,228 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:03,373 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:03,724 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:03,724 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:26:03,724 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:03,725 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:03,772 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:13 CST)" executed successfully
2025-08-05 18:26:12,002 - ConnectionManager - INFO - Redis连接池状态 (Worker 10288): 使用中=2, 可用=2, 总计=4
2025-08-05 18:26:12,003 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 10288): 连接中
2025-08-05 18:26:12,902 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:27:12 CST)" (scheduled at 2025-08-05 18:26:12.900596+08:00)
2025-08-05 18:26:12,959 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:push_online
2025-08-05 18:26:12,959 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:12,959 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行推送在线人数任务
2025-08-05 18:26:13,040 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:26:43 CST)" (scheduled at 2025-08-05 18:26:13.034854+08:00)
2025-08-05 18:26:13,086 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:26:13,087 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:13,179 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:23 CST)" (scheduled at 2025-08-05 18:26:13.169939+08:00)
2025-08-05 18:26:13,190 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:26:13,190 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:26:13,223 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:13,223 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:13,224 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:26:13,352 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:13,576 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:26:13,576 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:26:13,577 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 18:26:13,577 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:26:13,578 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:13,622 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:26:43 CST)" executed successfully
2025-08-05 18:26:13,703 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:13,704 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:26:13,704 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:13,707 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:13,751 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:23 CST)" executed successfully
2025-08-05 18:26:14,522 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 在线人数推送完成，当前在线: 0
2025-08-05 18:26:14,523 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.56秒
2025-08-05 18:26:14,530 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:26:14,535 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:14,581 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:27:12 CST)" executed successfully
2025-08-05 18:26:23,177 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:33 CST)" (scheduled at 2025-08-05 18:26:23.169939+08:00)
2025-08-05 18:26:23,221 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:23,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:23,353 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:23,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:23,711 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:26:23,712 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:23,712 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:23,759 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:33 CST)" executed successfully
2025-08-05 18:26:26,943 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:26:30,684 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:26:33,172 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:43 CST)" (scheduled at 2025-08-05 18:26:33.169939+08:00)
2025-08-05 18:26:33,216 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:33,217 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:33,352 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:33,702 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:33,703 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:26:33,705 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:33,705 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:33,749 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:43 CST)" executed successfully
2025-08-05 18:26:43,045 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:27:13 CST)" (scheduled at 2025-08-05 18:26:43.034854+08:00)
2025-08-05 18:26:43,088 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:26:43,089 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:43,175 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:53 CST)" (scheduled at 2025-08-05 18:26:43.169939+08:00)
2025-08-05 18:26:43,218 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 开始执行怪物冷却持久化任务
2025-08-05 18:26:43,220 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:43,221 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:43,354 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:43,574 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:26:43,575 - scheduler_tasks_unified - INFO - [定时任务] Worker 10288 怪物冷却持久化完成
2025-08-05 18:26:43,575 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 18:26:43,576 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:26:43,576 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:43,619 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:27:13 CST)" executed successfully
2025-08-05 18:26:43,720 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:43,721 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 18:26:43,724 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:43,727 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:43,777 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:26:53 CST)" executed successfully
2025-08-05 18:26:53,173 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:27:03 CST)" (scheduled at 2025-08-05 18:26:53.169939+08:00)
2025-08-05 18:26:53,217 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:26:53,217 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:26:53,350 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:26:53,709 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:26:53,710 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 18:26:53,710 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:26:53,711 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:26:53,758 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:27:03 CST)" executed successfully
2025-08-05 18:26:56,950 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:27:00,908 - ConnectionManager - INFO - 连接状态 (Worker 10288): 活跃连接数=0, 用户数=0
2025-08-05 18:27:03,182 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:27:13 CST)" (scheduled at 2025-08-05 18:27:03.169939+08:00)
2025-08-05 18:27:03,232 - distributed_lock - INFO - Worker 10288 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:27:03,232 - distributed_task - INFO - Worker 10288 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:27:03,366 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:27:03,732 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:27:03,732 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 18:27:03,732 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:27:03,733 - distributed_task - INFO - Worker 10288 任务执行完成: direct_wrapper
2025-08-05 18:27:03,781 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:27:13 CST)" executed successfully
2025-08-05 18:27:08,877 - game_server - INFO - 关闭服务器... (Worker: 10288)
2025-08-05 18:27:08,880 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 18:27:09,240 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:27:09,240 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 18:27:09,241 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 18:27:09,241 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 18:27:09,242 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 18:27:09,242 - game_server - INFO - 日志清理管理器已停止
2025-08-05 18:27:09,242 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 18:27:09,243 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 18:27:09,243 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 18:27:09,246 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 18:27:09,247 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 18:27:09,247 - game_server - INFO - 统一调度器已关闭
2025-08-05 18:27:09,247 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 18:27:09,249 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 10288)
2025-08-05 18:27:09,295 - game_server - INFO - 服务器资源已清理 (Worker: 10288)
