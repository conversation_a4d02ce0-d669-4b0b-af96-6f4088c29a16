server {
    listen 80;
    server_name *************;

    #      HTTP     
    location / {
        proxy_pass http://app:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log debug;
	client_max_body_size 10m;
        # CORS     
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "*" always;

        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "*";
            return 204;
        }
    }
    location /api/schedule/ {
        proxy_pass http://schedulesystem:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /game_server/ {
        proxy_pass http://game_server:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    # WebSocket ֧  
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }
    #     Ŀǰ  
    location /schedule/ {
       alias /www/server/fastapi/schedulesystem/;
       index index.html;
       try_files $uri $uri/ /index.html;
       access_log /var/log/nginx/schedule_access.log;
       error_log /var/log/nginx/schedule_error.log debug;
    }
    # WebSocket 配置 - app服务
    location /ws {
        proxy_pass http://app:8000;
        proxy_http_version 1.1;              # WebSocket 需要 HTTP/1.1
        proxy_set_header Upgrade $http_upgrade;  # 支持 WebSocket 升级
        proxy_set_header Connection "Upgrade";   # 连接升级设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 86400;  # 保持长连接
    }
}