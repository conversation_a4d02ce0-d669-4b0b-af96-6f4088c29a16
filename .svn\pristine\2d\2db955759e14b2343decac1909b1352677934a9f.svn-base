import functools
import asyncio
from typing import Callable, Any
from distributed_lock import DistributedLock

# 需要传入 redis_client，建议在注册任务时通过 partial 绑定

def distributed_task(redis_client, lock_key: str = None, ttl: int = 30):
    """
    分布式定时任务装饰器。
    :param redis_client: Redis 客户端
    :param lock_key: 锁的唯一key（默认用函数名）
    :param ttl: 锁超时时间（秒）
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            key = lock_key or f"lock:scheduled:{func.__name__}"
            lock = DistributedLock(redis_client, key, ttl)
            try:
                async with lock:
                    return await func(*args, **kwargs)
            except TimeoutError:
                # 未获取到锁，直接跳过
                return None
        return wrapper
    return decorator 