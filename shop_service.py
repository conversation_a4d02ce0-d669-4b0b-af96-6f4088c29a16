"""
商店主服务 - 统一入口
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from shop_database_manager import ShopDatabaseManager
from shop_cache_service import ShopCacheService
from shop_limit_service import ShopLimitService
from shop_discount_service import ShopDiscountService
from shop_purchase_service import ShopPurchaseService
from shop_models import (
    Shop, ShopItemConfig, ShopDiscount, ShopType,
    PurchaseRequest, PurchaseResult, PriceInfo, LimitCheckResult
)

logger = logging.getLogger(__name__)


class ShopService:
    """商店主服务"""
    
    def __init__(self):
        self.db_manager = ShopDatabaseManager()
        self.cache_service = ShopCacheService()
        self.limit_service = ShopLimitService()
        self.discount_service = ShopDiscountService()
        self.purchase_service = ShopPurchaseService()
    
    def set_external_services(self, currency_service, player_service):
        """设置外部服务依赖"""
        self.purchase_service.set_external_services(currency_service, player_service)
    
    # ==================== 商店管理 ====================
    
    async def create_shop(self, shop_data: Dict[str, Any]) -> bool:
        """创建商店"""
        try:
            shop = Shop(
                shop_id=self.db_manager.generate_shop_id(),
                shop_name=shop_data["shop_name"],
                shop_type=shop_data["shop_type"],
                description=shop_data.get("description", ""),
                icon=shop_data.get("icon", ""),
                is_active=shop_data.get("is_active", True),
                access_conditions=shop_data.get("access_conditions", {}),
                refresh_config=shop_data.get("refresh_config"),
                sort_order=shop_data.get("sort_order", 0),
                ui_config=shop_data.get("ui_config", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            success = await self.db_manager.create_shop(shop)
            if success:
                await self.cache_service.cache_shop_config(shop)
                logger.info(f"商店创建成功: {shop.shop_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建商店时发生错误: {str(e)}")
            return False
    
    async def get_shop(self, shop_id: str) -> Optional[Shop]:
        """获取商店信息"""
        try:
            # 先从缓存获取
            shop = await self.cache_service.get_cached_shop_config(shop_id)
            if shop:
                return shop
            
            # 从数据库获取
            shop = await self.db_manager.get_shop(shop_id)
            if shop:
                await self.cache_service.cache_shop_config(shop)
            
            return shop
            
        except Exception as e:
            logger.error(f"获取商店信息时发生错误: {str(e)}")
            return None
    
    async def get_available_shops(self, player_id: str) -> List[Shop]:
        """获取玩家可访问的商店列表"""
        try:
            # 获取所有激活的商店
            all_shops = []
            for shop_type in [ShopType.NORMAL, ShopType.GUILD, ShopType.VIP, ShopType.EVENT, ShopType.ARENA]:
                shops = await self.db_manager.get_shops_by_type(shop_type, is_active=True)
                all_shops.extend(shops)

            # 过滤玩家可访问的商店
            available_shops = []
            for shop in all_shops:
                if await self._check_shop_access(shop, player_id):
                    available_shops.append(shop)

            # 按排序权重排序
            available_shops.sort(key=lambda x: x.sort_order)

            return available_shops

        except Exception as e:
            logger.error(f"获取可访问商店列表时发生错误: {str(e)}")
            return []

    async def get_all_shops(self) -> List[Dict[str, Any]]:
        """获取所有商店列表 - 管理接口"""
        try:
            # 获取所有商店（包括未激活的）
            all_shops = []
            for shop_type in [ShopType.NORMAL, ShopType.GUILD, ShopType.VIP, ShopType.EVENT, ShopType.ARENA]:
                shops = await self.db_manager.get_shops_by_type(shop_type, is_active=None)
                all_shops.extend(shops)

            # 按排序权重排序
            all_shops.sort(key=lambda x: x.sort_order)

            # 转换为字典格式
            shops_data = []
            for shop in all_shops:
                shop_dict = shop.to_dict()
                shops_data.append(shop_dict)

            return shops_data

        except Exception as e:
            logger.error(f"获取所有商店列表时发生错误: {str(e)}")
            return []

    async def get_shop_detail(self, shop_id: str) -> Optional[Dict[str, Any]]:
        """获取商店详情 - 管理接口"""
        try:
            shop = await self.get_shop(shop_id)
            if shop:
                return shop.to_dict()
            return None

        except Exception as e:
            logger.error(f"获取商店详情时发生错误: {str(e)}")
            return None

    async def update_shop(self, shop_id: str, update_data: Dict[str, Any]) -> bool:
        """更新商店 - 管理接口"""
        try:
            # 获取现有商店
            shop = await self.get_shop(shop_id)
            if not shop:
                logger.error(f"商店不存在: {shop_id}")
                return False

            # 更新字段
            for key, value in update_data.items():
                if hasattr(shop, key):
                    setattr(shop, key, value)

            # 更新时间戳
            shop.updated_at = datetime.now()

            # 保存到数据库
            success = await self.db_manager.update_shop(shop)
            if success:
                # 更新缓存
                await self.cache_service.cache_shop_config(shop)
                logger.info(f"商店更新成功: {shop_id}")

            return success

        except Exception as e:
            logger.error(f"更新商店时发生错误: {str(e)}")
            return False

    async def delete_shop(self, shop_id: str) -> bool:
        """删除商店 - 管理接口"""
        try:
            # 检查商店是否存在
            shop = await self.get_shop(shop_id)
            if not shop:
                logger.error(f"商店不存在: {shop_id}")
                return False

            # 删除商店的所有商品配置
            await self.db_manager.delete_shop_item_configs(shop_id)

            # 删除商店
            success = await self.db_manager.delete_shop(shop_id)
            if success:
                # 尝试清除缓存，如果失败不影响删除操作
                try:
                    await self.cache_service.clear_shop_cache(shop_id)
                    logger.debug(f"商店缓存清除成功: {shop_id}")
                except Exception as cache_error:
                    logger.warning(f"清除商店缓存失败，但删除操作成功: {shop_id}, 错误: {str(cache_error)}")

                logger.info(f"商店删除成功: {shop_id}")

            return success

        except Exception as e:
            logger.error(f"删除商店时发生错误: {str(e)}")
            return False
    
    async def _check_shop_access(self, shop: Shop, player_id: str) -> bool:
        """检查商店访问权限"""
        try:
            conditions = shop.access_conditions
            
            # 检查玩家等级
            if conditions.get("player_level"):
                # 这里需要调用玩家服务获取等级
                # player_level = await self.player_service.get_player_level(player_id)
                # if player_level < conditions["player_level"]:
                #     return False
                pass
            
            # 检查VIP等级
            if conditions.get("vip_level"):
                # vip_level = await self.player_service.get_vip_level(player_id)
                # if vip_level < conditions["vip_level"]:
                #     return False
                pass
            
            # 检查公会等级
            if conditions.get("guild_level"):
                # guild_level = await self.player_service.get_guild_level(player_id)
                # if guild_level < conditions["guild_level"]:
                #     return False
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"检查商店访问权限时发生错误: {str(e)}")
            return False
    
    # ==================== 商品管理 ====================
    
    async def create_item_config(self, config_data: Dict[str, Any]) -> bool:
        """创建商品配置"""
        try:
            config = ShopItemConfig(
                config_id=self.db_manager.generate_config_id(
                    config_data["shop_id"],
                    config_data["item_template_id"]
                ),
                shop_id=config_data["shop_id"],
                slot_id=config_data.get("slot_id"),
                item_template_id=config_data["item_template_id"],
                item_type=config_data.get("item_type", "item"),
                item_quantity=config_data.get("item_quantity", 1),
                item_quality=config_data.get("item_quality"),
                price_config=config_data["price_config"],
                purchase_limit=config_data.get("purchase_limit"),
                availability=config_data.get("availability", {}),
                refresh_weight=config_data.get("refresh_weight", 1),
                refresh_probability=config_data.get("refresh_probability", 1.0),
                is_active=config_data.get("is_active", True),
                sort_order=config_data.get("sort_order", 0),
                display_config=config_data.get("display_config", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            success = await self.db_manager.create_item_config(config)
            if success:
                # 清除商店商品缓存
                await self.cache_service.invalidate_shop_items(config.shop_id)
                logger.info(f"商品配置创建成功: {config.config_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建商品配置时发生错误: {str(e)}")
            return False
    
    async def get_shop_items(self, shop_id: str, player_id: str) -> List[ShopItemConfig]:
        """获取商店商品列表"""
        try:
            # 先从缓存获取
            items = await self.cache_service.get_cached_shop_items(shop_id)
            if items is None:
                # 从数据库获取
                items = await self.db_manager.get_shop_item_configs(shop_id, is_active=True)
                if items:
                    await self.cache_service.cache_shop_items(shop_id, items)

            # 过滤玩家可购买的商品
            available_items = []
            for item in items:
                if await self._check_item_access(item, player_id):
                    available_items.append(item)

            return available_items

        except Exception as e:
            logger.error(f"获取商店商品列表时发生错误: {str(e)}")
            return []

    async def update_item_config(self, config_id: str, update_data: Dict[str, Any]) -> bool:
        """更新商品配置 - 管理接口"""
        try:
            # 获取现有配置
            config = await self.db_manager.get_item_config(config_id)
            if not config:
                logger.error(f"商品配置不存在: {config_id}")
                return False

            # 更新字段
            for key, value in update_data.items():
                if hasattr(config, key):
                    setattr(config, key, value)

            # 更新时间戳
            config.updated_at = datetime.now()

            # 保存到数据库
            success = await self.db_manager.update_item_config(config)
            if success:
                # 清除相关缓存
                await self.cache_service.invalidate_shop_items(config.shop_id)
                logger.info(f"商品配置更新成功: {config_id}")

            return success

        except Exception as e:
            logger.error(f"更新商品配置时发生错误: {str(e)}")
            return False

    async def delete_item_config(self, config_id: str) -> bool:
        """删除商品配置 - 管理接口"""
        try:
            # 获取现有配置
            config = await self.db_manager.get_item_config(config_id)
            if not config:
                logger.error(f"商品配置不存在: {config_id}")
                return False

            # 删除配置
            success = await self.db_manager.delete_item_config(config_id)
            if success:
                # 清除相关缓存
                await self.cache_service.invalidate_shop_items(config.shop_id)
                logger.info(f"商品配置删除成功: {config_id}")

            return success

        except Exception as e:
            logger.error(f"删除商品配置时发生错误: {str(e)}")
            return False
    
    async def _check_item_access(self, config: ShopItemConfig, player_id: str) -> bool:
        """检查商品访问权限"""
        try:
            now = datetime.now()
            
            # 检查时间限制
            availability = config.availability
            if availability.get("start_time"):
                start_time = datetime.fromisoformat(availability["start_time"])
                if now < start_time:
                    return False
            
            if availability.get("end_time"):
                end_time = datetime.fromisoformat(availability["end_time"])
                if now > end_time:
                    return False
            
            # 检查其他条件
            conditions = availability.get("conditions", {})
            if conditions.get("player_level"):
                # 检查玩家等级
                pass
            
            if conditions.get("vip_level"):
                # 检查VIP等级
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"检查商品访问权限时发生错误: {str(e)}")
            return False
    
    async def get_item_detail(self, config_id: str, player_id: str) -> Optional[Dict[str, Any]]:
        """获取商品详情"""
        try:
            config = await self.db_manager.get_item_config(config_id)
            if not config:
                return None
            
            # 检查访问权限
            if not await self._check_item_access(config, player_id):
                return None
            
            # 计算价格
            price_info = await self.discount_service.calculate_final_price(
                player_id, config_id, 1
            )
            
            # 检查限购状态
            limit_check = await self.limit_service.check_purchase_limit(
                player_id, config_id, 1
            )
            
            return {
                "config": config,
                "price_info": price_info,
                "limit_status": limit_check,
                "can_purchase": limit_check.can_purchase and limit_check.remaining > 0
            }
            
        except Exception as e:
            logger.error(f"获取商品详情时发生错误: {str(e)}")
            return None
    
    # ==================== 购买相关 ====================
    
    async def purchase_item(self, player_id: str, config_id: str, quantity: int, metadata: Optional[Dict] = None) -> PurchaseResult:
        """购买商品"""
        request = PurchaseRequest(
            player_id=player_id,
            config_id=config_id,
            quantity=quantity,
            metadata=metadata
        )
        
        return await self.purchase_service.purchase_item(request)
    
    async def preview_purchase(self, player_id: str, config_id: str, quantity: int) -> Dict[str, Any]:
        """预览购买 - 计算价格和检查条件"""
        try:
            # 验证购买条件
            request = PurchaseRequest(player_id=player_id, config_id=config_id, quantity=quantity)
            validation = await self.purchase_service.validate_purchase(request)
            
            if not validation.success:
                return {
                    "can_purchase": False,
                    "error": validation.error
                }
            
            config = validation.details["config"]
            price_info = validation.details["price_info"]
            limit_check = validation.details["limit_check"]
            
            return {
                "can_purchase": True,
                "item_name": config.item_template_id,  # 这里应该从道具模板获取名称
                "quantity": quantity,
                "original_price": price_info.original_price,
                "final_price": price_info.final_price,
                "currency_type": price_info.currency_type,
                "discount_amount": price_info.discount_amount,
                "discounts_applied": price_info.discounts_applied,
                "limit_remaining": limit_check.remaining,
                "limit_type": limit_check.limit_type
            }
            
        except Exception as e:
            logger.error(f"预览购买时发生错误: {str(e)}")
            return {
                "can_purchase": False,
                "error": f"预览失败: {str(e)}"
            }
    
    async def get_purchase_history(self, player_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取购买历史"""
        try:
            purchases = await self.purchase_service.get_purchase_history(player_id, limit, offset)
            
            # 转换为前端友好的格式
            history = []
            for purchase in purchases:
                history.append({
                    "purchase_id": purchase.purchase_id,
                    "shop_id": purchase.shop_id,
                    "item_template_id": purchase.item_template_id,
                    "quantity": purchase.item_quantity,
                    "currency_type": purchase.currency_type,
                    "final_price": purchase.final_price,
                    "purchase_time": purchase.purchase_time.isoformat(),
                    "discounts_applied": purchase.discount_applied
                })
            
            return history
            
        except Exception as e:
            logger.error(f"获取购买历史时发生错误: {str(e)}")
            return []
    
    # ==================== 限购管理 ====================
    
    async def get_player_limits(self, player_id: str) -> Dict[str, Any]:
        """获取玩家限购状态"""
        try:
            daily_status = await self.limit_service.get_player_limit_status(player_id, "daily")
            weekly_status = await self.limit_service.get_player_limit_status(player_id, "weekly")
            monthly_status = await self.limit_service.get_player_limit_status(player_id, "monthly")
            
            return {
                "player_id": player_id,
                "daily": daily_status,
                "weekly": weekly_status,
                "monthly": monthly_status
            }
            
        except Exception as e:
            logger.error(f"获取玩家限购状态时发生错误: {str(e)}")
            return {}
    
    async def reset_limits(self, limit_type: str) -> bool:
        """重置限购"""
        return await self.limit_service.trigger_global_reset(limit_type)
    
    # ==================== 折扣管理 ====================
    
    async def create_discount(self, discount_data: Dict[str, Any]) -> bool:
        """创建折扣"""
        try:
            discount = ShopDiscount(
                discount_id=self.db_manager.generate_discount_id(),
                discount_name=discount_data["discount_name"],
                scope_type=discount_data["scope_type"],
                scope_values=discount_data.get("scope_values", []),
                discount_rule=discount_data["discount_rule"],
                conditions=discount_data.get("conditions", {}),
                priority=discount_data.get("priority", 0),
                mutex_groups=discount_data.get("mutex_groups", []),
                stackable=discount_data.get("stackable", False),
                usage_stats={"total_used": 0, "total_saved": 0},
                is_active=discount_data.get("is_active", True),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return await self.discount_service.create_discount(discount)
            
        except Exception as e:
            logger.error(f"创建折扣时发生错误: {str(e)}")
            return False
    
    # ==================== 统计分析 ====================
    
    async def get_shop_statistics(self, shop_id: str, days: int = 7) -> Dict[str, Any]:
        """获取商店统计信息"""
        try:
            sales_stats = await self.db_manager.get_shop_sales_stats(shop_id, days)
            
            return {
                "shop_id": shop_id,
                "period_days": days,
                "sales_data": sales_stats,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取商店统计信息时发生错误: {str(e)}")
            return {}
