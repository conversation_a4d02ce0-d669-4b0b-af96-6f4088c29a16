[{"id": 1, "pro_id": 1, "key": "att", "val": 20, "rand": 5}, {"id": 2, "pro_id": 1, "key": "pow", "val": 80, "rand": 20}, {"id": 3, "pro_id": 1, "key": "con", "val": 50, "rand": 15}, {"id": 4, "pro_id": 1, "key": "nd", "val": 15, "rand": 5}, {"id": 5, "pro_id": 2, "key": "att", "val": 5}, {"id": 6, "pro_id": 2, "key": "int", "val": 100, "rand": 25}, {"id": 7, "pro_id": 2, "key": "con", "val": 30, "rand": 6}, {"id": 8, "pro_id": 2, "key": "m_att", "val": 10, "rand": 3}, {"id": 9, "pro_id": 2, "key": "sk", "val": 4161}, {"id": 10, "pro_id": 3, "key": "att", "val": 100, "rand": 20}, {"id": 11, "pro_id": 3, "key": "dex", "val": 100, "rand": 20}, {"id": 12, "pro_id": 3, "key": "sk", "val": 4162}, {"id": 13, "pro_id": 4, "key": "att", "val": 30, "rand": 6}, {"id": 14, "pro_id": 4, "key": "pow", "val": 120, "rand": 30}, {"id": 15, "pro_id": 4, "key": "con", "val": 40, "rand": 10}, {"id": 16, "pro_id": 4, "key": "bk", "val": 10, "rand": 5}, {"id": 17, "pro_id": 4, "key": "sk", "val": 4163}, {"id": 18, "pro_id": 5, "key": "att", "val": 100, "rand": 20}, {"id": 19, "pro_id": 5, "key": "pow", "val": 100, "rand": 20}, {"id": 20, "pro_id": 5, "key": "con", "val": 60, "rand": 15}, {"id": 21, "pro_id": 5, "key": "sk", "val": 4164}, {"id": 22, "pro_id": 6, "key": "att", "val": 60, "rand": 15}, {"id": 23, "pro_id": 6, "key": "m_att", "val": 60, "rand": 15}, {"id": 24, "pro_id": 6, "key": "pow", "val": 100, "rand": 20}, {"id": 25, "pro_id": 6, "key": "int", "val": 100, "rand": 20}, {"id": 26, "pro_id": 6, "key": "sk", "val": 4165}, {"id": 27, "pro_id": 7, "key": "dex", "val": 200, "rand": 40}, {"id": 28, "pro_id": 7, "key": "con", "val": 100, "rand": 20}, {"id": 29, "pro_id": 7, "key": "red", "val": 10, "rand": 5}, {"id": 30, "pro_id": 7, "key": "sk", "val": 4166}, {"id": 31, "pro_id": 8, "key": "int", "val": 80, "rand": 15}, {"id": 32, "pro_id": 8, "key": "con", "val": 50, "rand": 10}, {"id": 33, "pro_id": 8, "key": "m_att", "val": 20, "rand": 4}, {"id": 34, "pro_id": 8, "key": "int_p", "val": 10, "rand": 5}, {"id": 35, "pro_id": 9, "key": "int", "val": 200, "rand": 40}, {"id": 36, "pro_id": 9, "key": "red", "val": 5}, {"id": 37, "pro_id": 9, "key": "int_p", "val": 5, "rand": 5}, {"id": 38, "pro_id": 10, "key": "int", "val": 100, "rand": 20}, {"id": 39, "pro_id": 10, "key": "trt", "val": 100, "rand": 20}, {"id": 40, "pro_id": 10, "key": "con", "val": 40, "rand": 10}, {"id": 41, "pro_id": 10, "key": "trtad", "val": 20, "rand": 5}, {"id": 42, "pro_id": 11, "key": "con", "val": 180, "rand": 30}, {"id": 43, "pro_id": 11, "key": "int", "val": 40, "rand": 10}, {"id": 44, "pro_id": 11, "key": "pow", "val": 40, "rand": 10}, {"id": 45, "pro_id": 11, "key": "sk", "val": 4167}, {"id": 46, "pro_id": 12, "key": "att", "val": 80, "rand": 15}, {"id": 47, "pro_id": 12, "key": "m_att", "val": 80, "rand": 15}, {"id": 48, "pro_id": 12, "key": "sk", "val": 4168}, {"id": 49, "pro_id": 13, "key": "def", "val": 300, "rand": 60}, {"id": 50, "pro_id": 13, "key": "red", "val": 10, "rand": 5}, {"id": 51, "pro_id": 13, "key": "con", "val": 120, "rand": 40}, {"id": 52, "pro_id": 13, "key": "cirt_red", "val": 30, "rand": 10}, {"id": 53, "pro_id": 14, "key": "def", "val": 200, "rand": 40}, {"id": 54, "pro_id": 14, "key": "con", "val": 100, "rand": 20}, {"id": 55, "pro_id": 14, "key": "sk", "val": 4169}, {"id": 56, "pro_id": 15, "key": "def", "val": 280, "rand": 50}, {"id": 57, "pro_id": 15, "key": "con", "val": 100, "rand": 20}, {"id": 58, "pro_id": 15, "key": "red", "val": 20, "rand": 5}, {"id": 59, "pro_id": 16, "key": "def", "val": 250, "rand": 50}, {"id": 60, "pro_id": 16, "key": "con", "val": 120, "rand": 25}, {"id": 61, "pro_id": 16, "key": "sk", "val": 4170}, {"id": 62, "pro_id": 17, "key": "def", "val": 200, "rand": 40}, {"id": 63, "pro_id": 17, "key": "con", "val": 200, "rand": 40}, {"id": 64, "pro_id": 17, "key": "trtpd", "val": 25, "rand": 8}, {"id": 65, "pro_id": 18, "key": "def", "val": 500, "rand": 100}, {"id": 66, "pro_id": 18, "key": "con_p", "val": 10, "rand": 5}, {"id": 67, "pro_id": 18, "key": "sk", "val": 4171}, {"id": 68, "pro_id": 19, "key": "def", "val": 200, "rand": 40}, {"id": 69, "pro_id": 19, "key": "dex", "val": 200, "rand": 40}, {"id": 70, "pro_id": 19, "key": "con", "val": 60, "rand": 15}, {"id": 71, "pro_id": 19, "key": "sk", "val": 4172}, {"id": 72, "pro_id": 20, "key": "sk", "val": 4176}, {"id": 73, "pro_id": 20, "key": "int_p", "val": 10, "rand": 5}, {"id": 74, "pro_id": 20, "key": "int", "val": 150, "rand": 30}, {"id": 75, "pro_id": 20, "key": "con", "val": 30, "rand": 6}, {"id": 76, "pro_id": 21, "key": "sk", "val": 4231}, {"id": 77, "pro_id": 21, "key": "dmg_p", "val": 20, "rand": 10}, {"id": 78, "pro_id": 21, "key": "pow", "val": 150, "rand": 30}, {"id": 79, "pro_id": 21, "key": "con", "val": 100, "rand": 25}, {"id": 80, "pro_id": 21, "key": "dex", "val": 200, "rand": 50}, {"id": 81, "pro_id": 22, "key": "sk", "val": 4241}, {"id": 82, "pro_id": 22, "key": "pow", "val": 150, "rand": 30}, {"id": 83, "pro_id": 22, "key": "pow_p", "val": 10, "rand": 5}, {"id": 84, "pro_id": 22, "key": "cirt_odd", "val": 5, "rand": 5}, {"id": 85, "pro_id": 22, "key": "cirt_hit", "val": 10, "rand": 10}, {"id": 86, "pro_id": 23, "key": "sk", "val": 4242}, {"id": 87, "pro_id": 23, "key": "int", "val": 200, "rand": 50}, {"id": 88, "pro_id": 23, "key": "cirt_hit", "val": 20, "rand": 10}, {"id": 89, "pro_id": 23, "key": "con", "val": 100, "rand": 30}, {"id": 90, "pro_id": 24, "key": "sk", "val": 4313}, {"id": 91, "pro_id": 24, "key": "cirt_odd", "val": 5, "rand": 10}, {"id": 92, "pro_id": 24, "key": "int", "val": 200, "rand": 50}, {"id": 93, "pro_id": 24, "key": "dmg_p", "val": 5, "rand": 10}, {"id": 94, "pro_id": 24, "key": "con", "val": 100, "rand": 30}, {"id": 95, "pro_id": 25, "key": "sk", "val": 4314}, {"id": 96, "pro_id": 25, "key": "bk", "val": 10, "rand": 10}, {"id": 97, "pro_id": 25, "key": "cirt_hit", "val": 10, "rand": 10}, {"id": 98, "pro_id": 25, "key": "pow", "val": 200, "rand": 50}, {"id": 99, "pro_id": 25, "key": "dex", "val": 200, "rand": 50}, {"id": 100, "pro_id": 26, "key": "sk", "val": 4335}, {"id": 101, "pro_id": 26, "key": "int", "val": 200, "rand": 50}, {"id": 102, "pro_id": 26, "key": "int_p", "val": 10, "rand": 10}, {"id": 103, "pro_id": 26, "key": "cirt_hit", "val": 5, "rand": 10}, {"id": 104, "pro_id": 26, "key": "mdmg_p", "val": 10, "rand": 10}, {"id": 105, "pro_id": 26, "key": "con", "val": 100, "rand": 30}, {"id": 106, "pro_id": 27, "key": "sk", "val": 4336}, {"id": 107, "pro_id": 27, "key": "pow", "val": 200, "rand": 50}, {"id": 108, "pro_id": 27, "key": "cirt_odd", "val": 5, "rand": 10}, {"id": 109, "pro_id": 27, "key": "dex", "val": 150, "rand": 100}, {"id": 110, "pro_id": 27, "key": "con", "val": 100, "rand": 50}, {"id": 111, "pro_id": 27, "key": "akl_p", "val": 10, "rand": 10}, {"id": 112, "pro_id": 28, "key": "trtad", "val": 10, "rand": 20}, {"id": 113, "pro_id": 28, "key": "int", "val": 200, "rand": 50}, {"id": 114, "pro_id": 28, "key": "trt", "val": 100, "rand": 100}, {"id": 115, "pro_id": 28, "key": "con", "val": 100, "rand": 50}, {"id": 116, "pro_id": 28, "key": "sk", "val": 4395}, {"id": 117, "pro_id": 29, "key": "red", "val": 10, "rand": 20}, {"id": 118, "pro_id": 29, "key": "dex", "val": 200, "rand": 100}, {"id": 119, "pro_id": 29, "key": "con", "val": 150, "rand": 100}, {"id": 120, "pro_id": 29, "key": "pskl", "val": 2, "rand": 3}, {"id": 121, "pro_id": 29, "key": "sk", "val": 4499}, {"id": 122, "pro_id": 30, "key": "red", "val": 15, "rand": 20}, {"id": 123, "pro_id": 30, "key": "con", "val": 200, "rand": 100}, {"id": 124, "pro_id": 30, "key": "def", "val": 200, "rand": 100}, {"id": 125, "pro_id": 30, "key": "sk", "val": 4500}, {"id": 126, "pro_id": 31, "key": "pow", "val": 200, "rand": 100}, {"id": 127, "pro_id": 31, "key": "cirt_hit", "val": 10, "rand": 30}, {"id": 128, "pro_id": 31, "key": "con", "val": 20, "rand": 50}, {"id": 129, "pro_id": 31, "key": "sk", "val": 4501}, {"id": 130, "pro_id": 32, "key": "red", "val": 10, "rand": 5}, {"id": 131, "pro_id": 32, "key": "pcrit", "val": 5, "rand": 10}, {"id": 132, "pro_id": 32, "key": "def", "val": 200, "rand": 100}, {"id": 133, "pro_id": 32, "key": "con", "val": 200, "rand": 100}, {"id": 134, "pro_id": 33, "key": "int", "val": 200, "rand": 100}, {"id": 135, "pro_id": 33, "key": "dex", "val": 100, "rand": 100}, {"id": 136, "pro_id": 33, "key": "int_p", "val": 10, "rand": 5}, {"id": 137, "pro_id": 33, "key": "con", "val": 100, "rand": 100}, {"id": 138, "pro_id": 33, "key": "mmaskl", "val": 2, "rand": 8}, {"id": 139, "pro_id": 34, "key": "dex", "val": 200, "rand": 100}, {"id": 140, "pro_id": 34, "key": "def", "val": 200, "rand": 100}, {"id": 141, "pro_id": 34, "key": "def_p", "val": 5, "rand": 10}, {"id": 142, "pro_id": 34, "key": "con", "val": 200, "rand": 100}, {"id": 143, "pro_id": 34, "key": "pred", "val": 15, "rand": 20}, {"id": 144, "pro_id": 35, "key": "dex", "val": 200, "rand": 100}, {"id": 145, "pro_id": 35, "key": "trtpd", "val": 10, "rand": 20}, {"id": 146, "pro_id": 35, "key": "con", "val": 200, "rand": 100}, {"id": 147, "pro_id": 35, "key": "tps", "val": 5, "rand": 15}, {"id": 148, "pro_id": 36, "key": "dex", "val": 200, "rand": 100}, {"id": 149, "pro_id": 36, "key": "int", "val": 200, "rand": 100}, {"id": 150, "pro_id": 36, "key": "con", "val": 200, "rand": 100}, {"id": 151, "pro_id": 36, "key": "int_p", "val": 5, "rand": 10}, {"id": 152, "pro_id": 36, "key": "mdmg_p", "val": 10, "rand": 15}, {"id": 153, "pro_id": 37, "key": "dex", "val": 200, "rand": 100}, {"id": 154, "pro_id": 37, "key": "pow", "val": 200, "rand": 100}, {"id": 155, "pro_id": 37, "key": "con", "val": 200, "rand": 100}, {"id": 156, "pro_id": 37, "key": "pow_p", "val": 5, "rand": 10}, {"id": 157, "pro_id": 37, "key": "pdmg_p", "val": 10, "rand": 15}, {"id": 158, "pro_id": 38, "key": "con", "val": 50, "rand": 100}, {"id": 159, "pro_id": 38, "key": "con_p", "val": 5, "rand": 5}, {"id": 160, "pro_id": 38, "key": "def", "val": 50, "rand": 20}, {"id": 161, "pro_id": 39, "key": "cirt_odd", "val": 5, "rand": 10}, {"id": 162, "pro_id": 39, "key": "cirt_hit", "val": 10, "rand": 20}, {"id": 163, "pro_id": 39, "key": "con_p", "val": 10, "rand": 20}, {"id": 164, "pro_id": 39, "key": "pow", "val": 200, "rand": 100}, {"id": 165, "pro_id": 39, "key": "sk", "val": 4535}, {"id": 166, "pro_id": 40, "key": "pow", "val": 200, "rand": 100}, {"id": 167, "pro_id": 40, "key": "pdmg_p", "val": 10, "rand": 15}, {"id": 168, "pro_id": 40, "key": "cirt_hit", "val": 5, "rand": 10}, {"id": 169, "pro_id": 40, "key": "con_p", "val": 10, "rand": 15}, {"id": 170, "pro_id": 40, "key": "sk", "val": 4546}, {"id": 171, "pro_id": 41, "key": "int", "val": 200, "rand": 100}, {"id": 172, "pro_id": 41, "key": "int_p", "val": 10, "rand": 10}, {"id": 173, "pro_id": 41, "key": "mdmg_p", "val": 10, "rand": 20}, {"id": 174, "pro_id": 41, "key": "con", "val": 100, "rand": 50}, {"id": 175, "pro_id": 41, "key": "sk", "val": 4547}, {"id": 176, "pro_id": 42, "key": "int", "val": 200, "rand": 100}, {"id": 177, "pro_id": 42, "key": "cirt_odd", "val": 5, "rand": 10}, {"id": 178, "pro_id": 42, "key": "mdmg_p", "val": 10, "rand": 15}, {"id": 179, "pro_id": 42, "key": "con", "val": 50, "rand": 100}, {"id": 180, "pro_id": 42, "key": "sk", "val": 4548}, {"id": 181, "pro_id": 43, "key": "dex", "val": 300, "rand": 300}, {"id": 182, "pro_id": 43, "key": "pow", "val": 100, "rand": 100}, {"id": 183, "pro_id": 43, "key": "con", "val": 100, "rand": 100}, {"id": 184, "pro_id": 43, "key": "pow_p", "val": 5, "rand": 10}, {"id": 185, "pro_id": 43, "key": "pdmg_p", "val": 10, "rand": 10}, {"id": 186, "pro_id": 43, "key": "sk", "val": 4559}, {"id": 187, "pro_id": 44, "key": "int", "val": 200, "rand": 100}, {"id": 188, "pro_id": 44, "key": "con", "val": 200, "rand": 100}, {"id": 189, "pro_id": 44, "key": "pkl_p", "val": 20, "rand": 20}, {"id": 190, "pro_id": 44, "key": "cirt_odd", "val": 5, "rand": 10}, {"id": 191, "pro_id": 44, "key": "sk", "val": 4561}, {"id": 192, "pro_id": 45, "key": "con", "val": 200, "rand": 200}, {"id": 193, "pro_id": 45, "key": "con_p", "val": 20, "rand": 20}, {"id": 194, "pro_id": 45, "key": "red", "val": 10, "rand": 10}, {"id": 195, "pro_id": 45, "key": "def", "val": 200, "rand": 200}, {"id": 196, "pro_id": 45, "key": "sk", "val": 4560}, {"id": 197, "pro_id": 46, "key": "sk", "val": 4572}, {"id": 198, "pro_id": 46, "key": "int", "val": 200, "rand": 100}, {"id": 199, "pro_id": 46, "key": "int_p", "val": 10, "rand": 10}, {"id": 200, "pro_id": 46, "key": "mdmg_p", "val": 10, "rand": 20}, {"id": 201, "pro_id": 46, "key": "con", "val": 100, "rand": 50}, {"id": 202, "pro_id": 47, "key": "pow", "val": 200, "rand": 100}, {"id": 203, "pro_id": 47, "key": "pow_p", "val": 10, "rand": 15}, {"id": 204, "pro_id": 47, "key": "con", "val": 100, "rand": 100}, {"id": 205, "pro_id": 47, "key": "sk", "val": 4575}, {"id": 206, "pro_id": 48, "key": "pow", "val": 200, "rand": 100}, {"id": 207, "pro_id": 48, "key": "nd", "val": 20, "rand": 30}, {"id": 208, "pro_id": 48, "key": "pow_p", "val": 10, "rand": 10}, {"id": 209, "pro_id": 48, "key": "con", "val": 100, "rand": 100}, {"id": 210, "pro_id": 48, "key": "sk", "val": 4576}, {"id": 211, "pro_id": 49, "key": "pow", "val": 200, "rand": 100}, {"id": 212, "pro_id": 49, "key": "con", "val": 200, "rand": 100}, {"id": 213, "pro_id": 49, "key": "con_p", "val": 10, "rand": 30}, {"id": 214, "pro_id": 49, "key": "dmg_p", "val": 5, "rand": 10}, {"id": 215, "pro_id": 49, "key": "sk", "val": 4597}, {"id": 216, "pro_id": 50, "key": "int", "val": 200, "rand": 100}, {"id": 217, "pro_id": 50, "key": "trt", "val": 200, "rand": 100}, {"id": 218, "pro_id": 50, "key": "con_p", "val": 10, "rand": 20}, {"id": 219, "pro_id": 50, "key": "trtad", "val": 20, "rand": 20}, {"id": 220, "pro_id": 50, "key": "sk", "val": 4598}, {"id": 221, "pro_id": 51, "key": "int", "val": 200, "rand": 200}, {"id": 222, "pro_id": 51, "key": "mdmg_p", "val": 20, "rand": 20}, {"id": 223, "pro_id": 51, "key": "con", "val": 100, "rand": 100}, {"id": 224, "pro_id": 51, "key": "sk", "val": 4599}, {"id": 225, "pro_id": 52, "key": "int", "val": 200, "rand": 200}, {"id": 226, "pro_id": 52, "key": "int_p", "val": 10, "rand": 20}, {"id": 227, "pro_id": 52, "key": "mdmg_p", "val": 10, "rand": 20}, {"id": 228, "pro_id": 52, "key": "con", "val": 200, "rand": 100}, {"id": 229, "pro_id": 52, "key": "sk", "val": 4600}, {"id": 230, "pro_id": 53, "key": "int", "val": 200, "rand": 200}, {"id": 231, "pro_id": 53, "key": "con_p", "val": 20, "rand": 20}, {"id": 232, "pro_id": 53, "key": "sk", "val": 4601}, {"id": 233, "pro_id": 53, "key": "dmg_p", "val": 10, "rand": 10}, {"id": 234, "pro_id": 54, "key": "pow", "val": 200, "rand": 100}, {"id": 235, "pro_id": 54, "key": "pow_p", "val": 10, "rand": 10}, {"id": 236, "pro_id": 54, "key": "con_p", "val": 10, "rand": 5}, {"id": 237, "pro_id": 54, "key": "def_p", "val": 10, "rand": 5}, {"id": 238, "pro_id": 55, "key": "con_p", "val": 30, "rand": 10}, {"id": 239, "pro_id": 55, "key": "con", "val": 100, "rand": 200}, {"id": 240, "pro_id": 55, "key": "def", "val": 100, "rand": 100}, {"id": 241, "pro_id": 55, "key": "red", "val": 5, "rand": 5}, {"id": 242, "pro_id": 56, "key": "con", "val": 100, "rand": 200}, {"id": 243, "pro_id": 56, "key": "con_p", "val": 20, "rand": 10}, {"id": 244, "pro_id": 56, "key": "int", "val": 100, "rand": 200}, {"id": 245, "pro_id": 56, "key": "sk", "val": 4643}, {"id": 246, "pro_id": 57, "key": "red", "val": 10, "rand": 20}, {"id": 247, "pro_id": 57, "key": "con_p", "val": 10, "rand": 20}, {"id": 248, "pro_id": 57, "key": "def", "val": 100, "rand": 200}, {"id": 249, "pro_id": 57, "key": "con", "val": 100, "rand": 200}, {"id": 250, "pro_id": 57, "key": "sk", "val": 4644}, {"id": 251, "pro_id": 58, "key": "int", "val": 100, "rand": 200}, {"id": 252, "pro_id": 58, "key": "dmg_p", "val": 10, "rand": 10}, {"id": 253, "pro_id": 58, "key": "con_p", "val": 10, "rand": 10}, {"id": 254, "pro_id": 58, "key": "mdmg_p", "val": 5, "rand": 10}, {"id": 255, "pro_id": 58, "key": "sk", "val": 4645}, {"id": 256, "pro_id": 59, "key": "int", "val": 100, "rand": 200}, {"id": 257, "pro_id": 59, "key": "int_p", "val": 20, "rand": 30}, {"id": 258, "pro_id": 59, "key": "mdmg_p", "val": 5, "rand": 10}, {"id": 259, "pro_id": 59, "key": "sk", "val": 4646}, {"id": 260, "pro_id": 60, "key": "trt", "val": 100, "rand": 200}, {"id": 261, "pro_id": 60, "key": "trtad", "val": 10, "rand": 20}, {"id": 262, "pro_id": 60, "key": "con", "val": 100, "rand": 200}, {"id": 263, "pro_id": 60, "key": "con_p", "val": 5, "rand": 10}, {"id": 264, "pro_id": 60, "key": "sk", "val": 4647}, {"id": 265, "pro_id": 61, "key": "pow", "val": 100, "rand": 200}, {"id": 266, "pro_id": 61, "key": "con", "val": 100, "rand": 200}, {"id": 267, "pro_id": 61, "key": "dmg_p", "val": 10, "rand": 5}, {"id": 268, "pro_id": 61, "key": "sk", "val": 4648}, {"id": 269, "pro_id": 62, "key": "pow", "val": 100, "rand": 200}, {"id": 270, "pro_id": 62, "key": "pow_p", "val": 5, "rand": 10}, {"id": 271, "pro_id": 62, "key": "con", "val": 100, "rand": 200}, {"id": 272, "pro_id": 62, "key": "sk", "val": 4651}, {"id": 273, "pro_id": 63, "key": "int", "val": 100, "rand": 200}, {"id": 274, "pro_id": 63, "key": "con", "val": 100, "rand": 200}, {"id": 275, "pro_id": 63, "key": "int_p", "val": 10, "rand": 20}, {"id": 276, "pro_id": 63, "key": "sk", "val": 4650}, {"id": 277, "pro_id": 64, "key": "red", "val": 20, "rand": 20}, {"id": 278, "pro_id": 64, "key": "con", "val": 100, "rand": 200}, {"id": 279, "pro_id": 64, "key": "con_p", "val": 10, "rand": 20}, {"id": 280, "pro_id": 64, "key": "sk", "val": 4652}, {"id": 281, "pro_id": 65, "key": "pow", "val": 100, "rand": 100}, {"id": 282, "pro_id": 65, "key": "con", "val": 100, "rand": 100}, {"id": 283, "pro_id": 65, "key": "sk", "val": 4653}, {"id": 284, "pro_id": 67, "key": "int", "val": 200, "rand": 100}, {"id": 285, "pro_id": 67, "key": "int_p", "val": 10, "rand": 10}, {"id": 286, "pro_id": 67, "key": "con_p", "val": 10, "rand": 5}, {"id": 287, "pro_id": 67, "key": "def_p", "val": 10, "rand": 5}, {"id": 288, "pro_id": 66, "key": "con_p", "val": 30, "rand": 10}, {"id": 289, "pro_id": 66, "key": "con", "val": 100, "rand": 200}, {"id": 290, "pro_id": 66, "key": "trt", "val": 100, "rand": 100}, {"id": 291, "pro_id": 66, "key": "trtad", "val": 5, "rand": 5}, {"id": 292, "pro_id": 68, "key": "pow", "val": 200, "rand": 100}, {"id": 293, "pro_id": 68, "key": "dex", "val": 100, "rand": 100}, {"id": 294, "pro_id": 68, "key": "pow_p", "val": 10, "rand": 5}, {"id": 295, "pro_id": 68, "key": "con", "val": 100, "rand": 100}, {"id": 296, "pro_id": 68, "key": "amaskl", "val": 2, "rand": 8}, {"id": 297, "pro_id": 69, "key": "pow", "val": 200, "rand": 100}, {"id": 298, "pro_id": 69, "key": "cirt_odd", "val": 5, "rand": 5}, {"id": 299, "pro_id": 69, "key": "con", "val": 200, "rand": 100}, {"id": 300, "pro_id": 69, "key": "dmg_p", "val": 5, "rand": 10}, {"id": 301, "pro_id": 69, "key": "sk", "val": 4698}, {"id": 302, "pro_id": 70, "key": "pow", "val": 200, "rand": 100}, {"id": 303, "pro_id": 70, "key": "pow_p", "val": 10, "rand": 10}, {"id": 304, "pro_id": 70, "key": "cirt_hit", "val": 10, "rand": 20}, {"id": 305, "pro_id": 70, "key": "con", "val": 200, "rand": 100}, {"id": 306, "pro_id": 70, "key": "sk", "val": 4696}, {"id": 307, "pro_id": 71, "key": "int", "val": 200, "rand": 100}, {"id": 308, "pro_id": 71, "key": "int_p", "val": 10, "rand": 10}, {"id": 309, "pro_id": 71, "key": "con", "val": 200, "rand": 100}, {"id": 310, "pro_id": 71, "key": "con_p", "val": 10, "rand": 10}, {"id": 311, "pro_id": 71, "key": "sk", "val": 4697}, {"id": 312, "pro_id": 72, "key": "int", "val": 200, "rand": 100}, {"id": 313, "pro_id": 72, "key": "int_p", "val": 10, "rand": 10}, {"id": 314, "pro_id": 72, "key": "con", "val": 200, "rand": 100}, {"id": 315, "pro_id": 72, "key": "red", "val": 10, "rand": 20}, {"id": 316, "pro_id": 72, "key": "sk", "val": 4699}, {"id": 317, "pro_id": 73, "key": "int", "val": 200, "rand": 100}, {"id": 318, "pro_id": 73, "key": "mdmg_p", "val": 10, "rand": 10}, {"id": 319, "pro_id": 73, "key": "con", "val": 200, "rand": 100}, {"id": 320, "pro_id": 73, "key": "red", "val": 10, "rand": 5}, {"id": 321, "pro_id": 73, "key": "sk", "val": 4703}, {"id": 322, "pro_id": 74, "key": "int", "val": 200, "rand": 100}, {"id": 323, "pro_id": 74, "key": "int_p", "val": 10, "rand": 10}, {"id": 324, "pro_id": 74, "key": "mdmg_p", "val": 5, "rand": 10}, {"id": 325, "pro_id": 74, "key": "cirt_hit", "val": 10, "rand": 10}, {"id": 326, "pro_id": 74, "key": "con", "val": 100, "rand": 100}, {"id": 327, "pro_id": 74, "key": "sk", "val": 4704}, {"id": 328, "pro_id": 75, "key": "dex", "val": 200, "rand": 200}, {"id": 329, "pro_id": 75, "key": "dex_p", "val": 20, "rand": 20}, {"id": 330, "pro_id": 75, "key": "con", "val": 200, "rand": 100}, {"id": 331, "pro_id": 75, "key": "dmg_p", "val": 5, "rand": 10}, {"id": 332, "pro_id": 75, "key": "pkl_p", "val": 10, "rand": 10}, {"id": 333, "pro_id": 75, "key": "sk", "val": 4725}, {"id": 334, "pro_id": 76, "key": "con", "val": 200, "rand": 100}, {"id": 335, "pro_id": 76, "key": "con_p", "val": 20, "rand": 20}, {"id": 336, "pro_id": 76, "key": "red", "val": 20, "rand": 20}, {"id": 337, "pro_id": 76, "key": "trtad", "val": 20, "rand": 20}, {"id": 338, "pro_id": 76, "key": "def", "val": 200, "rand": 100}, {"id": 339, "pro_id": 76, "key": "sk", "val": 4726}, {"id": 340, "pro_id": 77, "key": "con", "val": 200, "rand": 100}, {"id": 341, "pro_id": 77, "key": "con_p", "val": 20, "rand": 20}, {"id": 342, "pro_id": 77, "key": "red", "val": 20, "rand": 20}, {"id": 343, "pro_id": 77, "key": "trtad", "val": 20, "rand": 20}, {"id": 344, "pro_id": 77, "key": "shield_p", "val": 15, "rand": 15}, {"id": 345, "pro_id": 77, "key": "sk", "val": 4727}, {"id": 346, "pro_id": 78, "key": "int", "val": 200, "rand": 100}, {"id": 347, "pro_id": 78, "key": "int_p", "val": 20, "rand": 20}, {"id": 348, "pro_id": 78, "key": "mdmg_p", "val": 10, "rand": 10}, {"id": 349, "pro_id": 78, "key": "con", "val": 200, "rand": 100}, {"id": 350, "pro_id": 78, "key": "sk", "val": 4728}, {"id": 351, "pro_id": 79, "key": "con", "val": 200, "rand": 100}, {"id": 352, "pro_id": 79, "key": "con_p", "val": 20, "rand": 20}, {"id": 353, "pro_id": 79, "key": "red", "val": 20, "rand": 20}, {"id": 354, "pro_id": 79, "key": "def", "val": 200, "rand": 200}, {"id": 355, "pro_id": 79, "key": "sk", "val": 4729}, {"id": 356, "pro_id": 80, "key": "pow", "val": 200, "rand": 100}, {"id": 357, "pro_id": 80, "key": "dmg_p", "val": 20, "rand": 20}, {"id": 358, "pro_id": 80, "key": "cirt_hit", "val": 5, "rand": 10}, {"id": 359, "pro_id": 80, "key": "con", "val": 200, "rand": 100}, {"id": 360, "pro_id": 80, "key": "sk", "val": 4730}, {"id": 361, "pro_id": 81, "key": "pow", "val": 300, "rand": 150}, {"id": 362, "pro_id": 81, "key": "pow_p", "val": 15, "rand": 15}, {"id": 363, "pro_id": 81, "key": "con_p", "val": 20, "rand": 10}, {"id": 364, "pro_id": 81, "key": "pdmg_p", "val": 10, "rand": 10}, {"id": 365, "pro_id": 82, "key": "con_p", "val": 40, "rand": 20}, {"id": 366, "pro_id": 82, "key": "con", "val": 250, "rand": 250}, {"id": 367, "pro_id": 82, "key": "def", "val": 100, "rand": 100}, {"id": 368, "pro_id": 82, "key": "red", "val": 10, "rand": 5}, {"id": 369, "pro_id": 84, "key": "int", "val": 300, "rand": 200}, {"id": 370, "pro_id": 84, "key": "int_p", "val": 20, "rand": 20}, {"id": 371, "pro_id": 84, "key": "con_p", "val": 20, "rand": 8}, {"id": 372, "pro_id": 84, "key": "def_p", "val": 10, "rand": 10}, {"id": 373, "pro_id": 83, "key": "con_p", "val": 40, "rand": 20}, {"id": 374, "pro_id": 83, "key": "con", "val": 200, "rand": 200}, {"id": 375, "pro_id": 83, "key": "trt", "val": 200, "rand": 200}, {"id": 376, "pro_id": 83, "key": "trtad", "val": 15, "rand": 10}, {"id": 377, "pro_id": 85, "key": "def", "val": 300, "rand": 200}, {"id": 378, "pro_id": 85, "key": "red", "val": 8, "rand": 8}, {"id": 379, "pro_id": 85, "key": "con_p", "val": 20, "rand": 20}, {"id": 380, "pro_id": 85, "key": "def_p", "val": 40, "rand": 20}]