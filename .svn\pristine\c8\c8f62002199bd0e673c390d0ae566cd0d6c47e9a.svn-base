/* 商店管理系统 - 组件样式文件 */

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    min-height: 36px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--bg-primary);
    color: var(--gray-700);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-300);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: #047857;
    border-color: #047857;
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    min-height: 28px;
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    min-height: 44px;
}

/* 图标 */
.icon-plus::before { content: "➕"; }
.icon-refresh::before { content: "🔄"; }
.icon-edit::before { content: "✏️"; }
.icon-delete::before { content: "🗑️"; }
.icon-view::before { content: "👁️"; }
.icon-settings::before { content: "⚙️"; }

/* 增强的商店卡片 */
.shop-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.shop-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.shop-card.active {
    border-left: 4px solid var(--green-500);
}

.shop-card.inactive {
    border-left: 4px solid var(--red-500);
    opacity: 0.85;
}

.shop-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
}

.shop-info {
    flex: 1;
}

.shop-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.shop-id {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-family: monospace;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.shop-status {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-end;
}

.status-badge {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    min-width: 80px;
    justify-content: center;
}

.status-badge.active {
    background: linear-gradient(135deg, var(--green-500), var(--green-600));
    color: white;
}

.status-badge.inactive {
    background: linear-gradient(135deg, var(--red-500), var(--red-600));
    color: white;
}

.shop-type-badge {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
}

.sort-order-badge {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    background: var(--gray-50);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.shop-type {
    padding: var(--spacing-1) var(--spacing-2);
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.shop-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    line-height: 1.5;
}

.shop-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.shop-actions {
    display: flex;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-top: 1px solid var(--border-color);
    margin: var(--spacing-4) calc(-1 * var(--spacing-6)) calc(-1 * var(--spacing-6));
}

.shop-actions .btn {
    flex: 1;
    font-size: var(--font-size-xs);
}

/* 商店分区样式 */
.shop-section {
    margin-bottom: var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.shop-details {
    padding: var(--spacing-3);
}

.shop-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.shop-detail-row:last-child {
    border-bottom: none;
}

.shop-detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.shop-detail-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 600;
}

.shop-detail-value.status-active {
    color: var(--green-600);
}

.shop-detail-value.status-inactive {
    color: var(--red-600);
}

/* 时间信息样式 */
.shop-time-info {
    padding: var(--spacing-3);
}

.time-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.time-item:last-child {
    border-bottom: none;
}

.time-value {
    font-family: var(--font-mono);
    font-size: var(--font-size-xs);
    color: var(--text-primary);
    font-weight: 600;
}

/* 状态切换开关样式 */
.shop-status-toggle {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.toggle-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.toggle-switch {
    position: relative;
    display: inline-block;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 34px;
    background-color: var(--red-500);
    border-radius: 34px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    z-index: 2;
}

.toggle-input:checked + .toggle-slider {
    background-color: var(--green-500);
}

.toggle-input:checked + .toggle-slider:before {
    transform: translateX(86px);
}

.toggle-text {
    color: white;
    font-size: var(--font-size-sm);
    font-weight: 700;
    z-index: 1;
    position: relative;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    letter-spacing: 0.5px;
}

/* 悬停效果增强 */
.toggle-slider:hover {
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2), 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.toggle-slider:hover .toggle-text {
    text-shadow: 0 1px 3px rgba(0,0,0,0.7);
}

/* 激活状态的文字优化 - 绿色背景上的白色文字 */
.toggle-input:checked + .toggle-slider {
    background: linear-gradient(135deg, #10b981, #059669);
}

.toggle-input:checked + .toggle-slider .toggle-text {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8);
    font-weight: 700;
}

/* 禁用状态的文字优化 - 红色背景上的白色文字 */
.toggle-input:not(:checked) + .toggle-slider {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.toggle-input:not(:checked) + .toggle-slider .toggle-text {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8);
    font-weight: 700;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .toggle-text {
        color: #ffffff !important;
        text-shadow: 0 0 0 #000000, 0 0 0 #000000, 0 0 0 #000000, 0 0 0 #000000 !important;
        font-weight: 900 !important;
    }

    .toggle-input:checked + .toggle-slider {
        background: #059669 !important;
        border: 2px solid #ffffff;
    }

    .toggle-input:not(:checked) + .toggle-slider {
        background: #dc2626 !important;
        border: 2px solid #ffffff;
    }
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.2s ease-out;
}

.modal-small .modal-content {
    max-width: 400px;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

/* 优化关闭按钮样式 - 更明显易用，避免误操作 */
.modal-close {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: 50%;
    transition: var(--transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.modal-close:hover {
    color: var(--red-600);
    background-color: var(--red-50);
    border-color: var(--red-300);
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(220,53,69,0.2);
}

.modal-close:active {
    transform: scale(0.95);
}

/* 为关闭按钮添加悬停提示 */
.modal-close:hover::after {
    content: "关闭 (ESC)";
    position: absolute;
    top: -35px;
    right: 0;
    background: var(--gray-900);
    color: white;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1000;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 模态框提示信息 */
.modal-hint {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-style: italic;
}

/* 按钮组 */
.modal-buttons {
    display: flex;
    gap: var(--spacing-3);
}

/* 表单组件 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-section {
    margin-top: var(--spacing-6);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--border-color);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

/* 复选框 */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.checkmark {
    font-size: var(--font-size-sm);
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 80px;
    right: var(--spacing-6);
    z-index: 1100;
    max-width: 400px;
}

.message {
    padding: var(--spacing-4);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
    box-shadow: var(--shadow-md);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message.success {
    background-color: #dcfce7;
    color: #166534;
    border-left: 4px solid var(--success-color);
}

.message.error {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.message.warning {
    background-color: #fefce8;
    color: #a16207;
    border-left: 4px solid var(--warning-color);
}

.message.info {
    background-color: #f0f9ff;
    color: #0c4a6e;
    border-left: 4px solid var(--info-color);
}

.warning-text {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--primary-hover);
}

.breadcrumb-separator {
    color: var(--gray-400);
}

/* 商品卡片 */
.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-4);
}

.item-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.item-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.item-card.active {
    border-left: 4px solid var(--green-500);
}

.item-card.inactive {
    border-left: 4px solid var(--red-500);
    opacity: 0.85;
}

.item-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-3);
}

.item-info {
    flex: 1;
}

.item-template-id {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.item-config-id {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-family: monospace;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    margin-bottom: var(--spacing-1);
}

.item-display-name {
    font-size: var(--font-size-md);
    color: var(--primary-600);
    font-weight: 600;
    font-style: italic;
}

.item-status {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-end;
}

.status-badge {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    min-width: 80px;
    justify-content: center;
}

.status-badge.active {
    background: linear-gradient(135deg, var(--green-500), var(--green-600));
    color: white;
}

.status-badge.inactive {
    background: linear-gradient(135deg, var(--red-500), var(--red-600));
    color: white;
}

.sort-order-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.item-details {
    margin-bottom: var(--spacing-3);
}

.item-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-1) 0;
    font-size: var(--font-size-sm);
}

.item-detail-label {
    color: var(--gray-600);
    font-weight: 500;
}

.item-detail-value {
    color: var(--gray-900);
}

.item-price {
    background-color: var(--gray-50);
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
}

.item-price-label {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.item-price-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
}

.item-actions {
    display: flex;
    gap: var(--spacing-2);
    justify-content: flex-end;
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-top: 1px solid var(--border-color);
    margin: var(--spacing-4) calc(-1 * var(--spacing-4)) calc(-1 * var(--spacing-4));
}

.item-actions .btn {
    flex: 1;
    max-width: 80px;
    font-size: var(--font-size-xs);
}

/* 品质颜色 */
.quality-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.quality-1 { background-color: #f3f4f6; color: #374151; } /* 白色 */
.quality-2 { background-color: #dcfce7; color: #166534; } /* 绿色 */
.quality-3 { background-color: #dbeafe; color: #1e40af; } /* 蓝色 */
.quality-4 { background-color: #e9d5ff; color: #7c3aed; } /* 紫色 */
.quality-5 { background-color: #fed7aa; color: #ea580c; } /* 橙色 */
.quality-6 { background-color: #fecaca; color: #dc2626; } /* 红色 */

/* 增强的商品卡片分区样式 */
.item-section {
    margin-bottom: var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.section-title {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    color: var(--primary-700);
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    border-bottom: 1px solid var(--border-color);
}

/* 价格信息增强样式 */
.item-price-info {
    padding: var(--spacing-3);
}

.item-price-main {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-600);
    text-align: center;
    margin-bottom: var(--spacing-2);
}

.item-price-details {
    display: flex;
    justify-content: space-around;
    font-size: var(--font-size-sm);
    gap: var(--spacing-2);
}

.original-price {
    color: var(--text-secondary);
    text-decoration: line-through;
}

.final-price {
    color: var(--green-600);
    font-weight: 600;
}

.discount {
    color: var(--red-600);
    font-weight: 600;
}

/* 限购信息样式 */
.item-limit-info {
    padding: var(--spacing-3);
}

.limit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.limit-item:last-child {
    border-bottom: none;
}

.limit-value {
    font-weight: 600;
    color: var(--orange-600);
}

/* 刷新信息样式 */
.item-refresh-info {
    padding: var(--spacing-3);
}

.refresh-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.refresh-row:last-child {
    border-bottom: none;
}

.refresh-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.refresh-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.weight-value {
    color: var(--blue-600);
}

.probability-value {
    color: var(--green-600);
}

.relative-weight {
    color: var(--purple-600);
}

/* 可用性信息样式 */
.item-availability-info {
    padding: var(--spacing-3);
}

.availability-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.availability-item:last-child {
    border-bottom: none;
}

.time-value {
    font-family: var(--font-mono);
    font-size: var(--font-size-xs);
    color: var(--text-primary);
    font-weight: 600;
}

.conditions-value {
    font-family: var(--font-mono);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 时间信息样式 */
.item-time-info {
    padding: var(--spacing-3);
}

.time-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.time-item:last-child {
    border-bottom: none;
}

/* 表单帮助文本 */
.form-help {
    display: block;
    margin-top: var(--spacing-1);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.4;
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: var(--spacing-4);
        max-height: calc(100vh - 2rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }

    .item-grid {
        grid-template-columns: 1fr;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }
}
