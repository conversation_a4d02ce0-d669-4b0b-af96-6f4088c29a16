#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import logging
import traceback
from typing import Dict, List, Callable, Any, Awaitable
from logger_config import setup_logger

# 初始化日志系统
logger = setup_logger(__name__)

class EventBus:
    """
    事件总线，用于实现事件驱动架构
    支持异步事件处理和多个订阅者
    """
    # 存储事件处理器的字典，格式为 {event_name: [handler1, handler2, ...]}
    _handlers: Dict[str, List[Callable[[Dict[str, Any]], Awaitable[None]]]] = {}
    
    # 事件历史记录，用于调试
    _event_history: List[Dict[str, Any]] = []
    _max_history_size = 100
    
    # 是否启用调试模式
    _debug = False
    
    @classmethod
    def enable_debug(cls, enable: bool = True, max_history_size: int = 100):
        """启用或禁用调试模式"""
        cls._debug = enable
        cls._max_history_size = max_history_size
        if not enable:
            cls._event_history.clear()
    
    @classmethod
    def subscribe(cls, event_name: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]):
        """
        订阅事件
        
        Args:
            event_name: 事件名称
            handler: 事件处理函数，必须是一个接受事件数据的异步函数
        """
        if event_name not in cls._handlers:
            cls._handlers[event_name] = []
        
        if handler not in cls._handlers[event_name]:
            cls._handlers[event_name].append(handler)
            logger.debug(f"已订阅事件 '{event_name}'，当前订阅者数量: {len(cls._handlers[event_name])}")
        else:
            logger.warning(f"处理器已经订阅了事件 '{event_name}'，忽略重复订阅")
    
    @classmethod
    def unsubscribe(cls, event_name: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]):
        """
        取消订阅事件
        
        Args:
            event_name: 事件名称
            handler: 要取消的事件处理函数
        """
        if event_name in cls._handlers and handler in cls._handlers[event_name]:
            cls._handlers[event_name].remove(handler)
            logger.debug(f"已取消订阅事件 '{event_name}'，剩余订阅者: {len(cls._handlers[event_name])}")
            
            # 如果没有订阅者了，删除这个事件
            if not cls._handlers[event_name]:
                del cls._handlers[event_name]
    
    @classmethod
    async def publish(cls, event_name: str, event_data: Dict[str, Any] = None):
        """
        发布事件
        
        Args:
            event_name: 事件名称
            event_data: 事件数据，必须是可序列化的字典
        """
        if event_data is None:
            event_data = {}
            
        # 记录事件历史
        if cls._debug:
            cls._event_history.append({
                "event": event_name,
                "data": event_data
            })
            # 限制历史记录大小
            if len(cls._event_history) > cls._max_history_size:
                cls._event_history = cls._event_history[-cls._max_history_size:]
        
        if event_name in cls._handlers:
            handlers = cls._handlers[event_name].copy()  # 创建副本，避免处理过程中的修改
            handler_count = len(handlers)
            
            if handler_count > 0:
                logger.debug(f"发布事件 '{event_name}'，有 {handler_count} 个订阅者")
                
                # 并行执行所有处理器
                tasks = []
                for handler in handlers:
                    try:
                        # 创建任务但不等待，实现并行处理
                        task = asyncio.create_task(cls._safe_execute_handler(event_name, handler, event_data))
                        tasks.append(task)
                    except Exception as e:
                        logger.error(f"创建事件处理任务失败，事件: {event_name}, 错误: {str(e)}")
                
                # 等待所有任务完成
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
            else:
                logger.debug(f"发布事件 '{event_name}'，但没有订阅者")
        else:
            logger.debug(f"发布事件 '{event_name}'，但没有订阅者")
    
    @classmethod
    async def _safe_execute_handler(cls, event_name: str, handler: Callable, event_data: Dict[str, Any]):
        """安全执行事件处理器，捕获所有异常"""
        try:
            await handler(event_data)
        except Exception as e:
            logger.error(f"处理事件 '{event_name}' 失败: {str(e)}")
            logger.error(traceback.format_exc())
    
    @classmethod
    def get_event_history(cls) -> List[Dict[str, Any]]:
        """获取事件历史记录，仅在调试模式下可用"""
        if not cls._debug:
            logger.warning("事件历史记录仅在调试模式下可用")
            return []
        return cls._event_history.copy()
    
    @classmethod
    def get_subscribers(cls, event_name: str = None) -> Dict[str, int]:
        """
        获取事件订阅者信息
        
        Args:
            event_name: 可选，指定事件名称
            
        Returns:
            Dict[str, int]: 事件名称和订阅者数量的字典
        """
        if event_name:
            return {event_name: len(cls._handlers.get(event_name, []))}
        
        return {event: len(handlers) for event, handlers in cls._handlers.items()} 