import json
import time
import contextlib
from typing import List, Dict, Any
from redis import exceptions as redis_exceptions  # 显式导入Redis异常
from logger_config import setup_logger
from utils import CustomJSONEncoder, CustomJSONDecoder
# 初始化日志系统
logger = setup_logger(__name__)
class CacheManager: # 缓存管理核心类
    """缓存管理核心类"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self._error_count = 0
        self._last_error_time = 0
        self._max_errors = 50  # 最大错误次数阈值
        self._error_window = 60  # 错误窗口期（秒）
    
    # 基本缓存操作
    async def get(self, key: str, default=None):
        """获取缓存，返回反序列化后的值"""
        try:
            if not self.redis:
                return default
            
            # 使用with语句自动管理连接
            data = await self.redis.get(key)
            if not data:
                return default
            
            try:
                return json.loads(data, cls=CustomJSONDecoder)
            except json.JSONDecodeError:
                # 可能是非JSON值，如简单字符串
                return data                
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"获取缓存失败: {key}, 错误: {str(e)}")
            return default
        except Exception as e:
            logger.error(f"处理缓存数据失败: {key}, 错误: {str(e)}")
            return default
    
    async def set(self, key: str, value, ttl: int = 3600):
        """设置缓存，自动处理序列化"""
        try:
            if not self.redis:
                return False
            
            # 处理复杂数据类型
            if isinstance(value, (dict, list)):
                serialized = json.dumps(value, cls=CustomJSONEncoder)
            else:
                serialized = value
            
            # 使用pipeline减少网络往返
            async with self.redis.pipeline() as pipe:
                await pipe.set(key, serialized, ex=ttl)
                await pipe.execute()
            
            # 成功操作后重置错误计数
            if self._error_count > 0:
                self._error_count = max(0, self._error_count - 1)
                
            return True
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"设置缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def delete(self, key: str):
        """删除缓存"""
        try:
            if not self.redis:
                return False
            
            await self.redis.delete(key)
            return True
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"删除缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if not self.redis:
                return False
            
            return await self.redis.exists(key) > 0
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"检查缓存是否存在失败: {key}, 错误: {str(e)}")
            return False
    
    # 批量操作
    async def multi_get(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取多个键的缓存"""
        result = {}
        if not keys or not self.redis:
            return result
        
        try:
            # 使用pipeline批量获取，减少网络往返
            pipeline = self.redis.pipeline()
            for key in keys:
                pipeline.get(key)
            
            values = await pipeline.execute()
            
            for i, key in enumerate(keys):
                if values[i]:
                    try:
                        result[key] = json.loads(values[i], cls=CustomJSONDecoder)
                    except json.JSONDecodeError:
                        result[key] = values[i].decode('utf-8')
            
            # 成功操作后重置错误计数
            if self._error_count > 0:
                self._error_count = max(0, self._error_count - 1)
                
            return result
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"批量获取缓存失败: {keys}, 错误: {str(e)}")
            return result
    
    async def multi_set(self, key_values: Dict[str, Any], ttl: int = 3600):
        """批量设置多个键的缓存"""
        if not key_values or not self.redis:
            return False
        
        try:
            pipeline = self.redis.pipeline()
            
            for key, value in key_values.items():
                # 处理复杂数据类型
                if isinstance(value, (dict, list)):
                    serialized = json.dumps(value, cls=CustomJSONEncoder)
                else:
                    serialized = value
                
                pipeline.set(key, serialized, ex=ttl)
            
            await pipeline.execute()
            
            # 成功操作后重置错误计数
            if self._error_count > 0:
                self._error_count = max(0, self._error_count - 1)
                
            return True
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"批量设置缓存失败, 错误: {str(e)}")
            return False
    
    async def multi_delete(self, keys: List[str]):
        """批量删除多个键的缓存"""
        if not keys or not self.redis:
            return False
        
        try:
            # 使用pipeline批量删除
            pipeline = self.redis.pipeline()
            for key in keys:
                pipeline.delete(key)
            await pipeline.execute()
            return True
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.warning(f"批量删除缓存失败: {keys}, 错误: {str(e)}")
            return False
    
    # 列表操作
    async def list_append(self, key: str, value, ttl: int = 3600):
        """向列表缓存添加元素"""
        try:
            if not self.redis:
                return False
            
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                current_list = []
            
            # 添加元素
            current_list.append(value)
            
            # 更新缓存
            return await self.set(key, current_list, ttl)
        except Exception as e:
            logger.warning(f"向列表缓存添加元素失败: {key}, 错误: {str(e)}")
            return False
    
    def _record_error(self):
        """记录Redis错误，用于错误率监控"""
        now = time.time()
        
        # 如果超过错误窗口期，重置计数
        if now - self._last_error_time > self._error_window:
            self._error_count = 1
        else:
            self._error_count += 1
        
        self._last_error_time = now
        
        # 如果错误次数超过阈值，记录警告
        if self._error_count >= self._max_errors:
            logger.warning(f"Redis错误率过高: {self._error_count}次/{self._error_window}秒")
    
    async def list_update(self, key: str, identifier_field: str, identifier_value: Any, new_value: Any, ttl: int = 3600):
        """更新列表缓存中的特定元素"""
        try:
            if not self.redis:
                return False
            
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                return False
            
            # 查找并更新元素
            found = False
            for i, item in enumerate(current_list):
                if isinstance(item, dict) and item.get(identifier_field) == identifier_value:
                    if isinstance(new_value, dict) and isinstance(current_list[i], dict):
                        # 更新字典
                        current_list[i].update(new_value)
                    else:
                        # 替换元素
                        current_list[i] = new_value
                    found = True
                    break
            
            if not found:
                # 如果未找到匹配元素，则添加新元素
                if isinstance(new_value, dict):
                    new_value[identifier_field] = identifier_value
                    current_list.append(new_value)
                else:
                    # 无法添加非字典元素
                    return False
            
            # 更新缓存
            return await self.set(key, current_list, ttl)
        except Exception as e:
            logger.warning(f"更新列表缓存中的元素失败: {key}, 错误: {str(e)}")
            return False
    
    async def list_remove(self, key: str, identifier_field: str, identifier_value: Any, ttl: int = 3600):
        """从列表缓存中移除特定元素"""
        try:
            if not self.redis:
                return False
                
            # 获取当前列表
            current_list = await self.get(key) or []
            if not isinstance(current_list, list):
                return False
                
            # 查找并移除元素
            new_list = [
                item for item in current_list 
                if not (isinstance(item, dict) and item.get(identifier_field) == identifier_value)
            ]
            
            if len(new_list) == len(current_list):
                # 未移除任何元素
                return False
            
            # 更新缓存
            return await self.set(key, new_list, ttl)
        except Exception as e:
            logger.warning(f"从列表缓存中移除元素失败: {key}, 错误: {str(e)}")
            return False
    
    @contextlib.asynccontextmanager
    async def transaction(self):
        """创建Redis事务上下文"""
        if not self.redis:
            # 如果Redis不可用，使用空事务
            class DummyTransaction:
                async def get(self, key: str):
                    return None
                    
                async def set(self, key: str, value, ttl: int = 3600):
                    return False
                    
                async def delete(self, key: str):
                    return False
            
            yield DummyTransaction()
            return
        
        try:
            # 创建管道
            pipeline = self.redis.pipeline()
            # 创建事务对象
            tx = CacheTransaction(pipeline)
            yield tx
            # 执行事务
            await pipeline.execute()
        except redis_exceptions.RedisError as e:
            # 记录错误并增加计数
            self._record_error()
            logger.error(f"Redis事务失败: {str(e)}")
            # 重新抛出异常，让调用者处理
            raise
class CacheTransaction: # 缓存事务类，用于管理Redis管道操作
    """缓存事务类，用于管理Redis管道操作"""
    
    def __init__(self, pipeline):
        self.pipeline = pipeline
    
    async def get(self, key: str):
        """获取值但不立即执行"""
        self.pipeline.get(key)
    
    async def set(self, key: str, value, ttl: int = 3600):
        """设置值但不立即执行"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value, cls=CustomJSONEncoder)
        self.pipeline.set(key, value, ex=ttl)
    
    async def delete(self, key: str):
        """删除值但不立即执行"""
        self.pipeline.delete(key)