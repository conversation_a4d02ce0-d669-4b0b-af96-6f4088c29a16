#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ShopItemConfig的item_type字段修复
"""

import asyncio
import logging
from datetime import datetime
from shop_models import ShopItemConfig, ItemType

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_from_dict_with_item_type():
    """测试包含item_type字段的数据"""
    logger.info("测试1: 包含item_type字段的数据")
    
    data = {
        "config_id": "test_001",
        "shop_id": "shop_001",
        "slot_id": 1,
        "item_template_id": "10001",
        "item_quantity": 1,
        "item_quality": 3,
        "item_type": "equipment",  # 字符串形式
        "price_config": {"currency": "gold", "amount": 100},
        "purchase_limit": None,
        "availability": {},
        "refresh_weight": 100,
        "refresh_probability": 1.0,
        "is_active": True,
        "sort_order": 0,
        "display_config": {},
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    try:
        config = ShopItemConfig.from_dict(data)
        logger.info(f"✅ 成功创建配置: {config.config_id}")
        logger.info(f"   道具类型: {config.item_type} (类型: {type(config.item_type)})")
        
        # 测试序列化
        serialized = config.to_dict()
        logger.info(f"   序列化后的item_type: {serialized['item_type']} (类型: {type(serialized['item_type'])})")
        
        return True
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

def test_from_dict_without_item_type():
    """测试不包含item_type字段的数据（模拟旧数据）"""
    logger.info("测试2: 不包含item_type字段的数据（旧数据）")
    
    data = {
        "config_id": "test_002",
        "shop_id": "shop_001",
        "slot_id": 2,
        "item_template_id": "10002",
        "item_quantity": 1,
        "item_quality": 2,
        # 注意：没有item_type字段
        "price_config": {"currency": "gold", "amount": 200},
        "purchase_limit": None,
        "availability": {},
        "refresh_weight": 100,
        "refresh_probability": 1.0,
        "is_active": True,
        "sort_order": 0,
        "display_config": {},
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    try:
        config = ShopItemConfig.from_dict(data)
        logger.info(f"✅ 成功创建配置: {config.config_id}")
        logger.info(f"   道具类型: {config.item_type} (类型: {type(config.item_type)})")
        logger.info(f"   默认值是否正确: {config.item_type == ItemType.ITEM}")
        
        # 测试序列化
        serialized = config.to_dict()
        logger.info(f"   序列化后的item_type: {serialized['item_type']} (类型: {type(serialized['item_type'])})")
        
        return True
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

def test_from_dict_with_null_item_type():
    """测试item_type为null的数据"""
    logger.info("测试3: item_type为null的数据")
    
    data = {
        "config_id": "test_003",
        "shop_id": "shop_001",
        "slot_id": 3,
        "item_template_id": "10003",
        "item_quantity": 1,
        "item_quality": 1,
        "item_type": None,  # null值
        "price_config": {"currency": "gold", "amount": 300},
        "purchase_limit": None,
        "availability": {},
        "refresh_weight": 100,
        "refresh_probability": 1.0,
        "is_active": True,
        "sort_order": 0,
        "display_config": {},
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    try:
        config = ShopItemConfig.from_dict(data)
        logger.info(f"✅ 成功创建配置: {config.config_id}")
        logger.info(f"   道具类型: {config.item_type} (类型: {type(config.item_type)})")
        logger.info(f"   默认值是否正确: {config.item_type == ItemType.ITEM}")
        
        # 测试序列化
        serialized = config.to_dict()
        logger.info(f"   序列化后的item_type: {serialized['item_type']} (类型: {type(serialized['item_type'])})")
        
        return True
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

def test_direct_creation():
    """测试直接创建ShopItemConfig对象"""
    logger.info("测试4: 直接创建ShopItemConfig对象")
    
    try:
        # 不指定item_type，应该使用默认值
        config = ShopItemConfig(
            config_id="test_004",
            shop_id="shop_001",
            slot_id=4,
            item_template_id="10004",
            item_quantity=1,
            item_quality=4,
            # item_type 使用默认值
            price_config={"currency": "gold", "amount": 400},
            purchase_limit=None,
            availability={},
            refresh_weight=100,
            refresh_probability=1.0,
            is_active=True,
            sort_order=0,
            display_config={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        logger.info(f"✅ 成功创建配置: {config.config_id}")
        logger.info(f"   道具类型: {config.item_type} (类型: {type(config.item_type)})")
        logger.info(f"   默认值是否正确: {config.item_type == ItemType.ITEM}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """运行所有测试"""
    logger.info("开始测试ShopItemConfig的item_type字段修复")
    logger.info("=" * 60)
    
    tests = [
        test_from_dict_with_item_type,
        test_from_dict_without_item_type,
        test_from_dict_with_null_item_type,
        test_direct_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        logger.info("")
        if test():
            passed += 1
        logger.info("-" * 40)
    
    logger.info("")
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！修复成功！")
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
