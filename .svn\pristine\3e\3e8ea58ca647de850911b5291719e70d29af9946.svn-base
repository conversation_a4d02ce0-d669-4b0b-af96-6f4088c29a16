from CacheKeyBuilder import CacheKeyBuilder
from redis_manager import RedisManager
from typing import Optional, Dict, Any, Tuple
import os
import traceback
from redis import exceptions as redis_exceptions  # 显式导入Redis异常
from enums import ItemType
from logger_config import setup_logger
from BaseModelORM import BaseModelORM
from mongodb_manager import MongoDBManager
from GlobalDBUtils import GlobalDBUtils
from CacheManager import CacheManager
from ItemCacheManager import ItemCacheManager
from typing import Union
import asyncio
from datetime import datetime
from typing import List, Union
from config import config
from utils import process_db_item
from logger_config import setup_logger
import uuid
logger = setup_logger(__name__)
# 初始化日志系统
class UserData(BaseModelORM):
    id: str
    password: Union[str, bytes]  # 修改为可以接受字符串或字节类型
    created_at: Optional[datetime] = None # 创建时间
    profile: Optional[Dict[str, Any]] = None # 角色信息
    nickname: Optional[str] = None # 角色昵称
    guild_info: Optional[Dict[str, Any]] = None # 公会信息
    
    @property
    def username(self) -> str:
        return self.id

    @classmethod
    async def validate_field(cls, field_path: str, value: Any, field_config: Dict[str, Any]) -> Tuple[bool, str]:
        try:
            field_spec = field_config.get("fields", {}).get(field_path)
            if not field_spec:
                logger.debug(f"字段 {field_path} 未在配置文件中定义，允许更新")
                return True, ""

            type_map = {"int": int, "str": str, "float": float, "list": list, "dict": dict}
            expected_type = type_map.get(field_spec.get("type"))
            if not expected_type:
                logger.warning(f"字段 {field_path} 配置无效类型: {field_spec.get('type')}")
                return True, ""

            if not isinstance(value, expected_type):
                return False, f"字段 {field_path} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"

            if "range" in field_spec and isinstance(value, (int, float)):
                min_val, max_val = field_spec["range"]
                if not (min_val <= value <= max_val):
                    return False, f"字段 {field_path} 值超出范围，期望 {min_val} 到 {max_val}，实际 {value}"
            elif "values" in field_spec:
                if value not in field_spec["values"]:
                    return False, f"字段 {field_path} 值无效，期望 {field_spec['values']}，实际 {value}"
            elif "element_type" in field_spec and isinstance(value, list):
                element_type = type_map.get(field_spec["element_type"])
                if not element_type or not all(isinstance(item, element_type) for item in value):
                    return False, f"字段 {field_path} 列表元素类型错误，期望 {field_spec['element_type']}"
            return True, ""
        except Exception as e:
            return False, f"字段验证失败: {str(e)}"

    @classmethod
    async def initialize_nested_field(cls, username: str, field_path: str, db, field_config: Dict[str, Any]) -> bool:
        max_retries = 3
        for attempt in range(max_retries):
            try:
                parts = field_path.split(".")
                if len(parts) <= 1:
                    return True
                parent_path = ".".join(parts[:-1])
                update = {parent_path: {}}
                for i in range(len(parts) - 1):
                    current_path = ".".join(parts[:i + 1])
                    update[current_path] = update.get(current_path, {})
                field_spec = field_config.get("fields", {}).get(field_path)
                if field_spec and "default" in field_spec:
                    update[field_path] = field_spec["default"]
                result = await db.users.update_one(
                    {"id": username, parent_path: {"$exists": False}},
                    {"$set": update}
                )
                if result.modified_count > 0:
                    logger.info(f"初始化用户 {username} 的嵌套字段 {parent_path} 成功")
                return True
            except Exception as e:
                logger.error(f"初始化嵌套字段 {field_path} 失败，用户 {username}，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                else:
                    return False

    @classmethod
    async def update_field(cls, username: str, updates: Dict[str, Any], db, redis_client, field_config: Dict[str, Any], caller: str = "unknown") -> Tuple[bool, str]:
        try:
            if not updates or not isinstance(updates, dict):
                return False, "更新数据为空或格式错误"
            set_updates = {}
            for field_path, value in updates.items():
                is_valid, error_msg = await cls.validate_field(field_path, value, field_config)
                if not is_valid:
                    return False, error_msg
                if "." in field_path:
                    if not await cls.initialize_nested_field(username, field_path, db, field_config):
                        return False, f"初始化嵌套字段 {field_path} 失败"
                set_updates[field_path] = value
            result = await db.users.update_one(
                {"id": username},
                {"$set": set_updates}
            )
            if result.matched_count == 0:
                return False, f"用户 {username} 不存在"
            if result.modified_count == 0:
                return True, "字段未发生变化"
            user = await db.users.find_one({"id": username})
            if user:
                from UserCacheManager import UserCacheManager
                user_cache = await UserCacheManager.get_instance()
                await user_cache.get(username, user)
                logger.info(f"[{caller}] 批量更新用户 {username} 的字段 {list(updates.keys())} 成功")
                return True, ""
            return False, "更新缓存失败"
        except Exception as e:
            logger.error(f"更新用户字段失败，用户: {username}, 错误: {str(e)}")
            return False, f"服务器错误: {str(e)}"

class UserCacheManager:
    _instance = None
    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            redis_manager = await RedisManager.get_instance()
            redis_client = await redis_manager.get_redis()
            cache_manager = CacheManager(redis_client)
            cls._instance = cls(cache_manager)
            # 初始化缓存失效通知
            await cls._instance._initialize_cache_invalidation()
        return cls._instance

    def __init__(self, cache_manager):
        self.cache = cache_manager
        self.invalidation_manager = None

    async def _initialize_cache_invalidation(self):
        """初始化缓存失效通知"""
        try:
            from CacheInvalidationManager import get_cache_invalidation_manager
            self.invalidation_manager = await get_cache_invalidation_manager()

            # 注册用户缓存失效监听器
            await self.invalidation_manager.register_listener(
                'user',
                self._handle_cache_invalidation
            )

            logger.debug("用户缓存失效通知初始化完成")
        except Exception as e:
            logger.error(f"初始化用户缓存失效通知失败: {str(traceback.format_exc())}")

    async def _handle_cache_invalidation(self, cache_key: str, data: Dict):
        """处理缓存失效通知"""
        try:
            # 从本地缓存中删除失效的数据
            await self.cache.delete(cache_key)
            logger.debug(f"处理用户缓存失效: {cache_key}")
        except Exception as e:
            logger.error(f"处理用户缓存失效失败: {cache_key}, 错误: {str(e)}")
    
    async def get_user_data(self, username: str) -> Optional[Dict]:
        """获取用户数据缓存"""
        key = CacheKeyBuilder.user_data(username)
        return await self.cache.get(key)
    
    async def set_user_data(self, username: str, user_data: Dict, ttl: int = 3600):
        """缓存用户数据"""
        key = CacheKeyBuilder.user_data(username)
        result = await self.cache.set(key, user_data, ttl)

        # 发送缓存更新通知
        if self.invalidation_manager and result:
            await self.invalidation_manager.invalidate_cache(
                'user', key, {'username': username, 'action': 'update'}
            )

        return result

    async def invalidate_user_data(self, username: str):
        """使用户数据缓存失效"""
        key = CacheKeyBuilder.user_data(username)
        result = await self.cache.delete(key)

        # 发送缓存失效通知
        if self.invalidation_manager and result:
            await self.invalidation_manager.invalidate_cache(
                'user', key, {'username': username, 'action': 'delete'}
            )

        return result
    
    async def update_user_field(self, username: str, field: str, value: Any, ttl: int = 3600):
        """更新用户特定字段并刷新缓存"""
        # 获取用户数据
        user_data = await self.get_user_data(username)
        if not user_data:
            return False
        
        # 支持嵌套字段路径 (如 "profile.avatar")
        if "." in field:
            parts = field.split(".")
            target = user_data
            for part in parts[:-1]:
                if part not in target:
                    target[part] = {}
                target = target[part]
            target[parts[-1]] = value
        else:
            user_data[field] = value
        
        # 更新缓存
        return await self.set_user_data(username, user_data, ttl)
    
    async def get_user_status(self, username: str) -> str:
        """获取用户状态"""
        key = CacheKeyBuilder.user_status(username)
        status = await self.cache.get(key)
        return status or "offline"
    
    async def set_user_status(self, username: str, status: str, ttl: int = 3600):
        """设置用户状态"""
        key = CacheKeyBuilder.user_status(username)
        result = await self.cache.set(key, status, ttl)

        # 发送缓存更新通知
        if self.invalidation_manager and result:
            await self.invalidation_manager.invalidate_cache(
                'user', key, {'username': username, 'status': status, 'action': 'status_update'}
            )

        return result
    
    async def register_connection(self, username: str, token: str, worker_id: int = None, ttl: int = 3600):
        """注册用户连接信息"""
        worker_id = worker_id or os.getpid()
        connection_info = {
            "worker_id": worker_id,
            "token": token,
            "last_active": datetime.now().isoformat()
        }
        
        # 设置用户连接信息
        user_conn_key = CacheKeyBuilder.user_connection(username)
        token_key = CacheKeyBuilder.token_to_user(token)
        
        # 使用事务确保两个键同时设置
        async with self.cache.transaction() as tx:
            await tx.set(user_conn_key, connection_info, ttl)
            await tx.set(token_key, username, ttl)
        
        # 同时更新用户状态
        await self.set_user_status(username, "online", ttl)
        return True
    
    async def remove_connection(self, username: str = None, token: str = None):
        """移除用户连接信息"""
        # 确保至少提供了一个参数
        if username is None and token is None:
            return False
        
        # 如果只提供了token，先查找对应的username
        if username is None and token is not None:
            token_key = CacheKeyBuilder.token_to_user(token)
            username = await self.cache.get(token_key)
            if not username:
                return False
        
        # 如果提供了username，查找对应的token
        token_to_delete = None
        if token is None and username is not None:
            user_conn_key = CacheKeyBuilder.user_connection(username)
            conn_info = await self.cache.get(user_conn_key)
            if conn_info and isinstance(conn_info, dict):
                token_to_delete = conn_info.get("token")
        else:
            token_to_delete = token
        
        # 删除连接信息
        keys_to_delete = []
        if username:
            keys_to_delete.append(CacheKeyBuilder.user_connection(username))
        
        if token_to_delete:
            keys_to_delete.append(CacheKeyBuilder.token_to_user(token_to_delete))
        
        if keys_to_delete:
            await self.cache.multi_delete(keys_to_delete)
            
            # 更新用户状态为离线
            if username:
                await self.set_user_status(username, "offline")
            
            return True
        return False
    
    async def update_connection_ttl(self, username: str, ttl: int = 3600):
        """更新连接TTL"""
        # 获取连接信息
        user_conn_key = CacheKeyBuilder.user_connection(username)
        conn_info = await self.cache.get(user_conn_key)
        
        if not conn_info or not isinstance(conn_info, dict):
            return False
        
        token = conn_info.get("token")
        if not token:
            return False
        
        # 更新两个键的TTL
        token_key = CacheKeyBuilder.token_to_user(token)
        
        # 使用Redis的EXPIRE命令
        try:
            if self.cache.redis:
                await self.cache.redis.expire(user_conn_key, ttl)
                await self.cache.redis.expire(token_key, ttl)
                
                # 同时更新用户状态的TTL
                status_key = CacheKeyBuilder.user_status(username)
                await self.cache.redis.expire(status_key, ttl)
                
                return True
        except redis_exceptions.RedisError as e:
            logger.warning(f"更新连接TTL失败: {str(e)}")
        
        return False
    async def get_user_by_username(self, username, mongo_only=False):
        worker_id = os.getpid()
        try:
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()
        
            try:
                user_data = await self.get_user_data(username)
                if user_data:
                    logger.debug(f"Worker {worker_id}: 从缓存获取用户: {username}")
                    return UserData(**user_data)
            except Exception as redis_err:
                logger.warning(f"Worker {worker_id}: 从Redis获取用户缓存失败: {str(redis_err)}")
                        
            # 从MongoDB获取
            try:
                collection = db["users"]
                user_doc = await collection.find_one({"id": username})
                
                if user_doc:
                    logger.debug(f"Worker {worker_id}: 从MongoDB获取用户: {username}")
                    user = UserData(**user_doc)
                    # 更新缓存                    
                    await self.set_user_data(username, user_doc)
                    return user
                else:
                    logger.warning(f"Worker {worker_id}: 用户不存在: {username}")
                    return None
            except Exception as mongo_err:
                logger.error(traceback.format_exc())
                logger.error(f"Worker {worker_id}: 从MongoDB获取用户数据失败，用户: {username}，错误: {str(mongo_err)}")
                return None
        except Exception as e:
            logger.error(f"Worker {worker_id}: 获取用户数据失败，用户: {username}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    async def update_user_fields(self, username: str, field_path: Optional[str] = None, value: Optional[Any] = None, updates: Optional[Dict[str, Any]] = None, caller: str = "unknown") -> Tuple[bool, str]:
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        redis_manager = await RedisManager.get_instance()
        redis_client = await redis_manager.get_redis()
        if field_path and updates:
            return False, "不能同时提供 field_path 和 updates"
        if field_path:
            if value is None:
                return False, "单一字段更新需要提供值"
            updates = {field_path: value}
        elif updates:
            if not isinstance(updates, dict) or not updates:
                return False, "更新数据为空或格式错误"
        else:
            return False, "必须提供字段路径或更新数据"
        return await UserData.update_field(username, updates, db, redis_client, {}, caller)
    async def update_user_connection_ttl(self,username: str, ttl: int = 3600) -> bool:
        """
        更新用户连接信息的过期时间        
        Args:
            username (str): 用户ID
            ttl (int): Redis键的过期时间（秒）
            
        Returns:
            bool: 是否成功更新
        """
        try:            
            return await self.update_connection_ttl(username, ttl)                
        except Exception as e:
            logger.error(f"更新用户连接信息TTL失败: {str(e)}")
            return False
    async def register_user_connection(self, username: str, token: str, worker_id: int = None, ttl: int = 3600) -> bool:
        """
        注册用户WebSocket连接信息到Redis
        
        Args:
            username (str): 用户ID
            token (str): WebSocket连接token
            worker_id (int): 处理连接的worker进程ID，默认为当前进程ID
            ttl (int): Redis键的过期时间（秒）
            
        Returns:
            bool: 是否成功注册
        """
        try:
            worker_id = worker_id or os.getpid()
            return await self.register_connection(username, token, worker_id, ttl)
            
        except Exception as e:
            logger.error(f"注册用户连接信息失败: {str(e)}")
            return False
            
    async def remove_user_connection(self, username: str = None, token: str = None) -> bool:
        """
        从Redis中移除用户WebSocket连接信息
        
        Args:
            username (str): 用户ID
            token (str): WebSocket连接token
            
        Returns:
            bool: 是否成功移除
        """
        try:
            return await self.remove_connection(username, token)                
        except Exception as e:
            logger.error(f"移除用户连接信息失败: {str(e)}")
            return False
    
    # 初始化用户 保证只在注册的时候调用    
    async def save_user(self, user: UserData):
        try:
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()
            data = user.serialize()
            # 检查用户是否已存在
            existing_user = await db.users.find_one({"id": user.id})
            if existing_user:
                logger.warning(f"用户 {user.id} 已存在，更新用户数据")
                result = await db.users.update_one(
                    {"id": user.id},
                    {"$set": data}
                )
                user._id = str(existing_user.get("_id"))
            else:
                # 创建新用户
                result = await db.users.insert_one(data)
                user._id = str(result.inserted_id)
                
            # 更新Redis缓存               
            await self.cache_user_data(user.id, data)
                
            # 初始化用户 - 不发送通知
            await self.add_user_asset(user.id, ItemType.ITEM, [{"defid": 82001, "quantity": 1000, "attributes": {}},
                                                               {"defid": 82002, "quantity": 10, "attributes": {}},
                                                               {"defid": 82003, "quantity": 100, "attributes": {}},
                                                               {"defid": 82004, "quantity": 1000, "attributes": {}},
                                                               {"defid": 82005, "quantity": 10000, "attributes": {}}],
                                      batch=True, notify=False)
            logger.info(f"用户 {user.id} 数据保存成功")
            return user
        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise Exception(f"保存用户数据失败: {str(e)}")
    async def cache_user_data(self, username, user, expire_time=3600):
        """缓存用户数据到Redis        
        此函数保留向后兼容，但内部使用新的缓存系统
        """
        try:            
            await self.set_user_data(username, user, expire_time)
            await self.set_user_status(username, "online", expire_time)
            logger.debug(f"使用缓存管理器缓存用户 {username} 数据成功")            
        except Exception as e:
            logger.error(f"缓存用户数据失败: {str(e)}")

    async def add_user_asset(self,
                        username: str, 
                        asset_type: str, 
                        assets_data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                        batch: bool = False,
                        notify: bool = True) -> Dict[str, Any]:
        """统一的资产添加方法，支持物品和装备，支持单个和批量
        
        Args:
            username (str): 用户ID
            asset_type (str): 资产类型，'item'或'equipment'
            assets_data: 单个资产数据或资产列表
                单个格式: {
                    "defid": int,            # 定义ID
                    "quantity": int,         # 数量 (仅物品类型)
                    "level": int,            # 等级 (仅装备类型)
                    "attributes": dict       # 属性 (可选)
                }
                列表格式: [单个格式, 单个格式, ...]
            batch (bool): 是否为批量操作，为False时assets_data应为单个资产数据，为True时应为列表
            notify (bool): 是否发送资产变更通知，默认为True
            
        Returns:
            Dict: 包含操作结果的字典
        """
        worker_id = os.getpid()
        
        # 验证资产类型
        if asset_type not in [ItemType.ITEM, ItemType.EQUIPMENT, ItemType.RUNE]:
            return {
                "success": False,
                "message": f"不支持的资产类型: {asset_type}，支持的类型: {ItemType.ITEM}, {ItemType.EQUIPMENT}, {ItemType.RUNE}"
            }
        

        # 统一资产数据格式
        assets_list = []
        if batch:
            if not isinstance(assets_data, list):
                return {
                    "success": False,
                    "message": "批量模式下，assets_data应为列表"
                }
            assets_list = assets_data
        else:
            if not isinstance(assets_data, dict):
                return {
                    "success": False,
                    "message": "单个资产模式下，assets_data应为字典"
                }
            assets_list = [assets_data]
        
        # 处理结果
        result_assets = []
        failed_assets = []
        
        try:
            now = datetime.now()
            asset_docs = []
            asset_ids = []
            
            # 准备所有资产数据
            for asset_data in assets_list:
                try:
                    defid = asset_data.get("defid")
                    if not defid:
                        failed_assets.append({"defid": defid, "error": "缺少定义ID"})
                        continue
                    if not config.is_valid_id(asset_type,defid):
                        failed_assets.append({"defid": defid, "error": f"不支持的资产ID: {defid}"})
                        continue
                    asset_id = str(uuid.uuid4())
                    attributes = asset_data.get("attributes", {})
                    
                    # 创建基础资产数据
                    asset = {
                        "id": asset_id,
                        "defid": defid,
                        "owner": username,
                        "type": asset_type,
                        "attributes": attributes,
                        "created_at": now,
                        "updated_at": now
                    }                    
                    # 根据资产类型添加特定字段
                    if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                        quantity = asset_data.get("quantity", 1)
                        if quantity <= 0:
                            failed_assets.append({"defid": defid, "error": "物品数量必须大于0"})
                            continue
                        asset["quantity"] = quantity
                        
                    elif asset_type == ItemType.EQUIPMENT:
                        level = asset_data.get("level", 1)
                        if level <= 0:
                            failed_assets.append({"defid": defid, "error": "装备等级必须大于0"})
                            continue
                        asset["level"] = level
                    
                    asset_docs.append(asset)
                    asset_ids.append(asset_id)
                    
                    # 添加到结果
                    result_asset = {
                        "id": asset_id,
                        "defid": defid
                    }
                    if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                        result_asset["quantity"] = asset.get("quantity", 1)
                    elif asset_type == ItemType.EQUIPMENT:
                        result_asset["level"] = asset.get("level", 1)
                    
                    result_assets.append(result_asset)
                    
                except Exception as e:
                    failed_assets.append({
                        "defid": asset_data.get("defid", "未知"),
                        "error": str(e)
                    })
            
            # 如果没有有效资产，直接返回
            if not asset_docs:
                message = config.get_item_type_name(asset_type) + "添加失败"
                return {
                    "success": True,
                    "message": message,
                    "added_assets": [],
                    "failed_assets": failed_assets
                }
            
            # 合并相同defid的物品，提高效率
            if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
                merged_assets = {}
                for asset in asset_docs:
                    defid = asset["defid"]
                    if defid in merged_assets:
                        merged_assets[defid]["quantity"] += asset["quantity"]
                        # 合并属性
                        if asset.get("attributes"):
                            merged_assets[defid]["attributes"].update(asset["attributes"])
                    else:
                        merged_assets[defid] = asset
                
                asset_docs = list(merged_assets.values())
                # 更新asset_ids，确保与asset_docs同步
                asset_ids = [asset["id"] for asset in asset_docs]
            
            # MongoDB操作
            changed_assets = []
            user = None
            db_manager = await MongoDBManager.get_instance()
            db = await db_manager.get_db()
            if db is not None:
                try:
                    # 检查MongoDB是否支持事务
                    is_replica_set = False
                    try:
                        # 检查是否为副本集
                        server_info = await db.client.admin.command('isMaster')
                        is_replica_set = server_info.get('setName') is not None or server_info.get('msg') == 'isdbgrid'
                    except Exception as e:
                        logger.warning(f"Worker {worker_id}: 检查MongoDB事务支持失败: {str(e)}")
                        is_replica_set = False
                    changed_assets, user = await self._process_assets_without_transaction(
                        username, asset_type, asset_docs, asset_ids, now
                    )
                except Exception as e:
                    logger.error(f"Worker {worker_id}: MongoDB操作失败: {str(e)}")
                    raise
            
            if user:
                try:
                    # 优先使用缓存管理器
                    item_cache = await ItemCacheManager.get_instance()
                    if item_cache is not None:
                        # 使用ItemCacheManager更新缓存
                        logger.debug(f"Worker {worker_id}: 使用缓存管理器更新缓存")
                        
                        # 1. 更新用户数据缓存
                        await self.set_user_data(username, user)
                        
                        # 2. 同步所有物品到缓存
                        items_query = {"owner": username, "type": asset_type}
                        items_cursor = db.items.find(items_query)
                        items_list = []
                        async for item in items_cursor:
                            item_copy = process_db_item(item)
                            items_list.append(item_copy)
                        
                        # 使用缓存管理器同步物品列表
                        await item_cache.sync_items_to_cache(username, items_list, asset_type)
                        
                        # 3. 单独更新每个变更的物品
                        for change in changed_assets:
                            asset = change["asset"]
                            await item_cache.set_item_details(
                                owner=username,
                                item_id=asset['id'],
                                item_data=asset
                            )
                except Exception as e:
                    logger.warning(f"Worker {worker_id}: 更新Redis缓存失败: {str(e)}")
            
            # 发送资产变更通知
            if notify and changed_assets:
                try:
                    # 准备通知数据
                    notification_assets = []
                    for change in changed_assets:
                        asset = change["asset"]
                        operation = change["operation"]
                        
                        notification_asset = {
                            "id": asset["id"],
                            "defid": asset["defid"]
                        }
                        
                        # 添加类型特定的字段
                        if (asset_type == ItemType.ITEM or asset_type == ItemType.RUNE) and "quantity" in asset:
                            notification_asset["quantity"] = asset["quantity"]
                        elif asset_type == ItemType.EQUIPMENT and "level" in asset:
                            notification_asset["level"] = asset["level"]
                        
                        # 添加属性（如果存在）
                        if "attributes" in asset and asset["attributes"]:
                            notification_asset["attributes"] = asset["attributes"]
                            
                        notification_assets.append(notification_asset)
                    
                    # 发送通知
                    await GlobalDBUtils.notify_asset_change(
                        username=username,
                        operation_type="add",  # 统一使用add，因为即使是更新也是增加数量
                        asset_type=asset_type,
                        assets_data=notification_assets
                    )
                except Exception as e:
                    logger.warning(f"Worker {worker_id}: 发送资产变更通知失败: {str(e)}")
            
            # 返回结果
            return {
                "success": True,
                "message": "添加成功",
                "added_assets": result_assets,
                "failed_assets": failed_assets
            }
        except Exception as e:
            logger.error(f"Worker {worker_id}: 添加资产失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"添加资产失败: {str(e)}",
                "added_assets": [],
                "failed_assets": failed_assets
            }
    async def _process_assets_without_transaction(self,username, asset_type, asset_docs, asset_ids, now):
        """不使用事务处理资产添加（用于不支持事务的MongoDB）"""
        # 创建一个列表来存储所有发生变更的资产（包括更新和新增）
        changed_assets = []
        
        # 检查用户是否存在
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        user = await db.users.find_one({"id": username})
        if not user:
            logger.error(f"用户 {username} 不存在")
            raise Exception(f"用户 {username} 不存在")
            
        # 处理物品类型的特殊逻辑
        if asset_type == ItemType.ITEM or asset_type == ItemType.RUNE:
            for asset in asset_docs[:]:  # 使用副本迭代
                existing_item = await db.items.find_one({
                    "owner": username, 
                    "type": asset_type, 
                    "defid": asset["defid"]
                })
                
                if existing_item:
                    # 更新现有物品
                    new_quantity = existing_item.get("quantity", 0) + asset["quantity"]
                    await db.items.update_one(
                        {"id": existing_item["id"]},
                        {"$set": {"quantity": new_quantity, "updated_at": now}}
                    )
                    
                    # 记录更新的物品信息
                    updated_asset = existing_item.copy()
                    updated_asset["quantity"] = new_quantity
                    updated_asset["updated_at"] = now
                    changed_assets.append({"asset": updated_asset, "operation": "update"})
                    
                    # 从待插入列表中移除
                    asset_docs.remove(asset)
                    asset_ids.remove(asset["id"])
        
        # 批量插入新资产
        if asset_docs:
            # 分批插入以减少失败风险
            batch_size = 50
            for i in range(0, len(asset_docs), batch_size):
                batch = asset_docs[i:i + batch_size]
                await db.items.insert_many(batch)
                # 记录新增的物品信息
                for asset in batch:
                    changed_assets.append({"asset": asset, "operation": "add"})
        
        try:            
            await self.update_connection_ttl(username, 3600)
        except Exception as e:
            logger.warning(f"更新用户连接TTL失败: {str(e)}")
        
        # 获取最新的用户数据
        user = await db.users.find_one({"id": username})
        
        # 返回更新后的用户数据和变更信息
        return changed_assets, user

    # ==================== 公会信息相关方法 ====================

    async def update_guild_info(self, username: str, guild_info: Dict[str, Any]) -> bool:
        """更新用户的公会信息"""
        try:
            return await self.update_user_field(username, "guild_info", guild_info)
        except Exception as e:
            logger.error(f"更新用户公会信息失败，用户: {username}, 错误: {str(e)}")
            return False

    async def get_guild_info(self, username: str) -> Optional[Dict[str, Any]]:
        """获取用户的公会信息"""
        try:
            user_data = await self.get_user_data(username)
            if user_data:
                return user_data.get("guild_info")
            return None
        except Exception as e:
            logger.error(f"获取用户公会信息失败，用户: {username}, 错误: {str(e)}")
            return None

    async def clear_guild_info(self, username: str) -> bool:
        """清除用户的公会信息"""
        try:
            return await self.update_user_field(username, "guild_info", None)
        except Exception as e:
            logger.error(f"清除用户公会信息失败，用户: {username}, 错误: {str(e)}")
            return False

    async def batch_update_guild_info(self, updates: List[Dict[str, Any]]) -> Dict[str, bool]:
        """批量更新多个用户的公会信息"""
        results = {}
        for update in updates:
            username = update.get("username")
            guild_info = update.get("guild_info")
            if username and guild_info is not None:
                results[username] = await self.update_guild_info(username, guild_info)
            else:
                results[username] = False
        return results