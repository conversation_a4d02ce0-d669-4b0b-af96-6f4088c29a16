# -*- coding: utf-8 -*-
"""
基于DistributedEquipmentService的装备WebSocket消息处理器
适配多Worker模式，使用分布式装备服务
"""

from typing import Dict, Any
from fastapi import WebSocket
import logging
import traceback
from datetime import datetime
from equipment_service_distributed import distributed_equipment_service
from ConnectionManager import ConnectionManager
from enums import MessageId, ItemType
from logger_config import setup_logger
from models import MessageModel

logger = setup_logger(__name__)

# 装备操作相关的消息ID
class EquipmentMessageId:
    EQUIP_ITEM = 2001      # 穿戴装备
    UNEQUIP_ITEM = 2002    # 卸下装备
    INTENSIFY_EQUIPMENT = 2003  # 强化装备
    UPGRADE_STAR = 2004    # 装备升星
    INLAY_RUNE = 2005      # 镶嵌符文
    REMOVE_RUNE = 2006     # 移除符文
    LOCK_EQUIPMENT = 2007  # 锁定装备
    UNLOCK_EQUIPMENT = 2008 # 解锁装备
    REROLL_PROPERTY = 2009 # 洗练属性
    GET_EQUIPMENT_DETAIL = 2010 # 获取装备详情
    GET_EQUIPMENT_SUMMARY = 2011 # 获取装备摘要

async def equip_item_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理穿戴装备请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.equip(username, item_id)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.EQUIP_ITEM, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} equipped item {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.EQUIP_ITEM, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to equip item {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling equip item: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"穿戴装备失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def unequip_item_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理卸下装备请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.unequip(username, item_id)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UNEQUIP_ITEM, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} unequipped item {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UNEQUIP_ITEM, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to unequip item {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling unequip item: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"卸下装备失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def intensify_equipment_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理强化装备请求"""
    try:
        item_id = data.get("item_id")
        use_blessing_stone = data.get("use_blessing_stone", False)
        
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.intensify_equipment(username, item_id, use_blessing_stone)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.INTENSIFY_EQUIPMENT, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} intensified equipment {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.INTENSIFY_EQUIPMENT, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to intensify equipment {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling intensify equipment: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"强化装备失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def upgrade_star_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理装备升星请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.upgrade_star(username, item_id)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UPGRADE_STAR, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} upgraded star for equipment {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UPGRADE_STAR, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to upgrade star for equipment {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling upgrade star: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"装备升星失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def inlay_rune_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理镶嵌符文请求"""
    try:
        item_id = data.get("item_id")
        rune_def_id = data.get("rune_def_id")
        hole_index = data.get("hole_index")
        
        if not item_id or rune_def_id is None or hole_index is None:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID、符文ID和孔位索引不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.inlay_rune(username, item_id, rune_def_id, hole_index)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.INLAY_RUNE, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} inlayed rune {rune_def_id} to equipment {item_id} at hole {hole_index}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.INLAY_RUNE, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to inlay rune to equipment {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling inlay rune: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"镶嵌符文失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def remove_rune_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理移除符文请求"""
    try:
        item_id = data.get("item_id")
        hole_index = data.get("hole_index")
        
        if not item_id or hole_index is None:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID和孔位索引不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 使用分布式装备服务
        success, msg = await distributed_equipment_service.remove_rune(username, item_id, hole_index)
        
        if success:
            # 获取更新后的装备数据
            equip_data = await distributed_equipment_service.get_serialized_data(username)
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.GET_EQUIPMENT, data=equip_data).model_dump(), 
                token
            )
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.REMOVE_RUNE, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} removed rune from equipment {item_id} at hole {hole_index}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.REMOVE_RUNE, data={"success": False, "error": msg}).model_dump(), 
                token
            )
            logger.warning(f"Player {username} failed to remove rune from equipment {item_id}: {msg}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling remove rune: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"移除符文失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def lock_equipment_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理锁定装备请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 获取装备
        equipment = await distributed_equipment_service.get_equipment(username, item_id)
        if not equipment:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.LOCK_EQUIPMENT, data={"success": False, "error": "装备不存在"}).model_dump(), 
                token
            )
            return False
        
        # 锁定装备
        success, msg = equipment.lock()
        
        if success:
            # 更新缓存和数据库
            await distributed_equipment_service._cache_equipment(username, equipment)
            db_manager = manager.db_manager
            await equipment.save_equipment(db_manager.db, db_manager.redis_client)
            
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.LOCK_EQUIPMENT, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} locked equipment {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.LOCK_EQUIPMENT, data={"success": False, "error": msg}).model_dump(), 
                token
            )
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling lock equipment: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"锁定装备失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def unlock_equipment_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理解锁装备请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 获取装备
        equipment = await distributed_equipment_service.get_equipment(username, item_id)
        if not equipment:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UNLOCK_EQUIPMENT, data={"success": False, "error": "装备不存在"}).model_dump(), 
                token
            )
            return False
        
        # 解锁装备
        success, msg = equipment.unlock()
        
        if success:
            # 更新缓存和数据库
            await distributed_equipment_service._cache_equipment(username, equipment)
            db_manager = manager.db_manager
            await equipment.save_equipment(db_manager.db, db_manager.redis_client)
            
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UNLOCK_EQUIPMENT, data={"success": True, "message": msg}).model_dump(), 
                token
            )
            logger.info(f"Player {username} unlocked equipment {item_id}")
        else:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.UNLOCK_EQUIPMENT, data={"success": False, "error": msg}).model_dump(), 
                token
            )
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling unlock equipment: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"解锁装备失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def get_equipment_detail_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理获取装备详情请求"""
    try:
        item_id = data.get("item_id")
        if not item_id:
            await manager.send_personal_message(
                MessageModel(msgId=MessageId.ERROR, data={"error": "装备ID不能为空"}).model_dump(), 
                token
            )
            return False
        
        # 获取装备详情
        equipment = await distributed_equipment_service.get_equipment(username, item_id)
        if not equipment:
            await manager.send_personal_message(
                MessageModel(msgId=EquipmentMessageId.GET_EQUIPMENT_DETAIL, data={"success": False, "error": "装备不存在"}).model_dump(), 
                token
            )
            return False
        
        # 获取装备详情
        detail = equipment.serialize_equipment()
        await manager.send_personal_message(
            MessageModel(msgId=EquipmentMessageId.GET_EQUIPMENT_DETAIL, data={"success": True, "equipment": detail}).model_dump(), 
            token
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling get equipment detail: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"获取装备详情失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

async def get_equipment_summary_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理获取装备摘要请求"""
    try:
        # 获取装备摘要
        summary = await distributed_equipment_service.get_equipment_summary(username)
        await manager.send_personal_message(
            MessageModel(msgId=EquipmentMessageId.GET_EQUIPMENT_SUMMARY, data={"success": True, "summary": summary}).model_dump(), 
            token
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling get equipment summary: {str(traceback.format_exc())}")
        await manager.send_personal_message(
            MessageModel(msgId=MessageId.ERROR, data={"error": f"获取装备摘要失败: {str(e)}"}).model_dump(), 
            token
        )
        return False

# 注册装备相关的消息处理器
def register_equipment_handlers_distributed(message_manager):
    """注册基于DistributedEquipmentService的装备消息处理器"""
    message_manager.register_handler(EquipmentMessageId.EQUIP_ITEM, equip_item_handler)
    message_manager.register_handler(EquipmentMessageId.UNEQUIP_ITEM, unequip_item_handler)
    message_manager.register_handler(EquipmentMessageId.INTENSIFY_EQUIPMENT, intensify_equipment_handler)
    message_manager.register_handler(EquipmentMessageId.UPGRADE_STAR, upgrade_star_handler)
    message_manager.register_handler(EquipmentMessageId.INLAY_RUNE, inlay_rune_handler)
    message_manager.register_handler(EquipmentMessageId.REMOVE_RUNE, remove_rune_handler)
    message_manager.register_handler(EquipmentMessageId.LOCK_EQUIPMENT, lock_equipment_handler)
    message_manager.register_handler(EquipmentMessageId.UNLOCK_EQUIPMENT, unlock_equipment_handler)
    message_manager.register_handler(EquipmentMessageId.GET_EQUIPMENT_DETAIL, get_equipment_detail_handler)
    message_manager.register_handler(EquipmentMessageId.GET_EQUIPMENT_SUMMARY, get_equipment_summary_handler)
    
    logger.info("Distributed equipment handlers registered successfully") 