"""
日志清理管理器
定期清理旧的日志文件，防止磁盘空间耗尽
"""

import asyncio
import glob
import logging
import os
import time
import traceback
from typing import Optional
from logger_config import setup_logger

logger = setup_logger(__name__)


class LogCleanupManager:
    """日志清理管理器"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.cleanup_task = None
        self.is_running = False
        self.cleanup_interval = 3600  # 1小时清理一次
        self.max_age_days = 7  # 保留7天的日志
        self.max_total_size_mb = 1000  # 最大总大小1GB
        
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    async def start_cleanup_task(self):
        """启动清理任务"""
        if self.is_running:
            logger.warning("日志清理任务已在运行")
            return
        
        self.is_running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("日志清理任务已启动")
    
    async def stop_cleanup_task(self):
        """停止清理任务"""
        self.is_running = False
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("日志清理任务已停止")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                await self.cleanup_logs()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"日志清理循环异常: {str(e)}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(60)  # 出错后等待1分钟再重试
    
    async def cleanup_logs(self):
        """执行日志清理"""
        try:
            from config import config
            log_config = config.get_log_config()
            log_dir = os.path.dirname(log_config["file"])
            
            if not os.path.exists(log_dir):
                return
            
            # 按年龄清理
            age_cleaned = await self._cleanup_by_age(log_dir)
            
            # 按总大小清理
            size_cleaned = await self._cleanup_by_size(log_dir)
            
            if age_cleaned > 0 or size_cleaned > 0:
                logger.info(f"日志清理完成: 按年龄清理 {age_cleaned} 个文件, 按大小清理 {size_cleaned} 个文件")
            
        except Exception as e:
            logger.error(f"日志清理失败: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def _cleanup_by_age(self, log_dir: str) -> int:
        """按年龄清理日志文件"""
        try:
            current_time = time.time()
            max_age_seconds = self.max_age_days * 24 * 3600
            current_pid = os.getpid()
            
            # 查找所有日志文件
            log_pattern = os.path.join(log_dir, "*.log*")
            log_files = glob.glob(log_pattern)
            
            cleaned_count = 0
            for log_file in log_files:
                try:
                    # 跳过当前进程的主日志文件
                    if f"pid-{current_pid}.log" in log_file and not log_file.endswith(('.1', '.2', '.3', '.4', '.5')):
                        continue
                    
                    # 检查文件年龄
                    file_age = current_time - os.path.getmtime(log_file)
                    if file_age > max_age_seconds:
                        file_size = os.path.getsize(log_file)
                        os.remove(log_file)
                        cleaned_count += 1
                        logger.debug(f"删除过期日志文件: {log_file} (大小: {file_size} bytes)")
                        
                except Exception as e:
                    logger.error(f"删除日志文件失败: {log_file}, 错误: {str(e)}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"按年龄清理日志失败: {str(e)}")
            return 0
    
    async def _cleanup_by_size(self, log_dir: str) -> int:
        """按总大小清理日志文件"""
        try:
            max_total_size = self.max_total_size_mb * 1024 * 1024  # 转换为字节
            current_pid = os.getpid()
            
            # 获取所有日志文件及其信息
            log_files = []
            total_size = 0
            
            log_pattern = os.path.join(log_dir, "*.log*")
            for log_file in glob.glob(log_pattern):
                try:
                    stat = os.stat(log_file)
                    log_files.append({
                        'path': log_file,
                        'size': stat.st_size,
                        'mtime': stat.st_mtime,
                        'is_current': f"pid-{current_pid}.log" in log_file and not log_file.endswith(('.1', '.2', '.3', '.4', '.5'))
                    })
                    total_size += stat.st_size
                except Exception as e:
                    logger.error(f"获取日志文件信息失败: {log_file}, 错误: {str(e)}")
            
            if total_size <= max_total_size:
                return 0
            
            # 按修改时间排序，最旧的在前
            log_files.sort(key=lambda x: x['mtime'])
            
            cleaned_count = 0
            size_to_free = total_size - max_total_size
            freed_size = 0
            
            for file_info in log_files:
                # 跳过当前进程的主日志文件
                if file_info['is_current']:
                    continue
                
                try:
                    os.remove(file_info['path'])
                    freed_size += file_info['size']
                    cleaned_count += 1
                    logger.debug(f"删除日志文件以释放空间: {file_info['path']} (大小: {file_info['size']} bytes)")
                    
                    if freed_size >= size_to_free:
                        break
                        
                except Exception as e:
                    logger.error(f"删除日志文件失败: {file_info['path']}, 错误: {str(e)}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"按大小清理日志失败: {str(e)}")
            return 0
    
    async def get_log_statistics(self) -> dict:
        """获取日志统计信息"""
        try:
            from config import config
            log_config = config.get_log_config()
            log_dir = os.path.dirname(log_config["file"])
            
            if not os.path.exists(log_dir):
                return {"error": "日志目录不存在"}
            
            log_pattern = os.path.join(log_dir, "*.log*")
            log_files = glob.glob(log_pattern)
            
            total_size = 0
            file_count = 0
            oldest_file = None
            newest_file = None
            
            for log_file in log_files:
                try:
                    stat = os.stat(log_file)
                    total_size += stat.st_size
                    file_count += 1
                    
                    if oldest_file is None or stat.st_mtime < oldest_file:
                        oldest_file = stat.st_mtime
                    
                    if newest_file is None or stat.st_mtime > newest_file:
                        newest_file = stat.st_mtime
                        
                except Exception:
                    continue
            
            return {
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_count": file_count,
                "oldest_file_age_hours": round((time.time() - oldest_file) / 3600, 2) if oldest_file else 0,
                "newest_file_age_hours": round((time.time() - newest_file) / 3600, 2) if newest_file else 0,
                "log_directory": log_dir
            }
            
        except Exception as e:
            logger.error(f"获取日志统计信息失败: {str(e)}")
            return {"error": str(e)}


# 全局实例
log_cleanup_manager = None


async def get_log_cleanup_manager():
    """获取全局日志清理管理器实例"""
    global log_cleanup_manager
    if log_cleanup_manager is None:
        log_cleanup_manager = await LogCleanupManager.get_instance()
    return log_cleanup_manager
