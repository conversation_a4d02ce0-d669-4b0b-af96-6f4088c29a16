import sys
import os
import asyncio
import json
import time
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from xxsg.monster_cooldown import Monster<PERSON>ooldownManager, CooldownType
from xxsg.monster_cooldown_service import MonsterCooldownService
from game_database import DatabaseManager
from service_locator import ServiceLocator
from ConnectionManager import ConnectionManager

async def test_monster_respawn_notification():
    """测试怪物重生通知功能"""
    print("初始化数据库管理器...")
    db_manager = DatabaseManager()
    await db_manager.startup()
    
    print("初始化连接管理器...")
    conn_manager = ConnectionManager(db_manager)
    await conn_manager.initialize()
    
    # 注册服务
    ServiceLocator.register("db_manager", db_manager)
    ServiceLocator.register("conn_manager", conn_manager)
    
    # 创建冷却管理器和服务
    print("初始化怪物冷却服务...")
    cooldown_service = MonsterCooldownService()
    cooldown_manager = cooldown_service.cooldown_manager
    
    # 测试数据
    monster_id_global = "test_global_monster"
    monster_id_guild = "test_guild_monster"
    username = "test_user"
    guild_id = "test_guild"
    
    # 模拟用户连接
    print("模拟用户连接...")
    await db_manager.register_user_connection(username, "test_token")
    
    # 模拟军团数据
    print("创建测试军团数据...")
    await db_manager.db.guilds.update_one(
        {"guild_id": guild_id},
        {"$set": {"name": "测试军团", "members": [username]}},
        upsert=True
    )
    
    # 测试全服怪物冷却和通知
    print("\n=== 测试全服怪物重生通知 ===")
    print(f"1. 设置全服怪物 {monster_id_global} 的冷却时间为5秒...")
    await cooldown_manager.set_cooldown(
        monster_id_global, username, CooldownType.GLOBAL, 5  # 5秒冷却
    )
    
    # 测试军团怪物冷却和通知
    print(f"2. 设置军团怪物 {monster_id_guild} 的冷却时间为8秒...")
    await cooldown_manager.set_cooldown(
        monster_id_guild, username, CooldownType.GUILD, 8, guild_id  # 8秒冷却
    )
    
    # 启动冷却服务
    print("3. 启动怪物冷却服务...")
    await cooldown_service.start()
    
    # 监听广播消息
    print("4. 开始监听通知消息...")
    
    # 模拟连接管理器的广播方法，用于捕获通知
    original_broadcast = conn_manager.broadcast
    original_broadcast_to_users = conn_manager.broadcast_to_users
    
    notifications = []
    
    async def mock_broadcast(message, priority=0):
        print(f"\n收到全服广播: {json.dumps(message, indent=2, ensure_ascii=False)}")
        notifications.append(("global", message))
        # 调用原始方法确保正常功能
        await original_broadcast(message, priority)
    
    async def mock_broadcast_to_users(message, usernames, priority=0):
        print(f"\n收到军团广播: {json.dumps(message, indent=2, ensure_ascii=False)}")
        notifications.append(("guild", message))
        # 调用原始方法确保正常功能
        await original_broadcast_to_users(message, usernames, priority)
    
    # 替换广播方法
    conn_manager.broadcast = mock_broadcast
    conn_manager.broadcast_to_users = mock_broadcast_to_users
    
    # 等待冷却结束和通知
    print("5. 等待冷却结束和通知...")
    await asyncio.sleep(10)  # 等待足够长的时间确保冷却结束和通知发送
    
    # 恢复原始广播方法
    conn_manager.broadcast = original_broadcast
    conn_manager.broadcast_to_users = original_broadcast_to_users
    
    # 验证通知
    print("\n=== 通知结果 ===")
    print(f"收到的通知总数: {len(notifications)}")
    for i, (notify_type, message) in enumerate(notifications):
        print(f"通知 {i+1} - 类型: {notify_type}")
        data = message.get("data", {})
        print(f"  - 怪物ID: {data.get('monster_id') or data.get('monsters', [{}])[0].get('monster_id')}")
        print(f"  - 消息: {data.get('message')}")
    
    # 停止服务
    print("\n6. 停止怪物冷却服务...")
    await cooldown_service.stop()
    
    # 清理测试数据
    print("7. 清理测试数据...")
    await cooldown_manager.reset_cooldown(monster_id_global, CooldownType.GLOBAL)
    await cooldown_manager.reset_cooldown(monster_id_guild, CooldownType.GUILD, None, guild_id)
    await db_manager.db.guilds.delete_one({"guild_id": guild_id})
    
    # 关闭连接
    await conn_manager.cleanup()
    await db_manager.shutdown()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(test_monster_respawn_notification()) 