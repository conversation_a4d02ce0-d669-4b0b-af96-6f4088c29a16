"""
商店系统Redis连接管理器
解决Redis连接在事件循环关闭时的清理问题
"""

import logging
import asyncio
from typing import Optional
from redis_manager import RedisManager

logger = logging.getLogger(__name__)


class ShopRedisManager:
    """商店系统专用Redis管理器"""
    
    _instance = None
    _redis_client = None
    _redis_manager = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    async def get_redis(self):
        """获取Redis连接"""
        try:
            if self._redis_client is None:
                if self._redis_manager is None:
                    self._redis_manager = await RedisManager.get_instance()
                self._redis_client = await self._redis_manager.get_redis()
                logger.debug("商店系统Redis连接已建立")
            
            return self._redis_client
            
        except Exception as e:
            logger.error(f"获取Redis连接失败: {str(e)}")
            # 重置连接，下次重试
            self._redis_client = None
            self._redis_manager = None
            raise
    
    async def close(self):
        """关闭Redis连接"""
        try:
            if self._redis_client:
                # 不直接关闭连接，让RedisManager管理
                self._redis_client = None
                logger.debug("商店系统Redis连接已释放")
            
            if self._redis_manager:
                self._redis_manager = None
                
        except Exception as e:
            logger.error(f"关闭Redis连接时发生错误: {str(e)}")
    
    async def ping(self) -> bool:
        """检查Redis连接是否正常"""
        try:
            redis = await self.get_redis()
            await redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接检查失败: {str(e)}")
            return False
    
    async def reset_connection(self):
        """重置Redis连接"""
        try:
            await self.close()
            # 下次调用get_redis时会重新建立连接
            logger.info("Redis连接已重置")
        except Exception as e:
            logger.error(f"重置Redis连接失败: {str(e)}")


# 全局实例
_shop_redis_manager = None


async def get_shop_redis_manager():
    """获取商店Redis管理器实例"""
    global _shop_redis_manager
    if _shop_redis_manager is None:
        _shop_redis_manager = await ShopRedisManager.get_instance()
    return _shop_redis_manager


async def get_shop_redis():
    """获取商店Redis连接的便捷函数"""
    manager = await get_shop_redis_manager()
    return await manager.get_redis()


async def close_shop_redis():
    """关闭商店Redis连接的便捷函数"""
    global _shop_redis_manager
    if _shop_redis_manager:
        await _shop_redis_manager.close()
        _shop_redis_manager = None


# 在模块卸载时自动清理
import atexit

def cleanup_on_exit():
    """程序退出时清理资源"""
    try:
        loop = asyncio.get_event_loop()
        if not loop.is_closed():
            loop.run_until_complete(close_shop_redis())
    except Exception:
        pass  # 忽略清理时的错误

atexit.register(cleanup_on_exit)
