"""
Monster System Package

This package implements a comprehensive monster system for the game server,
including monster cooldown management and WebSocket handlers.

Key components:
- monster_cooldown.py: Monster cooldown data structure and operations
- monster_handlers.py: WebSocket handlers for monster-related operations

Note: Monster configuration is now integrated into the main config system.
"""

__version__ = "1.0.0" 