from enum import IntEnum, Enum

class MessageId(IntEnum):
    GM = 1001 # GM命令
    ERROR = 1000 # 错误
    HEARTBEAT = 0 # 心跳
    PING = -1 # Ping消息，用于连接检测
    CHAT = 1 # 聊天
    PRIVATE_CHAT =2 # 私聊
    GROUP_CHAT = 3 # 群聊
    GET_ITEMS = 4 # 获取道具
    DELETE_ITEM = 5 # 删除道具
    BROADCAST_MESSAGE = 6 # 广播消息
    SET_NICKNAME = 7 # 设置昵称
    ROLE_INFO = 8 # 角色信息 
    ENTER_GAME = 9 # 进入游戏
    CREATE_ROLE = 10 # 创建角色
    ASSET_CHANGED = 11       # 单个资产变更
    ASSET_BATCH_CHANGED = 12 # 批量资产变更
    GET_EQUIPMENT = 13 # 获取装备
    GET_RUNE = 14 # 获取符文    
    
    # Role related message IDs
    GET_ROLES = 20 # 获取角色列表
    CREATE_ROLE = 21 # 创建角色
    DELETE_ROLE = 22 # 删除角色
    LEVEL_UP_ROLE = 23 # 角色升级
    STAR_UP_ROLE = 24 # 角色升星
    ADD_EXP_ROLE = 25 # 角色加经验
    EQUIP_WEAPON = 26 # 装备武器
    UNEQUIP_WEAPON = 27 # 卸下武器
    EQUIP_ARMOR = 28 # 装备防具
    UNEQUIP_ARMOR = 29 # 卸下防具
    LEARN_SKILL = 30 # 学习技能
    EQUIP_SKILL = 31 # 装备技能
    UNEQUIP_SKILL = 32 # 卸下技能
    CHANGE_ROLE_STATE = 33 # 改变角色状态
    LOCK_ROLE = 34 # 锁定角色
    UNLOCK_ROLE = 35 # 解锁角色
    
    # Monster related message IDs
    CHECK_COOLDOWN = 100     # Check monster cooldown
    MONSTER_KILLED = 101     # Monster killed notification
    GET_ALL_COOLDOWNS = 102  # Get all active cooldowns
    RESET_COOLDOWN = 103     # Reset cooldown (GM only)
    MONSTER_RESPAWNED = 104  # Monster respawned notification
    GET_MONSTERS = 105       # Get monster list
    
class ItemType(str, Enum):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文

class RoleState(str, Enum):
    """角色状态枚举"""
    IDLE = "idle"                   # 空闲
    RETINUE = "retinue"             # 上阵
    MANOR_TASK = "manor_task"       # 任务
    MANOR_STATION = "manor_station" # 驻守
    MANOR_BATTLE = "manor_battle"   # 出征
    MANOR_REC = "manor_rec"         # 募兵
    MANOR_TRAIN = "manor_train"     # 训练
    MANOR_TECH = "manor_tech"       # 研究中
    MANOR_LEARN = "manor_learn"     # 进修
    MANOR_LEVELUP = "manor_levelup" # 升级建筑
    MANOR_MASTER = "manor_master"   # 军团长
    MANOR_TRIM = "manor_trim"       # 整备

class CooldownType(str, Enum):
    PERSONAL = "personal"  # Personal cooldown
    GUILD = "guild"        # Guild cooldown
    GLOBAL = "global"      # Global cooldown