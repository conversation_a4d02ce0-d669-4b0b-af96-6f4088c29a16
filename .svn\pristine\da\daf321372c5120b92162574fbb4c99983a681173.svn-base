﻿-- [Comment]
-- jnmo
roleData = class("roleData")
roleData.__index = roleData
wSoulName = {
    [1] = "剑",
    [2] = "匕",
    [3] = "矛",
    [4] = "锤",
    [5] = "斧",
    [6] = "镖"
}
-- 空闲
RETINUE_STATE_IDLE = 1
-- 上阵
RETINUE_STATE_RETINUE = 2
-- 任务
RETINUE_STATE_MANOR_TASK = 3
-- 驻守
RETINUE_STATE_MANOR_STATION = 4
-- 出征
RETINUE_STATE_MANOR_BATTLE = 5
-- 募兵
RETINUE_STATE_MANOR_REC = 6
-- 训练
RETINUE_STATE_MANOR_TRAIN = 7
-- 研究中
RETINUE_STATE_MANOR_TECH = 8
-- 进修
RETINUE_STATE_MANOR_LEARN = 9
-- 升级建筑
RETINUE_STATE_MANOR_LEVELUP = 10
-- 军团长
RETINUE_STATE_MANOR_MASTER = 11
-- 整备
RETINUE_STATE_MANOR_TRIM = 12
-- 属性表
pro_arr = {
    hp = 0,
    -- 血量
    bp = 0,
    -- 蓝
    pow = 0,
    -- 力量
    dex = 0,
    -- 敏捷
    int = 0,
    -- 智力
    -- 体质
    con = 0,
    -- 精神
    men = 0,
    -- 幸运
    luck = 0,
    -- 防御
    def = 0,
    -- 攻击
    att = 0,
    -- 魔法攻击
    m_att = 0,
    -- 魔法防御
    m_def = 0,
    pow_p = 0,
    -- 武力
    int_p = 0,
    -- 智力
    def_p = 0,
    -- 防御
    dex_p = 0,
    -- 速度
    con_p = 0,
    -- 体魄
    cirt_odd = 0,
    -- 暴击率
    cirt_hit = 0,
    -- 暴击伤害
    cirt_red = 0,
    -- 暴击伤害减免
    akl_p = 0,
    -- 主动伤害
    pkl_p = 0,
    -- 追击伤害
    askl = 0,
    -- 主动发动率
    pskl = 0,
    -- 追击发动率
    dmg_p = 0,
    -- 伤害提升百分比
    red = 0,
    -- 伤害减免
    trt = 0,
    -- 治疗
    hit = 0,
    -- 命中
    --盾牌
    bk = 0,
    -- 破防
    nd = 0,
    -- 普攻伤害
    trtad = 0,
    -- 治疗加成
    trtpd = 0,
    -- 被治疗加成
    tps = 0,
    -- 韧性
    mdmg_p = 0,
    -- 法术伤害
    pdmg_p = 0,
    -- 物理伤害
    tran = 0,
    -- 伤害转换为血量
    mred = 0,
    -- 魔法免伤
    pred = 0,
    -- 物理免伤
    pcrit = 0,
    -- 受暴击几率

    mmaskl = 0,
    -- 造成法术伤害的主战法发动几率
    amaskl = 0,
    -- 造成武力伤害的主战法发动几率

    shield_dmg = 0,
    -- 有护盾时 伤害增加

    shield_trtad = 0,
    -- 有护盾时治疗增加

    shield_trtpd = 0,
    -- 有护盾时被治疗加成

    pbk_dmg = 0,
    -- 对被破甲敌人造成伤害提升
    pbk_red = 0,
    -- 受到被破甲敌人的伤害降低

    bburn_dmg = 0,
    -- 对燃烧敌人造成伤害提升
    bburn_red = 0,
    -- 受到燃烧敌人的伤害降低

    avoid_dmg = 0,
    -- 有规避时伤害增加
    avoid_red = 0,
    -- 有规避时受到伤害降低

    chop = 0,
    -- 对比自己血量百分比高的单位伤害提升
    health_dmg = 0,
    -- 血量高于80%时，伤害提升
    health_red = 0,
    -- 血量高于80%时，受到伤害降低
    hurt_dmg = 0,
    -- 血量低于30%时,伤害提升
    hurt_red = 0,
    -- 血量低于30%时,受到伤害降低

    tag_health_dmg = 0,
    -- 对血量高于80%的敌人造成伤害提升

    tag_hurt_dmg = 0,
    -- 对血量低于30%的敌人造成伤害提升

    tag_soldier1_dmg = 0,
    -- 对步兵伤害提升
    tag_soldier1_red = 0,
    -- 受到步兵伤害降低

    tag_soldier2_dmg = 0,
    -- 对骑伤害提升
    tag_soldier2_red = 0,
    -- 受到骑兵伤害降低

    tag_soldier3_dmg = 0,
    -- 对弓兵伤害提升
    tag_soldier3_red = 0,
    -- 受到弓兵伤害降低
    tag_soldier_dmg = 0,
    tag_soldier_red = 0,
    --护盾强度增加
    shield_p = 0,
    --
}


soldier_datas = {
    [1] = { 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211 },
    [2] = { 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235 },
    [3] = { 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223 },
}
function roleData:ctor(defid)
    print("roleData:ctor()")
    self.defid = defid
    self.roleid = me.guid()
    self.level = ec_int(1)
    -- 进阶级数
    self.starLevel = ec_int(0)
    self.name = nil
    self.bLead = false
    self.state = RETINUE_STATE_IDLE
    -- 称号
    self.titleId = 1000
    -- 自动加血加蓝
    self.addhp_per = 70
    self.addbp_per = 70
    -- 自动给加血的道具id
    self.addhp_id = nil
    -- 自动给加蓝的道具id
    self.addbp_id = nil
    -- 功勋
    self.achCount = 0
    -- 图片ID
    self.imgId = nil
    -- 士兵数据
    self.soldierdatas = { }

    -- 当前出战士兵
    self.curSoldier = 0
    self.exp_asp = 0
    self.weapons = {
        -- [1] = 1000,
        -- [2] = 1001,
        -- [3] = 1002,
    }
    -- 已经装备的将星
    self.rolestars = { }
    -- 已经学会的技能
    self.learnedskills = { }
    -- 已经装备的技能
    self.skills = { }
    -- 被动技能
    self.parSkills = { }
    self.armors = { }
    -- 特技 主要用于领地和领地战斗
    self.stunts = {
        [1] = 0,
        [2] = 0,
        [3] = 0,
    }
    -- 称号增加的
    self.title_add = me.copyTab(pro_arr)

    -- 永久增加的。药剂等等
    self.perpetual_add = me.copyTab(pro_arr)
    self.pro_add = me.copyTab(pro_arr)
    -- 剩余的属性点
    self.leftproCount = 0
    -- 装备增加的
    self.armors_add = me.copyTab(pro_arr)
    self.weapons_add = me.copyTab(pro_arr)
    -- 套装加成
    self.equip_suit = me.copyTab(pro_arr)
    -- bosscard加成
    self.bosscard_add =  me.copyTab(pro_arr)
    -- 属性防 属性伤
    self.imgdue_def = { }
    self.imgdue_dmg = { }
    self.buffs = { }
    self.exp = 0
    -- 默认三格怒气
    self.maxMp = 3
    -- 套装数据
    self.eSuitDatas = { }

    self.battle_camp = nil
    self.battle_pos = nil
    -- 当前施法技能id
    self.cur_skillid = nil
    -- 当前施法倒计时
    self.cur_release = nil

    self.bRetinue = false

    self.bchoose = false
    -- 伤害统计
    self.normalHits = 0
    self.skillHits = 0
    -- 治疗统计
    self.treatNums = 0
    -- 突破星级
    self.star = ec_int(0)
    
    -- 暴走状态是否消失了
    self.buffclear = false
    self.locked = false
    -- 伤兵
    self.hurt = 0
    self.starPro = { }

    -- 一键穿戴符文的序列
    self.qkIndex = 1

    -- 临时技能组
    self.uskills = { }
    -- 战斗是临时的附加属性
    self.battlepro = { }
    -- netpro
    self.netBattlePro = me.copyTab(pro_arr)
    self.netBattleBasePro = me.copyTab(pro_arr)

    -- 体力
    self.strength = 0

    -- 定位4
    self.jobPos = 0
    --怪物困难属性
    self.statePro =  me.copyTab(pro_arr)

    -- 装备存储
    self.saveArmors = { }
    self.saveWeapons = { }
    self.saveSoldier = ""
    -- 当前升阶进度
    self.starProcess = 0
    --转生次数
    self.turnNum = ec_int(0)
    self:initDataByLevel(self.level)
end
function roleData:costStrength(num)
    self.strength = math.max(0, self.strength - num)
end
function roleData:addTempBattlePro(str)
    local t1 = me.split(str, ",")
    if t1 then
        for key, var in pairs(t1) do
            local t2 = me.split(var, ":")
            if t2 then
                if t2[1] == "sk" then
                    local sdata = cfg[CfgType.SKILL][tonumber(t2[2])]
                    if tonumber(sdata.iStunt) == SKILL_KIND_PAS then
                        if sdata.addition then
                            local s = me.split(tostring(sdata.addition), SKILL_BUFF_SPLIT)
                            if s then
                                for key, var in pairs(s) do
                                    self:addBuff(var, self)
                                end
                            end
                        end
                    else
                        table.insert(self.uskills, tonumber(t2[2]))
                    end
                else
                    if self.battlepro[t2[1]] == nil then
                        self.battlepro[t2[1]] = 0
                    end
                    self.battlepro[t2[1]] = self.battlepro[t2[1]] + tonumber(t2[2])
                end
            end
        end
    end
end
function roleData:getStar()
    return ec_int(self.star)
end
function roleData:getLevel()
    return ec_int(self.level)
end
function roleData:getStarVal()
    return(self:getStar() / 8 + 1)
end
function roleData:bCanUse()
    if self.state ~= RETINUE_STATE_IDLE or self.locked then
        return false
    end
    if next(self.weapons) or next(self.armors) or next(self.rolestars) then
        return false
    end
    if self.skills[2] or self.skills[3] or self.skills[4] then
        return false
    end
    return not self:getCurSoldierData()
end
function roleData:IsHealth()
    return self:getHpPercent() >= 80
end
function roleData:IsHurt()
    return self:getHpPercent() <= 30
end
-- 最大进阶数
function roleData:getMaxStarLevel()
    return self:getDef().star
end
-- 获取进阶时消耗的卡片数量
function roleData:getStarLevelNeedCards()
    return self:getStarLevel() + 1
end
-- 获取进阶价值
function roleData:getStarLevelVal()
    if self:getStarLevel() > 1 then
        local val = 0
        for var = 1, self:getStarLevel() do
            val = var + val
        end
        -- 1:1 , 2:3 ,3:6 4:10,5:15
        return val + 1 + self.starProcess
    else
        return self:getStarLevel() + 1 + self.starProcess
    end
    return 1
end
-- 获取当前进阶数
function roleData:getStarLevel()
    return ec_int(self.starLevel)
end
-- local rand_pro_star = {
-- quality_rand1 = {
--    50,30,15,10,5,2
-- },
-- quality_rand2 = {
--    80,80,50,30,15,10,5,2
-- },
-- quality_rand3 = {
--    100,100,80,80,50,30,15,10,5,2
-- },
-- quality_rand4 = {
--    120,120,100,100,80,80,50,30,15,10,5,2
-- },
-- quality_rand5 = {
--    150,140,140,120,120,100,100,80,80,50,30,15,10,5,2
-- }
-- }
local rand_pro_star = {
    quality_rand1 =
    {
        70,60,30,24,12,6
    },
    quality_rand2 =
    {
        80,70,60,50,35,25,15,8
    },
    quality_rand3 =
    {
        110,90,80,70,60,50,40,30,20,10
    },
    quality_rand4 =
    {
        130,110,100,90,80,70,60,50,40,30,20,10
    },
    quality_rand5 =
    {
        150,140,130,120,110,100,90,80,70,60,50,40,30,20,10
    }
}
function roleData:addStarLevel(num)
    local xnum = 0
    if ec_int(self.starLevel) + num >= self:getMaxStarLevel() then
        xnum = self:getMaxStarLevel() - ec_int(self.starLevel)
    else
        xnum = num
    end

    if xnum > 0 then
        for i = 1, xnum do
            self:starProAdd(ec_int(self.starLevel) + i)
        end
    end
    self.starLevel = ec_add(self.starLevel, xnum)
end
function roleData:starProAdd(i)
    local msg = { }
    local pro = cfg[CfgType.RANDIMDUE]
    local def = pro[me.getRandom(#pro)]
    msg.qua = getRandByExpect(rand_pro_star["quality_rand" .. i])
    msg.key = def.imdue
    msg.val = me.getRandomBetween(def.base *(msg.qua), def.base *(msg.qua + 1))
    self.starPro[i] = msg
end
function roleData:resetStarProAdd(idx)
    local msg = { }
    local pro = cfg[CfgType.RANDIMDUE]
    local def = pro[me.getRandom(#pro)]
    msg.qua = getRandByExpect(rand_pro_star["quality_rand" .. idx])
    msg.key = def.imdue
    msg.val = me.getRandomBetween(def.base *(msg.qua), def.base *(msg.qua + 1))
    return msg
end
function roleData:resetStarProTmp(data)
    local starpro = { }
    for key, var in pairs(data) do
        if var == false then
            starpro[key] = self:resetStarProAdd(key)
        else
            starpro[key] = self.starPro[key]
        end
    end
    return starpro
end
function roleData:resetStarPro(data)
    self.starPro = me.copyTab(data)
    gameLogic:getInstance():save( { SAVE_ROLE_DATA })
end
function roleData:getParamDef()
    local def = cfg[CfgType.CAREER][self.defid]
    local pid = def.paramsid
    return cfg[CfgType.CLASSES][pid]
end
function roleData:initDataByLevel(lv)
    print("initDataByLevel " .. lv .. " - " .. self.defid)
    local def = cfg[CfgType.CAREER][self.defid]
    local pid = def.paramsid
    local pdata = cfg[CfgType.CLASSES][pid]
    self.level = lv
    self.ani = def.ani
    lv = ec_int(lv)
    self.maxHp = pdata.min_hp + math.ceil((pdata.max_hp - pdata.min_hp) * self:getStarVal() * lv / self:getDefMaxLevel())  
    self.hp = self.maxHp
    self.maxBp = pdata.min_bp + math.ceil((pdata.max_bp - pdata.min_bp) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.bp = self.maxBp
    self.spd = pdata.min_spd + math.ceil((pdata.max_spd - pdata.min_spd) * self:getStarVal() * lv / self:getDefMaxLevel()) + ec_int( self.turnNum )*lv
    self.power = pdata.min_power + math.ceil((pdata.max_power - pdata.min_power) * self:getStarVal() * lv / self:getDefMaxLevel()) + ec_int( self.turnNum )*lv
    self.con = pdata.min_con + math.ceil((pdata.max_con - pdata.min_con) * self:getStarVal() * lv / self:getDefMaxLevel())  + ec_int( self.turnNum )*lv
    self.defence = pdata.min_defence + math.ceil((pdata.max_defence - pdata.min_defence) * self:getStarVal() * lv / self:getDefMaxLevel()) 
    self.luck = pdata.min_luck + math.ceil((pdata.max_luck - pdata.min_luck) * self:getStarVal() * lv / self:getDefMaxLevel()) 
    self.int = pdata.min_int + math.ceil((pdata.max_int - pdata.min_int) * self:getStarVal() * lv / self:getDefMaxLevel()) + ec_int( self.turnNum )*lv
    self.men = pdata.min_men + math.ceil((pdata.max_men - pdata.min_men) * self:getStarVal() * lv / self:getDefMaxLevel()) 
    self.maxExp = expForUserLevel(lv + 1)
    if def.skill and tonumber(def.skill) > 0 and self.skills[1] == nil then
        self.skills[1] = tonumber(def.skill)
    end
    -- 默认怒气为0
    self.mp = 0
    -- 怒气值
    self.rage = 0
    self.name = def.name
    self.bpRestore = 10
    self.isUpdateBuff = false
end
function roleData:addTurnNum()
    self.turnNum = ec_add(self.turnNum,1)
    self:initDataByLevel(ec_int( 1))
    self:addExp(1,true)
    self:unEquipAllWeapon()
    showTips("转生成功")
end
function roleData:initWithMonsterId(id, percent)
    local mdef = cfg[CfgType.MONSTER][tonumber(id)]
    if mdef then
        self.mdef = mdef
        self.bRetinue = true
        self:setLevel(mdef.level)
        self:setStar(mdef.awake or 0)
        if mdef.master_skill then
            self.skills[1] = mdef.master_skill
        end
        if mdef.skills then
            if mdef.skills then
                local s = me.split(mdef.skills, ",")
                if s then
                    for key, var in pairs(s) do
                        local skill_def = cfg[CfgType.SKILL][tonumber(var)]
                        table.insert(self.skills, tonumber(var))
                    end
                end
            end
        end
        local per = percent or 1
        self.pro_add["hp"] =(mdef.hp or 0) * per
        self.pro_add["con"] =(mdef.con or 0) * per
        self.pro_add["dex"] =(mdef.spd or 0) * per
        self.pro_add["pow"] =(mdef.power or 0) * per
        self.pro_add["int"] =(mdef.int or 0) * per
        self.pro_add["def"] =(mdef.defence or 0) * per
        self.pro_add["att"] =(mdef.att or 0) * per
        self.pro_add["m_att"] =(mdef.m_att or 0) * per
        self.pro_add["trt"] =(mdef.trt or 0) * per
        self.def_bless_monster = mdef.def_bless or 0
        if mdef.master_name then
            self.name = mdef.master_name
        end
        -- 符文属性
        if mdef.rune then
            local t = me.split(mdef.rune, ",")
            if t then
                for key, var in pairs(t) do
                    local imgbue = me.split(var, ":")
                    if imgbue then
                        self.pro_add[imgbue[1]] = self.pro_add[imgbue[1]] + tonumber(imgbue[2])
                    end
                end
            end
        end
        self:initStageBossPro()
        self:initPasSkill()
        self:resetBattlePro()
        self:resurgence()
    end
end
function roleData:initStageBossPro()
    local def = self:getMonsterDef()
    local level = def.level
    local bosstype = def.bsoldier
    local leveladd = level
    local level150 = level>=100 and math.floor( (level - 100) / 10 ) or 0
    if ec_int(user.stage) == 2 then               
        if bosstype == 1 then  
            self.statePro["hp"] = self.statePro["hp"] + level*150000 + 10000000 + level150*5000000
        elseif bosstype == 2 then
            self.statePro["hp"] = self.statePro["hp"] + 800000 + level * 10000+ level150*500000
        elseif bosstype == 3 then
            self.statePro["hp"] = self.statePro["hp"] + 5000000 + def.hp + level * 100000 +level150*1000000
        end
        self.statePro["dex"] = self.statePro["dex"] + 2000
        self.statePro["pow"] = self.statePro["pow"] + 5000
        self.statePro["int"] = self.statePro["int"] + 5000
        self.pro_add["def"] = self.pro_add["def"] + 5000
        self.statePro["trt"] = self.statePro["trt"] + 2000
        self.statePro["cirt_odd"] = self.statePro["cirt_odd"] + 20
        self.statePro["red"] = self.statePro["red"] + 200
        self.statePro["dmg_p"] = self.statePro["dmg_p"] + 30 + leveladd + level150*10
        self.statePro["att"] = self.statePro["att"] + level*15 + 1000 +  level150*150
        self.statePro["m_att"] = self.statePro["m_att"] + level*15 + 1000+  level150*150

        if def.stage2_hp then            
            self.statePro["hp"] = self.statePro["hp"] + def.stage2_hp
        end
        if def.stage2_pow then            
            self.statePro["pow"] = self.statePro["pow"] + def.stage2_pow
        end
        if def.stage2_int then            
            self.statePro["int"] = self.statePro["int"] + def.stage2_int
        end
    elseif ec_int(user.stage) == 3 then 
        local level150 = level>=100 and math.floor( (level - 100) / 10 ) or 0      
        if bosstype == 1 then  
            self.statePro["hp"] = self.statePro["hp"] + level*450000 + 50000000  + level150*10000000
        elseif bosstype == 2 then
            self.statePro["hp"] = self.statePro["hp"] + 2400000 + level * 20000+ level150*1000000
        elseif bosstype == 3 then
            self.statePro["hp"] = self.statePro["hp"] + 30000000 + def.hp + level * 200000 +level150*3000000
        end
        self.statePro["dex"] = self.statePro["dex"] + 8000
        self.statePro["pow"] = self.statePro["pow"] + 15000
        self.statePro["int"] = self.statePro["int"] + 15000
        self.pro_add["def"] = self.pro_add["def"] + 5000
        self.statePro["trt"] = self.statePro["trt"] + 12000
        self.statePro["cirt_odd"] = self.statePro["cirt_odd"] + 30
        self.statePro["red"] = self.statePro["red"] + 500
        self.statePro["pcrit"] = self.statePro["pcrit"] + 20   
        self.statePro["dmg_p"] = self.statePro["dmg_p"] + 80 + leveladd*2 + level150*20  
        self.statePro["att"] = self.statePro["att"] + level*30+2000
        self.statePro["m_att"] = self.statePro["m_att"] + level*30+2000
    elseif ec_int(user.stage) == 4 then
        if bosstype == 1 then  
            self.statePro["hp"] = self.statePro["hp"] + level*1200000 + 150000000 + level150*20000000
        elseif bosstype == 2 then
            self.statePro["hp"] = self.statePro["hp"] + 6000000 + level * 20000+ level150*2000000
        elseif bosstype == 3 then
            self.statePro["hp"] = self.statePro["hp"] + 150000000 + def.hp * 2  + level * 400000+ level150*6000000
        end
        self.statePro["dex"] = self.statePro["dex"] + 20000
        self.statePro["pow"] = self.statePro["pow"] + 35000
        self.statePro["int"] = self.statePro["int"] + 35000
        self.pro_add["def"] = self.pro_add["def"] + 5000
        self.statePro["trt"] = self.statePro["trt"] + 32000
        self.statePro["cirt_odd"] = self.statePro["cirt_odd"] + 45
        self.statePro["red"] = self.statePro["red"] + 1000
        self.statePro["pcrit"] = self.statePro["pcrit"] + 50
        self.statePro["dmg_p"] = self.statePro["dmg_p"] + 200 + leveladd*6 + level150
        self.statePro["att"] = self.statePro["att"] + level*80
        self.statePro["m_att"] = self.statePro["m_att"] + level*80
    end
end
function roleData:initWithSeasonMonsterId(id, clevel)
    local tower = clevel
    local level = 80 + math.floor(tower / 2)
    local basehp = tower * 10000 + math.floor(tower / 10) * math.floor(tower / 5) * 5000
    local basepow = tower * 50
    local baseint = tower * 50
    local basedex = tower * 30
    local basedef = tower * 40
    local basered = tower
    local basedmg = math.floor(tower / 30) * 10
    local basercrit = math.floor(tower / 25) * 5
    local basetrtad = math.floor(tower / 25) * 5
--    if ec_int(user.stage) == 2 then       
--         basehp = tower * 100000 + math.floor(tower / 10) * math.floor(tower / 5) * 10000 + 2000000
--         basepow = tower * 60 + 5000
--         baseint = tower * 60+ 5000
--         basedex = tower * 40+ 2000
--         basedef = tower * 50+ 5000
--         basered = tower+tower*2
--         basedmg = math.floor(tower / 30) * 20
--         basercrit = math.floor(tower / 25) * 10
--         basetrtad = math.floor(tower / 25) * 10
--    elseif ec_int(user.stage) == 3 then
--         basehp = tower * 1000000 + math.floor(tower / 10) * math.floor(tower / 5) * 100000 + 100000000
--         basepow = tower * 60 + 15000
--         baseint = tower * 60+ 15000
--         basedex = tower * 40+ 12000
--         basedef = tower * 50+ 15000
--         basered = tower+tower*2+500
--         basedmg = math.floor(tower / 30) * 25
--         basercrit = math.floor(tower / 25) * 10
--         basetrtad = math.floor(tower / 25) * 10
--         self.armors_add["pcrit"] = self.armors_add["pcrit"] + 20     
--    elseif ec_int(user.stage) == 4 then       
--         basehp = tower * 500000 + math.floor(tower / 10) * math.floor(tower / 5) * 50000 + 20000000
--         basepow = tower * 60 + 35000
--         baseint = tower * 60+ 35000
--         basedex = tower * 40+ 32000
--         basedef = tower * 50+ 35000
--         basered = tower+tower*2+1000
--         basedmg = math.floor(tower / 30) * 25
--         basercrit = math.floor(tower / 25) * 10
--         basetrtad = math.floor(tower / 25) * 10
--         self.armors_add["pcrit"] = self.armors_add["pcrit"] + 45       
--    end

    local mdef = cfg[CfgType.MONSTER][tonumber(id)]
    if mdef then
        self.mdef = mdef
        self.bRetinue = true
        self:setLevel(level)
        self:setStar(mdef.awake or 0)
        if mdef.master_skill then
            self.skills[1] = mdef.master_skill
        end
        if mdef.skills then
            if mdef.skills then
                local s = me.split(mdef.skills, ",")
                if s then
                    for key, var in pairs(s) do
                        local skill_def = cfg[CfgType.SKILL][tonumber(var)]
                        table.insert(self.skills, tonumber(var))
                    end
                end
            end
        end
        local per = 1
        self.pro_add["hp"] = basehp * per
        self.pro_add["con"] =(mdef.con or 0) * per
        self.pro_add["dex"] = basedex * per
        self.pro_add["pow"] = basepow * per
        self.pro_add["int"] = baseint * per
        self.pro_add["def"] = basedef * per
        self.pro_add["att"] =(mdef.att or 0) * per
        self.pro_add["m_att"] =(mdef.m_att or 0) * per
        self.pro_add["trt"] =(mdef.trt or 0) * per
        self.pro_add["cirt_odd"] = basercrit * per
        self.pro_add["trtad"] = basetrtad * per
        self.pro_add["red"] = basered * per
        self.pro_add["dmg_p"] = basedmg * per
        self.def_bless_monster = mdef.def_bless or 0
        if mdef.master_name then
            self.name = mdef.master_name
        end
       -- self:initStagePro()
        self:initPasSkill()
        self:resetBattlePro()
        self:resurgence()
    end
end
function roleData:initWithTowerMonsterId(id,towerType)
    local tower = ec_int(user.towerFloorCur)
    local level = 80 + math.floor(tower / 2)
    local tower200 =   math.floor(tower/200)*math.floor((tower-200)/2)  
    local basehp = tower * 10000 + math.floor(tower / 10) * math.floor(tower / 5) * 5000 + tower200*1000000
    local basepow = tower * 50 + tower200*200
    local baseint = tower * 50+ tower200*200
    local basedex = tower * 30+ tower200*200
    local basedef = tower * 40+ tower200*200
    local basered = tower + math.floor(tower/200)*200
    local basedmg = math.floor(tower / 30) * 10  + tower200*20
    local basercrit = math.floor(tower / 25) * 5
    local basetrtad = math.floor(tower / 25) * 5
    if towerType and towerType == RAID_TYPE_ELITETOWER then
         tower = ec_int(user.eliteTowerFloorCur)
         level = 150 + math.floor(tower / 2)    
         level = math.min(level,220)     
         basehp =  2000000 + tower * 100000 + math.floor(tower / 10) * 1000000 
         basepow = tower * 50 + 5000
         baseint = tower * 50 + 5000
         basedex = tower * 50 + 5000
         basedef = tower * 50 + 5000
         basered = tower*2 + math.floor(tower / 10) * 20
         basedmg = math.floor(tower / 10) * 20 
         basercrit = math.floor(tower / 25) * 5
         basetrtad = math.floor(tower / 25) * 5
    end
    local mdef = cfg[CfgType.MONSTER][tonumber(id)]
    if mdef then
        self.mdef = mdef
        self.bRetinue = true
        self:setLevel(level)
        self:setStar(mdef.awake or 0)
        if mdef.master_skill then
            self.skills[1] = mdef.master_skill
        end
        if mdef.skills then
            if mdef.skills then
                local s = me.split(mdef.skills, ",")
                if s then
                    for key, var in pairs(s) do
                        local skill_def = cfg[CfgType.SKILL][tonumber(var)]
                        table.insert(self.skills, tonumber(var))
                    end
                end
            end
        end
        local per = 1
        self.pro_add["hp"] = basehp * per
        self.pro_add["con"] =(mdef.con or 0) * per
        self.pro_add["dex"] = basedex * per
        self.pro_add["pow"] = basepow * per
        self.pro_add["int"] = baseint * per
        self.pro_add["def"] = basedef * per
        self.pro_add["att"] =(mdef.att or 0) * per
        self.pro_add["m_att"] =(mdef.m_att or 0) * per
        self.pro_add["trt"] =(mdef.trt or 0) * per
        self.pro_add["cirt_odd"] = basercrit * per
        self.pro_add["trtad"] = basetrtad * per
        self.pro_add["red"] = basered * per
        self.pro_add["dmg_p"] = basedmg * per
        self.def_bless_monster = mdef.def_bless or 0
        if mdef.master_name then
            self.name = mdef.master_name
        end
        --self:initStagePro()
        self:initPasSkill()
        self:resetBattlePro()
        self:resurgence()
    end
end
function roleData:getNetBattleDataStr()
    self:initPasSkill()
    self:resurgence()
    local msg = { }
    local tmp = me.copyTab(pro_arr)
    for key, var in pairs(tmp) do
        tmp[key] = self:getValPropertyAdd(key)
    end
    local basepro = me.copyTab(pro_arr)
    for key, var in pairs(basepro) do
        basepro[key] = self:getBasePorpertyAdd(key)
    end
    msg.pro = tmp
    msg.basepro = basepro
    msg.level = self:getLevel()
    msg.awake = self:getStar()
    msg.star = self:getStarLevel()
    msg.defid = self:getDef().id
    msg.saveskill = self.saveSkills
    msg.bskill = self.bskill
    local str = me.cjson.encode(msg)
    -- print(str)
    return str
end
function roleData:initWithMonsterIdNET(msg)
    self.bRetinue = true
    local def = cfg[CfgType.CAREER][msg.defid]
    local pid = def.paramsid
    local pdata = cfg[CfgType.CLASSES][pid]

    self.ani = def.ani
    self:setLevel(msg.level)
    self:setStar(msg.awake)
    self:addStarLevel(msg.star)
    local lv = self:getLevel()
    self.maxHp = pdata.min_hp + math.ceil((pdata.max_hp - pdata.min_hp) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.hp = self.maxHp
    self.maxBp = pdata.min_bp + math.ceil((pdata.max_bp - pdata.min_bp) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.bp = self.maxBp
    self.spd = pdata.min_spd + math.ceil((pdata.max_spd - pdata.min_spd) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.power = pdata.min_power + math.ceil((pdata.max_power - pdata.min_power) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.con = pdata.min_con + math.ceil((pdata.max_con - pdata.min_con) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.defence = pdata.min_defence + math.ceil((pdata.max_defence - pdata.min_defence) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.luck = pdata.min_luck + math.ceil((pdata.max_luck - pdata.min_luck) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.int = pdata.min_int + math.ceil((pdata.max_int - pdata.min_int) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.men = pdata.min_men + math.ceil((pdata.max_men - pdata.min_men) * self:getStarVal() * lv / self:getDefMaxLevel())
    self.maxExp = expForUserLevel(lv + 1)
    -- 默认怒气为0
    self.mp = 0
    -- 怒气值
    self.rage = 0
    self.name = def.name
    self.bpRestore = 10
    self.isUpdateBuff = false
    self.skills = msg.bskill
    self.netSkills = msg.saveskill
    self.netBattleBasePro = msg.basepro
    self.netBattlePro = msg.pro

    self:initPasSkill()
    self:resetBattlePro()
    self:resurgence()
end
function roleData:addNetBattleRed()
    -- self.netBattlePro["red"] = self.netBattlePro["red"]  + 100
end
-- 获取战斗力
function roleData:getFightPower()
    local tmp = me.copyTab(pro_arr)
    local pv = 0
    for key, var in pairs(tmp) do
        if key == "sk" then
            pv = pv + 5000
        else
            pv = pv + getFightCheck(key, self:getFightType()) *(self:getPropertyAdd(key) + self:getBaseProByString(key))
        end
    end
    local skillScore = 0
    for key, var in pairs(self.skills) do
        local skill = cfg[CfgType.SKILL][tonumber(var)]
        skillScore = skillScore + skill.qua * 500 + skill.level * 100
    end
    return math.floor(pv + skillScore)
end
function roleData:resetFisrtSkill()
    local def = self:getDef()
    if def.skill and tonumber(def.skill) > 0 then
        self.skills[1] = def.skill
    end
    gameLogic:getInstance():saveGame()
end
function roleData:isMagic()
    return self:getInt() > self:getPower()
end
-- 获取剩余点数
function roleData:getLeftPro()
    local count = self:getStarLevel() * 30 + math.floor(self:getLevel() / 10) * 10
    local num = 0
    for key, var in pairs(self.pro_add) do
        num = num + var
    end
    return math.max(0, count - num)
end
function roleData:getDef()
    return cfg[CfgType.CAREER][self.defid]
end
function roleData:getMonsterDef()
    return self.mdef
end
-- 全恢复
function roleData:resurgence()
    self:checkSpe()
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateTitleAdd()
    self:updateSuitData()
    self.bosscard_add = bossCardData.getPro()
    self.hp = self:getMaxHp()
    self.bp = self:getMaxBp()
    self.mp = self:getMaxMp()
    self.rage = 0
end
function roleData:updatePro()
    self:checkSpe()
    self.bosscard_add = bossCardData.getPro()
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateTitleAdd()
    self:updateSuitData()
end
function roleData:cutMp(m)
    self.mp = self.mp - m
end
function roleData:getRoleStarData(v)
    if self.rolestars[1] == nil then
        return 0
    end
    if v == "hp" then
        return user.rolestars[self.rolestars[1]]:getHp()
    elseif v == "bp" then
        return user.rolestars[self.rolestars[1]]:getBp()
    elseif v == "int" then
        return user.rolestars[self.rolestars[1]]:getInt()
    elseif v == "pow" then
        return user.rolestars[self.rolestars[1]]:getPower()
    elseif v == "dex" then
        return user.rolestars[self.rolestars[1]]:getDex()
    elseif v == "luck" then
        return user.rolestars[self.rolestars[1]]:getLuck()
    elseif v == "men" then
        return user.rolestars[self.rolestars[1]]:getMen()
    elseif v == "con" then
        return user.rolestars[self.rolestars[1]]:getCon()
    else
        return 0
    end
    return 0
end
function roleData:getNextLevelNeedExp()
    return self.maxExp - self.exp
end
function roleData:getNextTenLevelNeedExp()
    local level = self:getLevel()
    local exp = self:getNextLevelNeedExp()
    local maxlevel = math.min(self:getDefMaxLevel(), level + 10)
    for var = level + 1, maxlevel do
        exp = exp + expForUserLevel(var)
    end
    return exp
end
function roleData:addMaxRage(num)
    if self.maxMp < 10 then
        local x = self.maxMp
        self.maxMp = math.min(self.maxMp + num, 10)
        if 10 - x < num then
            return 10 - x
        end

        return num
    else
        showTips("怒气值已经达到上限，无法再增加。")
        return 0
    end
end
function roleData:addRage()
    -- eeeeeeeeee
end
function roleData:isSoldier()
   return self:getMonsterDef().bsoldier and self:getMonsterDef().bsoldier == 2
end
function roleData:getMaxMp()
    return self.maxMp
end
-- 行动不能
function roleData:isAkinesia()
    local bf = self.buffs[1]
    -- 眩晕
    if bf then
        return true
    end
    return false
end
-- 是否犹豫 无法施法
function roleData:isSilent()
    local bf = self.buffs[33] or self.buffs[33000]
    if bf then
        return true
    end
    return false
end
--SKILL_KIND_SRI = 1  -- 必杀
--SKILL_KIND_SKL = 2  -- 武将技
--SKILL_KIND_PAS = 0  -- 被动
--SKILL_KIND_POL = 3  -- 秘策
--SKILL_KIND_CAE = 4  -- 追击
function roleData:getSkillNumsByKind(kind)
    local count = 0;
    for key, var in pairs(self.skills) do
        local sdata = cfg[CfgType.SKILL][var]        
        if sdata.iStunt == kind then
            count = count + 1
        end
    end
    for key, var in pairs(self.uskills) do
        local sdata = cfg[CfgType.SKILL][var]        
        if sdata.iStunt == kind then
            count = count + 1
        end
    end
    return count
end
function roleData:checkTimeEquip()
    if self.armors[9] and user.equipdatas[self.armors[9]] and user.equipdatas[self.armors[9]]:isTimeOut() then
        self:unEquipArmor(self.armors[9])
    end
end 
function roleData:perpetualAdd(key, var)
    self.perpetual_add[key] = self.perpetual_add[key] + var
end
function roleData:addHp(num)
    self.hp = math.min(self.hp + num, self:getMaxHp())
end
function roleData:getExpASP()
    local _, _, ach = string.find(self.exp_asp, "exp_xd(%d+)ach_end")
    return tonumber(ach) or 0
end

function roleData:addExpASP(num)
    local s = "exp_xd%sach_end"
    local n = self:getExpASP()
    local x = tonumber(n) + tonumber(num)
    self.exp_asp = string.format(s, tostring(x))
end
function roleData:setExpASP(num)
    local s = "exp_xd%sach_end"
    local x = tonumber(num)
    self.exp_asp = string.format(s, tostring(x))
end
function roleData:addBp(num)
    self.bp = math.min(self.bp + num, self:getMaxBp())
end
function roleData:addExp(exp,notani)     
    self.exp = self.exp + exp
    self:addExpASP(exp)
    if self:getLevel() >= self:getMaxLevel() then
        --self.exp = math.min(self.exp, expForUserLevel(self:getMaxLevel()))
        return
    end   
    if self:isRetinue() then
        taskMgr:getInstance():checkCurMainTask(TASK_TYPE_REC_EXP, exp)
    end
    if tonumber(self.exp) ~= tonumber(self:getExpASP()) then
        self.exp = 0
        self:setExpASP(0)
    end
    while self.exp >= self.maxExp and self:getLevel() < self:getMaxLevel() do
        if not self:isRetinue() then
            self.exp = self.exp - self.maxExp
            self:setExpASP(self.exp)
        end
        self.level = ec_add(self.level, 1)
        self:initDataByLevel(self.level)
        if self:getLevel() > 50 then
            self.locked = true
        end
        if notani == nil then
            self:resurgence()
            me.DelayRun( function()
                allAnimation.showAni(ALL_COMMON_LEVELUP)
            end , 0.6)
            showTips(self.name .. "升到了" .. self:getLevel() .. "级")
            taskMgr:getInstance():checkCurMainTask(TASK_TYPE_CARD_LEVEL_UP, self:getLevel())
            taskMgr:getInstance():checkCurMainTask(TASK_TYPE_LEVEL_UP_BY, 1)
        end
    end
    gameLogic:getInstance():saveGame()
end
function roleData:setLevel(level)
    if level <= self:getMaxLevel() then
        self.level = ec_int(level)
        self:initDataByLevel(self.level)
        self:resurgence()
    end
end
function roleData:setLevelForMaster(level)
    if level <= self:getMaxLevel() then
        self.level = ec_int(level)
        self:initDataByLevel(self.level)
        self:resurgence()
    end
end
function roleData:getMaxLevel()
    if self:isRetinue() then
        return DEF_MAX_RETINUE_LEVEL
    else
        if self:getDef().star <= 3 then
            return 50
        end
        return DEF_MAX_LEVEL
    end
end
function roleData:getDefMaxLevel()
    if self:isRetinue() then
        return 200
    else
        return 200
    end
end
function roleData:getEmptyWeapon()
    --    for var = 1, 3 do
    --        if self.weapons[var] == nil then
    --            return var
    --        end
    --    end
    if self.weapons[1] == nil then
        return var
    end
    return -1
end
function roleData:getEmptyArmor()
    for var = 1, 3 do
        if self.armors[var] == nil then
            return var
        end
    end
    return -1
end
function roleData:saveEqiupAndSoldier()
    for var = 1, 6 do
        self.saveWeapons[tostring(var)] = self.weapons[var]
        self.saveArmors[tostring(var)] = self.armors[var]
        self.saveSoldier = self.curSoldier
    end
    gameLogic:getInstance():saveGame()
    showTips("方案保存成功")
end
function roleData:loadEquipAndSodier()
    for var = 1, 6 do
        local wid = self.saveWeapons[tostring(var)]
        if wid and wid ~= self.weapons[var] then
            local tagdata = user.equipdatas[wid]
            if tagdata and tagdata.state == EQUIP_STATE_UNEQUIP then
                self:equipWeapon(wid)
            end
        end
        local aid = self.saveArmors[tostring(var)]
        if aid and aid ~= self.armors[var] then
            local tagdata = user.equipdatas[aid]
            if tagdata and tagdata.state == EQUIP_STATE_UNEQUIP then
                self:equipArmor(aid)
            end
        end
    end
    if me.isValidStr(self.saveSoldier) then
        if user.soldierDatas[self.saveSoldier].state == SOLDIER_STATE_IDLE then
            self:downSoldier()
            self:setCurSoldier(self.saveSoldier)
        end
    end
    showTips("读取方案成功")
end
function roleData:getSuitData(skillid)
    return self.eSuitDatas[skillid]
end
function roleData:getSaveString()
    local msg = { }
    msg.maxHp = self.maxHp
    msg.state = self.state or RETINUE_STATE_IDLE
    msg.addhp_per = self.addhp_per
    msg.addbp_per = self.addbp_per
    msg.addhp_id = self.addhp_id
    msg.addbp_id = self.addbp_id
    msg.leftproCount = self.leftproCount
    msg.level = self.level
    msg.starLevel = self.starLevel
    msg.exp = self.exp
    msg.exp_asp = self.exp_asp
    msg.titleId = self.titleId
    msg.teampos = self.teampos or 0
    msg.bLead = self.bLead
    msg.star = self.star or 0
    msg.locked = self.locked or false
    msg.curSoldier = self.curSoldier or ""
    msg.starPro = self.starPro or { }

    msg.saveArmors = self.saveArmors or { }
    msg.saveWeapons = self.saveWeapons or { }
    msg.saveSoldier = self.saveSoldier or ""
    msg.starProcess = self.starProcess or 0
    msg.strength = self.strength or 0
    msg.turnNum =  self.turnNum or  ec_int(0)
    msg.jobPos = self.jobPos or 0
    local wstr = me.cjson.encode(self.weapons)
    if wstr then
        msg.weapons = self.weapons
    else
        msg.weapons = { }
    end
    local rstr = me.cjson.encode(self.rolestars)
    if rstr then
        msg.rolestars = self.rolestars
    else
        msg.rolestars = { }
    end
    local tempskills = { }
    for key, var in pairs(self.skills) do
        if var then
            table.insert(tempskills, var)
        end
    end
    local sstr = me.cjson.encode(tempskills)
    if sstr then
        msg.skills = tempskills
    else
        msg.skills = { }
    end
    local templearn = { }
    for key, var in pairs(self.learnedskills) do
        if var then
            table.insert(templearn, var)
        end
    end
    local lstr = me.cjson.encode(templearn)
    if lstr then
        msg.learnedskills = templearn
    else
        msg.learnedskills = { }
    end

    local astr = me.cjson.encode(self.armors)
    if astr then
        msg.armors = self.armors
    else
        msg.armors = { }
    end
    msg.achCount = self.achCount
    msg.imgId = self.imgId
    msg.stunts = self.stunts
    msg.eSuitDatas = { }
    local suit = { }
    for key, var in pairs(self.eSuitDatas) do
        table.insert(suit, key)
    end
    local suitstr = me.cjson.encode(suit)
    if suitstr then
        msg.eSuitDatas = suit
    else
        msg.eSuitDatas = { }
    end
    msg.soldierdatas = self.soldierdatas
    msg.roleid = self.roleid
    msg.defid = self.defid
    -- msg.weapon_affinity = me.cjson.encode(self.weapon_affinity)
    msg.perpetual_add = me.cjson.encode(self.perpetual_add)
    msg.pro_add = me.cjson.encode(self.pro_add)


    return me.cjson.encode(msg)
end
-- 获取功勋
function roleData:getAch()
    local _, _, ach = string.find(self.achCount, "ach_xd(%d+)ach_end")
    return ach or 0
end
-- 增加功勋
function roleData:addAch(num)
    local s = "ach_xd%sach_end"
    local n = self:getAch()
    taskMgr:getInstance():checkCurMainTask(TASK_TYPE_ARCH, num)
    self.achCount = string.format(s, tostring(tonumber(n) + tonumber(num)))
end
function roleData:loadSaveString(str)
    local msg = me.cjson.decode(str)
    self.maxHp = msg.maxHp
    self.state = msg.state or RETINUE_STATE_IDLE
    self.addhp_per = msg.addhp_per
    self.addbp_per = msg.addbp_per
    self.addhp_id = msg.addhp_id
    self.addbp_id = msg.addbp_id
    self.leftproCount = msg.leftproCount
    self.level = msg.level
    self.starLevel = msg.starLevel or ec_int(0)
    self.achCount = msg.achCount
    self.imgId = msg.imgId
    self.exp = msg.exp
    self.exp_asp = msg.exp_asp
    self.roleid = msg.roleid
    self.defid = msg.defid
    self.titleId = msg.titleId or 1000
    self.teampos = msg.teampos or 0
    self.bLead = msg.bLead or false
    self.star = msg.star or 0
    self.locked = msg.locked or false
    self.curSoldier = msg.curSoldier or ""
    self.starPro = msg.starPro or { }
    self.saveArmors = msg.saveArmors or { }
    self.saveWeapons = msg.saveWeapons or { }
    self.saveSoldier = msg.saveSoldier or ""
    self.starProcess = msg.starProcess or 0
    self.jobPos = msg.jobPos or 0
    self.strength = msg.strength or 0
    self.turnNum =  msg.turnNum or  ec_int(0)

    local padd = me.cjson.decode(msg.perpetual_add)
    if padd then
        self.perpetual_add = padd
    else
        self.perpetual_add = me.copyTab(pro_arr)
    end
    local proadd = me.cjson.decode(msg.pro_add)
    if proadd then
        self.pro_add = proadd
    else
        self.pro_add = me.copyTab(pro_arr)
    end
    self.weapons = { }
    for key, var in pairs(msg.weapons) do
        if var and var ~= "nil" then
            self.weapons[key] = var
        end
    end
    self.rolestars = { }
    if msg.rolestars then
        for key, var in pairs(msg.rolestars) do
            if var and var ~= "nil" then
                self.rolestars[key] = var
            end
        end
    end
    self.armors = { }
    for key, var in pairs(msg.armors) do
        if var and var ~= "nil" then
            self.armors[key] = var
        end
    end
    self.eSuitDatas = { }
    if msg.eSuitDatas then
        for key, var in pairs(msg.eSuitDatas) do
            if var and var ~= "nil" then
                self.eSuitDatas[tonumber(var)] = equipSuitData.new(tonumber(var))
            end
        end
    end
    self.skills = { }
    if msg.skills then
        for key, var in pairs(msg.skills) do
            if var and var ~= "nil" then
                self.skills[key] = var
            end
        end
    end
    self.learnedskills = { }
    if msg.learnedskills then
        for key, var in pairs(msg.learnedskills) do
            if var and var ~= "nil" then
                self.learnedskills[key] = var
            end
        end
    end
    self.stunts = {
        [1] = 0,
        [2] = 0,
        [3] = 0,
    }
    if msg.stunts then
        self.stunts = msg.stunts
    end
    if msg.soldierdatas then
        self.soldierdatas = msg.soldierdatas
    else
        self.soldierdatas = { }
        for key, var in pairs(soldier_datas[self:getDef().soldier]) do
            self.soldierdatas[var] = 0
        end
    end
    self:initDataByLevel(self.level)
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateTitleAdd()
    --self:updateSuitData()
end
function roleData:isOpenSkillBox(idx)
    if idx == 2 then
        return self:getLevel() >= 10
    elseif idx == 3 then
        return self:getStar() >= 2
    elseif idx == 4 then
        return self:getStar() >= 4
    end
    return false
end
local awakeLevel = {
    20,30,50,80,100,150,180,200
}
-- 等级是否够突破
function roleData:levelCanAwake()
    -- return self:getLevel() >= self:getAwakeCardNums() * 10
    return self:getLevel() >= awakeLevel[self:getStar() + 1]
end
-- 下次突破等级
function roleData:getAwakeLevel()
    return awakeLevel[self:getStar() + 1]
end
-- 获取需要突破的卡牌数量
function roleData:getAwakeCardNums()
    if self:getStar() < 5 then
        return self:getStar() + 1
    elseif self:getStar() == 5 then
        return 10
    elseif self:getStar() == 6 then
        return 20
    elseif self:getStar() == 7 then
        return 30
    end
end
-- 获取当前突破卡牌的价值数量
function roleData:getAwakeCardVal()
    local num = 0
    for var = self:getStar(), 1, -1 do
        if var <= 5 then
            num = num + var
        elseif var == 6 then
            num = num + 10
        elseif var == 7 then
            num = num + 20
        end
    end

    num = num + self:getStarLevelVal()
    return num
end

function roleData:addStar()
    self.star = ec_add(self.star, 1)
    self:initDataByLevel(self.level)
end

function roleData:setStar(star)
    self.star = ec_int(star)
    self:initDataByLevel(self.level)

end
function roleData:delStunt(defid)
    for key, var in ipairs(self.stunts) do
        if var == defid then
            self.stunts[key] = 0
            break
        end
    end
    gameLogic:getInstance():save( { SAVE_ROLE_DATA })
end
function roleData:learnStunt(defid)
    local idx = 0
    for key, var in ipairs(self.stunts) do
        if var == 0 then
            idx = key
            break
        end
    end
    if idx == 0 then
        showTips("特技数达到最大数量，无法学习")
    else
        self.stunts[idx] = defid
        gameLogic:getInstance():save( { SAVE_ROLE_DATA })
    end
end
function roleData:checkHaveStunt(defid)
    for key, var in pairs(self.stunts) do
        if var == defid then
            return true
        end
    end
    return false
end
function roleData:retAddStar(exp)

end
function roleData:init()
    for key, var in pairs(soldier_datas[self:getDef().soldier]) do
        self.soldierdatas[tostring(var)] = 0
    end
    return true
end
function roleData:cutBp(num)
    self.bp = self.bp -(num or 0)
end
function roleData:cutHp(num)
    if gameLogic:getInstance().raidType == RAID_TYPE_NET_BATTLE then
        num = num / 2
    end

    local bf = self.buffs[260]
    if bf then
        return true
    end
    self:addRage()
    if self.hp - num > 0 then
        self.hp = self.hp - num
        return true
    else
        self.hp = 0
        return false
    end
end
-- 获取暴击
function roleData:getCirt_odd()
    return self:getPropertyAdd("cirt_odd")
end
-- 降低被暴击的几率
function roleData:getCirt_POdd()
    return self:getPropertyAdd("pcrit")
end
-- 获取暴击伤害
function roleData:getCirt_hit()
    return 2 + self:getPropertyAdd("cirt_hit") / 100
end
-- 获取命中
function roleData:getAtt_rating(args)

end
-- 用于计算战斗伤害 
function roleData:getDamge()
    local hp_add = 0
    if not self:isRetinue() then
        local bf = self.buffs[152]
        if bf then
            hp_add = math.floor(self:getMaxHp() * tonumber(bf.paramsdata[1]) / 100)
        end
    end
    return self:getPower() / power_scale + self:getPropertyAdd("att") + hp_add
end
function roleData:getMagicDamge()
    return self:getInt() / power_scale + self:getPropertyAdd("m_att")
end
function roleData:getBaseDamge()
    local wp = user.equipdatas[self.weapons[1]]
    if wp then
        return math.ceil((self:getPower() / power_scale))
    else
        return math.ceil(self:getPower() / power_scale)
    end
end
-- 获取持续增加的状态
function roleData:getContinuousBuff(...)
    local agrs = { ...}
    local per = 0
    for key, var in pairs(agrs) do
        local bf = self.buffs[var]
        if bf then
            per = per + tonumber(bf.paramsdata[2]) / 100 * bf.dtime
        end
    end
    -- per = math.max(0, per)
    return per
end
function roleData:getBaseMagicDamge()
    local wp = user.equipdatas[self.weapons[1]]
    if wp then
        return math.ceil((self:getInt() / power_scale))
    else
        return math.ceil(self:getInt() / power_scale)
    end
end
function roleData:getWeapon()
    return user.equipdatas[self.weapons[1]]
end
function roleData:updateSuitData()
    self.equip_suit = me.copyTab(pro_arr)
    for key, var in pairs(self.eSuitDatas) do
        local sdata = var:getImgDue(self.roleid)
        if sdata then
            for k, v in pairs(sdata) do
                self.equip_suit[k] = self.equip_suit[k] + v
            end
        end
    end
end
function roleData:getSuitDataByKind(kind)
    local tmp_suit = me.copyTab(pro_arr)
    for key, var in pairs(self.eSuitDatas) do
        if kind and kind == var:getDef().kind then
            local sdata = var:getImgDue(self.roleid)
            if sdata then
                for k, v in pairs(sdata) do
                    tmp_suit[k] = tmp_suit[k] + v
                end
            end
        end
    end
    return tmp_suit
end
function roleData:resetPro()
    self.pro_add = me.copyTab(pro_arr)
end
-- 重置战斗属性
function roleData:resetBattlePro()
    -- 攻击间隔
    self:checkSpe()
    self.attack_interval = 0
    -- 技能间隔 负数为施法中 0 技能可以施法 整数 正在倒计时冷却
    self.skills_interval = { }
    for key, var in pairs(self.skills) do
        self.skills_interval[var] = 0
    end
    if self.uskills then
        for key, var in pairs(self.uskills) do
            self.skills_interval[var] = 0
        end
    end
    if self.optSkills then
        for key, var in pairs(self.optSkills) do
            local data = cfg[CfgType.SKILL][var]
            self.skills_interval[var] = data.precd or 0
        end
    end
    -- 当前目标
    self.cur_taget = nil
    self.hurt = 0
    self.normalHits = 0
    self.skillHits = 0
    self.treatNums = 0
    -- 当前施法技能id
    self.cur_skillid = nil
    -- 当前施法倒计时
    self.cur_release = nil
    self.battlepro = me.copyTab(pro_arr)
end
function roleData:addNormalHits(hit)
    self.normalHits = self.normalHits + hit
end
function roleData:addSkillHits(hit)
    self.skillHits = self.skillHits + hit
end
function roleData:addTeatNums(hit)
    self.treatNums = self.treatNums + hit
end
function roleData:checkAttackInterval(dt)
    if self:isAkinesia() == false then
        if self.attack_interval < self:getAttackInterval() then
            self.attack_interval = self.attack_interval + dt * 1000
            return false
        else
            -- 释放技能读条时不普攻
            if self.cur_skillid ~= nil and self.cur_release > 0 then
                return false
            else
                self.attack_interval = 0
                return true
            end

        end
    end
    return false
end
-- 速度影响技能CD
function roleData:redDexSkillInterval()
    local staradd = 0
    if self:getStar() >= 5 then
        staradd = 0.1
    end
    local spd = self:getSpd()
    local staradd1 = 0.2 * spd /(DEFAULT_ATTACK_INTERVAL + spd)
    return 1 - staradd - staradd1
end
-- 获取技能CD
function roleData:getSkillCd(defid)
    local sdata = cfg[CfgType.SKILL][defid]
    local bf = self.buffs[96]
    local cut = 0
    if bf then
        if sdata.baseid == tonumber(bf.paramsdata[2]) then
            cut = math.min(tonumber(bf.paramsdata[3]), math.floor(self:getSpd() / tonumber(bf.paramsdata[1])) * 500)
        end
    end
    bf = self.buffs[61]
    if bf then
        if sdata.baseid == tonumber(bf.paramsdata[1]) then
            cut = tonumber(bf.paramsdata[2]) * 1000 + cut
        end
    end
    bf = self.buffs[160]
    if bf and bf:isActive() then
        cut = tonumber(bf.paramsdata[1]) * 1000 + cut
    end
    bf = self.buffs[161]
    if bf and bf:isActive() then
        cut = cut - tonumber(bf.paramsdata[1]) * 1000
    end
    return math.max(100, sdata.cd - cut)
end
function roleData:getSkillRelease(defid)
    local sdata = cfg[CfgType.SKILL][defid]
    local bf = self.buffs[84]
    if bf then
        if self:isMainSkill(defid) then
            return math.max(10, sdata.release - tonumber(bf.paramsdata[1]) * 1000)
        end
    end
    return sdata.release
end
function roleData:getOptSkillCd(defid)
    local sdata = cfg[CfgType.SKILL][defid]
    return sdata.cd
end
function roleData:optSkillCanUse()
    local defid = self.optSkills[1]
    local cd = self:getOptSkillCd(defid)
    return  cd <= self.skills_interval[defid]
end
function roleData:getOptSkillPercent()
    local defid = self.optSkills[1]
    local cd = self:getOptSkillCd(defid)
    return ( cd - self.skills_interval[defid])*100 / cd 
end
function roleData:checkOptSkillInterval(dt)
      for key, var in pairs(self.optSkills) do
        local sdata = cfg[CfgType.SKILL][var]
        if sdata.iStunt == SKILL_KIND_OPT then
                if self.skills_interval[var]  == nil then
                    self.skills_interval[var] = 0
                end
                if self.skills_interval[var] < self:getOptSkillCd(var)  then
                    self.skills_interval[var] = self.skills_interval[var] + dt * 1000
                end
        end
      end
end
function roleData:checkSkillInterval(dt)
    for key, var in pairs(self.uskills) do
        local sdata = cfg[CfgType.SKILL][var]
        if sdata.iStunt == SKILL_KIND_SKL or sdata.iStunt == SKILL_KIND_SRI then
            if self.skills_interval[var] >= 0 then
                --- 眩晕不走CD
                if self:isAkinesia() == false then
                    if self.skills_interval[var] <(self:getSkillCd(var) * self:redDexSkillInterval()) then
                        self.skills_interval[var] = self.skills_interval[var] + dt * 1000
                    else
                        self.skills_interval[var] = -1
                        if self.cur_skillid == nil then
                            self.cur_skillid = var
                            self.cur_release = 0
                        end
                    end
                else
                    self.cur_release = 0
                end
            else
                if self:isSilent() == false then
                    self.skills_interval[var] = -1
                    if self.cur_skillid == nil then
                        self.cur_skillid = var
                        self.cur_release = 0
                    end
                else
                    self.cur_release = 0
                end
            end
        end
    end
    local caninterrupt = true
    if self.cur_skillid then
        local skilldata = cfg[CfgType.SKILL][self.cur_skillid]
        local maindata = cfg[CfgType.SKILL][tonumber(self.skills[1])]
        if self.buffs[150] and skilldata.baseid == maindata.baseid then
            caninterrupt = false
        end
    end
    if self:isAkinesia() == false or caninterrupt == false then
        -- 犹豫
        if self:isSilent() == false or caninterrupt == false then
            if self.cur_skillid then
                local sdata = cfg[CfgType.SKILL][self.cur_skillid]
                if self.cur_release == 0 then
                    -- 开始释法 贾诩
                    local bf_70 = self.buffs[70]
                    if bf_70 then
                        -- local c = bf_70.user:getMagicDamge() * bf_70.params / 100
                        local c = skillData.getBuffDamage(bf_70.user, self, 2, tonumber(bf_70.paramsdata[1]) * bf_70.dtime)
                        battleLayer:getInstance():showHurtEffectNumByCampPos(math.floor(- c), self.battle_camp, self.battle_pos, EFFECT_NUM_COLOR_RED)
                        bf_70.user:addSkillHits(c)
                        battleLayer:getInstance():getCampPosModel(self.battle_camp, self.battle_pos):cutHp(c)
                    end
                    local release_odds = sdata.release_odds
                    if release_odds then
                        if sdata.iStunt == 1 or sdata.iStunt == 2 then
                            local askl = self:getPropertyAdd("askl")
                            if askl > 0 then
                                release_odds = release_odds + askl * 100
                            end
                        elseif sdata.iStunt == 4 then
                            local pskl = self:getPropertyAdd("pskl")
                            if pskl > 0 then
                                release_odds = release_odds + pskl * 100
                            end
                        end


                        -- 百出
                        local bf_77 = self.buffs[77]
                        if bf_77 then
                            if self:isMainSkill(self.cur_skillid) then
                                release_odds = release_odds + tonumber(bf_77.params)
                            end
                        end
                        if sdata.hitType == 1 then
                            release_odds = release_odds + self:getPropertyAdd("amaskl") * 100
                        elseif sdata.hitType == 2 then
                            release_odds = release_odds + self:getPropertyAdd("mmaskl") * 100
                        end

                        local zbf = self.buffs[159]
                        if zbf and self:isMainSkill(self.cur_skillid) then
                            release_odds = release_odds +(zbf.dtime - 1) * tonumber(zbf.paramsdata[1]) * 100
                        end

                    end
                    local mdbuff = self.buffs[39]
                    if (release_odds and me.getRandom(10000) <= tonumber(release_odds)) or mdbuff then
                        print("开始释放技能" .. sdata.name)
                        -- 发动率100直接跳过施法
                        local zbf = self.buffs[159]
                        if zbf and self:isMainSkill(self.cur_skillid) then
                            zbf.dtime = 1
                        end
                        if mdbuff then
                            mdbuff.dtime = mdbuff.dtime + 1
                            if mdbuff.dtime >= tonumber(mdbuff.params) then
                                self:removeBuff(mdbuff.id)
                            end
                            self.cur_release = 0
                            local tmp = self.cur_skillid
                            self.skills_interval[tmp] = 0
                            self.cur_skillid = nil
                            return tmp
                        end
                    else
                        local zbf = self.buffs[159]
                        if zbf and self:isMainSkill(self.cur_skillid) then
                            zbf.dtime = zbf.dtime + 1
                            zbf.dmgper = zbf.dmgper + 1
                        end
                        self.cur_release = 0
                        self.skills_interval[self.cur_skillid] = 0
                        self.cur_skillid = nil
                        print("没有施法")

                    end
                end
                if self.cur_skillid then
                    if self.cur_release < self:getSkillRelease(self.cur_skillid) * self:redDexSkillInterval() then
                        self.cur_release = self.cur_release + dt * 1000
                    else
                        self.cur_release = 0
                        local tmp = self.cur_skillid
                        self.skills_interval[tmp] = 0
                        self.cur_skillid = nil
                        return tmp
                    end
                end
            end
        end
    else
        self.cur_release = 0
    end
    return nil
end
-- 攻击间隔
function roleData:getAttackInterval()
    local spd = self:getSpd()
    local bf = self.buffs[171]
    local val = 0
    if bf then
        val = tonumber( bf.paramsdata[1] )*1000
    end
    bf = self.buffs[1710]
    if bf then
        val = tonumber( bf.paramsdata[1] )*1000
    end
    return 5000 *(1 - 0.5 *(spd /(spd + DEFAULT_ATTACK_INTERVAL))) - val
end
-- 获取百分比增幅 或减幅 
function roleData:getBuffsPer(...)
    local agrs = { ...}
    local per = 0
    for key, var in pairs(agrs) do
        local bf = self.buffs[var]
        if bf then
            if bf.kind == 72 then
                -- 飞将
                per = per + math.floor(self:getSpd() / tonumber(bf.paramsdata[1])) * tonumber(bf.paramsdata[2]) / 100
            else
                if #bf.paramsdata == 1 then
                    per = per + bf.params / 100
                elseif #bf.paramsdata == 3 then
                    per = per +(tonumber(bf.paramsdata[1]) + bf.user:getBaseProByString(bf.paramsdata[2]) / tonumber(bf.paramsdata[3])) / 100
                end
            end
        end
    end
    return per
end
-- 获取作为攻击者时的增伤，减伤
function roleData:getAttkerBuffsPer(atpye)
    local per = 0
    if atpye == 1 then
        per = self:getBuffsPer(3004, 3104, 103004, 4000, 30104, 72)
        per = per + self:getContinuousBuff(100, 110, 57)
    elseif atpye == 2 then
        per = self:getBuffsPer(3005, 3105, 103005, 310500, 4000, 33105, 72)
        per = per + self:getContinuousBuff(102, 112, 57)
    end
    local xbf = self:getPropertyAdd("dmg_p")
    if atpye == 1 then
        xbf = xbf + self:getPropertyAdd("pdmg_p")
    elseif atpye == 2 then
        xbf = xbf + self:getPropertyAdd("mdmg_p")
    end
    local bf = self.buffs[19]
    if bf then
        -- 有护盾时伤害提升
        xbf = xbf + self:getPropertyAdd("shield_dmg")
    end
    bf = self.buffs[9]
    if bf then
        -- 有规避时伤害提升
        xbf = xbf + self:getPropertyAdd("avoid_dmg")
    end

    local healthdmg = self:getPropertyAdd("health_dmg")
    if healthdmg > 0 and self:getHpPercent() > 80 then
        xbf = xbf + healthdmg
    end
    local hurt_dmg = self:getPropertyAdd("hurt_dmg")
    if hurt_dmg > 0 and self:getHpPercent() < 30 then
        xbf = xbf + hurt_dmg
    end
    local soldierper = 0
    local soldierdata = self:getCurSoldierData()
    if soldierdata and self:soldierActive() then
        soldierper = soldierper + soldierdata:getProByKey("dmg_p")
    end

    if xbf > 0 then
        per = per + xbf / 100 + soldierper / 1000
    end
    -- 虎豹突袭
    local bf = self.buffs[164]
    if bf then
        per = per + bf.dtime * tonumber(bf.paramsdata[1]) / 100
    end

    local zbf = self.buffs[159]
    if zbf and tonumber(zbf.paramsdata[2]) then
        per = per + zbf.dmgper * tonumber(zbf.paramsdata[2]) / 100
        zbf.dmgper = 0
    end
    bf = self.buffs[94]
    -- 盛气
    if bf and bf.dtime > 1 then
        per = per + bf.dtime * tonumber(bf.paramsdata[3]) / 100
    end
    bf = self.buffs[85]
    if bf then
        -- 血战
        per = per + math.floor((100 - self:getHpPercent()) / 10) * tonumber(bf.paramsdata[2]) / 100
    end
    bf = self.buffs[166]
    if bf then        --
        local v = self:getBuffCount()
        v = math.min(v, tonumber(bf.paramsdata[2]))
        per = per +  v*tonumber(bf.paramsdata[1]) / 100
    end

    bf = self.buffs[170]
    if bf then
        local num = battleLayer:getInstance():getPosTeamPlayer(self.battle_camp, 1)
        per = per + num * tonumber(bf.paramsdata[1]) / 100
    end

    return per
end
-- 获取作为防御者时的增伤，减伤
function roleData:getDefenderBuffsPer(dtype)
    local per = 0
    if dtype == 1 then
        per = self:getBuffsPer(3008, 3108, 4108)
        per = per + self:getContinuousBuff(101)

    elseif dtype == 2 then
        per = self:getBuffsPer(3009, 3109, 4109)
        per = per + self:getContinuousBuff(103)
    end

    local soldierdata = self:getCurSoldierData()
    local soldierred = 0
    if soldierdata and self:soldierActive() then
        soldierred = soldierred +(soldierdata:getProByKey("red") or 0)
    end

    local bf = self.buffs[8]
    -- 被攻击时减少伤害
    if bf then
        local params = me.split(bf.params, ",")
        if #params == 2 then
            if me.getRandom(1000, 1, 3) < params[1] * 10 then
                per = per - params[2] / 100
            end
        elseif #params == 4 then
            if me.getRandom(1000, 1, 3) < params[1] * 10 then
                per = per -(params[2] + bf.user:getBaseProByString(params[3]) / params[4]) / 100
            end
        end
    end
    bf = self.buffs[162]
    if bf then
        -- 击瑕
        local count = self:getDeBuffCount()
        per = per + tonumber(bf.paramsdata[1]) * count / 100
    end
    local xbf = self:getPropertyAdd("red")
    if atpye == 1 then
        xbf = xbf + self:getPropertyAdd("pred")
    elseif atpye == 2 then
        xbf = xbf + self:getPropertyAdd("mred")
    end
    bf = self.buffs[9]
    if bf then
        -- 有规避时减伤
        xbf = xbf + self:getPropertyAdd("avoid_red")
    end

    local health_red = self:getPropertyAdd("health_red")
    if health_red > 0 and self:getHpPercent() > 80 then
        xbf = xbf + health_red
    end
    local hurt_red = self:getPropertyAdd("hurt_red")
    if hurt_red > 0 and self:getHpPercent() < 30 then
        xbf = xbf + hurt_red
    end
    bf = self.buffs[193]
    if bf then
        per = per - tonumber(bf.paramsdata[1])/100
    end
    if xbf > 0 then
        per = per - xbf / 100
    end
    per = per - soldierred / 1000
    return per
end
-- 获取值增幅 或减幅 
function roleData:getBuffsVal(...)
    local agrs = { ...}
    local val = 0
    for key, var in pairs(agrs) do
        local bf = self.buffs[var]
        if bf then
            if #bf.paramsdata == 1 then
                val = val + bf.params
            elseif #bf.paramsdata == 3 then
                dump(bf.paramsdata)
                val = val + tonumber(bf.paramsdata[1]) + math.floor(bf.user:getBaseProByString(bf.paramsdata[2]) / tonumber(bf.paramsdata[3]))
            end
        end
    end
    return val
end
function roleData:getDeBuffCount()
    local count = 0
    for key, var in pairs(self.buffs) do
        if var.debuff and var.debuff == 1 then
            count = count + 1
        end
    end
    return count
end
function roleData:getBuffCount()
    local count = 0
    for key, var in pairs(self.buffs) do
        if var.debuff and var.debuff == 0 then
            count = count + 1
        end
    end
    return count
end
function roleData:getBaseProByString(str)
    if str == "pow" then
        return self:getBasePower()
    elseif str == "con" then
        return self:getBaseCon()
    elseif str == "dex" then
        return self:getBaseSpd()
    elseif str == "int" then
        return self:getBaseInt()
    elseif str == "att" then
        return self:getBaseDamge()
    elseif str == "matt" then
        return self:getBaseMagicDamge()
    elseif str == "def" then
        return self:getBaseDefence()
    else
        return 0
    end
    return 0
end
-- 获取升星获得的随机属性
function roleData:getStarPro(k)
    local val = 0
    for key, var in pairs(self.starPro) do
        if var.key == k then
            val = val + var.val
        end
    end
    return val
end
-- 获取所有的值 包含了百分比和一般值
function roleData:getPropertyAdd(k)
    return
    (self.armors_add[k] or 0)
    +(self.weapons_add[k] or 0)
    +(self.equip_suit[k] or 0)
    +(self.title_add[k] or 0)
    +(self:getRoleStarData(k) or 0)
    +(self.pro_add[k] or 0)
    +(self.perpetual_add[k] or 0)
    +(self:getStarPro(k) or 0)
    +(self.battlepro[k] or 0)
    +(self.netBattleBasePro[k] or 0)
    +(self.netBattlePro[k] or 0)
    +(self.statePro[k] or 0 )
    +(self.bosscard_add[k] or 0)
    + self:getBuffProAdd(k)
    +(equipAtlasData.getProByKey(k, self:getDef().id, self:isRetinue()) or 0)
end
function roleData:getBuffProAdd(k)
    local val = 0
    --增加可以叠加的属性的buff
    val = self:getBuffProAddById(k)
    bf = self.buffs[184]
    if bf then
         if bf.paramsdata[1] == k then
              val =  val + bf.dtime * tonumber( bf.paramsdata[2] )
         end
    end
    return val
end
function roleData:getBuffProAddById(k)
    local agrs = {158,2588,2589,2590,2591,2592,2593,2594,2595}
    local val = 0
    for key, var in pairs(agrs) do
        local bf = self.buffs[var]
        if bf then
            val = val +(bf.proAdd[k] or 0)
        end
    end
    return val
end
-- 获取单独值 不参与百分比计算的部分
function roleData:getValPropertyAdd(k)    
    return
    (self.armors_add[k] or 0)
    +(self.weapons_add[k] or 0)
    +(self.equip_suit[k] or 0)
    +(self.title_add[k] or 0)
    +(self:getRoleStarData(k) or 0)
    +(self:getStarPro(k) or 0)
    +(self.netBattlePro[k] or 0)
    +(self.statePro[k] or 0)
    +(self.bosscard_add[k] or 0)
    + self:getBuffProAdd(k)
    +(equipAtlasData.getProByKey(k, self:getDef().id, self:isRetinue()) or 0)
end
function roleData:getBasePorpertyAdd(k)
    return(self.perpetual_add[k] or 0)
    +(self.pro_add[k] or 0)
    +(self.battlepro[k] or 0)
    +(self.netBattleBasePro[k] or 0)
end
function roleData:getBasePower()
    return self.power + self:getBasePorpertyAdd("pow")
end
function roleData:getPower()
    local per = self:getBuffsPer(103000, 3000, 3100, 93100, 83100, 73100) + 1
    local val = self:getBuffsVal(3011, 3111)
    local gper = self:getContinuousBuff(55)
    -- 突破I 武力增加5%
    if self:getStar() >= 1 then
        per = per + 0.05
    end 
    local xper = self:getPropertyAdd("pow_p")
    if xper > 0 then
        per = per + xper / 100
    end
    --殚谋戮力
    local bf = self.buffs[174]
    if bf and bf.paramsdata[4] == "pow"  then
         val = val +  tonumber(bf.paramsdata[3])*bf.dtime
    end
    local bf = self.buffs[180]    
    if bf and bf.paramsdata[3] == "pow" then
         if bf.paramsdata[1] == "dex" then
            local spd = self:getSpd()
            if spd >= tonumber( bf.paramsdata[2]) then
                val = val +  spd * tonumber( bf.paramsdata[4])/100
            end
         elseif bf.paramsdata[1] == "pow" then
         elseif bf.paramsdata[1] == "int" then
         elseif bf.paramsdata[1] == "con" then
         end
     end
     --龙魂
     bf = self.buffs[194]
    if bf  and bf.paramsdata[1] == "pow"  then
        val =  val +  tonumber(bf.paramsdata[3])* math.min(bf.dtime ,tonumber( bf.paramsdata[4] ))
    end


    return self:getBaseProByString("pow") *(per + gper) + val + self:getValPropertyAdd("pow")
end
function roleData:getBaseCon()
    return self.con + self:getBasePorpertyAdd("con")
end
--获取总加持
function roleData:getAllBless()
    local count = 0
    for key, var in pairs(self.weapons) do
        local wp = user.equipdatas[var]
        if wp then
            count = count + wp:getLevel()
        end
    end 
    return count
end
function roleData:getCon()
    local per = self:getBuffsPer(103003, 3003, 3103) + 1
    local val = self:getBuffsVal(3014, 3114)
    local xper = self:getPropertyAdd("con_p")
    if xper > 0 then
        per = per + xper / 100
    end
    local soldierdata = self:getCurSoldierData()
    local soldiercon = 0
    if soldierdata and self:soldierActive() then
        soldiercon = soldiercon +(soldierdata:getProByKey("con") or 0)
    end
    return self:getBaseProByString("con") * per + val + self:getValPropertyAdd("con") + soldiercon
end
function roleData:getBaseInt()
    return self.int + self:getBasePorpertyAdd("int")
end
function roleData:getInt()
    local per = self:getBuffsPer(103001, 3001, 3101, 93101, 83101, 73101) + 1
    local val = self:getBuffsVal(3012, 3112)
    local gper = self:getContinuousBuff(55)
    local gval = self:getContinuousBuff(3212)
    -- 突破I 智力增加5%
    if self:getStar() >= 1 then
        per = per + 0.05
    end
    local xper = self:getPropertyAdd("int_p")
    if xper > 0 then
        per = per + xper / 100
    end
    local bf = self.buffs[174]
    --殚谋戮力
    if bf and bf.paramsdata[4] == "int"  then
        val = val +  tonumber(bf.paramsdata[3])*bf.dtime
    end
    --龙魂
    bf = self.buffs[194]
    if bf  and bf.paramsdata[1] == "int"  then
        val =  val +  tonumber(bf.paramsdata[3])* math.min(bf.dtime ,tonumber( bf.paramsdata[4] ))
    end
    return self:getBaseProByString("int") *(per + gper) + val + gval * 100 + self:getValPropertyAdd("int")
end
function roleData:getMen()
    return self.men + self:getPropertyAdd("men")
end
function roleData:getBaseSpd()
    return self.spd + self:getBasePorpertyAdd("dex")
end
function roleData:getSpd()
    local per = self:getBuffsPer(103002, 3002, 3102, 93102, 83102, 73102) + 1
    local val = self:getBuffsVal(3013, 3113, 31113)
    local xper = self:getPropertyAdd("dex_p")
    if xper > 0 then
        per = per + xper / 100
    end

    local soldierdata = self:getCurSoldierData()
    local soldierspd = 0
    if soldierdata and self:soldierActive() then
        soldierspd = soldierspd +(soldierdata:getProByKey("dex") or 0)
    end
    return self:getBaseProByString("dex") * per + val + self:getValPropertyAdd("dex") + soldierspd
end
function roleData:getBaseDefence()
    return math.max(0,(self.defence + self:getBasePorpertyAdd("def")))
end
function roleData:getDefence()
    local per = self:getBuffsPer(3006, 3106, 93106) + 1
    local val = self:getBuffsVal(3017, 3117)
    local xper = self:getPropertyAdd("def_p")
    if xper > 0 then
        per = per + xper / 100
    end
    -- 白耗兵
    local bf = self.buffs[71]
    if bf then
        val = val + bf.dtime * tonumber(bf.paramsdata[1])
    end
    return self:getBaseProByString("def") * per + val + self:getValPropertyAdd("def")
end
function roleData:getLuck()
    return self.luck + self:getPropertyAdd("luck")
end

function roleData:getLuckAddDrop()
    local luck = self:getLuck()
    local num = 0
    if luck <= 4000 then
        num = math.floor(luck / 500)
    else
        num = 8 + math.floor((luck - 4000) / 1000)
    end
    return num
end
-- 获取属性伤
function roleData:getImgdue_dmg()
    return self.imgdue_dmg
end
-- 获取属性防
function roleData:getImgdue_def()
    return self.imgdue_def
end
function roleData:getHp()
    if not self:isRetinue() then
        if self.hp > 988888 then
            showCheck(21)
            return me.getRandom(1000) + 1000
        end
    end
    return self.hp
end
function roleData:getHpPercent()
    return self.hp * 100 / self:getMaxHp()
end
function roleData:getConAddHp()
    local r = 6
    local level = self:getLevel()
    r = math.floor(level / 10) + r
    return self:getCon() * r
end
function roleData:getProPoints()
    local num = 0
    for key, var in pairs(self.pro_add) do
        num = num + var
    end
    return num
end
-- 设置服务器数据血量 或者本地存储的血量
function roleData:setNetHp(hp, maxhp)
    if maxhp > 0 then
        self.netMaxHp = maxhp
    end
    if hp > 0 then
        self.hp = hp
    end
end
function roleData:getMaxHp()
    local viphp = 0
    if self:isRetinue() then
        -- 接受服务器数据
        if self.netMaxHp and self.netMaxHp > 0 then
            return self.netMaxHp
        end
    else
        viphp =(tonumber(getVipPro("hp_add")) or 0)
    end
    local per = self:getBuffsPer(3010, 3110, 93110) + 1
    local val = self:getBuffsVal(3021, 3121)
    -- 突破三 血量增加5%
    if self:getStar() >= 3 then
        per = per + 0.05
    end
--    if self:getStar() >= 5 then
--        per = per + 0.05
--    end
--    if self:getStar() >= 6 then
--        per = per + 0.08
--    end
    if not self:isRetinue() and isWindows() then
        viphp = viphp + (user.tmphp or 0)
    end
    local hp = math.floor((self.maxHp + self:getPropertyAdd("hp") + self:getConAddHp()) * per) + val + viphp
    if not self:isRetinue() then
        if hp > 988888 then
            showCheck(21)
            return me.getRandom(1000) + 1000
        end
    end
    return hp
end
function roleData:getMaxBp()
    return self.maxBp
end
function roleData:equipArmorGroup(gps)
    for key, var in pairs(gps) do
        local itemid = var.itemid
        local def = user.equipdatas[itemid]:getDef()
        local idx = def.mtype
        if tonumber(def.limit) == 1
            or tonumber(user.equipdatas[itemid]:getElevel()) <= self:getLevel() then
            if user.equipdatas[itemid].state == EQUIP_STATE_UNEQUIP then
                local olditemid = self.armors[idx]
                if olditemid then
                    user.equipdatas[olditemid].state = EQUIP_STATE_UNEQUIP
                    user.equipdatas[olditemid].eroleid = ""
                end
                user.equipdatas[itemid].state = EQUIP_STATE_EQUIPED
                user.equipdatas[itemid].eroleid = self.roleid
                self.armors[idx] = itemid

                taskMgr:getInstance():checkCurMainTask(TASK_TYPE_EARMOR, tonumber(def.id))
                if def.skillid and def.skillid > 0 then
                    self:checkSuit(def.skillid)
                end

            end
        else
            showTips("角色等级不足")
        end
    end
    self:updateArmorAdd()
    self:updateSuitData()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:equipWeaponGroup(gps)
    for key, var in pairs(gps) do
        local itemid = var.itemid
        local idx = user.equipdatas[itemid]:getDef().mtype
        if tonumber(user.equipdatas[itemid]:getDef().limit) == 1 or tonumber(user.equipdatas[itemid]:getElevel()) <= self:getLevel() then
            local olditemid = self.weapons[idx]
            if olditemid then
                user.equipdatas[olditemid].state = EQUIP_STATE_UNEQUIP
                user.equipdatas[olditemid].eroleid = ""
            end
            user.equipdatas[itemid].state = EQUIP_STATE_EQUIPED
            user.equipdatas[itemid].eroleid = self.roleid
            self.weapons[idx] = itemid
            taskMgr:getInstance():checkCurMainTask(TASK_TYPE_EWEAPON, tonumber(user.equipdatas[itemid]:getDef().id))
        else
            showTips("角色等级不足")
        end
    end
    self:updateWeaponAdd()
    self:updateSuitData()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
    me.dispatchCustomEvent(SELECTLAYER_CLOSE)
end
function roleData:equipWeapon(itemid)
    local idx = user.equipdatas[itemid]:getDef().mtype
    if not user.equipdatas[itemid]:isActive(self:getDef().baseid) then
        showTips("装备为专属装备，其他武将无法穿戴")
        return
    end
    if tonumber(user.equipdatas[itemid]:getDef().limit) == 1
        or tonumber(user.equipdatas[itemid]:getElevel()) <= self:getLevel()
    then
        local olditemid = self.weapons[idx]
        if olditemid then
            user.equipdatas[olditemid].state = EQUIP_STATE_UNEQUIP
            user.equipdatas[olditemid].eroleid = ""
        end
        user.equipdatas[itemid].state = EQUIP_STATE_EQUIPED
        user.equipdatas[itemid].eroleid = self.roleid
        self.weapons[idx] = itemid
        self:updateWeaponAdd()
        self:updateSuitData()
        taskMgr:getInstance():checkCurMainTask(TASK_TYPE_EWEAPON, tonumber(user.equipdatas[itemid]:getDef().id))
        me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
        me.dispatchCustomEvent(SELECTLAYER_CLOSE)
    else
        showTips("角色等级不足")
    end
end
function roleData:unEquipRoleStar(itemid)
    for key, var in pairs(self.rolestars) do
        if var == itemid then
            user.rolestars[var].state = EQUIP_STATE_UNEQUIP
            self.rolestars[key] = nil
            break
        end
    end
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:equipRoleStar(itemid, idx)
    taskMgr:getInstance():checkCurMainTask(TASK_TYPE_STAR_EQUIP, 1)
    local olditemid = self.rolestars[idx]
    if olditemid then
        user.rolestars[olditemid].state = EQUIP_STATE_UNEQUIP
    end
    user.rolestars[itemid].state = EQUIP_STATE_EQUIPED
    self.rolestars[idx] = itemid
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
    me.dispatchCustomEvent(SELECTLAYER_CLOSE)
end
function roleData:checkSuit(skillid)
    if skillid then
        if self.eSuitDatas[tonumber(skillid)] == nil then
            self.eSuitDatas[tonumber(skillid)] = equipSuitData.new(tonumber(skillid))
        end
    end
end
-- 是否是怪物
function roleData:isRetinue()
    return self.bRetinue
end
function roleData:isPlayer()
    return true
end
function roleData:posHaveWeapon(wid)
    return self.weapons[wid] ~= nil
end
-- 是否穿戴某件套装
function roleData:haveThisSuit(id)
    for key, var in pairs(self.weapons) do
        if user.equipdatas[var] then
            if tonumber(user.equipdatas[var].defid) == tonumber(id) then
                return true
            end
        end
    end
    for key, var in pairs(self.armors) do
        if user.equipdatas[var] then
            if tonumber(user.equipdatas[var].defid) == tonumber(id) then
                return true
            end
        end
    end
    return false
end
function roleData:isMainSkill(defid)
    if defid == nil then
        return false
    end
    local skilldata = cfg[CfgType.SKILL][tonumber(self.skills[1])]
    if skilldata.addition then
        local s = me.split(tostring(skilldata.addition), SKILL_BUFF_SPLIT)
        if s then
            for key, var in pairs(s) do
                local bf = buff:create(var)
                if bf then
                    -- 判断触发型的
                    if bf.kind == 6 then
                        if cfg[CfgType.SKILL][tonumber(bf.paramsdata[2])].baseid == cfg[CfgType.SKILL][tonumber(defid)].baseid then
                            return true
                        else
                            return false
                        end
                    end
                end
            end
        end
    end
    return skilldata.baseid == cfg[CfgType.SKILL][tonumber(defid)].baseid
end
function roleData:posHaveArmor(aid)
    return self.armors[aid] ~= nil
end
function roleData:posHaveRoleStar(sid)
    return self.rolestars[sid] ~= nil
end
function roleData:posHaveSkill(sid)
    return self.skills[sid] ~= nil
end
function roleData:unEquipWeapon(itemid)
    for key, var in pairs(self.weapons) do
        if var == itemid then
            user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
            user.equipdatas[var].eroleid = ""
            self.weapons[key] = nil
            break
        end
    end
    self:updateWeaponAdd()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:haveLearnedSkill(defid)
    for key, var in pairs(self.skills) do
        if var == defid then
            return true
        end
    end
    for key, var in pairs(self.learnedskills) do
        if var == defid then
            return true
        end
    end
    return false
end
-- 升级技能
function roleData:upSkill(oldid, tagid)
    for key, var in pairs(self.skills) do
        if var == oldid then
            self.skills[key] = tagid
            gameLogic:getInstance():save( { SAVE_ROLE_DATA })
            return
        end
    end
    for key, var in pairs(self.learnedskills) do
        if var == oldid then
            self.learnedskills[key] = tagid
            gameLogic:getInstance():save( { SAVE_ROLE_DATA })
            return
        end
    end
end
function roleData:learnSkill(defid)
    for key, var in pairs(self.skills) do
        if var == defid then
            showTips("您已经学习过此技能")
            return false
        end
    end
    for key, var in pairs(self.learnedskills) do
        if var == defid then
            showTips("您已经学习过此技能")
            return false
        end
    end
    if cfg[CfgType.SKILL][defid] then
        table.insert(self.learnedskills, defid)
        showTips("学会了" .. cfg[CfgType.SKILL][defid].name)
    else
        error("没有找到技能 id = " .. defid)
    end
    return true
end
-- 穿戴的套装数量
function roleData:haveSuitNums(skillid)
    self:checkSuit(skillid)
    local num = 0
    local broken = { }
    for key, var in pairs(self.weapons) do
        if user.equipdatas[var] then
            local sid = user.equipdatas[var]:getDef().skillid
            if sid and sid == skillid then
                num = num + 1
            end
        else
            table.insert(broken, key)
        end
    end
    for key, var in pairs(broken) do
        self.weapons[var] = nil
    end
    for key, var in pairs(self.armors) do
        if user.equipdatas[var] then
            local sid = user.equipdatas[var]:getDef().skillid
            if sid and sid == skillid then
                num = num + 1
            end
        end
    end
    print("suits " .. num)
    return num
end
function roleData:equipSkill(defid, idx)
    for key, var in pairs(self.skills) do
        if var == defid then
            showTips("您已经装备了此技能")
            return false
        end
    end
    if self.skills[idx] ~= nil or idx == 1 then
        showTips("该技能位已经装备了技能")
        return false
    end

    local book = user.skill_books[tonumber(cfg[CfgType.SKILL][defid].baseid)]
    if book.defid == defid and not book:haveUser() then
        book.owner_id = self.roleid

    else
        showTips("该技能已经被装备了")
        return
    end

    self.skills[idx] = defid
    taskMgr:getInstance():checkCurMainTask(TASK_TYPE_KILL_ESKILL, defid)
    showTips("装备了" .. cfg[CfgType.SKILL][defid].name)
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
    me.dispatchCustomEvent(SELECTLAYER_CLOSE)
end
function roleData:unEquipSkill(defid)
    for key, var in pairs(self.skills) do
        if var == defid and key ~= 1 then
            self.skills[key] = nil
            local book = user.skill_books[tonumber(cfg[CfgType.SKILL][defid].baseid)]
            if book then
                book.owner_id = ""
            end
            me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
            return true
        end
    end
    return false
end
-- 是否有更好的符文，只比较同类型同位置的主属性
function roleData:haveBestRune(idx)
    local ftype = self:getFightType()
    if self.armors[idx] then
        local old = user.equipdatas[self.armors[idx]]
        for key, var in pairs(user.equipdatas) do
            if var.state == EQUIP_STATE_UNEQUIP and
                var:getDef().kind == old:getDef().kind and
                var:getDef().mtype == old:getDef().mtype and
                var:getDef().skillid == old:getDef().skillid and
                var:getFightPower(ftype) > old:getFightPower(ftype)
            then
                return key
            end
        end
        return nil
    end
    return nil
end
function roleData:haveBestEquip(idx)
    local ftype = self:getFightType()
    if self.weapons[idx] then
        local old = user.equipdatas[self.weapons[idx]]
        if old then
            for key, var in pairs(user.equipdatas) do
                if var.state == EQUIP_STATE_UNEQUIP and
                    var:getDef().kind == old:getDef().kind and

                    (old:getDef().skillid == nil or(var:getDef().skillid and old:getDef().skillid and var:getDef().skillid == old:getDef().skillid)) and
                    var:getDef().mtype == old:getDef().mtype and
                    var:getElevel() <= self:getLevel() and
                    var:getFightPower(ftype) > old:getFightPower(ftype)

                then
                    return key
                end
            end
        end
        return nil
    end
    return nil
end
function roleData:upLevelSkill(defid, idx)
    self.skills[idx] = defid
    gameLogic:getInstance():save( { SAVE_ROLE_DATA })
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:getFightType()
    if self.jobPos == 0 then
        self.jobPos = self:getPower() >= self:getInt() and 1 or 2
    end
    return self.jobPos
end 
function roleData:qkEquipArmor()
    local curSkillids = { }
    local function isInTable(tb, id)
        for key, var in pairs(tb) do
            if var == id then
                return true
            end
        end
        return false
    end
    local function fillLeftPos()
        local tmp_chooselst = { }
        local ftype = self:getFightType()
        local tmp_pos_chooselst = { }
        local allPower = 0
        local curArmors = { }
        for key, var in pairs(user.equipdatas) do
            if var:getDef().kind == EQUIP_KIND_ARMOR and(var.state == EQUIP_STATE_UNEQUIP or var.itemid == self.armors[var:getDef().mtype]) then
                if tmp_chooselst[var:getDef().skillid] == nil then
                    tmp_chooselst[var:getDef().skillid] = { }
                end
                if tmp_chooselst[var:getDef().skillid][var:getDef().mtype] == nil then
                    tmp_chooselst[var:getDef().skillid][var:getDef().mtype] = var
                else
                    if tmp_chooselst[var:getDef().skillid][var:getDef().mtype]:getFightPower(ftype) < var:getFightPower(ftype) then
                        tmp_chooselst[var:getDef().skillid][var:getDef().mtype] = var
                    end
                end
                if tmp_pos_chooselst[var:getDef().mtype] == nil then
                    tmp_pos_chooselst[var:getDef().mtype] = var
                else
                    if tmp_pos_chooselst[var:getDef().mtype]:getFightPower(ftype) < var:getFightPower(ftype) then
                        tmp_pos_chooselst[var:getDef().mtype] = var
                    end
                end
            end
        end
        for key, var in pairs(tmp_pos_chooselst) do
            allPower = allPower + var:getFightPower(ftype)
        end
        local function xM()
            local posx = { }
            for var = 1, 6 do
                if curArmors[var] == nil then
                    table.insert(posx, var)
                end
            end
            if #posx > 0 then
                local tmpFights = { }
                local curMax = 0
                local curMaxSkillId = nil
                for key, var in pairs(tmp_chooselst) do
                    if not isInTable(curSkillids, key) then
                        for k, v in pairs(var) do
                            if tmpFights[key] == nil then
                                tmpFights[key] = 0
                            end
                            if isInTable(posx, v:getDef().mtype) then
                                tmpFights[key] = tmpFights[key] + v:getFightPower(ftype)
                            end
                        end
                        local suit = equipSuitData.new(key)
                        tmpFights[key] = tmpFights[key] + suit:fightPower(table.nums(var), ftype)
                        if curMax < tmpFights[key] then
                            curMax = tmpFights[key]
                            curMaxSkillId = key
                        end
                    end
                end
                if curMax > 0 and curMaxSkillId then
                    local curChoose = tmp_chooselst[curMaxSkillId]
                    for key, var in pairs(curChoose) do
                        if isInTable(posx, key) then
                            curArmors[var:getDef().mtype] = var
                        end
                    end
                    table.insert(curSkillids, curMaxSkillId)
                    return true
                end
                local allx = 0
                for var = 1, 6 do
                    if curArmors[var] == nil then
                        curArmors[var] = tmp_pos_chooselst[var]
                    end
                    if curArmors[var] then
                        allx = allx + curArmors[var]:getFightPower(ftype)
                    end
                end

                if allx >= allPower then
                    self:equipArmorGroup(curArmors)
                else
                    self:equipArmorGroup(tmp_pos_chooselst)
                end
                return false
            else
                local allx = 0
                for var = 1, 6 do
                    if curArmors[var] == nil then
                        curArmors[var] = tmp_pos_chooselst[var]
                    end
                    if curArmors[var] then
                        allx = allx + curArmors[var]:getFightPower(ftype)
                    end
                end

                if allx >= allPower then
                    self:equipArmorGroup(curArmors)
                else
                    self:equipArmorGroup(tmp_pos_chooselst)
                end
                return false
            end
        end
        while xM() do

        end
    end
    fillLeftPos()
end
-- 快速装备装备
function roleData:qkEquipWeapon()
    self:unEquipAllWeapon()
    local curSkillids = { }
    local curWeapons = { }
    local function isInTable(tb, id)
        for key, var in pairs(tb) do
            if var == id then
                return true
            end
        end
        return false
    end
    local function fillLeftPos()
        local tmp_chooselst = { }
        local ftype = self:getFightType()
        local suidixd = 0
        local tmp_pos_chooselst = { }
        local allPower = 0
        for key, var in pairs(user.equipdatas) do
            if var:getDef().kind == EQUIP_KIND_WEAPON
                and(tonumber(var:getDef().limit) == 1 or var:getElevel() <= self:getLevel())
                and(var.state == EQUIP_STATE_UNEQUIP or var.itemid == self.weapons[var:getDef().mtype]) then
                local skillid = 0
                if var:getDef().skillid == nil then
                    suidixd = suidixd - 1
                    skillid = -1
                else
                    skillid = var:getDef().skillid
                end
                if tmp_chooselst[skillid] == nil then
                    tmp_chooselst[skillid] = { }
                end
                if tmp_chooselst[skillid][var:getDef().mtype] == nil then
                    tmp_chooselst[skillid][var:getDef().mtype] = var
                else
                    if tmp_chooselst[skillid][var:getDef().mtype]:getFightPower(ftype) < var:getFightPower(ftype) then
                        tmp_chooselst[skillid][var:getDef().mtype] = var
                    end
                end
                if tmp_pos_chooselst[var:getDef().mtype] == nil then
                    tmp_pos_chooselst[var:getDef().mtype] = var
                else
                    if tmp_pos_chooselst[var:getDef().mtype]:getFightPower(ftype) < var:getFightPower(ftype) then
                        tmp_pos_chooselst[var:getDef().mtype] = var
                    end
                end
            end
        end
        for key, var in pairs(tmp_pos_chooselst) do
            allPower = allPower + var:getFightPower(ftype)
        end
        local function xM()
            local posx = { }
            for var = 1, 6 do
                if curWeapons[var] == nil then
                    table.insert(posx, var)
                end
            end
            if #posx > 0 then
                local tmpFights = { }
                local curMax = 0
                local curMaxSkillId = nil
                for key, var in pairs(tmp_chooselst) do
                    if not isInTable(curSkillids, key) then
                        for k, v in pairs(var) do
                            if tmpFights[key] == nil then
                                tmpFights[key] = 0
                            end
                            if isInTable(posx, v:getDef().mtype) then
                                tmpFights[key] = tmpFights[key] + v:getFightPower(ftype)
                            end

                        end
                        if key >= 0 then
                            local suit = equipSuitData.new(key)
                            tmpFights[key] = tmpFights[key] + suit:fightPower(table.nums(var), ftype)
                        end
                        if curMax < tmpFights[key] then
                            curMax = tmpFights[key]
                            curMaxSkillId = key
                        end
                    end
                end


                if curMax > 0 and curMaxSkillId then
                    local curChoose = tmp_chooselst[curMaxSkillId]
                    for key, var in pairs(curChoose) do
                        if isInTable(posx, key) then
                            curWeapons[var:getDef().mtype] = var
                        end
                    end
                    table.insert(curSkillids, curMaxSkillId)
                    return true
                end

                local allx = 0
                for var = 1, 6 do
                    if curWeapons[var] == nil then
                        curWeapons[var] = tmp_pos_chooselst[var]
                    end
                    if curWeapons[var] then
                        allx = allx + curWeapons[var]:getFightPower(ftype)
                    end
                end

                if allx >= allPower then
                    self:equipWeaponGroup(curWeapons)
                else
                    self:equipWeaponGroup(tmp_pos_chooselst)
                end

                return false
            else
                local allx = 0
                for var = 1, 6 do
                    if curWeapons[var] == nil then
                        curWeapons[var] = tmp_pos_chooselst[var]
                    end
                    if curWeapons[var] then
                        allx = allx + curWeapons[var]:getFightPower(ftype)
                    end
                end

                if allx >= allPower then
                    self:equipWeaponGroup(curWeapons)
                else
                    self:equipWeaponGroup(tmp_pos_chooselst)
                end
                return false
            end
        end
        while xM() do

        end
    end
    fillLeftPos()
end
function roleData:unEquipAllArmor()
    for key, var in pairs(self.armors) do
        user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
        user.equipdatas[var].eroleid = ""
    end
    me.tableClear(self.armors)
    self.armors = { }
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateSuitData()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:unEquipAllWeapon()
    for key, var in pairs(self.weapons) do
        if user.equipdatas[var] then
            user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
            user.equipdatas[var].eroleid = ""
        end
    end
    me.tableClear(self.weapons)
    self.weapons = { }
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateSuitData()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:unEquipAll()
    for key, var in pairs(self.weapons) do
        user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
        user.equipdatas[var].eroleid = ""
    end
    me.tableClear(self.weapons)
    self.weapons = { }
    for key, var in pairs(self.armors) do
        user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
        user.equipdatas[var].eroleid = ""
    end
    me.tableClear(self.armors)
    self.armors = { }
    self:updateArmorAdd()
    self:updateWeaponAdd()
    self:updateSuitData()
    for key, var in pairs(self.skills) do
        if key ~= 1 then
            local book = user.skill_books[tonumber(cfg[CfgType.SKILL][var].baseid)]
            book.owner_id = ""
        end
    end
    local skillid = self.skills[1]
    me.tableClear(self.skills)
    self.skills = { }
    self.skills[1] = skillid
    for key, var in pairs(self.rolestars) do
        user.rolestars[var].state = EQUIP_STATE_UNEQUIP
    end
    me.tableClear(self.rolestars)
    self.rolestars = { }
    self:downSoldier()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:equipArmor(itemid)
    local def = user.equipdatas[itemid]:getDef()
    local idx = def.mtype
    if tonumber(def.limit) == 1
        or tonumber(user.equipdatas[itemid]:getElevel()) <= self:getLevel() then
        if user.equipdatas[itemid].state == EQUIP_STATE_UNEQUIP then
            local olditemid = self.armors[idx]
            if olditemid then
                user.equipdatas[olditemid].state = EQUIP_STATE_UNEQUIP
                user.equipdatas[olditemid].eroleid = ""
            end
            user.equipdatas[itemid].state = EQUIP_STATE_EQUIPED
            user.equipdatas[itemid].eroleid = self.roleid
            self.armors[idx] = itemid
            self:checkSpe()
            taskMgr:getInstance():checkCurMainTask(TASK_TYPE_EARMOR, tonumber(def.id))
            if def.skillid and def.skillid > 0 then
                self:checkSuit(def.skillid)
            end
            self:updateArmorAdd()
            self:updateSuitData()
            me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
        end
    else
        showTips("角色等级不足")
    end
end
function roleData:getFaceId()
    return self:getDef().ani
end
function roleData:initPasSkill()
    -- 战斗前初始化被动技能，清空BUFF
    self.buffs = { }

    self.saveSkills = { }
    -- 用于记录装备的被动技能
    self.uskills = { }
    self.optSkills = {}
    -- 装备被动
    for key, var in pairs(self.armors) do
        local adata = user.equipdatas[var]
        if adata and adata:isActive(self:getDef().baseid) then
            if adata.skills then
                for k, v in pairs(adata.skills) do
                    local sdata = cfg[CfgType.SKILL][tonumber(v)]
                    table.insert(self.saveSkills, sdata.id)
                    if tonumber(sdata.iStunt) == SKILL_KIND_PAS then
                        if sdata.addition then
                            local s = me.split(tostring(sdata.addition), SKILL_BUFF_SPLIT)
                            if s then                                
                                for key, mvar in pairs(s) do
                                    print("buff " .. mvar)
                                    self:addBuff(mvar, self)
                                end
                            end
                        end                    
                    else
                        table.insert(self.uskills, tonumber(v))
                    end
                    if sdata.passive then
                        local s = me.split(tostring(sdata.passive), SKILL_BUFF_SPLIT)
                        if s then
                            for xkey, xvar in pairs(s) do
                                self:addBuff(xvar, self)
                            end
                        end
                    end
                end
            end
        end
    end
    local reslut = { }
    for key, var in pairs(self.weapons) do
        local adata = user.equipdatas[var]
        if adata then
            if adata.skills then
                for k, v in pairs(adata.skills) do
                    local sdata = cfg[CfgType.SKILL][tonumber(v)]
                    table.insert(self.saveSkills, sdata.id)
                    if tonumber(sdata.iStunt) == SKILL_KIND_PAS then
                        if sdata.addition then
                            local s = me.split(tostring(sdata.addition), SKILL_BUFF_SPLIT)
                            if s then
                                for _, buffid in pairs(s) do
                                    self:addBuff(buffid, self)
                                end
                            end
                        end
                    elseif tonumber(sdata.iStunt) == SKILL_KIND_OPT then
                        table.insert(self.optSkills, tonumber(v))
                    else
                        table.insert(self.uskills, tonumber(v))
                    end
                    if sdata.passive then
                        local s = me.split(tostring(sdata.passive), SKILL_BUFF_SPLIT)
                        if s then
                            for xkey, xvar in pairs(s) do
                                self:addBuff(xvar, self)
                            end
                        end
                    end
                end
            end
        end
    end

    for key, var in pairs(self.skills) do
        local sdata = cfg[CfgType.SKILL][var]
        if tonumber(sdata.iStunt) == SKILL_KIND_PAS then
            if sdata.addition then
                local s = me.split(tostring(sdata.addition), SKILL_BUFF_SPLIT)
                if s then
                    for xkey, xvar in pairs(s) do
                        self:addBuff(xvar, self)
                    end
                end
            end
        else
            table.insert(self.uskills, var)
        end
        if sdata.passive then
            local s = me.split(tostring(sdata.passive), SKILL_BUFF_SPLIT)
            if s then
                for key, var in pairs(s) do
                    self:addBuff(var, self)
                end
            end
        end
    end
    -- 读取网络的技能
    if self.netSkills then
        for key, var in pairs(self.netSkills) do
            local sdata = cfg[CfgType.SKILL][var]
            if tonumber(sdata.iStunt) == SKILL_KIND_PAS then
                if sdata.addition then
                    local s = me.split(tostring(sdata.addition), SKILL_BUFF_SPLIT)
                    if s then
                        for xkey, xvar in pairs(s) do
                            self:addBuff(xvar, self)
                        end
                    end
                end
            else
                table.insert(self.uskills, var)
            end
            if sdata.passive then
                local s = me.split(tostring(sdata.passive), SKILL_BUFF_SPLIT)
                if s then
                    for key, var in pairs(s) do
                        self:addBuff(var, self)
                    end
                end
            end
        end
    end
    -- 怪物被动
    if self:isRetinue() then
        local def = self:getMonsterDef()
        if def and def.pasbuff then
            local s = me.split(tostring(def.pasbuff), SKILL_BUFF_SPLIT)
            if s then
                for key, var in pairs(s) do
                    self:addBuff(var, self)
                end
            end
        end
    end
    -- 去除相同的技能
    self.uskills = me.deleteTheSameElement(self.uskills)
    self.saveSkills = me.deleteTheSameElement(self.saveSkills)
    self.bskill = me.copyTab(self.skills)
    self.isUpdateBuff = true
end
-- 消除减益
function roleData:clearReduceBuff()
    local bfs = { }
    for key, bf in pairs(self.buffs) do
        if bf.debuff >= 1 and bf.dispel == 1 then
            -- battleLayer:getInstance():showHurtEffectNumByCampPos("消除减益", self.battle_camp, self.battle_pos, EFFECT_NUM_COLOR_GREEN)
            self.isUpdateBuff = true
            battleLayer:getInstance():getCampPosModel(self.battle_camp, self.battle_pos).target = nil
        else
            bfs[key] = bf
        end
    end
    me.tableClear(self.buffs)
    self.buffs = bfs
end
function roleData:clearBuffs()
    me.tableClear(self.buffs)
    self.buffs = { }
end
function roleData:saveEquipCopy(rdata)
    self.saveArmors = rdata.saveArmors
    self.saveWeapons = rdata.saveWeapons
    self.saveSoldier = rdata.saveSoldier
end
function roleData:roleReset(free)
    local num = 0
    for var = self:getStar(), 1, -1 do
        if var <= 5 then
            num = num + var
        elseif var == 6 then
            num = num + 10
        elseif var == 7 then
            num = num + 20
        end
    end
    print("突破卡=" .. num)
    if self:getDef().star == 5 then
        addGroupGift("4:82297|" .. num)
    elseif self:getDef().star == 4 then
        addGroupGift("4:82296|" .. num)
    end
    if self:getStarLevelVal() >= 1 then
        for var = 1, self:getStarLevelVal() do
            local rd = roleData:create(self.defid)
            if var == 1 then
                rd:setLevel(2)
                rd:saveEquipCopy(self)
            end
            roleMgr:getInstance():addRoleWithData(rd)
        end
    end
    -- 重置进阶进度
    self.starProcess = 0
    if getSkillCostByLevel(self.skills[1]) > 0 then
        addItem(82243, getSkillCostByLevel(self.skills[1]))
        showTips("战法经验+" .. getSkillCostByLevel(self.skills[1]))
    end
    if free then
        local exp = self:getExpASP()
        for var = 1, self:getLevel() do
            exp = exp + expForUserLevel(var)
        end
        local num = math.floor(exp / 80000)
        if num > 0 then
            addGroupGift("4:82026|" .. num)
        end
    else
        local exp = 0
        for var = 1, self:getLevel() do
            exp = exp + expForUserLevel(var)
        end
        local num = math.floor(exp * 0.8 / 80000)
        if num > 0 then
            addGroupGift("4:82026|" .. num)
        end
    end
    roleMgr:getInstance():removeRole(self.roleid)
end
-- 消除增益
function roleData:clearGainBuff()
    local bfs = { }
    for key, bf in pairs(self.buffs) do
        if bf.debuff == 0 and bf.dispel == 1 then
            -- battleLayer:getInstance():showHurtEffectNumByCampPos("消除增益", self.battle_camp, self.battle_pos, EFFECT_NUM_COLOR_RED)
            self.isUpdateBuff = true
        else
            bfs[key] = bf
        end
    end
    me.tableClear(self.buffs)
    self.buffs = bfs
end
function roleData:isEquip(wid)
    for key, var in pairs(self.weapons) do
        if wid == var then
            return true
        end
    end
    for key, var in pairs(self.armors) do
        if wid == var then
            return true
        end
    end
    return false
end
function roleData:getProByKey(key)
    if key == "pow" then
        return self:getPower()
    elseif key == "int" then
        return self:getInt()
    elseif key == "dex" then
        return self:getSpd()
    elseif key == "def" then
        return self:getDefence()
    elseif key == "con" then
        return self:getCon()
    end
    return 0
end
function roleData:addBuff(ds, whoUse)
    local curbuff = buff:create(ds)
    local bf = self.buffs[48]
    -- 洞察
    if bf then       
        -- 免疫控制效果
        if curbuff.debuff == 2 then
            battleLayer:getInstance():showEffectName(self, "洞察")
            return
        end        
    end
    bf = self.buffs[56]
    if bf then
        -- 免疫控制
        local tagbuff = cfg[CfgType.BUFF][curbuff.id]
        if tagbuff then
            local ods = tonumber(bf.params)
            if self.buffs[93] then
                -- 孙坚猛虎
                ods = 100
            end
            if tagbuff.debuff == 2 and me.getRandom(100) <= ods then
                battleLayer:getInstance():showEffectName(self, "识破")
                return
            end
        end
    end
    if curbuff.limit and curbuff.limit ~= self:getDef().baseid then
        return
    end
    bf = self.buffs[53]
    if bf then
        -- tagbuff.dispel == 2 为不可免疫类型
       
        if curbuff.kind == 31 or curbuff.kind == 31000 and curbuff.dispel and curbuff.dispel ~= 2 then
            -- 免疫缴械
            return
        end
    end
    bf = self.buffs[54]
    if bf then
        -- tagbuff.dispel == 2 为不可免疫类型
        if curbuff.kind == 33 or curbuff.kind == 33000 and curbuff.dispel and curbuff.dispel ~= 2 then
            -- 免疫技穷
            return
        end
    end
    bf = self.buffs[82]
    if bf then
        -- tagbuff.dispel == 2 为不可免疫类型
        if curbuff.kind == 1 or curbuff.kind == 10 and curbuff.dispel and curbuff.dispel ~= 2 then
            -- 免疫眩晕
            return
        end
    end
     bf = self.buffs[179]
    if bf then
        -- tagbuff.dispel == 2 为不可免疫类型
        if curbuff.kind == 1 or curbuff.kind == 34 and curbuff.dispel and curbuff.dispel ~= 2 then
            -- 免疫混乱
            return
        end
    end

    -- 消除增益
    if curbuff.kind == 51 then
        self:clearGainBuff()
        return
    end
    -- 消除减益
    if curbuff.kind == 52 then
        self:clearReduceBuff()
        return
    end
    if curbuff.kind == 155 then
        if self.buffs[tonumber(curbuff.paramsdata[1])] then
            if me.getRandom(100) <= tonumber(curbuff.paramsdata[2]) then
                self:addBuff(curbuff.paramsdata[3] .. "|" .. curbuff.paramsdata[4])
            end
        end
        return
    end
    if curbuff.kind == 89 then
        -- 增加技能
        table.insert(self.uskills, tonumber(curbuff.paramsdata[1]))
        self.uskills = me.deleteTheSameElement(self.uskills)
        return
    end
    if curbuff.kind == 3113 then
        bf = self.buffs[184]
        if bf then
             if tonumber( bf.paramsdata[3] ) > bf.dtime then
                 bf.dtime = bf.dtime +1
                 local model = battleLayer:getInstance():getCampPosModel(self.battle_camp, self.battle_pos)
                 battleLayer:getInstance():showSkillNameEffect(model:getWorldPos(), "方天映黛眉", me.convert3Color_("E05F22"))
             end
        end        
    end
    bf = self.buffs[curbuff.kind]
    if bf then
        if curbuff.kind >= 1000 and curbuff.kind <= 2000 then
            local max = 10
            if curbuff.paramsdata[4] then
                -- 最大次数
                max = tonumber(curbuff.paramsdata[4])
            end
            if bf.htime < max then
                bf.htime = bf.htime + 1
            end
        end
        if curbuff.kind == 70 then
            -- 算无遗策
            local max = tonumber(bf.paramsdata[2])
            max = max or 1
            local addtime = 1
            if self.buffs[165] then
                -- 最大次数
                max = max + tonumber(self.buffs[165].paramsdata[1])
                addtime = tonumber(self.buffs[165].paramsdata[2])
            end
            if bf.htime < max then
                bf.htime = bf.htime + addtime
            end
        end
        -- 存在燃烧效果则立即造成480伤害
        if curbuff.kind == 1000 then
            local c = 0
            if tonumber(bf.paramsdata[3]) == 1 then
                -- 法术
                c = skillData.getBuffDamage(bf.user, self, 2, tonumber(bf.paramsdata[2]))
            elseif tonumber(bf.paramsdata[3]) == 0 then
                -- 物理
                c = skillData.getBuffDamage(bf.user, self, 1, tonumber(bf.paramsdata[2]))
            end
            local mbf = bf.user.buffs[163]
            if mbf then
                -- 焱炎
                c = c *(1 + tonumber(mbf.paramsdata[1]) / 100)
            end
            mbf = bf.user.buffs[195]
            if mbf then
                --燃烬
                c = c +  bf.user:getProByKey( mbf.paramsdata[1] ) * tonumber(mbf.paramsdata[2])                
            end
            bf.user:addSkillHits(c)
            local model = battleLayer:getInstance():getCampPosModel(self.battle_camp, self.battle_pos)
            model:cutHp(c)
            for key, var in pairs(battleLayer:getInstance().battleTeams) do
                for k, v in pairs(var) do
                     if not v:bDeath() then
                        local mbf = v.data.buffs[182] --连营
                        if mbf then
                             mbf.dtime = mbf.dtime + 1
                             if mbf.dtime >= tonumber(mbf.paramsdata[1]) then
                                 mbf.dtime = 0                                                             
                                 battleLayer:getInstance():useSkill(v,tonumber(mbf.paramsdata[2]))
                             end
                        end
                        mbf = v.data.buffs[183] --西陵克敌人
                        if mbf and not tolua.isnull(model) then    
                           --if tonumber(mbf.paramsdata[2]) >= me.getRandom(100) then
                               local c = skillData.getBuffDamage(v.data, self, 2, tonumber(mbf.paramsdata[1]))  
                               battleLayer:getInstance():showSkillNameEffect(v:getWorldPos(), "伤敌当决死", me.convert3Color_("E05F22"))
                               print("伤敌当决死 -- "..bf.paramsdata[1])
                               v.data:addSkillHits(c)             
                               model:cutHp(c)
                          -- end
                        end                    
                     end
                end                
            end            
        end
        if curbuff.kind == 19 then
            -- 护盾
            local addp =  self:getPropertyAdd("shield_p")
            if curbuff.paramsdata[3] == "int" then
                bf.paramsdata[2] = tonumber(bf.paramsdata[2]) +  (whoUse:getInt() * tonumber(curbuff.paramsdata[4])*(1+addp/100))
            end
        end

        if curbuff.kind == 158 or curbuff.kind == 2588 then
            if curbuff.duration == -1 or  curbuff.duration >= bf.duration then
                bf:addPro(curbuff)
                return
            end
        end
        if curbuff.debuff == 2 then
            -- 控制buf不再叠加时间
            bf.dtime = bf.dtime + 1
            curbuff:resetTimeWithTps(self:getPropertyAdd("tps"))
            local maxtime = math.min(curbuff.duration, bf:getLeftTime() + math.floor(curbuff.duration / bf.dtime))
            curbuff.m_duration = curbuff.duration - maxtime
        else
            if bf.duration == -1 or curbuff.duration < bf.duration then
                return
            end
        end

    else
        if curbuff.debuff == 2 then
            -- tps 最大100%
            curbuff:resetTimeWithTps(self:getPropertyAdd("tps"))
        end
    end
    if curbuff.kind == 1 or curbuff.kind == 33 or curbuff.kind == 33000 then
        -- 眩晕，犹豫 打断技能
        local caninterrupt = true
        if self.buffs[150] and self:isMainSkill(self.cur_skillid) then
            caninterrupt = false
        end
        if self.cur_skillid and caninterrupt then

            self.cur_release = 0
            if curbuff.kind == 1 then
                self.attack_interval = 0
            end
            if battleLayer:getInstance():isBattling() then
                battleLayer:getInstance():getCampPosModel(self.battle_camp, self.battle_pos):showInterruptSkill()
            end
        end
        curbuff.bnew = true
        curbuff.user = whoUse
        self.buffs[curbuff.kind] = curbuff
        self.isUpdateBuff = true
    else
        curbuff.bnew = true
        curbuff.user = whoUse
        self.buffs[curbuff.kind] = curbuff
        self.isUpdateBuff = true
    end
end
function roleData:removeBuff(bfid)
    for key, var in pairs(self.buffs) do
        if tonumber(var.id) == tonumber(bfid) then
            self.buffs[key] = nil
            self.isUpdateBuff = true
            -- 暴走清除了
            if key == 34 or key == 34000 then
                self.buffclear = true
            end
            break
        end
    end
end
-- 获取治疗能力 per技能威力
function roleData:getTreat(per)
    -- 治疗能力
    local x =(self:getPropertyAdd("trt") + self:getInt() / 2) *(per / 200) + 50
    return x
end
function roleData:getTreaterAd()
    local trtper = self:getPropertyAdd("trtad")
    local bf = self.buffs[86]
    if bf then
        -- 仁义
        trtper = trtper + tonumber(bf.paramsdata[1])
    end
    bf = self.buffs[19]
    if bf then
        trtper = trtper + self:getPropertyAdd("shield_trtad")
    end
    bf = self.buffs[189]
    local per = 0
    if bf  then
        per = per + tonumber(bf.paramsdata[2]) / 100 * bf.dtime
    end
    return trtper
end
function roleData:getTreatPd()
    local trtper = self:getPropertyAdd("trtpd")
    local bf = self.buffs[19]
    if bf then
        trtper = trtper + self:getPropertyAdd("shield_trtpd")
    end
    return trtper
end
-- 获取恢复能力 不基于魔攻的
function roleData:getNatTreat(per)
    local x =(self:getDefence()) *(per / 200) * 0.45 + 50
    return x
end
function roleData:getJob()
    return cfg[CfgType.JOB][user.jobid].name
end
function roleData:updateTitleAdd()
    self.title_add = me.copyTab(pro_arr)
    local data = cfg[CfgType.UTITLE][self.titleId]
    if me.isValidStr(data.imgbue) then
        local t = me.split(data.imgbue, ",")
        if t then
            for key, var in pairs(t) do
                local imgbue = me.split(var, ":")
                if imgbue then
                    if self.title_add[imgbue[1]] == nil then
                        self.title_add[imgbue[1]] = 0
                    end
                    self.title_add[imgbue[1]] = self.title_add[imgbue[1]] + imgbue[2]
                end
            end
        end
    end
end
function roleData:setCurSoldier(sid)
    if user.soldierDatas[sid] then
        self.curSoldier = sid
        user.soldierDatas[self.curSoldier].state = SOLDIER_STATE_USE
        gameLogic:getInstance():saveGame()
        self:updateTitleAdd()
    end
end
function roleData:downSoldier()
    if self.curSoldier and user.soldierDatas[self.curSoldier] then
        user.soldierDatas[self.curSoldier].state = SOLDIER_STATE_IDLE
        self.curSoldier = ""
        gameLogic:getInstance():saveGame()
        self:updateTitleAdd()
    end
end
function roleData:getCurSoldierId()
    if user.soldierDatas[self.curSoldier] then
        return self.curSoldier
    end
    self.curSoldier = ""
    return self.curSoldier
end
function roleData:getCurSoldierData()
    if me.isValidStr(self.curSoldier) and user.soldierDatas[self.curSoldier] then
        return user.soldierDatas[self.curSoldier]
    end
    self.curSoldier = ""
    return nil
end
function roleData:soldierActive()
    if me.isValidStr(self.curSoldier) and user.soldierDatas[self.curSoldier] then
        local def = user.soldierDatas[self.curSoldier]:getDef()
        if def.soldier == 0 or def.soldier == self:getDef().soldier
            or((self:getDef().soldier == 2 or self:getDef().soldier == 3) and def.soldier == 6) then
            return true
        end
    end
    return false
end
function roleData:bHaveSoldier()

    return me.isValidStr(self:getCurSoldierId())
end
local steps = {35,56,70,82,98,105}
function roleData:updateWeaponAdd()
    self.weapons_add = me.copyTab(pro_arr)
    local count = 0
    for key, var in pairs(self.weapons) do
        local adata = user.equipdatas[var]
        if adata then
            adata:updateRuneInlayData()
            for k, v in pairs(self.weapons_add) do
                self.weapons_add[k] = self.weapons_add[k] + adata:getImgdueVar(k)
            end
            if self.weapons_add["att"] == nil then
                self.weapons_add["att"] = 0
            end
            self.weapons_add["att"] = self.weapons_add["att"]
            if self.weapons_add["m_att"] == nil then
                self.weapons_add["m_att"] = 0
            end
            self.weapons_add["m_att"] = self.weapons_add["m_att"]
            count = count + adata:getLevel()
        end
    end
    if self.jobPos ~= 0 and not self:isRetinue() then
        for var = 1, 6 do
             local st = cfg[CfgType.CFG_FIGHTEXPECT][self.jobPos]["step"..var]
             if steps[var] <= count then
                    local imgbue = me.split(st, ":")
                    if imgbue then
                        if self.weapons_add[imgbue[1]] == nil then
                            self.weapons_add[imgbue[1]] = 0
                        end
                        self.weapons_add[imgbue[1]] = self.weapons_add[imgbue[1]] + imgbue[2]
                    end
             end
        end
    end
    local jobdata = cfg[CfgType.JOB][user.jobid]
    if jobdata then
        if me.isValidStr(jobdata.imgbue) then
            local t = me.split(jobdata.imgbue, ",")
            if t then
                for key, var in pairs(t) do
                    local imgbue = me.split(var, ":")
                    if imgbue then
                        if self.weapons_add[imgbue[1]] == nil then
                            self.weapons_add[imgbue[1]] = 0
                        end
                        self.weapons_add[imgbue[1]] = self.weapons_add[imgbue[1]] + imgbue[2]
                    end
                end
            end
        end
    end
end
-- 检测专属
function roleData:checkSpe()
    --    if self:isRetinue() == false then
    --        if self.armors[4] and user.equipdatas[self.armors[4]] then
    --            self.skills[1] = tonumber(self:getDef().tskill)
    --        else
    --            self.skills[1] = tonumber(self:getDef().skill)
    --        end
    --    end
end
function roleData:updateArmorAdd()
    self.armors_add = me.copyTab(pro_arr)
    for key, var in pairs(self.armors) do
        local adata = user.equipdatas[var]
        if adata and adata:isActive(self:getDef().baseid) then
            adata:updateRuneInlayData()
            for k, v in pairs(self.armors_add) do
                self.armors_add[k] = self.armors_add[k] + adata:getImgdueVar(k)
            end
            if self.armors_add["def"] == nil then
                self.armors_add["def"] = 0
            end
            self.armors_add["def"] = self.armors_add["def"]
            if self.armors_add["m_def"] == nil then
                self.armors_add["m_def"] = 0
            end
            self.armors_add["m_def"] = self.armors_add["m_def"]
        end
    end
end
function roleData:unEquipArmor(itemid)
    for key, var in pairs(self.armors) do
        if var == itemid then
            user.equipdatas[var].state = EQUIP_STATE_UNEQUIP
            user.equipdatas[var].eroleid = ""
            self.armors[key] = nil
            break
        end
    end
    self:updateArmorAdd()
    self:updateSuitData()
    self:checkSpe()
    me.dispatchCustomEvent(ROLELAYER_UPDATEUI)
end
function roleData:create(defid)
    local m = roleData.new(defid)
    if m and m:init() then
        return m
    end
    return nil
end

