from pydantic import BaseModel, ConfigDict
from typing import Dict, List, Optional, Any, Union, Tuple
import redis.asyncio as redis
from redis import exceptions as redis_exceptions  # 显式导入Redis异常
import motor.motor_asyncio
from bson import ObjectId
from datetime import datetime
import json
import uuid
import logging
import traceback
import asyncio
import os
from abc import ABC
from models import ResponseModel
from config import config
from utils import handle_error
import pymongo
from logger_config import setup_logger
# 初始化日志系统
logger = setup_logger(__name__)

# 设置其他库的日志级别
logging.getLogger("pymongo").setLevel(logging.INFO)
logging.getLogger("pymongo.topology").setLevel(logging.WARNING)

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        try:
            if isinstance(obj, ObjectId):
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, bytes):
                return obj.decode('utf-8')
            return super().default(obj)
        except Exception as e:
            logger.error(f"JSON 编码失败: {str(e)}，对象: {obj}")
            return str(obj)

class BaseModelORM(BaseModel, ABC):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def serialize(self, exclude_fields=None) -> dict:
        """
        序列化模型数据，支持排除指定字段
        
        Args:
            exclude_fields: 要排除的字段列表，如 ['password', 'token']
            
        Returns:
            dict: 序列化后的字典
        """
        try:
            # 构建排除字段集合
            exclude_set = set()
            if exclude_fields:
                if isinstance(exclude_fields, (list, tuple, set)):
                    exclude_set = set(exclude_fields)
                else:
                    exclude_set = {exclude_fields}
            
            # 获取模型数据 - 修改为包含所有字段，不论是否显式设置
            data = self.dict(exclude_unset=False)
            
            # 手动排除字段和None值
            for field, value in list(data.items()):
                if field in exclude_set or value is None:
                    del data[field]
            
            # 处理特殊类型
            if "_id" in data:
                data["_id"] = str(data["_id"])
            if "created_at" in data and isinstance(data["created_at"], datetime):
                data["created_at"] = data["created_at"].isoformat()
            
            # 如果密码字段没有被排除，则确保它是字符串形式
            if "password" in data and isinstance(data["password"], bytes):
                data["password"] = data["password"].decode('utf-8')
                
            return data
        except Exception as e:
            logger.error(f"序列化失败: {str(e)}, 排除字段: {exclude_fields}")
            return {}

    @classmethod
    def deserialize(cls, data: dict) -> 'BaseModelORM':
        try:
            clean_data = {k: v for k, v in data.items() if k != "_id"}
            if "created_at" in clean_data and isinstance(clean_data["created_at"], str):
                clean_data["created_at"] = datetime.fromisoformat(clean_data["created_at"])
            if "password" in clean_data and isinstance(clean_data["password"], str):
                clean_data["password"] = clean_data["password"].encode('utf-8')
            return cls(**clean_data)
        except Exception as e:
            logger.error(f"反序列化失败: {str(e)}，数据: {data}")
            raise

class UserData(BaseModelORM):
    id: str
    password: Union[str, bytes]  # 修改为可以接受字符串或字节类型
    gold: int = 0 # 金币
    created_at: Optional[datetime] = None # 创建时间    
    items: List[str] = [] # 物品列表
    profile: Optional[Dict[str, Any]] = None # 角色信息
    nickname: Optional[str] = None # 角色昵称
    
    @property
    def username(self) -> str:
        return self.id

    @classmethod
    async def validate_field(cls, field_path: str, value: Any, field_config: Dict[str, Any]) -> Tuple[bool, str]:
        try:
            field_spec = field_config.get("fields", {}).get(field_path)
            if not field_spec:
                logger.debug(f"字段 {field_path} 未在配置文件中定义，允许更新")
                return True, ""

            type_map = {"int": int, "str": str, "float": float, "list": list, "dict": dict}
            expected_type = type_map.get(field_spec.get("type"))
            if not expected_type:
                logger.warning(f"字段 {field_path} 配置无效类型: {field_spec.get('type')}")
                return True, ""

            if not isinstance(value, expected_type):
                return False, f"字段 {field_path} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"

            if "range" in field_spec and isinstance(value, (int, float)):
                min_val, max_val = field_spec["range"]
                if not (min_val <= value <= max_val):
                    return False, f"字段 {field_path} 值超出范围，期望 {min_val} 到 {max_val}，实际 {value}"
            elif "values" in field_spec:
                if value not in field_spec["values"]:
                    return False, f"字段 {field_path} 值无效，期望 {field_spec['values']}，实际 {value}"
            elif "element_type" in field_spec and isinstance(value, list):
                element_type = type_map.get(field_spec["element_type"])
                if not element_type or not all(isinstance(item, element_type) for item in value):
                    return False, f"字段 {field_path} 列表元素类型错误，期望 {field_spec['element_type']}"
            return True, ""
        except Exception as e:
            return False, f"字段验证失败: {str(e)}"

    @classmethod
    async def initialize_nested_field(cls, username: str, field_path: str, db, field_config: Dict[str, Any]) -> bool:
        max_retries = 3
        for attempt in range(max_retries):
            try:
                parts = field_path.split(".")
                if len(parts) <= 1:
                    return True
                parent_path = ".".join(parts[:-1])
                update = {parent_path: {}}
                for i in range(len(parts) - 1):
                    current_path = ".".join(parts[:i + 1])
                    update[current_path] = update.get(current_path, {})
                field_spec = field_config.get("fields", {}).get(field_path)
                if field_spec and "default" in field_spec:
                    update[field_path] = field_spec["default"]
                result = await db.users.update_one(
                    {"id": username, parent_path: {"$exists": False}},
                    {"$set": update}
                )
                if result.modified_count > 0:
                    logger.info(f"初始化用户 {username} 的嵌套字段 {parent_path} 成功")
                return True
            except Exception as e:
                logger.error(f"初始化嵌套字段 {field_path} 失败，用户 {username}，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                else:
                    return False

    @classmethod
    async def update_field(cls, username: str, updates: Dict[str, Any], db, redis_client, field_config: Dict[str, Any], caller: str = "unknown") -> Tuple[bool, str]:
        try:
            if not updates or not isinstance(updates, dict):
                return False, "更新数据为空或格式错误"
            set_updates = {}
            for field_path, value in updates.items():
                is_valid, error_msg = await cls.validate_field(field_path, value, field_config)
                if not is_valid:
                    return False, error_msg
                if "." in field_path:
                    if not await cls.initialize_nested_field(username, field_path, db, field_config):
                        return False, f"初始化嵌套字段 {field_path} 失败"
                set_updates[field_path] = value
            result = await db.users.update_one(
                {"id": username},
                {"$set": set_updates}
            )
            if result.matched_count == 0:
                return False, f"用户 {username} 不存在"
            if result.modified_count == 0:
                return True, "字段未发生变化"
            user = await db.users.find_one({"id": username})
            if user:
                await cache_user_data(redis_client, username, user)
                logger.info(f"[{caller}] 批量更新用户 {username} 的字段 {list(updates.keys())} 成功")
                return True, ""
            return False, "更新缓存失败"
        except Exception as e:
            logger.error(f"更新用户字段失败，用户: {username}, 错误: {str(e)}")
            return False, f"服务器错误: {str(e)}"

class Item(BaseModelORM):
    defid: int
    attributes: Dict[str, Any] = {}
    id: Optional[str] = None
    owner: Optional[str] = None
    type: Optional[str] = None
    quantity: Optional[int] = None
    created_at: Optional[datetime] = None

    async def save(self, db, redis_client) -> 'Item':
        try:
            if not self.id:
                self.id = str(uuid.uuid4())
            if self.type == "item":
                existing_item = await db.items.find_one(
                    {"defid": self.defid, "owner": self.owner, "type": "item"}
                )
                if existing_item:
                    new_quantity = existing_item.get("quantity", 1) + (self.quantity or 1)
                    update_result = await db.items.update_one(
                        {"id": existing_item["id"]},
                        {"$set": {"quantity": new_quantity, "updated_at": datetime.now()}}
                    )
                    if update_result.modified_count > 0:
                        self.id = existing_item["id"]
                        self.quantity = new_quantity
                        await self._sync_to_redis(db, redis_client)
                        await self._sync_user_items(db, redis_client)
                        return self
            self.quantity = self.quantity or 1 if self.type == "item" else None
            self.created_at = self.created_at or datetime.now()
            data = self.serialize()
            data["updated_at"] = datetime.now()
            result = await db.items.insert_one(data)
            self._id = str(result.inserted_id)
            await self._sync_to_redis(db, redis_client)
            await self._sync_user_items(db, redis_client)
            return self
        except Exception as e:
            raise

    async def _sync_to_redis(self, db, redis_client):
        """同步物品数据到Redis缓存"""
        try:
            if redis_client is None:
                return
                
            # 获取Redis缓存键
            cache_keys = config.get_cache_keys()
            items_key = cache_keys.get("user_items", f"game:v2:users:{self.owner}:items")
            if "{username}" in items_key:
                items_key = items_key.format(username=self.owner)
            
            # 序列化物品数据
            item_data = self.serialize()
            
            # 更新Redis缓存
            try:
                # 检查是否存在物品列表缓存
                if await redis_client.exists(items_key):
                    # 获取当前物品列表
                    items_data = await redis_client.get(items_key)
                    if items_data:
                        items = json.loads(items_data.decode('utf-8'))
                        
                        # 更新或添加物品
                        found = False
                        for i, item in enumerate(items):
                            if item.get("id") == self.id:
                                items[i] = item_data
                                found = True
                                break
                        
                        if not found:
                            items.append(item_data)
                        
                        # 更新缓存
                        await redis_client.set(items_key, json.dumps(items), ex=3600)
                else:
                    # 如果不存在缓存，创建新的
                    await redis_client.set(items_key, json.dumps([item_data]), ex=3600)
            except redis_exceptions.RedisError as e:
                logger.warning(f"同步物品到Redis缓存失败: {str(e)}")
        except Exception as e:
            logger.error(f"同步物品数据到Redis失败: {str(e)}")

    async def _sync_user_items(self, db, redis_client):
        try:
            user = await db.users.find_one({"id": self.owner})
            if user and self.id not in user.get("items", []):
                user["items"] = user.get("items", []) + [self.id]
                await db.users.update_one(
                    {"id": self.owner},
                    {"$set": {"items": user["items"]}}
                )
                await cache_user_data(redis_client, self.owner, user)
        except Exception as e:
            logger.warning(f"同步用户道具到 Redis 失败，用户: {self.owner}, 错误: {str(e)}")

    @classmethod
    async def find_by_owner(cls, owner: str, db, redis_client, skip: int = 0, limit: int = 100) -> List[dict]:
        try:
            cache_keys = config.get_cache_keys()
            user_cache = await redis_client.get(cache_keys["user_data"].format(username=owner))
            items = []
            if user_cache:
                user = json.loads(user_cache)
                item_ids = user.get("items", [])[skip:skip + limit]
                if item_ids:
                    item_keys = [cache_keys["item_data"].format(owner=owner, item_id=item_id) for item_id in item_ids]
                    async with redis_client.pipeline() as pipe:
                        for key in item_keys:
                            pipe.get(key)
                        item_cached = await pipe.execute()
                    for cached_data in item_cached:
                        if cached_data:
                            items.append(json.loads(cached_data))
                    if len(items) == len(item_ids):
                        return items

            user = await db.users.find_one({"id": owner})
            if not user:
                raise handle_error(f"用户 {owner} 不存在")
            items_cursor = db.items.find({"owner": owner}).skip(skip).limit(limit)
            items_list = await items_cursor.to_list(length=limit)
            result = []
            async with redis_client.pipeline() as pipe:
                for item_dict in items_list:
                    item_dict["_id"] = str(item_dict["_id"])
                    if "created_at" in item_dict and isinstance(item_dict["created_at"], datetime):
                        item_dict["created_at"] = item_dict["created_at"].isoformat()
                    if "updated_at" in item_dict and isinstance(item_dict["updated_at"], datetime):
                        item_dict["updated_at"] = item_dict["updated_at"].isoformat()
                    result.append(item_dict)
                    pipe.set(
                        cache_keys["item_data"].format(owner=owner, item_id=item_dict["id"]),
                        json.dumps(item_dict, cls=CustomJSONEncoder),
                        ex=86400
                    )
                await pipe.execute()
            return result
        except Exception as e:
            logger.error(f"查询用户 {owner} 的道具失败: {str(e)}")
            raise

    @classmethod
    async def delete(cls, owner: str, item_id: str, db, redis_client) -> bool:
        try:
            result = await db.items.delete_one({"id": item_id, "owner": owner})
            if result.deleted_count == 0:
                return False
            user = await db.users.find_one({"id": owner})
            if user:
                user["items"] = [i for i in user.get("items", []) if i != item_id]
                await db.users.update_one(
                    {"id": owner},
                    {"$set": {"items": user["items"]}}
                )
                cache_keys = config.get_cache_keys()
                async with redis_client.pipeline() as pipe:
                    pipe.delete(cache_keys["item_data"].format(owner=owner, item_id=item_id))
                    pipe.set(
                        cache_keys["user_data"].format(username=owner),
                        json.dumps(user, cls=CustomJSONEncoder),
                        ex=3600
                    )
                    await pipe.execute()
            return True
        except Exception as e:
            raise

    @classmethod
    async def modify_quantity(cls, owner: str, defid: int, amount: int, db, redis_client, increase: bool = True) -> Tuple[Optional['Item'], bool]:
        try:
            if amount <= 0:
                raise handle_error("数量变化必须大于 0")
            existing_item = await db.items.find_one({"defid": defid, "owner": owner, "type": "item"})
            if not existing_item:
                if not increase:
                    return None, False
                instance = cls(owner=owner, defid=defid, type="item", quantity=amount)
                saved_item = await instance.save(db, redis_client)
                return saved_item, True
            current_quantity = existing_item.get("quantity", 1)
            new_quantity = current_quantity + amount if increase else current_quantity - amount
            if new_quantity <= 0:
                success = await cls.delete(owner, existing_item["id"], db, redis_client)
                return None, success
            update_result = await db.items.update_one(
                {"id": existing_item["id"]},
                {"$set": {"quantity": new_quantity, "updated_at": datetime.now()}}
            )
            if update_result.modified_count > 0:
                instance = cls.deserialize(existing_item)
                instance.quantity = new_quantity
                await instance._sync_to_redis(db, redis_client)
                return instance, True
            return cls.deserialize(existing_item), True
        except Exception as e:
            raise

    @classmethod
    async def increase_quantity(cls, owner: str, defid: int, amount: int, db, redis_client) -> Optional['Item']:
        item, success = await cls.modify_quantity(owner, defid, amount, db, redis_client, increase=True)
        return item if success else None

    @classmethod
    async def decrease_quantity(cls, owner: str, defid: int, amount: int, db, redis_client) -> bool:
        item, success = await cls.modify_quantity(owner, defid, amount, db, redis_client, increase=False)
        return success

class DatabaseManager:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            logger.debug("创建DatabaseManager单例实例")
            cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not DatabaseManager._initialized:
            logger.debug("初始化DatabaseManager单例")
            self.redis_client = None
            self.client = None
            self.db = None
            self._closed = False
            self.USERS_COLLECTION = "users"
            DatabaseManager._initialized = True
        else:
            logger.debug("复用现有的DatabaseManager单例")

    async def connect_with_retry(self, client_class, connection_args, max_retries=5, delay=2):
        retries = 0
        while retries < max_retries:
            try:
                if client_class == redis.Redis:
                    client = redis.Redis(
                        connection_pool=self.redis_pool,
                        socket_timeout=connection_args["socket_timeout"],
                        socket_connect_timeout=connection_args["socket_connect_timeout"],
                        retry_on_timeout=True
                    )
                    await client.ping()
                else:
                    client = client_class(**connection_args)
                    await client.admin.command('ping')
                return client
            except Exception as e:
                retries += 1
                logger.error(f"连接失败，重试 {retries}/{max_retries}: {str(e)}")
                if retries == max_retries:
                    raise Exception(f"无法连接数据库: {str(e)}")
                await asyncio.sleep(delay * (2 ** retries))

    async def setup_indexes(self):
        try:
            await self.db.users.create_index([("id", 1)], unique=True)
            await self.db.items.create_index([("owner", 1)])
            await self.db.items.create_index([("defid", 1), ("owner", 1), ("type", 1)])
            await self.db.items.create_index([("id", 1)], unique=True)
        except Exception as e:
            logger.warning(f"创建索引失败: {str(e)}")

    async def startup(self):
        """初始化数据库连接"""
        worker_id = os.getpid()
        logger.info(f"Worker {worker_id}: 开始初始化数据库连接 (DatabaseManager id: {id(self)})")
        
        retry_count = 0
        max_retries = 3
        retry_delay = 2  # 秒
        
        while retry_count < max_retries:
            try:
                # 获取数据库配置
                mongo_config = config.get_mongodb_config()
                redis_config = config.get_redis_config()
                
                # 初始化Redis连接
                if self.redis_client is None:
                    try:
                        redis_host = redis_config['host']
                        redis_port = redis_config['port']
                        redis_password = redis_config.get('password')
                        redis_db = redis_config.get('db', 0)
                        logger.info(f"Worker {worker_id}: 正在连接到Redis: {redis_host}:{redis_port}")
                        self.redis_client = redis.Redis(
                            host=redis_host,
                            port=redis_port,
                            password=redis_password,
                            db=redis_db,
                            encoding="utf-8",
                            decode_responses=False,
                            socket_timeout=3,
                            socket_connect_timeout=3,
                            socket_keepalive=True,
                            health_check_interval=15,
                            max_connections=300
                        )
                        # 验证连接可用性
                        await self.redis_client.ping()
                        logger.info(f"Worker {worker_id}: Redis连接成功: {redis_host}:{redis_port}")
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Redis连接失败: {str(e)}")
                        # 回退到本地Redis
                        if redis_host != 'localhost' and redis_host != '127.0.0.1':
                            try:
                                logger.warning(f"Worker {worker_id}: 尝试连接本地Redis")
                                self.redis_client = redis.Redis(
                                    host='localhost',
                                    port=6379,
                                    encoding="utf-8",
                                    decode_responses=False,
                                    socket_timeout=3,
                                    socket_connect_timeout=3,
                                    socket_keepalive=True,
                                    health_check_interval=15,
                                    max_connections=300
                                )
                                await self.redis_client.ping()
                                logger.info(f"Worker {worker_id}: 成功连接到本地Redis")
                            except Exception as local_e:
                                logger.error(f"Worker {worker_id}: 连接本地Redis也失败了: {str(local_e)}")
                                self.redis_client = None

                # 初始化MongoDB连接
                if self.db is None:
                    try:
                        mongo_uri = f"mongodb://{mongo_config['host']}:{mongo_config['port']}"
                        
                        # 如果配置了用户名和密码，添加认证信息
                        if mongo_config.get('username') and mongo_config.get('password'):
                            mongo_uri = f"mongodb://{mongo_config['username']}:{mongo_config['password']}@{mongo_config['host']}:{mongo_config['port']}"
                        
                        logger.info(f"Worker {worker_id}: 正在连接到MongoDB: {mongo_config['host']}:{mongo_config['port']}")
                        self.client = motor.motor_asyncio.AsyncIOMotorClient(
                            mongo_uri,
                            serverSelectionTimeoutMS=3000,  # 降低服务器选择超时
                            connectTimeoutMS=3000,  # 降低连接超时
                            socketTimeoutMS=3000,  # 降低socket超时
                            maxPoolSize=50,  # 增加连接池大小
                            minPoolSize=10,  # 设置最小池大小，保持连接温暖
                            maxIdleTimeMS=60000,  # 设置最大空闲时间
                            waitQueueTimeoutMS=3000,  # 等待队列超时
                            retryWrites=True  # 启用写入重试
                        )
                        # 验证连接可用性
                        await self.client.server_info()
                        self.db = self.client[mongo_config['database']]
                        logger.info(f"Worker {worker_id}: MongoDB连接成功: {mongo_config['host']}:{mongo_config['port']}")
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: MongoDB连接失败: {str(e)}")
                        # 回退到本地MongoDB
                        if mongo_config['host'] != 'localhost' and mongo_config['host'] != '127.0.0.1':
                            try:
                                logger.warning(f"Worker {worker_id}: 尝试连接本地MongoDB")
                                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                                    "mongodb://localhost:27017",
                                    serverSelectionTimeoutMS=3000,
                                    connectTimeoutMS=3000,
                                    socketTimeoutMS=3000,
                                    maxPoolSize=50,
                                    minPoolSize=10,
                                    maxIdleTimeMS=60000,
                                    waitQueueTimeoutMS=3000,
                                    retryWrites=True
                                )
                                await self.client.server_info()
                                self.db = self.client["gamedb"]
                                logger.info(f"Worker {worker_id}: 成功连接到本地MongoDB")
                            except Exception as local_e:
                                logger.error(f"Worker {worker_id}: 连接本地MongoDB也失败了: {str(local_e)}")
                                self.db = None
                
                # 检查所有必需的连接是否都已建立
                if self.db is not None:
                    if self.redis_client is not None:
                        logger.info(f"Worker {worker_id}: 数据库初始化完成，所有连接正常")
                    else:
                        logger.warning(f"Worker {worker_id}: 数据库部分初始化，Redis连接不可用")
                    return
                
                # 如果到达这里，说明有连接未建立，需要重试
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Worker {worker_id}: 将在 {retry_delay} 秒后重试连接数据库 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error(f"Worker {worker_id}: 数据库连接重试 {max_retries} 次后仍失败")
                    
            except Exception as e:
                logger.error(f"Worker {worker_id}: 数据库初始化出现未处理异常: {str(e)}")
                logger.error(traceback.format_exc())
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Worker {worker_id}: 将在 {retry_delay} 秒后重试连接数据库 (尝试 {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    logger.error(f"Worker {worker_id}: 数据库连接重试 {max_retries} 次后仍失败")
                    break

    async def shutdown(self):
        """关闭数据库连接"""
        worker_id = os.getpid()
        
        if self._closed:
            logger.debug(f"Worker {worker_id}: DatabaseManager已关闭，无需再次关闭")
            return
        
        try:
            if self.redis_client is not None:
                try:
                    logger.info(f"Worker {worker_id}: 关闭Redis连接...")
                    await self.redis_client.close()
                    logger.info(f"Worker {worker_id}: Redis连接已关闭")
                except Exception as e:
                    logger.error(f"Worker {worker_id}: 关闭Redis连接失败: {str(e)}")

            if self.client is not None:
                try:
                    logger.info(f"Worker {worker_id}: 关闭MongoDB连接...")
                    self.client.close()
                    logger.info(f"Worker {worker_id}: MongoDB连接已关闭")
                except Exception as e:
                    logger.error(f"Worker {worker_id}: 关闭MongoDB连接失败: {str(e)}")
            
            # 标记为已关闭
            self._closed = True
            logger.info(f"Worker {worker_id}: 所有数据库连接已关闭")
        except Exception as e:
            logger.error(f"Worker {worker_id}: 数据库关闭过程中发生异常: {str(e)}")
            logger.error(traceback.format_exc())
    # 初始化用户 保证只在注册的时候调用
    async def save_user(self, user: UserData):
        try:
            if self.db is None:
                logger.error("MongoDB连接未初始化，无法保存用户数据")
                raise Exception("数据库连接未初始化")
                
            data = user.serialize()

            # 检查用户是否已存在
            existing_user = await self.db.users.find_one({"id": user.id})
            if existing_user:
                logger.warning(f"用户 {user.id} 已存在，更新用户数据")
                result = await self.db.users.update_one(
                    {"id": user.id},
                    {"$set": data}
                )
                user._id = str(existing_user.get("_id"))
            else:
                # 创建新用户
                result = await self.db.users.insert_one(data)
                user._id = str(result.inserted_id)
                
            # 更新Redis缓存
            if self.redis_client is not None:
                await cache_user_data(self.redis_client, user.id, data)
                
            # 初始化用户
            await self.add_items_to_user(user.id, [{"defid": 82001, "quantity": 1, "attributes": {}},
                                                   {"defid": 82002, "quantity": 1, "attributes": {}},
                                                   {"defid": 82003, "quantity": 1, "attributes": {}},
                                                   {"defid": 82004, "quantity": 1, "attributes": {}},
                                                   {"defid": 82005, "quantity": 1, "attributes": {}}])
            logger.info(f"用户 {user.id} 数据保存成功")
            return user
        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise Exception(f"保存用户数据失败: {str(e)}")

    async def get_user_by_username(self, username,mongo_only=False):
        worker_id = os.getpid()
        try:
            if self.db is None:
                logger.error(f"Worker {worker_id}: MongoDB连接未初始化，无法获取用户 {username} 数据")
                return None
            
            # 先尝试从Redis获取
            if self.redis_client is not None and not mongo_only:
                try:
                    cache_keys = config.get_cache_keys()
                    user_key = cache_keys["user_data"].format(username=username)
                    cached_user = await self.redis_client.get(user_key)
                    
                    if cached_user:
                        try:
                            user_data = json.loads(cached_user.decode('utf-8'))
                            logger.debug(f"Worker {worker_id}: 从Redis缓存获取用户: {username}")
                            return UserData(**user_data)
                        except Exception as e:
                            logger.warning(f"Worker {worker_id}: 解析Redis缓存的用户数据失败，用户: {username}，错误: {str(e)}")
                except Exception as redis_err:
                    logger.warning(f"Worker {worker_id}: 从Redis获取用户缓存失败: {str(redis_err)}")
            else:
                logger.debug(f"Worker {worker_id}: Redis客户端未初始化，跳过缓存查询，用户: {username}")
            
            # 从MongoDB获取
            try:
                collection = self.db[self.USERS_COLLECTION]
                user_doc = await collection.find_one({"id": username})
                
                if user_doc:
                    logger.debug(f"Worker {worker_id}: 从MongoDB获取用户: {username}")
                    user = UserData(**user_doc)
                    # 更新缓存
                    if self.redis_client is not None:
                        await cache_user_data(self.redis_client, username, user_doc)
                    return user
                else:
                    logger.warning(f"Worker {worker_id}: 用户不存在: {username}")
                    return None
            except Exception as mongo_err:
                logger.error(f"Worker {worker_id}: 从MongoDB获取用户数据失败，用户: {username}，错误: {str(mongo_err)}")
                return None
        except Exception as e:
            logger.error(f"Worker {worker_id}: 获取用户数据失败，用户: {username}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    async def update_user_fields(self, username: str, field_path: Optional[str] = None, value: Optional[Any] = None, updates: Optional[Dict[str, Any]] = None, caller: str = "unknown") -> Tuple[bool, str]:
        try:
            if field_path and updates:
                return False, "不能同时提供 field_path 和 updates"
            if field_path:
                if value is None:
                    return False, "单一字段更新需要提供值"
                updates = {field_path: value}
            elif updates:
                if not isinstance(updates, dict) or not updates:
                    return False, "更新数据为空或格式错误"
            else:
                return False, "必须提供字段路径或更新数据"
            return await UserData.update_field(username, updates, self.db, self.redis_client, {}, caller)
        except Exception as e:
            logger.error(f"更新用户字段失败，用户: {username}, 错误: {str(e)}")
            return False, f"服务器错误: {str(e)}"
            
    async def add_items_to_user(self, user_id: str, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量向用户添加物品，高效实现
        
        Args:
            user_id (str): 用户ID
            items (List[Dict]): 物品列表，每个物品是一个字典，格式为:
                {
                    "defid": int,       # 物品定义ID
                    "quantity": int,     # 数量
                    "attributes": dict,  # 属性（可选）
                }
                
        Returns:
            Dict: 包含操作结果的字典
        """
        if not items:
            return {"success": True, "message": "没有物品需要添加", "added_items": []}
        
        worker_id = os.getpid()
        logger.info(f"Worker {worker_id}: 开始批量添加物品，用户: {user_id}，物品数量: {len(items)}")
        
        # 合并相同defid的物品，提高效率
        merged_items = {}
        for item in items:
            defid = item.get("defid")
            if not defid:
                continue
                
            quantity = item.get("quantity", 1)
            attributes = item.get("attributes", {})
            
            if defid in merged_items:
                merged_items[defid]["quantity"] += quantity
                # 合并属性（如果有新属性则添加/覆盖）
                if attributes:
                    merged_items[defid]["attributes"].update(attributes)
            else:
                merged_items[defid] = {
                    "defid": defid,
                    "quantity": quantity,
                    "attributes": attributes.copy() if attributes else {}
                }
        
        # 转换回列表
        items_to_add = list(merged_items.values())
        
        # 批量操作结果
        result_items = []
        failed_items = []
        
        try:
            # 使用事务或批量操作
            async with await self.db.client.start_session() as session:
                async with session.start_transaction():
                    # 批量获取现有物品
                    existing_items = {}
                    if self.db is not None:
                        cursor = self.db.items.find(
                            {"owner": user_id, "type": "item", "defid": {"$in": list(merged_items.keys())}}
                        )
                        async for item in cursor:
                            existing_items[item["defid"]] = item
                    
                    # 批量更新或插入
                    bulk_operations = []
                    new_items = []
                    
                    for item_data in items_to_add:
                        defid = item_data["defid"]
                        quantity = item_data["quantity"]
                        attributes = item_data["attributes"]
                        
                        if defid in existing_items:
                            # 更新现有物品数量
                            existing_item = existing_items[defid]
                            new_quantity = existing_item.get("quantity", 0) + quantity
                            bulk_operations.append(
                                pymongo.UpdateOne(
                                    {"id": existing_item["id"]},
                                    {"$set": {
                                        "quantity": new_quantity,
                                        "updated_at": datetime.now()
                                    }, "$addToSet": {"attributes": {"$each": list(attributes.items())}}}
                                )
                            )
                            
                            # 记录结果
                            result_item = {
                                "id": existing_item["id"],
                                "defid": defid,
                                "quantity": new_quantity,
                                "is_new": False
                            }
                            result_items.append(result_item)
                        else:
                            # 创建新物品
                            item_id = str(uuid.uuid4())
                            now = datetime.now()
                            new_item = {
                                "id": item_id,
                                "defid": defid,
                                "owner": user_id,
                                "type": "item",
                                "quantity": quantity,
                                "attributes": attributes,
                                "created_at": now,
                                "updated_at": now
                            }
                            new_items.append(new_item)
                            
                            # 记录结果
                            result_item = {
                                "id": item_id,
                                "defid": defid,
                                "quantity": quantity,
                                "is_new": True
                            }
                            result_items.append(result_item)
                    
                    # 执行批量操作
                    if bulk_operations:
                        await self.db.items.bulk_write(bulk_operations, ordered=False)
                    
                    # 批量插入新物品
                    if new_items:
                        await self.db.items.insert_many(new_items)
            
            # 更新Redis缓存（在事务外执行，因为Redis不支持事务回滚）
            if self.redis_client is not None:
                # 获取缓存键
                cache_keys = config.get_cache_keys()
                user_items_key = cache_keys.get("user_items", f"game:v2:users:{user_id}:items")
                if "{username}" in user_items_key:
                    user_items_key = user_items_key.format(username=user_id)
                
                # 获取当前缓存
                cached_items = []
                cached_data = await self.redis_client.get(user_items_key)
                if cached_data:
                    try:
                        cached_items = json.loads(cached_data.decode('utf-8'))
                        if not isinstance(cached_items, list):
                            cached_items = []
                    except json.JSONDecodeError:
                        cached_items = []
                
                # 更新缓存中的物品
                for result_item in result_items:
                    defid = result_item["defid"]
                    item_id = result_item["id"]
                    quantity = result_item["quantity"]
                    is_new = result_item["is_new"]
                    
                    if is_new:
                        # 添加新物品到缓存
                        new_item = {
                            "id": item_id,
                            "defid": defid,
                            "owner": user_id,
                            "type": "item",
                            "quantity": quantity
                        }
                        cached_items.append(new_item)
                    else:
                        # 更新现有物品
                        for i, item in enumerate(cached_items):
                            if item.get("id") == item_id:
                                cached_items[i]["quantity"] = quantity
                                break
                
                # 更新Redis缓存
                await self.redis_client.set(user_items_key, json.dumps(cached_items), ex=3600)
            
            logger.info(f"Worker {worker_id}: 批量添加物品成功，用户: {user_id}，添加物品: {len(result_items)}，失败: {len(failed_items)}")
            return {
                "success": True,
                "message": f"成功添加 {len(result_items)} 个物品",
                "added_items": result_items,
                "failed_items": failed_items
            }
        
        except Exception as e:
            logger.error(f"Worker {worker_id}: 批量添加物品失败，用户: {user_id}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"批量添加物品失败: {str(e)}",
                "added_items": result_items,
                "failed_items": failed_items
            }

    async def add_equipment_to_user(self, user_id: str, equip_def_id: int, attributes: Optional[Dict[str, Any]] = None, level: int = 1) -> Dict[str, Any]:
        """向用户添加一件装备，高效实现
        
        Args:
            user_id (str): 用户ID
            equip_def_id (int): 装备定义ID
            attributes (Dict): 装备属性
            level (int): 装备等级
            
        Returns:
            Dict: 包含操作结果的字典，包括装备ID和状态
        """
        worker_id = os.getpid()
        logger.info(f"Worker {worker_id}: 添加装备，用户: {user_id}，装备ID: {equip_def_id}")
        
        try:
            # 生成唯一装备ID
            equip_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 准备装备数据
            equipment = {
                "id": equip_id,
                "defid": equip_def_id,
                "owner": user_id,
                "type": "equipment",  # 标记为装备类型
                "level": level,
                "attributes": attributes or {},
                "created_at": now,
                "updated_at": now
            }
            
            # 插入到数据库
            if self.db is not None:
                await self.db.items.insert_one(equipment)
                
                # 更新用户的装备列表
                await self.db.users.update_one(
                    {"id": user_id},
                    {"$push": {"items": equip_id}}
                )
            
            # 更新Redis缓存
            if self.redis_client is not None:
                # 获取缓存键
                cache_keys = config.get_cache_keys()
                user_items_key = cache_keys.get("user_items", f"game:v2:users:{user_id}:items")
                if "{username}" in user_items_key:
                    user_items_key = user_items_key.format(username=user_id)
                
                item_key = cache_keys.get("item_data", f"game:v2:items:{user_id}:{equip_id}")
                if "{owner}" in item_key and "{item_id}" in item_key:
                    item_key = item_key.format(owner=user_id, item_id=equip_id)
                
                # 使用pipeline批量更新缓存
                try:
                    async with self.redis_client.pipeline() as pipe:
                        # 缓存装备数据
                        pipe.set(
                            item_key, 
                            json.dumps(equipment, cls=CustomJSONEncoder),
                            ex=86400  # 缓存24小时
                        )
                        
                        # 更新用户装备列表缓存
                        cached_items = await self.redis_client.get(user_items_key)
                        if cached_items:
                            try:
                                items = json.loads(cached_items.decode('utf-8'))
                                if isinstance(items, list):
                                    items.append(equipment)
                                    pipe.set(user_items_key, json.dumps(items, cls=CustomJSONEncoder), ex=3600)
                            except json.JSONDecodeError:
                                # 如果缓存数据无效，设置新缓存
                                pipe.set(user_items_key, json.dumps([equipment], cls=CustomJSONEncoder), ex=3600)
                        else:
                            # 如果没有缓存，创建新缓存
                            pipe.set(user_items_key, json.dumps([equipment], cls=CustomJSONEncoder), ex=3600)
                        
                        # 执行所有Redis操作
                        await pipe.execute()
                except redis_exceptions.RedisError as e:
                    logger.warning(f"Redis缓存装备失败: {str(e)}，装备ID: {equip_id}")
            
            logger.info(f"Worker {worker_id}: 成功添加装备，用户: {user_id}，装备ID: {equip_id}")
            return {
                "success": True,
                "equipment_id": equip_id,
                "defid": equip_def_id,
                "level": level
            }
            
        except Exception as e:
            logger.error(f"Worker {worker_id}: 添加装备失败，用户: {user_id}，装备ID: {equip_def_id}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"添加装备失败: {str(e)}"
            }

    async def add_equipments_to_user(self, user_id: str, equipments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量向用户添加装备，高效实现
        
        Args:
            user_id (str): 用户ID
            equipments (List[Dict]): 装备列表，每个装备是一个字典，格式为:
                {
                    "defid": int,       # 装备定义ID
                    "level": int,       # 装备等级 (可选，默认为1)
                    "attributes": dict,  # 属性（可选）
                }
                
        Returns:
            Dict: 包含操作结果的字典
        """
        if not equipments:
            return {"success": True, "message": "没有装备需要添加", "added_equipments": []}
        
        worker_id = os.getpid()
        logger.info(f"Worker {worker_id}: 开始批量添加装备，用户: {user_id}，装备数量: {len(equipments)}")
        
        # 处理结果
        result_equipments = []
        failed_equipments = []
        
        try:
            now = datetime.now()
            equipment_docs = []
            equipment_ids = []
            
            # 准备所有装备数据
            for equip_data in equipments:
                try:
                    defid = equip_data.get("defid")
                    if not defid:
                        failed_equipments.append({"defid": defid, "error": "缺少装备定义ID"})
                        continue
                        
                    equip_id = str(uuid.uuid4())
                    level = equip_data.get("level", 1)
                    attributes = equip_data.get("attributes", {})
                    
                    equipment = {
                        "id": equip_id,
                        "defid": defid,
                        "owner": user_id,
                        "type": "equipment",
                        "level": level,
                        "attributes": attributes,
                        "created_at": now,
                        "updated_at": now
                    }
                    
                    equipment_docs.append(equipment)
                    equipment_ids.append(equip_id)
                    result_equipments.append({
                        "id": equip_id,
                        "defid": defid,
                        "level": level
                    })
                except Exception as e:
                    failed_equipments.append({
                        "defid": equip_data.get("defid", "未知"),
                        "error": str(e)
                    })
            
            # 如果没有有效装备，直接返回
            if not equipment_docs:
                return {
                    "success": True,
                    "message": "没有添加有效装备",
                    "added_equipments": [],
                    "failed_equipments": failed_equipments
                }
            
            # 使用MongoDB事务批量插入装备并更新用户
            if self.db is not None:
                async with await self.db.client.start_session() as session:
                    async with session.start_transaction():
                        # 批量插入装备
                        await self.db.items.insert_many(equipment_docs, session=session)
                        
                        # 更新用户的装备列表
                        await self.db.users.update_one(
                            {"id": user_id},
                            {"$push": {"items": {"$each": equipment_ids}}},
                            session=session
                        )
                        
                        # 获取更新后的用户数据，用于更新Redis缓存
                        user = await self.db.users.find_one({"id": user_id}, session=session)
            
            # 更新Redis缓存
            if self.redis_client is not None and user:
                try:
                    cache_keys = config.get_cache_keys()
                    user_data_key = cache_keys.get("user_data", f"game:v2:users:{user_id}")
                    if "{username}" in user_data_key:
                        user_data_key = user_data_key.format(username=user_id)
                    
                    user_items_key = cache_keys.get("user_items", f"game:v2:users:{user_id}:items")
                    if "{username}" in user_items_key:
                        user_items_key = user_items_key.format(username=user_id)
                    
                    async with self.redis_client.pipeline() as pipe:
                        # 更新用户数据缓存
                        user_json = json.dumps(user, cls=CustomJSONEncoder)
                        pipe.set(user_data_key, user_json, ex=3600)
                        
                        # 更新装备列表缓存
                        cached_items = await self.redis_client.get(user_items_key)
                        items_list = []
                        
                        if cached_items:
                            try:
                                items_list = json.loads(cached_items.decode('utf-8'))
                                if not isinstance(items_list, list):
                                    items_list = []
                            except json.JSONDecodeError:
                                items_list = []
                        
                        # 添加新装备到列表
                        items_list.extend(equipment_docs)
                        pipe.set(user_items_key, json.dumps(items_list, cls=CustomJSONEncoder), ex=3600)
                        
                        # 单独缓存每件装备
                        for equipment in equipment_docs:
                            item_key = cache_keys.get("item_data", f"game:v2:items:{user_id}:{equipment['id']}")
                            if "{owner}" in item_key and "{item_id}" in item_key:
                                item_key = item_key.format(owner=user_id, item_id=equipment['id'])
                            
                            pipe.set(item_key, json.dumps(equipment, cls=CustomJSONEncoder), ex=86400)
                        
                        # 执行所有Redis操作
                        await pipe.execute()
                except redis_exceptions.RedisError as e:
                    logger.warning(f"Redis缓存批量装备失败: {str(e)}")
            
            logger.info(f"Worker {worker_id}: 批量添加装备成功，用户: {user_id}，添加装备: {len(result_equipments)}，失败: {len(failed_equipments)}")
            return {
                "success": True,
                "message": f"成功添加 {len(result_equipments)} 件装备",
                "added_equipments": result_equipments,
                "failed_equipments": failed_equipments
            }
        
        except Exception as e:
            logger.error(f"Worker {worker_id}: 批量添加装备失败，用户: {user_id}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"批量添加装备失败: {str(e)}",
                "added_equipments": result_equipments,
                "failed_equipments": failed_equipments
            }

    async def data_access(self, 
                        operation: str,
                        collection: str,
                        query: Dict = None,
                        data: Dict = None,
                        use_cache: bool = True,
                        cache_key_pattern: str = None,
                        cache_key_args: Dict = None,
                        cache_ttl: int = 3600,
                        skip: int = 0,
                        limit: int = 100,
                        sort_by: str = None,
                        sort_direction: int = -1,
                        filter_func = None) -> Dict[str, Any]:
        """通用数据访问层，处理MongoDB和Redis缓存
        
        Args:
            operation (str): 操作类型，如'find', 'find_one', 'insert', 'update', 'delete', 'count'
            collection (str): MongoDB集合名称
            query (Dict): 查询条件
            data (Dict): 要插入或更新的数据
            use_cache (bool): 是否使用缓存
            cache_key_pattern (str): 缓存键模式，如'game:v2:users:{username}'
            cache_key_args (Dict): 缓存键参数，如{'username': 'user123'}
            cache_ttl (int): 缓存过期时间（秒）
            skip (int): 分页跳过数量
            limit (int): 分页限制数量
            sort_by (str): 排序字段
            sort_direction (int): 排序方向，1为升序，-1为降序
            filter_func (callable): 后处理过滤函数
            
        Returns:
            Dict: 包含操作结果的字典
        """
        worker_id = os.getpid()
        cache_key = None
        
        try:
            # 构建缓存键
            if use_cache and self.redis_client is not None and cache_key_pattern and cache_key_args:
                try:
                    cache_key = cache_key_pattern
                    for k, v in cache_key_args.items():
                        cache_key = cache_key.replace(f"{{{k}}}", str(v))
                except Exception as e:
                    logger.warning(f"Worker {worker_id}: 构建缓存键失败: {str(e)}")
                    cache_key = None
            
            # 根据操作类型执行不同的逻辑
            if operation == 'find':
                # 查询多条记录
                
                # 1. 尝试从缓存获取
                if use_cache and cache_key and self.redis_client is not None:
                    try:
                        cached_data = await self.redis_client.get(cache_key)
                        if cached_data:
                            try:
                                results = json.loads(cached_data.decode('utf-8'))
                                if isinstance(results, list):
                                    # 应用过滤和分页
                                    if filter_func:
                                        results = [item for item in results if filter_func(item)]
                                    
                                    total = len(results)
                                    results = results[skip:skip+limit]
                                    
                                    return {
                                        "success": True,
                                        "from_cache": True,
                                        "total": total,
                                        "items": results,
                                        "pagination": {
                                            "skip": skip,
                                            "limit": limit,
                                            "has_more": skip + len(results) < total
                                        }
                                    }
                            except json.JSONDecodeError:
                                logger.warning(f"Worker {worker_id}: 缓存数据解析失败: {cache_key}")
                    except redis_exceptions.RedisError as e:
                        logger.warning(f"Worker {worker_id}: 从Redis获取数据失败: {str(e)}")
                
                # 2. 从MongoDB获取
                if self.db is not None:
                    try:
                        # 获取总数
                        total = await self.db[collection].count_documents(query or {})
                        
                        # 构建查询
                        cursor = self.db[collection].find(query or {})
                        
                        # 应用排序
                        if sort_by:
                            cursor = cursor.sort(sort_by, sort_direction)
                            
                        # 应用分页
                        cursor = cursor.skip(skip).limit(limit)
                        
                        # 执行查询
                        results = await cursor.to_list(length=limit)
                        
                        # 处理结果
                        processed_results = []
                        for item in results:
                            # 处理ObjectId
                            if "_id" in item:
                                item["_id"] = str(item["_id"])
                            
                            # 处理日期
                            for field in ["created_at", "updated_at"]:
                                if field in item and isinstance(item[field], datetime):
                                    item[field] = item[field].isoformat()
                            
                            processed_results.append(item)
                        
                        # 更新缓存
                        if use_cache and cache_key and self.redis_client is not None:
                            try:
                                # 这里缓存完整结果集，后续可按需修改为仅缓存当前页
                                cache_data = json.dumps(processed_results, cls=CustomJSONEncoder)
                                await self.redis_client.set(cache_key, cache_data, ex=cache_ttl)
                            except redis_exceptions.RedisError as e:
                                logger.warning(f"Worker {worker_id}: 缓存数据失败: {str(e)}")
                        
                        return {
                            "success": True,
                            "from_cache": False,
                            "total": total,
                            "items": processed_results,
                            "pagination": {
                                "skip": skip,
                                "limit": limit,
                                "has_more": skip + len(processed_results) < total
                            }
                        }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 从MongoDB获取数据失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"从数据库获取数据失败: {str(e)}",
                            "items": []
                        }
            
            elif operation == 'find_one':
                # 查询单条记录
                
                # 1. 尝试从缓存获取
                if use_cache and cache_key and self.redis_client is not None:
                    try:
                        cached_data = await self.redis_client.get(cache_key)
                        if cached_data:
                            try:
                                item = json.loads(cached_data.decode('utf-8'))
                                if isinstance(item, dict):
                                    return {
                                        "success": True,
                                        "from_cache": True,
                                        "item": item
                                    }
                            except json.JSONDecodeError:
                                logger.warning(f"Worker {worker_id}: 缓存数据解析失败: {cache_key}")
                    except redis_exceptions.RedisError as e:
                        logger.warning(f"Worker {worker_id}: 从Redis获取数据失败: {str(e)}")
                
                # 2. 从MongoDB获取
                if self.db is not None:
                    try:
                        result = await self.db[collection].find_one(query or {})
                        
                        if result:
                            # 处理ObjectId
                            if "_id" in result:
                                result["_id"] = str(result["_id"])
                            
                            # 处理日期
                            for field in ["created_at", "updated_at"]:
                                if field in result and isinstance(result[field], datetime):
                                    result[field] = result[field].isoformat()
                            
                            # 更新缓存
                            if use_cache and cache_key and self.redis_client is not None:
                                try:
                                    cache_data = json.dumps(result, cls=CustomJSONEncoder)
                                    await self.redis_client.set(cache_key, cache_data, ex=cache_ttl)
                                except redis_exceptions.RedisError as e:
                                    logger.warning(f"Worker {worker_id}: 缓存数据失败: {str(e)}")
                            
                            return {
                                "success": True,
                                "from_cache": False,
                                "item": result
                            }
                        else:
                            return {
                                "success": True,
                                "from_cache": False,
                                "item": None
                            }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 从MongoDB获取数据失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"从数据库获取数据失败: {str(e)}",
                            "item": None
                        }
            
            elif operation == 'insert':
                # 插入记录
                if not data:
                    return {
                        "success": False,
                        "message": "没有提供要插入的数据"
                    }
                
                # 确保数据有ID和时间戳
                if "id" not in data:
                    data["id"] = str(uuid.uuid4())
                
                now = datetime.now()
                if "created_at" not in data:
                    data["created_at"] = now
                if "updated_at" not in data:
                    data["updated_at"] = now
                
                # 插入MongoDB
                if self.db is not None:
                    try:
                        result = await self.db[collection].insert_one(data)
                        
                        # 更新缓存
                        if use_cache and cache_key and self.redis_client is not None:
                            try:
                                # 处理ObjectId
                                inserted_data = data.copy()
                                inserted_data["_id"] = str(result.inserted_id)
                                
                                # 缓存单条记录
                                cache_data = json.dumps(inserted_data, cls=CustomJSONEncoder)
                                await self.redis_client.set(cache_key, cache_data, ex=cache_ttl)
                                
                                # 如果有列表缓存，可能需要更新
                                if cache_key_pattern and '{id}' in cache_key_pattern:
                                    list_key = cache_key_pattern.split(':{id}')[0]
                                    try:
                                        list_data = await self.redis_client.get(list_key)
                                        if list_data:
                                            items = json.loads(list_data.decode('utf-8'))
                                            if isinstance(items, list):
                                                items.append(inserted_data)
                                                await self.redis_client.set(list_key, json.dumps(items, cls=CustomJSONEncoder), ex=cache_ttl)
                                    except Exception:
                                        pass
                            except redis_exceptions.RedisError as e:
                                logger.warning(f"Worker {worker_id}: 缓存数据失败: {str(e)}")
                        
                        return {
                            "success": True,
                            "id": data["id"],
                            "_id": str(result.inserted_id)
                        }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 向MongoDB插入数据失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"插入数据失败: {str(e)}"
                        }
            
            elif operation == 'update':
                # 更新记录
                if not query:
                    return {
                        "success": False,
                        "message": "没有提供查询条件"
                    }
                
                if not data:
                    return {
                        "success": False,
                        "message": "没有提供要更新的数据"
                    }
                
                # 添加更新时间
                update_data = data.copy()
                if "$set" in update_data:
                    update_data["$set"]["updated_at"] = datetime.now()
                else:
                    update_data = {"$set": {**update_data, "updated_at": datetime.now()}}
                
                # 更新MongoDB
                if self.db is not None:
                    try:
                        result = await self.db[collection].update_one(query, update_data)
                        
                        # 无论修改与否，都尝试更新缓存
                        if use_cache and cache_key and self.redis_client is not None:
                            try:
                                # 获取更新后的记录
                                updated_item = await self.db[collection].find_one(query)
                                
                                if updated_item:
                                    # 处理ObjectId
                                    if "_id" in updated_item:
                                        updated_item["_id"] = str(updated_item["_id"])
                                    
                                    # 处理日期
                                    for field in ["created_at", "updated_at"]:
                                        if field in updated_item and isinstance(updated_item[field], datetime):
                                            updated_item[field] = updated_item[field].isoformat()
                                    
                                    # 更新缓存
                                    cache_data = json.dumps(updated_item, cls=CustomJSONEncoder)
                                    await self.redis_client.set(cache_key, cache_data, ex=cache_ttl)
                                else:
                                    # 记录不存在，删除缓存
                                    await self.redis_client.delete(cache_key)
                            except redis_exceptions.RedisError as e:
                                logger.warning(f"Worker {worker_id}: 更新缓存失败: {str(e)}")
                        
                        return {
                            "success": True,
                            "matched_count": result.matched_count,
                            "modified_count": result.modified_count
                        }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 更新MongoDB数据失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"更新数据失败: {str(e)}"
                        }
            
            elif operation == 'delete':
                # 删除记录
                if not query:
                    return {
                        "success": False,
                        "message": "没有提供查询条件"
                    }
                
                # 删除MongoDB记录
                if self.db is not None:
                    try:
                        result = await self.db[collection].delete_one(query)
                        
                        # 删除缓存
                        if use_cache and cache_key and self.redis_client is not None:
                            try:
                                await self.redis_client.delete(cache_key)
                            except redis_exceptions.RedisError as e:
                                logger.warning(f"Worker {worker_id}: 删除缓存失败: {str(e)}")
                        
                        return {
                            "success": True,
                            "deleted_count": result.deleted_count
                        }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 删除MongoDB数据失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"删除数据失败: {str(e)}"
                        }
            
            elif operation == 'count':
                # 计数
                
                # 尝试从缓存获取
                if use_cache and cache_key and self.redis_client is not None:
                    try:
                        cached_count = await self.redis_client.get(cache_key)
                        if cached_count:
                            try:
                                count = int(cached_count.decode('utf-8'))
                                return {
                                    "success": True,
                                    "from_cache": True,
                                    "count": count
                                }
                            except (ValueError, TypeError):
                                logger.warning(f"Worker {worker_id}: 缓存计数解析失败: {cache_key}")
                    except redis_exceptions.RedisError as e:
                        logger.warning(f"Worker {worker_id}: 从Redis获取计数失败: {str(e)}")
                
                # 从MongoDB获取
                if self.db is not None:
                    try:
                        count = await self.db[collection].count_documents(query or {})
                        
                        # 更新缓存
                        if use_cache and cache_key and self.redis_client is not None:
                            try:
                                await self.redis_client.set(cache_key, str(count), ex=cache_ttl)
                            except redis_exceptions.RedisError as e:
                                logger.warning(f"Worker {worker_id}: 缓存计数失败: {str(e)}")
                        
                        return {
                            "success": True,
                            "from_cache": False,
                            "count": count
                        }
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: 从MongoDB获取计数失败，集合: {collection}，错误: {str(e)}")
                        return {
                            "success": False,
                            "message": f"获取计数失败: {str(e)}",
                            "count": 0
                        }
            
            else:
                return {
                    "success": False,
                    "message": f"不支持的操作类型: {operation}"
                }
            
            return {
                "success": False,
                "message": "数据库操作失败，未配置有效的数据库连接"
            }
            
        except Exception as e:
            logger.error(f"Worker {worker_id}: 数据访问层出现未处理异常，操作: {operation}，集合: {collection}，错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"数据操作失败: {str(e)}"
            }
            
    async def get_user_items_by_type(self, user_id: str, item_type: str, skip: int = 0, limit: int = 100) -> Dict[str, Any]:
        """使用通用数据访问层实现的根据物品类型获取用户物品
        
        Args:
            user_id (str): 用户ID
            item_type (str): 物品类型，如 "equipment", "item" 等
            skip (int): 跳过的数量，用于分页
            limit (int): 返回的最大数量
            
        Returns:
            Dict: 包含物品列表和总数的字典
        """
        # 构建查询条件
        query = {
            "owner": user_id,
            "type": item_type
        }
        
        # 构建缓存键
        cache_key_pattern = config.get_cache_keys()["user_items"]
        cache_key_args = {
            "username": user_id
        }
        if "{owner}" in cache_key_pattern:
            cache_key_args["owner"] = user_id
        
        # 调用通用数据访问层
        result = await self.data_access(
            operation='find',
            collection='items',
            query=query,
            use_cache=True,
            cache_key_pattern=cache_key_pattern,
            cache_key_args=cache_key_args,
            cache_ttl=3600,
            skip=skip,
            limit=limit,
            sort_by="created_at"
        )
        
        return result

    # 向用户添加物品
    async def add_item_to_user(self, user_id, item_def_id, quantity=1, attributes=None):
        """向用户添加物品"""
        try:
            # 实现重试机制
            max_retries = 3
            retry_delay = 0.5  # 秒
            
            for attempt in range(max_retries):
                try:
                    # 生成唯一物品ID
                    item_id = str(uuid.uuid4())
                    
                    # 创建物品记录
                    item = {
                        "id": item_id,
                        "def_id": item_def_id,
                        "quantity": quantity,
                        "attributes": attributes or {},
                        "created_at": datetime.now().isoformat()
                    }
                    
                    # 更新MongoDB中的用户物品数据
                    result = await self.db.users.update_one(
                        {"user_id": user_id},
                        {"$push": {"items": item}}
                    )
                    
                    # 同步到Redis缓存
                    await self.sync_item_to_redis(user_id, item)
                    
                    return item_id
                except (redis_exceptions.RedisError, asyncio.TimeoutError, pymongo.errors.PyMongoError) as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"添加物品操作重试 ({attempt+1}/{max_retries})，用户: {user_id}, 错误: {str(e)}")
                        await asyncio.sleep(retry_delay)
                    else:
                        raise
        except Exception as e:
            logger.error(f"向用户 {user_id} 添加物品失败: {str(e)}")
            raise

    async def sync_item_to_redis(self, user_id, item):
        """同步物品数据到Redis缓存，支持重试机制"""
        max_retries = 3
        retry_delay = 0.5
        worker_id = os.getpid()
        
        for attempt in range(max_retries):
            try:
                # 获取缓存键
                cache_keys = config.get_cache_keys()
                user_items_key = cache_keys.get("user_items", f"game:v2:users:{user_id}:items")
                if "{username}" in user_items_key:
                    user_items_key = user_items_key.format(username=user_id)
                
                # 检查用户数据是否在缓存中
                if not await self.redis_client.exists(user_items_key):
                    # 创建新的物品列表缓存
                    await self.redis_client.set(user_items_key, json.dumps([item]), ex=3600)
                    logger.debug(f"Worker {worker_id}: 为用户 {user_id} 创建新的物品列表缓存")
                    return
                
                # 获取当前用户物品数据
                items_data = await self.redis_client.get(user_items_key)
                if items_data:
                    try:
                        items = json.loads(items_data)
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，创建新的物品列表
                        items = []
                    
                    # 确保items是列表
                    if not isinstance(items, list):
                        items = []
                    
                    # 检查物品是否已存在
                    found = False
                    for i, existing_item in enumerate(items):
                        if existing_item.get("id") == item.get("id"):
                            items[i] = item
                            found = True
                            break
                    
                    # 如果物品不存在，添加到列表
                    if not found:
                        items.append(item)
                    
                    # 更新缓存
                    await self.redis_client.set(user_items_key, json.dumps(items), ex=3600)
                    logger.debug(f"Worker {worker_id}: 同步物品到Redis成功，用户: {user_id}, 物品ID: {item.get('id')}")
                return
            except redis_exceptions.RedisError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Worker {worker_id}: 同步道具到 Redis 重试 ({attempt+1}/{max_retries})，用户: {user_id}, 道具: {item['id']}, 错误: {str(e)}")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.warning(f"Worker {worker_id}: 同步道具到 Redis 失败，用户: {user_id}, 道具: {item['id']}, 错误: {str(e)}")

async def cache_user_data(redis_client, username, user, expire_time=3600):
    try:
        if redis_client is None:
            logger.warning("Redis客户端未初始化，跳过缓存用户数据")
            return
            
        async with redis_client.pipeline() as pipe:
            await pipe.set(
                f"user:{username}:data",
                json.dumps(user, cls=CustomJSONEncoder),
                ex=expire_time
            )
            await pipe.set(
                f"user:{username}:status",
                "online",
                ex=expire_time
            )
            await pipe.execute()
    except Exception as e:
        logger.error(f"缓存用户数据失败: {str(e)}")

async def check_user_status(redis_client, username):
    try:
        if redis_client is None:
            logger.warning("Redis客户端未初始化，无法检查用户状态，默认返回offline")
            return "offline"
            
        cache_keys = config.get_cache_keys()
        status_key = cache_keys["user_status"].format(username=username)
        status = await redis_client.get(status_key)
        if status is None:
            return "offline"
        status = status.decode('utf-8')
        return status
    except Exception as e:
        logger.error(f"检查用户状态失败，用户: {username}, 错误: {str(e)}")
        return "offline"