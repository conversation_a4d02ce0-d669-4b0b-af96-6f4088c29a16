# 商店管理系统

## 📋 **系统概述**

这是一个现代化的商店管理系统前端界面，用于管理游戏中的商店和商品配置。系统采用响应式设计，支持桌面和移动设备。

## 🏗️ **当前集成状态**

### ✅ **已完成的功能**
- **完整的前端界面**: 商店管理和商品管理页面
- **响应式设计**: 支持各种屏幕尺寸
- **用户体验优化**: 加载状态、错误提示、实时反馈
- **数据验证**: 前端表单验证和数据格式检查

### 🔄 **API集成状态**

#### **已集成的接口**
- ✅ `POST /api/shop/admin/shop` - 创建商店
- ✅ `POST /api/shop/admin/item-config` - 创建商品配置

#### **使用模拟数据的功能**
- 🔄 商店列表获取 - 等待后端接口完善
- 🔄 商店详情获取 - 等待后端接口完善  
- 🔄 商店更新/删除 - 等待后端接口完善
- 🔄 商品配置列表 - 等待后端接口完善
- 🔄 商品配置更新/删除 - 等待后端接口完善

## 📁 **文件结构**

```
admin/shopadmin/
├── index.html              # 商店管理主页面
├── items.html              # 商品管理页面
├── css/
│   ├── main.css            # 主样式文件
│   └── components.css      # 组件样式文件
├── js/
│   ├── api.js              # API通信模块
│   ├── shop-manager.js     # 商店管理逻辑
│   ├── item-manager.js     # 商品管理逻辑
│   └── main.js             # 主逻辑文件
└── README.md               # 本文档
```

## 🚀 **使用方法**

### **1. 部署前端**
将整个 `admin/shopadmin` 目录部署到Web服务器即可。

### **2. 访问系统**
- 商店管理: `http://your-domain/admin/shopadmin/index.html`
- 商品管理: `http://your-domain/admin/shopadmin/items.html?shop_id=商店ID`

### **3. 功能说明**

#### **商店管理页面**
- **查看商店列表**: 卡片式展示所有商店
- **新增商店**: 点击"新增商店"按钮，填写表单
- **编辑商店**: 点击商店卡片的"编辑"按钮
- **删除商店**: 点击"删除"按钮，确认后删除
- **筛选搜索**: 按类型、状态筛选，支持关键词搜索

#### **商品管理页面**
- **查看商品配置**: 显示指定商店的所有商品配置
- **新增商品**: 点击"新增商品"按钮，配置商品参数
- **编辑商品**: 点击商品卡片的"编辑"按钮
- **删除商品**: 点击"删除"按钮，确认后删除
- **返回商店**: 点击"返回商店列表"按钮

## 🔧 **配置说明**

### **API配置 (js/api.js)**
```javascript
class ShopAPI {
    constructor() {
        this.baseURL = '/api/shop';           // API基础路径
        this.adminBaseURL = '/api/shop/admin'; // 管理接口路径
        this.timeout = 10000;                 // 请求超时时间
    }
}
```

### **数据格式**

#### **商店数据格式**
```javascript
{
    shop_id: "shop_001",                    // 商店ID
    shop_name: "装备商店",                  // 商店名称
    shop_type: "normal",                    // 商店类型
    description: "出售各种装备的商店",       // 描述
    icon: "equipment_shop.png",             // 图标
    is_active: true,                        // 是否激活
    sort_order: 1,                          // 排序权重
    access_conditions: {},                  // 访问条件
    refresh_config: {},                     // 刷新配置
    ui_config: {}                          // UI配置
}
```

#### **商品配置数据格式**
```javascript
{
    config_id: "config_001",               // 配置ID
    shop_id: "shop_001",                   // 所属商店ID
    item_template_id: "10001",             // 道具模板ID
    item_quantity: 1,                      // 道具数量
    item_quality: 3,                       // 道具品质(1-6)
    slot_id: 1,                           // 槽位ID
    price_config: {                        // 价格配置
        currency: "gold",
        amount: 100
    },
    purchase_limit: {                      // 限购配置
        type: "daily",
        count: 5
    },
    availability: {                        // 可用性配置
        start_time: null,
        end_time: null,
        conditions: {}
    },
    refresh_weight: 100,                   // 刷新权重
    refresh_probability: 1.0,              // 出现概率
    sort_order: 1,                         // 排序权重
    is_active: true,                       // 是否激活
    display_config: {}                     // 显示配置
}
```

## 🔗 **后端接口对接**

### **需要完善的管理接口**

为了完整支持管理功能，建议在后端添加以下接口：

```python
# 商店管理接口
GET    /api/shop/admin/shops              # 获取所有商店列表
GET    /api/shop/admin/shop/{shop_id}     # 获取商店详情
PUT    /api/shop/admin/shop/{shop_id}     # 更新商店
DELETE /api/shop/admin/shop/{shop_id}     # 删除商店

# 商品配置管理接口
GET    /api/shop/admin/shop/{shop_id}/items    # 获取商店商品配置列表
GET    /api/shop/admin/item/{config_id}        # 获取商品配置详情
PUT    /api/shop/admin/item/{config_id}        # 更新商品配置
DELETE /api/shop/admin/item/{config_id}        # 删除商品配置
```

### **响应格式**
所有接口应返回统一的JSON格式：
```javascript
{
    "success": true,                       // 操作是否成功
    "data": {...},                        // 返回数据
    "message": "操作成功",                 // 消息说明
    "timestamp": "2024-01-01T00:00:00Z"   // 时间戳
}
```

## 🛠️ **开发说明**

### **添加新功能**
1. 在对应的管理器类中添加方法
2. 在API类中添加接口调用
3. 更新界面和事件处理

### **修改样式**
- 主样式: `css/main.css`
- 组件样式: `css/components.css`

### **调试模式**
在浏览器控制台中可以查看详细的日志信息：
```javascript
// 查看API调用日志
console.log('[ShopAPI] 请求详情');

// 查看管理器状态
console.log('[ShopManager] 当前状态');
```

## 📞 **技术支持**

如需技术支持或有问题反馈，请联系开发团队。

---

**版本**: 1.0.0  
**更新时间**: 2024-01-01  
**兼容性**: 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)
