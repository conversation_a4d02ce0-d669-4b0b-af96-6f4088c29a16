"""
游戏事件定义
定义游戏中的各种事件类型和事件数据结构
"""

from enum import Enum
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel


class GameEventType(str, Enum):
    """游戏事件类型枚举"""
    # 玩家相关事件
    PLAYER_LOGIN = "player_login"
    PLAYER_LOGOUT = "player_logout"
    PLAYER_LEVEL_UP = "player_level_up"
    PLAYER_DATA_UPDATE = "player_data_update"
    
    # 公会相关事件
    GUILD_JOIN = "guild_join"
    GUILD_LEAVE = "guild_leave"
    GUILD_POSITION_CHANGE = "guild_position_change"
    GUILD_INFO_UPDATE = "guild_info_update"
    
    # 系统相关事件
    SYSTEM_NOTIFICATION = "system_notification"
    SYSTEM_MAINTENANCE = "system_maintenance"
    SYSTEM_ANNOUNCEMENT = "system_announcement"
    
    # 游戏相关事件
    MONSTER_RESPAWN = "monster_respawn"
    ITEM_OBTAINED = "item_obtained"
    EQUIPMENT_CHANGED = "equipment_changed"


class NotificationLevel(str, Enum):
    """通知级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class GameEvent(BaseModel):
    """游戏事件基础模型"""
    event_id: str
    event_type: GameEventType
    timestamp: datetime
    source_worker: int
    data: Dict[str, Any]
    target_players: Optional[list] = None  # 目标玩家列表，None表示全服
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type,
            "timestamp": self.timestamp.isoformat(),
            "source_worker": self.source_worker,
            "data": self.data,
            "target_players": self.target_players
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GameEvent":
        """从字典创建事件"""
        data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        return cls(**data)


class PlayerLoginEvent(BaseModel):
    """玩家登录事件"""
    username: str
    token: str
    login_time: datetime
    worker_id: int
    ip_address: Optional[str] = None


class PlayerLogoutEvent(BaseModel):
    """玩家登出事件"""
    username: str
    logout_time: datetime
    worker_id: int
    reason: str = "normal"  # normal, disconnect, timeout


class PlayerDataUpdateEvent(BaseModel):
    """玩家数据更新事件"""
    username: str
    data_type: str  # items, equipment, etc.
    data: Dict[str, Any]
    update_time: datetime


class SystemNotificationEvent(BaseModel):
    """系统通知事件"""
    message: str
    level: NotificationLevel
    target_players: Optional[list] = None
    expire_time: Optional[datetime] = None


class GuildEvent(BaseModel):
    """公会事件基础模型"""
    guild_id: str
    player_id: str
    event_time: datetime


class GuildJoinEvent(GuildEvent):
    """加入公会事件"""
    guild_name: str
    position: str


class GuildLeaveEvent(GuildEvent):
    """离开公会事件"""
    guild_name: str
    reason: str = "leave"  # leave, kicked, disbanded


class GuildPositionChangeEvent(GuildEvent):
    """公会职位变更事件"""
    old_position: str
    new_position: str


class MonsterRespawnEvent(BaseModel):
    """怪物刷新事件"""
    monster_id: str
    monster_name: str
    location: str
    respawn_time: datetime


class ItemObtainedEvent(BaseModel):
    """获得道具事件"""
    username: str
    item_id: str
    item_name: str
    quantity: int
    source: str  # drop, purchase, reward, etc.


# 事件工厂类
class GameEventFactory:
    """游戏事件工厂"""
    
    @staticmethod
    def create_player_login_event(username: str, token: str, worker_id: int, 
                                ip_address: str = None) -> PlayerLoginEvent:
        """创建玩家登录事件"""
        return PlayerLoginEvent(
            username=username,
            token=token,
            login_time=datetime.now(),
            worker_id=worker_id,
            ip_address=ip_address
        )
    
    @staticmethod
    def create_player_logout_event(username: str, worker_id: int, 
                                 reason: str = "normal") -> PlayerLogoutEvent:
        """创建玩家登出事件"""
        return PlayerLogoutEvent(
            username=username,
            logout_time=datetime.now(),
            worker_id=worker_id,
            reason=reason
        )
    
    @staticmethod
    def create_player_data_update_event(username: str, data_type: str, 
                                      data: Dict[str, Any]) -> PlayerDataUpdateEvent:
        """创建玩家数据更新事件"""
        return PlayerDataUpdateEvent(
            username=username,
            data_type=data_type,
            data=data,
            update_time=datetime.now()
        )
    
    @staticmethod
    def create_system_notification_event(message: str, level: NotificationLevel = NotificationLevel.INFO,
                                       target_players: list = None, 
                                       expire_time: datetime = None) -> SystemNotificationEvent:
        """创建系统通知事件"""
        return SystemNotificationEvent(
            message=message,
            level=level,
            target_players=target_players,
            expire_time=expire_time
        )
    
    @staticmethod
    def create_guild_join_event(guild_id: str, guild_name: str, player_id: str, 
                              position: str) -> GuildJoinEvent:
        """创建加入公会事件"""
        return GuildJoinEvent(
            guild_id=guild_id,
            guild_name=guild_name,
            player_id=player_id,
            position=position,
            event_time=datetime.now()
        )
    
    @staticmethod
    def create_guild_leave_event(guild_id: str, guild_name: str, player_id: str,
                               reason: str = "leave") -> GuildLeaveEvent:
        """创建离开公会事件"""
        return GuildLeaveEvent(
            guild_id=guild_id,
            guild_name=guild_name,
            player_id=player_id,
            reason=reason,
            event_time=datetime.now()
        )
    
    @staticmethod
    def create_monster_respawn_event(monster_id: str, monster_name: str, 
                                   location: str) -> MonsterRespawnEvent:
        """创建怪物刷新事件"""
        return MonsterRespawnEvent(
            monster_id=monster_id,
            monster_name=monster_name,
            location=location,
            respawn_time=datetime.now()
        )
    
    @staticmethod
    def create_item_obtained_event(username: str, item_id: str, item_name: str,
                                 quantity: int, source: str) -> ItemObtainedEvent:
        """创建获得道具事件"""
        return ItemObtainedEvent(
            username=username,
            item_id=item_id,
            item_name=item_name,
            quantity=quantity,
            source=source
        )


# 事件处理器接口
class EventHandler:
    """事件处理器基类"""
    
    async def handle(self, event: GameEvent) -> bool:
        """处理事件"""
        raise NotImplementedError
    
    def can_handle(self, event_type: GameEventType) -> bool:
        """检查是否可以处理指定类型的事件"""
        raise NotImplementedError


# 预定义的事件处理器
class PlayerLoginHandler(EventHandler):
    """玩家登录事件处理器"""
    
    def can_handle(self, event_type: GameEventType) -> bool:
        return event_type == GameEventType.PLAYER_LOGIN
    
    async def handle(self, event: GameEvent) -> bool:
        """处理玩家登录事件"""
        # 这里可以添加登录后的处理逻辑
        # 比如更新在线状态、发送欢迎消息等
        return True


class PlayerLogoutHandler(EventHandler):
    """玩家登出事件处理器"""
    
    def can_handle(self, event_type: GameEventType) -> bool:
        return event_type == GameEventType.PLAYER_LOGOUT
    
    async def handle(self, event: GameEvent) -> bool:
        """处理玩家登出事件"""
        # 这里可以添加登出后的清理逻辑
        # 比如保存数据、清理缓存等
        return True


class SystemNotificationHandler(EventHandler):
    """系统通知事件处理器"""
    
    def can_handle(self, event_type: GameEventType) -> bool:
        return event_type == GameEventType.SYSTEM_NOTIFICATION
    
    async def handle(self, event: GameEvent) -> bool:
        """处理系统通知事件"""
        # 这里可以添加系统通知的处理逻辑
        # 比如发送通知给指定玩家
        return True
