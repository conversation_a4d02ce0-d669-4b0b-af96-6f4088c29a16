"""
商店系统初始化脚本
"""

import asyncio
import logging
from datetime import datetime, timedelta
from shop_service import ShopService
from shop_models import ShopType, LimitType, DiscountType, ScopeType

logger = logging.getLogger(__name__)


class ShopSystemInitializer:
    """商店系统初始化器"""
    
    def __init__(self):
        self.shop_service = ShopService()
    
    async def initialize_system(self):
        """初始化商店系统"""
        try:
            logger.info("开始初始化商店系统...")
            
            # 1. 创建示例商店
            await self._create_sample_shops()
            
            # 2. 创建示例商品配置
            await self._create_sample_items()
            
            # 3. 创建示例折扣
            await self._create_sample_discounts()
            
            logger.info("商店系统初始化完成")
            
        except Exception as e:
            logger.error(f"商店系统初始化失败: {str(e)}")
            raise
    
    async def _create_sample_shops(self):
        """创建示例商店"""
        shops_data = [
            {
                "shop_name": "普通商店",
                "shop_type": ShopType.NORMAL,
                "description": "出售基础道具和装备",
                "icon": "shop_normal.png",
                "is_active": True,
                "access_conditions": {
                    "player_level": 1,
                    "vip_level": 0
                },
                "refresh_config": None,
                "sort_order": 1,
                "ui_config": {
                    "theme": "normal",
                    "layout": "grid"
                }
            }
        ]
        
        for shop_data in shops_data:
            success = await self.shop_service.create_shop(shop_data)
            if success:
                logger.info(f"创建商店成功: {shop_data['shop_name']}")
            else:
                logger.error(f"创建商店失败: {shop_data['shop_name']}")
    
    async def _create_sample_items(self):
        """创建示例商品配置"""
        
        # 获取已创建的商店
        shops = await self.shop_service.get_available_shops("system")  # 使用系统用户获取所有商店
        shop_map = {shop.shop_type: shop.shop_id for shop in shops}
        
        items_data = [
            # 普通商店商品
            {
                "shop_id": shop_map.get(ShopType.NORMAL),
                "item_template_id": "82026",
                "item_quantity": 1,
				"item_type":"item",
                "price_config": {
                    "currency_type": "diamond",
                    "base_price": 100
                },
                "purchase_limit": {
                    "type": LimitType.DAILY,
                    "count": 5,
                    "reset_time": {"hour": 5, "minute": 0}
                },
                "availability": {
                    "conditions": {
                        "player_level": 1
                    }
                },
                "sort_order": 1,
                "display_config": {
                    "name": "金币包",
                    "description": "包含1000金币"
                }
            }
        ]
        
        for item_data in items_data:
            if item_data["shop_id"]:  # 确保商店ID存在
                success = await self.shop_service.create_item_config(item_data)
                if success:
                    logger.info(f"创建商品配置成功: {item_data['display_config']['name']}")
                else:
                    logger.error(f"创建商品配置失败: {item_data['display_config']['name']}")
    
    async def _create_sample_discounts(self):
        """创建示例折扣"""
        
        # 获取已创建的商店
        shops = await self.shop_service.get_available_shops("system")
        shop_map = {shop.shop_type: shop.shop_id for shop in shops}
        
        discounts_data = [
            # VIP折扣
            {
                "discount_name": "VIP3专属9折",
                "scope_type": ScopeType.SHOP,
                "scope_values": [shop_map.get(ShopType.VIP)],
                "discount_rule": {
                    "type": DiscountType.PERCENTAGE,
                    "value": 0.9,
                    "min_price": 1
                },
                "conditions": {
                    "vip_level": 3
                },
                "priority": 10,
                "mutex_groups": ["vip_discount"],
                "stackable": False,
                "is_active": True
            },
            
            # 首次购买折扣
            {
                "discount_name": "首次购买8折",
                "scope_type": ScopeType.GLOBAL,
                "scope_values": [],
                "discount_rule": {
                    "type": DiscountType.PERCENTAGE,
                    "value": 0.8,
                    "min_price": 1,
                    "max_discount": 100
                },
                "conditions": {
                    "first_purchase": True
                },
                "priority": 5,
                "mutex_groups": ["first_purchase"],
                "stackable": False,
                "is_active": True
            },
            
            # 限时折扣
            {
                "discount_name": "周末特惠",
                "scope_type": ScopeType.SHOP,
                "scope_values": [shop_map.get(ShopType.NORMAL)],
                "discount_rule": {
                    "type": DiscountType.FIXED_AMOUNT,
                    "value": 10,
                    "min_price": 1
                },
                "conditions": {
                    "time_range": {
                        "start": (datetime.now() + timedelta(days=1)).isoformat(),
                        "end": (datetime.now() + timedelta(days=7)).isoformat()
                    }
                },
                "priority": 8,
                "mutex_groups": ["time_discount"],
                "stackable": False,
                "is_active": True
            }
        ]
        
        for discount_data in discounts_data:
            if not discount_data["scope_values"] or all(shop_id for shop_id in discount_data["scope_values"]):
                success = await self.shop_service.create_discount(discount_data)
                if success:
                    logger.info(f"创建折扣成功: {discount_data['discount_name']}")
                else:
                    logger.error(f"创建折扣失败: {discount_data['discount_name']}")
    
    async def create_test_data(self):
        """创建测试数据"""
        try:
            logger.info("开始创建测试数据...")
            
            # 创建测试商店
            test_shop_data = {
                "shop_name": "测试商店",
                "shop_type": ShopType.NORMAL,
                "description": "用于测试的商店",
                "icon": "test_shop.png",
                "is_active": True,
                "access_conditions": {},
                "sort_order": 999,
                "ui_config": {}
            }
            
            await self.shop_service.create_shop(test_shop_data)
            
            logger.info("测试数据创建完成")
            
        except Exception as e:
            logger.error(f"创建测试数据失败: {str(e)}")


async def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    initializer = ShopSystemInitializer()
    
    try:
        # 初始化商店系统
        await initializer.initialize_system()
        
        # 创建测试数据
        await initializer.create_test_data()
        
        print("商店系统初始化完成！")
        
    except Exception as e:
        print(f"初始化失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
