<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强商店面板演示</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            background-color: #f8fafc;
            padding: 20px;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 30px;
        }
        .demo-description {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }
        .feature-icon {
            color: #10b981;
            font-weight: bold;
        }
        .shop-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🏪 增强商店面板演示</h1>
        
        <div class="demo-description">
            <h2>✨ 新增功能特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">🔄</span>
                    <span>快速状态切换开关</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎯</span>
                    <span>一键启用/禁用按钮</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <span>详细的商店信息展示</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⏰</span>
                    <span>创建和更新时间信息</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span>分区式信息组织</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🌈</span>
                    <span>状态指示颜色边框</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span>响应式设计优化</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⚡</span>
                    <span>流畅的交互动画</span>
                </div>
            </div>
        </div>

        <div class="shop-list" id="demoShopList">
            <!-- 演示商店卡片将在这里生成 -->
        </div>
    </div>

    <script>
        // 模拟商店数据
        const mockShops = [
            {
                shop_id: "shop_main_001",
                shop_name: "主商店",
                shop_type: "normal",
                description: "游戏主要商店，提供各种基础道具和装备",
                icon: "main_shop.png",
                is_active: true,
                sort_order: 1,
                created_at: "2024-01-15T10:30:00Z",
                updated_at: "2024-01-20T14:45:00Z"
            },
            {
                shop_id: "shop_premium_002",
                shop_name: "高级商店",
                shop_type: "premium",
                description: "高级商店，提供稀有道具和限时商品",
                icon: "premium_shop.png",
                is_active: false,
                sort_order: 2,
                created_at: "2024-01-10T08:15:00Z",
                updated_at: "2024-01-18T16:20:00Z"
            },
            {
                shop_id: "shop_event_003",
                shop_name: "活动商店",
                shop_type: "event",
                description: "特殊活动期间开放的限时商店",
                icon: "event_shop.png",
                is_active: true,
                sort_order: 3,
                created_at: "2024-01-05T12:00:00Z",
                updated_at: "2024-01-25T09:30:00Z"
            }
        ];

        // 创建模拟的ShopManager
        class DemoShopManager {
            constructor() {
                this.shops = mockShops;
            }

            getShopTypeText(shopType) {
                const typeMap = {
                    'normal': '普通商店',
                    'premium': '高级商店',
                    'event': '活动商店',
                    'guild': '公会商店',
                    'arena': '竞技场商店'
                };
                return typeMap[shopType] || '未知类型';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            formatShopTimeInfo(shop) {
                let timeParts = [];
                
                if (shop.created_at) {
                    const createdTime = new Date(shop.created_at);
                    timeParts.push(`<div class="time-item">
                        <i class="icon-created">➕</i>
                        创建时间: <span class="time-value">${this.formatDateTime(createdTime)}</span>
                    </div>`);
                }
                
                if (shop.updated_at) {
                    const updatedTime = new Date(shop.updated_at);
                    timeParts.push(`<div class="time-item">
                        <i class="icon-updated">✏️</i>
                        更新时间: <span class="time-value">${this.formatDateTime(updatedTime)}</span>
                    </div>`);
                }

                return {
                    html: timeParts.join('')
                };
            }

            formatDateTime(date) {
                if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
                    return '无效时间';
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }

            // 模拟状态切换（仅演示效果）
            toggleShopStatus(shopId, isActive) {
                console.log(`演示：切换商店 ${shopId} 状态为 ${isActive ? '激活' : '禁用'}`);

                // 更新本地数据
                const shop = this.shops.find(s => s.shop_id === shopId);
                if (shop) {
                    shop.is_active = isActive;
                    shop.updated_at = new Date().toISOString();

                    // 更新开关文字
                    const toggleText = document.querySelector(`#toggle-${shopId} + .toggle-slider .toggle-text`);
                    if (toggleText) {
                        toggleText.textContent = isActive ? '已激活' : '已禁用';
                    }

                    // 重新渲染
                    this.renderDemo();

                    // 显示提示
                    const action = isActive ? '激活' : '禁用';
                    console.log(`商店${action}成功！（这是演示效果）`);
                }
            }

            createShopCard(shop) {
                const statusClass = shop.is_active ? 'active' : 'inactive';
                const statusText = shop.is_active ? '激活' : '禁用';
                const typeText = this.getShopTypeText(shop.shop_type);
                const timeInfo = this.formatShopTimeInfo(shop);
                
                return `
                    <div class="shop-card ${statusClass}" data-shop-id="${shop.shop_id}">
                        <div class="shop-card-header">
                            <div class="shop-info">
                                <h3 class="shop-title">
                                    🏪 ${this.escapeHtml(shop.shop_name)}
                                </h3>
                                <div class="shop-id">
                                    🆔 ${this.escapeHtml(shop.shop_id)}
                                </div>
                            </div>
                            <div class="shop-status">
                                <span class="status-badge ${statusClass}">
                                    ${shop.is_active ? '✅' : '❌'} ${statusText}
                                </span>
                                <span class="shop-type-badge">${typeText}</span>
                                <span class="sort-order-badge">排序: ${shop.sort_order || 0}</span>
                            </div>
                        </div>
                        
                        ${shop.description ? `
                        <div class="shop-description">
                            📝 ${this.escapeHtml(shop.description)}
                        </div>
                        ` : ''}
                        
                        <div class="shop-section">
                            <div class="section-title">
                                ℹ️ 商店信息
                            </div>
                            <div class="shop-details">
                                <div class="shop-detail-row">
                                    <span class="shop-detail-label">
                                        🏷️ 类型:
                                    </span>
                                    <span class="shop-detail-value">${typeText}</span>
                                </div>
                                <div class="shop-detail-row">
                                    <span class="shop-detail-label">
                                        🔢 排序:
                                    </span>
                                    <span class="shop-detail-value">${shop.sort_order || 0}</span>
                                </div>
                                <div class="shop-detail-row">
                                    <span class="shop-detail-label">
                                        📊 状态:
                                    </span>
                                    <span class="shop-detail-value status-${statusClass}">${statusText}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="shop-section">
                            <div class="section-title">
                                🕒 时间信息
                            </div>
                            <div class="shop-time-info">
                                ${timeInfo.html}
                            </div>
                        </div>
                        
                        <div class="shop-status-toggle">
                            <div class="toggle-label">
                                🔄 快速切换状态
                            </div>
                            <div class="toggle-switch">
                                <input type="checkbox" 
                                       id="toggle-${shop.shop_id}" 
                                       class="toggle-input" 
                                       ${shop.is_active ? 'checked' : ''}
                                       onchange="demoManager.toggleShopStatus('${shop.shop_id}', this.checked)">
                                <label for="toggle-${shop.shop_id}" class="toggle-slider">
                                    <span class="toggle-text">${shop.is_active ? '已激活' : '已禁用'}</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="shop-actions">
                            <button class="btn btn-sm btn-primary" title="管理商店商品">
                                📦 商品管理
                            </button>
                            <button class="btn btn-sm btn-secondary" title="编辑商店配置">
                                ✏️ 编辑
                            </button>
                            <button class="btn btn-sm ${shop.is_active ? 'btn-warning' : 'btn-success'}" 
                                    onclick="demoManager.toggleShopStatus('${shop.shop_id}', ${!shop.is_active})" 
                                    title="${shop.is_active ? '禁用' : '启用'}商店">
                                ${shop.is_active ? '⏸️ 禁用' : '▶️ 启用'}
                            </button>
                            <button class="btn btn-sm btn-danger" title="删除商店">
                                🗑️ 删除
                            </button>
                        </div>
                    </div>
                `;
            }

            renderDemo() {
                const container = document.getElementById('demoShopList');
                if (container) {
                    container.innerHTML = this.shops.map(shop => this.createShopCard(shop)).join('');
                }
            }
        }

        // 全局变量供HTML调用
        let demoManager;

        // 页面加载完成后渲染演示
        document.addEventListener('DOMContentLoaded', () => {
            demoManager = new DemoShopManager();
            demoManager.renderDemo();
        });
    </script>
</body>
</html>
