<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>道具类型选择功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>道具类型选择功能测试</h1>
    
    <div class="test-container">
        <h2>测试表单</h2>
        <form id="testForm">
            <div class="form-group">
                <label for="itemTemplateId">道具模板ID *</label>
                <input type="text" id="itemTemplateId" name="item_template_id" required 
                       placeholder="例: 10001" pattern="[0-9]+" value="10001">
            </div>
            
            <div class="form-group">
                <label for="itemType">道具类型 *</label>
                <select id="itemType" name="item_type" required>
                    <option value="">请选择道具类型</option>
                    <option value="item">道具 (item)</option>
                    <option value="equipment">装备 (equipment)</option>
                    <option value="rune">符文 (rune)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="itemQuantity">道具数量 *</label>
                <input type="number" id="itemQuantity" name="item_quantity" required 
                       min="1" max="999999" value="1">
            </div>
            
            <div class="form-group">
                <label for="itemQuality">道具品质</label>
                <select id="itemQuality" name="item_quality">
                    <option value="">默认品质</option>
                    <option value="1">白色</option>
                    <option value="2">绿色</option>
                    <option value="3">蓝色</option>
                    <option value="4">紫色</option>
                    <option value="5">橙色</option>
                    <option value="6">红色</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="priceConfig">价格配置 (JSON) *</label>
                <textarea id="priceConfig" name="price_config" required rows="3">{"currency": "gold", "amount": 100}</textarea>
            </div>
            
            <div class="form-group">
                <label for="availability">可用性配置 (JSON) *</label>
                <textarea id="availability" name="availability" required rows="3">{"start_time": null, "end_time": null, "conditions": {}}</textarea>
            </div>
            
            <button type="button" class="btn" onclick="testFormData()">测试表单数据</button>
            <button type="button" class="btn" onclick="testValidation()">测试验证逻辑</button>
            <button type="button" class="btn" onclick="resetForm()">重置表单</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟前端验证逻辑
        function validateFormData(data) {
            if (!data.item_template_id) {
                throw new Error('道具模板ID不能为空');
            }

            if (!data.item_type || !data.item_type.trim()) {
                throw new Error('道具类型不能为空');
            }

            // 验证道具类型是否有效
            const validItemTypes = ['item', 'equipment', 'rune'];
            if (!validItemTypes.includes(data.item_type)) {
                throw new Error('道具类型无效，必须是: ' + validItemTypes.join(', '));
            }

            if (!data.item_quantity || data.item_quantity <= 0) {
                throw new Error('道具数量必须大于0');
            }

            // 验证道具模板ID格式
            if (!/^\d+$/.test(data.item_template_id)) {
                throw new Error('道具模板ID必须是数字');
            }

            // 验证品质范围
            if (data.item_quality !== null && data.item_quality !== '' && (data.item_quality < 1 || data.item_quality > 6)) {
                throw new Error('道具品质必须在1-6之间');
            }
        }

        function getFormData() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            const data = {
                item_template_id: formData.get('item_template_id')?.trim(),
                item_type: formData.get('item_type'),
                item_quantity: parseInt(formData.get('item_quantity')) || 1,
                item_quality: formData.get('item_quality') ? parseInt(formData.get('item_quality')) : null,
                price_config: JSON.parse(formData.get('price_config') || '{}'),
                availability: JSON.parse(formData.get('availability') || '{}')
            };
            
            return data;
        }

        function testFormData() {
            try {
                const data = getFormData();
                showResult('表单数据获取成功', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('表单数据获取失败', error.message, 'error');
            }
        }

        function testValidation() {
            try {
                const data = getFormData();
                validateFormData(data);
                showResult('验证通过', '所有字段验证成功', 'success');
            } catch (error) {
                showResult('验证失败', error.message, 'error');
            }
        }

        function resetForm() {
            document.getElementById('testForm').reset();
            document.getElementById('itemType').value = 'item';
            document.getElementById('itemQuantity').value = '1';
            document.getElementById('priceConfig').value = '{"currency": "gold", "amount": 100}';
            document.getElementById('availability').value = '{"start_time": null, "end_time": null, "conditions": {}}';
            showResult('表单已重置', '所有字段已恢复默认值', 'success');
        }

        function showResult(title, message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<h3>${title}</h3><pre>${message}</pre>`;
            resultDiv.style.display = 'block';
            
            // 添加到测试结果历史
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `
                <div class="result ${type}" style="margin-bottom: 10px;">
                    <strong>[${timestamp}] ${title}</strong><br>
                    <pre>${message}</pre>
                </div>
            `;
        }

        // 页面加载时设置默认值
        window.onload = function() {
            document.getElementById('itemType').value = 'item';
        };
    </script>
</body>
</html>
