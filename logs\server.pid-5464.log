2025-08-05 18:27:25,028 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:25,978 - models - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,010 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,372 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,383 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,392 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,405 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:26,416 - auth - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,226 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,269 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,297 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,305 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,316 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,316 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 1a231c33)
2025-08-05 18:27:29,322 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,358 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,371 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,377 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,384 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,476 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,491 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,501 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,511 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,520 - game_server - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:29,521 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 18:27:29,521 - msgManager - INFO - Monster handlers registered
2025-08-05 18:27:29,521 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 18:27:29,523 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 18:27:29,536 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 18:27:29,536 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 18:27:29,536 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: e8186f44)
2025-08-05 18:27:29,590 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 18:27:29,641 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 18:27:29,641 - game_server - INFO - 模板引擎初始化成功
2025-08-05 18:27:29,643 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:27:29,644 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 5464)
2025-08-05 18:27:29,645 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 18:27:29,817 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:27:29,830 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 18:27:30,353 - ConnectionManager - INFO - 后台任务已启动 (Worker 5464)
2025-08-05 18:27:30,353 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 5464)
2025-08-05 18:27:30,366 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 18:27:30,367 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 18:27:30,367 - game_server - INFO - 游戏配置加载完成 (Worker: 5464)
2025-08-05 18:27:30,369 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 18:27:34,649 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 18:27:34,649 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=0, 总计=2
2025-08-05 18:27:34,650 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 5464): 2/2 (100.0%)
2025-08-05 18:27:34,652 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 18:27:34,692 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: e8186f44)
2025-08-05 18:27:34,692 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: e8186f44)
2025-08-05 18:27:34,699 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:34,701 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 18:27:34,702 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 18:27:34,709 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 18:27:34,710 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 18:27:34,711 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 18:27:34,712 - ConnectionManager - INFO - Worker 5464 开始消费广播消息，消费者标签: ctag1.426c5c1b3d954e9da6f5dae05047aeaf
2025-08-05 18:27:34,761 - ConnectionManager - INFO - Worker 5464 开始消费个人消息，消费者标签: ctag1.5af9891004cd4749a2921d97f2ad36b5
2025-08-05 18:27:34,849 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 5464)
2025-08-05 18:27:34,901 - distributed_lock - INFO - Worker 5464 成功获取锁: scheduler_initialization
2025-08-05 18:27:34,901 - game_server_scheduler_integration - INFO - Worker 5464 获得调度器初始化权限
2025-08-05 18:27:34,905 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 18:27:34,906 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 18:27:34,906 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 18:27:34,906 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 18:27:34,907 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 18:27:34,907 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 18:27:34,908 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 18:27:34,908 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 18:27:34,908 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 18:27:34,910 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 18:27:34,917 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 18:27:34,917 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 18:27:34,917 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 18:27:34,918 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 18:27:34,918 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 18:27:34,918 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 18:27:35,000 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 18:27:35,001 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 18:27:35,001 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 18:27:35,002 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 18:27:35,293 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 18:27:35,383 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 18:27:35,384 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 18:27:35,384 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 18:27:35,385 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 18:27:35,385 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 18:27:35,385 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:27:35,464 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 18:27:35,594 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:27:35,594 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 18:27:35,594 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 18:27:35,595 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 18:27:35,595 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 18:27:35,596 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 18:27:35,600 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,600 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:27:35,600 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 18:27:35,602 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,602 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:27:35,602 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 18:27:35,722 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,723 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:27:35,723 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 18:27:35,845 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,845 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:27:35,846 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 18:27:35,847 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,848 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:27:35,849 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 18:27:35,849 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 18:27:35,850 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:27:35,850 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 18:27:35,851 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 18:27:35,852 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,852 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,852 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,853 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,853 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,855 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 18:27:35,855 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:27:35,855 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 18:27:35,856 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 18:27:35,856 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 18:27:35,856 - game_server_scheduler_integration - INFO - Worker 5464 调度器初始化成功
2025-08-05 18:27:35,901 - game_server - INFO - 统一调度器初始化成功 (Worker: 5464)
2025-08-05 18:27:35,907 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 5464)
2025-08-05 18:27:35,908 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 18:27:35,909 - game_server - INFO - 日志清理管理器已启动 (Worker: 5464)
2025-08-05 18:27:35,911 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 18:27:35,912 - game_server - INFO - Monster cooldown manager initialized (Worker: 5464)
2025-08-05 18:27:36,054 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 18:27:36,055 - game_server - INFO - 公会系统初始化成功 (Worker: 5464)
2025-08-05 18:27:36,055 - game_server - INFO - 邮件系统初始化成功 (Worker: 5464)
2025-08-05 18:27:36,058 - game_server - INFO - 商店系统初始化成功 (Worker: 5464)
2025-08-05 18:27:36,058 - game_server - INFO - 初始化完成 (Worker: 5464)
2025-08-05 18:27:51,635 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:27:55 CST)" was missed by 0:00:05.790515
2025-08-05 18:27:52,304 - distributed_lock - INFO - Worker 5464 成功获取锁: shop:create:普通商店
2025-08-05 18:27:52,305 - shop_service - INFO - Worker 5464 获取商店创建锁: 普通商店
2025-08-05 18:27:52,305 - shop_service - ERROR - 检查商店名称时发生错误: 'ShopDatabaseManager' object has no attribute 'get_all_shops'
2025-08-05 18:27:53,022 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 18:27:53,069 - shop_database_manager - INFO - 商店创建成功: shop_6e698f3ec79c
2025-08-05 18:27:53,159 - shop_service - INFO - Worker 5464 商店创建成功: shop_6e698f3ec79c
2025-08-05 18:27:53,211 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 18:27:55,850 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:28:05 CST)" (scheduled at 2025-08-05 18:27:55.845013+08:00)
2025-08-05 18:27:55,895 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:27:55,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:27:56,026 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:27:56,380 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:27:56,380 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:27:56,381 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:27:56,381 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:27:56,429 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:28:05 CST)" executed successfully
2025-08-05 18:28:00,378 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 18:33:17,846 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=2, 总计=4
2025-08-05 18:33:17,846 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 18:33:17,848 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost?heartbeat=30" at 0x2c3500d60d0> was stuck. No frames were received in 93 seconds.
2025-08-05 18:33:17,849 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost" at 0x2c351cbe080> was stuck. No frames were received in 183 seconds.
2025-08-05 18:33:17,850 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:33:35 CST)" was missed by 0:00:12.128130
2025-08-05 18:33:17,851 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:25 CST)" was missed by 0:00:02.006844
2025-08-05 18:33:17,852 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:33:35 CST)" was missed by 0:00:42.250772
2025-08-05 18:33:17,854 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost?heartbeat=30 closed. Reconnecting after 5 seconds.
2025-08-05 18:33:17,855 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost closed. Reconnecting after 5 seconds.
2025-08-05 18:33:22,857 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 18:33:22,857 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 18:33:23,029 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 18:33:25,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:35 CST)" (scheduled at 2025-08-05 18:33:25.845013+08:00)
2025-08-05 18:33:25,889 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:33:25,891 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:33:26,022 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:33:26,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:33:26,367 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:33:26,370 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:33:26,371 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:33:26,413 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:35 CST)" executed successfully
2025-08-05 18:33:30,943 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 18:33:35,606 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:34:35 CST)" (scheduled at 2025-08-05 18:33:35.602103+08:00)
2025-08-05 18:33:35,650 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 18:33:35,650 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:33:35,651 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 18:33:35,652 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 18:33:35,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:34:05 CST)" (scheduled at 2025-08-05 18:33:35.722714+08:00)
2025-08-05 18:33:35,769 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:33:35,769 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:33:35,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:45 CST)" (scheduled at 2025-08-05 18:33:35.845013+08:00)
2025-08-05 18:33:35,893 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 18:33:36,021 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 18:33:36,022 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 18:33:36,227 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:33:36,228 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 18:33:36,229 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:33:36,229 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:33:36,230 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:33:36,274 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:34:05 CST)" executed successfully
2025-08-05 18:33:36,703 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 18:33:36,709 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.06秒
2025-08-05 18:33:36,712 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 18:33:36,714 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:33:36,761 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 18:34:35 CST)" executed successfully
2025-08-05 18:33:37,058 - equipment_service_distributed - ERROR - Error handling cache message: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 18:33:37,166 - ConnectionManager - ERROR - Redis获取消息出错: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 18:33:37,167 - ConnectionManager - WARNING - Redis连接错误，将重新订阅
2025-08-05 18:33:37,301 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 5464)
2025-08-05 18:33:40,858 - distributed_lock - ERROR - 获取锁时发生错误: lock:scheduled:monster_cooldown_notify, 错误: Timeout reading from *************:6379
2025-08-05 18:33:40,859 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:45 CST)" executed successfully
2025-08-05 18:33:45,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:55 CST)" (scheduled at 2025-08-05 18:33:45.845013+08:00)
2025-08-05 18:33:46,048 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:33:46,048 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:33:46,177 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:33:46,527 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:33:46,527 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 18:33:46,527 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:33:46,528 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:33:46,572 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:33:55 CST)" executed successfully
2025-08-05 18:33:47,860 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 18:33:55,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:34:05 CST)" (scheduled at 2025-08-05 18:33:55.845013+08:00)
2025-08-05 18:33:55,896 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:33:55,896 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:33:56,025 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:33:56,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:33:56,370 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 18:33:56,372 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:33:56,373 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:33:56,418 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:34:05 CST)" executed successfully
2025-08-05 18:34:00,125 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 18:34:05,732 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:34:35 CST)" (scheduled at 2025-08-05 18:34:05.722714+08:00)
2025-08-05 18:34:05,776 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 18:34:05,778 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:34:05,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:34:15 CST)" (scheduled at 2025-08-05 18:34:05.845013+08:00)
2025-08-05 18:34:05,898 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 18:34:05,898 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 18:34:05,909 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 18:34:06,023 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 18:34:06,251 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 18:34:06,251 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 18:34:06,252 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 18:34:06,252 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 18:34:06,253 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:34:06,297 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 18:34:35 CST)" executed successfully
2025-08-05 18:34:06,348 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 18:34:06,348 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 18:34:06,349 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 18:34:06,349 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 18:34:06,390 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 18:34:15 CST)" executed successfully
2025-08-05 20:24:23,356 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:24:23,356 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=2, 总计=4
2025-08-05 20:24:23,357 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:24:23,358 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:24:23,368 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost?heartbeat=30" at 0x2c351cbfe80> was stuck. No frames were received in 93 seconds.
2025-08-05 20:24:23,368 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost" at 0x2c351cbf930> was stuck. No frames were received in 183 seconds.
2025-08-05 20:24:23,369 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:25 CST)" was missed by 0:00:07.524300
2025-08-05 20:24:23,369 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:24:35 CST)" was missed by 0:00:47.767642
2025-08-05 20:24:23,373 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:24:35 CST)" was missed by 0:00:17.650961
2025-08-05 20:24:23,374 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], next run at: 2025-08-05 20:27:35 CST)" was missed by 0:56:47.526842
2025-08-05 20:24:23,376 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost?heartbeat=30 closed. Reconnecting after 5 seconds.
2025-08-05 20:24:23,377 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost closed. Reconnecting after 5 seconds.
2025-08-05 20:24:23,379 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:24:25,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:35 CST)" (scheduled at 2025-08-05 20:24:25.845013+08:00)
2025-08-05 20:24:28,378 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 20:24:28,378 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:24:28,558 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:24:30,425 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:24:30,858 - distributed_lock - ERROR - 获取锁时发生错误: lock:scheduled:monster_cooldown_notify, 错误: Timeout reading from *************:6379
2025-08-05 20:24:30,859 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:35 CST)" executed successfully
2025-08-05 20:24:33,740 - scheduler_health_checks - ERROR - 怪物冷却管理器健康检查失败: Timeout reading from *************:6379
2025-08-05 20:24:33,799 - scheduler_health_checks - ERROR - Traceback (most recent call last):
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 534, in read_response
    response = await self._parser.read_response(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        disable_decoding=disable_decoding
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\python_evns\hj\Lib\site-packages\redis\_parsers\hiredis.py", line 213, in read_response
    await self.read_from_socket()
  File "D:\python_evns\hj\Lib\site-packages\redis\_parsers\hiredis.py", line 191, in read_from_socket
    buffer = await self._stream.read(self._read_size)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 730, in read
    await self._wait_for_data('read')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 539, in _wait_for_data
    await self._waiter
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 533, in read_response
    async with async_timeout(read_timeout):
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\timeouts.py", line 116, in __aexit__
    raise TimeoutError from exc_val
TimeoutError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\python_evns\newServer\scheduler_health_checks.py", line 98, in check_monster_cooldown_manager_health
    await redis_client.set(test_key, "test", ex=5)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 615, in execute_command
    return await conn.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 62, in call_with_retry
    await fail(error)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 602, in _disconnect_raise
    raise error
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 59, in call_with_retry
    return await do()
           ^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\client.py", line 588, in _send_command_parse_response
    await conn.send_command(*args)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 498, in send_command
    await self.send_packed_command(
        self.pack_command(*args), check_health=kwargs.get("check_health", True)
    )
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 461, in send_packed_command
    await self.check_health()
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 449, in check_health
    await self.retry.call_with_retry(self._send_ping, self._ping_failed)
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 64, in call_with_retry
    raise error
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\retry.py", line 59, in call_with_retry
    return await do()
           ^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 436, in _send_ping
    if str_if_bytes(await self.read_response()) != "PONG":
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python_evns\hj\Lib\site-packages\redis\asyncio\connection.py", line 552, in read_response
    raise TimeoutError(f"Timeout reading from {host_error}")
redis.exceptions.TimeoutError: Timeout reading from *************:6379

2025-08-05 20:24:33,800 - unified_scheduler_manager - WARNING - 服务 怪物冷却管理器 健康检查失败
2025-08-05 20:24:33,801 - unified_scheduler_manager - WARNING - 检测到不健康的服务: ['怪物冷却管理器']
2025-08-05 20:24:35,611 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:25:35 CST)" (scheduled at 2025-08-05 20:24:35.602103+08:00)
2025-08-05 20:24:35,735 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:25:05 CST)" (scheduled at 2025-08-05 20:24:35.722714+08:00)
2025-08-05 20:24:35,804 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:24:35,805 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:24:35,805 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:24:35,850 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:45 CST)" (scheduled at 2025-08-05 20:24:35.845013+08:00)
2025-08-05 20:24:35,894 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:24:35,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:24:35,921 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:24:35,924 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:24:36,026 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:24:36,060 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:24:36,162 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:24:36,162 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:24:36,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:24:36,380 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:24:36,381 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:24:36,382 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:24:36,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:45 CST)" executed successfully
2025-08-05 20:24:36,536 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:24:36,537 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:24:36,538 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.48秒
2025-08-05 20:24:36,538 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:24:36,539 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:24:36,582 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:25:05 CST)" executed successfully
2025-08-05 20:24:37,127 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:24:37,129 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.32秒
2025-08-05 20:24:37,139 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:24:37,140 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:24:37,184 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:25:35 CST)" executed successfully
2025-08-05 20:24:39,051 - equipment_service_distributed - ERROR - Error handling cache message: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 20:24:42,594 - ConnectionManager - ERROR - Redis获取消息出错: Error while reading from *************:6379 : (10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-08-05 20:24:42,594 - ConnectionManager - WARNING - Redis连接错误，将重新订阅
2025-08-05 20:24:42,732 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 5464)
2025-08-05 20:24:45,849 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:55 CST)" (scheduled at 2025-08-05 20:24:45.845013+08:00)
2025-08-05 20:24:45,894 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:24:45,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:24:46,028 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:24:46,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:24:46,380 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:24:46,381 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:24:46,381 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:24:46,434 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:24:55 CST)" executed successfully
2025-08-05 20:24:53,362 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:24:55,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:05 CST)" (scheduled at 2025-08-05 20:24:55.845013+08:00)
2025-08-05 20:24:55,898 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:24:55,898 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:24:56,027 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:24:56,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:24:56,376 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:24:56,377 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:24:56,377 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:24:56,422 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:05 CST)" executed successfully
2025-08-05 20:25:00,641 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:25:05,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:25:35 CST)" (scheduled at 2025-08-05 20:25:05.722714+08:00)
2025-08-05 20:25:05,771 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:25:05,772 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:05,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:15 CST)" (scheduled at 2025-08-05 20:25:05.845013+08:00)
2025-08-05 20:25:05,902 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:05,902 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:05,903 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:25:06,037 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:06,300 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:25:06,300 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:25:06,301 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.40秒
2025-08-05 20:25:06,302 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:25:06,302 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:06,368 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:25:35 CST)" executed successfully
2025-08-05 20:25:06,456 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:06,459 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.42秒
2025-08-05 20:25:06,460 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:06,467 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:06,516 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:15 CST)" executed successfully
2025-08-05 20:25:15,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:25 CST)" (scheduled at 2025-08-05 20:25:15.845013+08:00)
2025-08-05 20:25:15,970 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:15,971 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:16,109 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:16,463 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:16,464 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:25:16,465 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:16,465 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:16,513 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:25 CST)" executed successfully
2025-08-05 20:25:23,362 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=2, 总计=4
2025-08-05 20:25:23,363 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:25:23,365 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:25:25,858 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:35 CST)" (scheduled at 2025-08-05 20:25:25.845013+08:00)
2025-08-05 20:25:25,902 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:25,903 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:26,040 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:26,398 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:26,399 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:25:26,399 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:26,399 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:26,455 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:35 CST)" executed successfully
2025-08-05 20:25:30,868 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:25:35,612 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:26:35 CST)" (scheduled at 2025-08-05 20:25:35.602103+08:00)
2025-08-05 20:25:35,657 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:25:35,657 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:35,658 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:25:35,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:26:05 CST)" (scheduled at 2025-08-05 20:25:35.722714+08:00)
2025-08-05 20:25:35,774 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:25:35,775 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:35,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:45 CST)" (scheduled at 2025-08-05 20:25:35.845013+08:00)
2025-08-05 20:25:35,874 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:25:35,874 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:25:35,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:35,901 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:35,917 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:25:36,051 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:36,668 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:25:36,669 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:25:36,669 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.75秒
2025-08-05 20:25:36,669 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:25:36,673 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:36,718 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:26:05 CST)" executed successfully
2025-08-05 20:25:36,899 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:36,899 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.85秒
2025-08-05 20:25:36,900 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:36,900 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:36,964 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:45 CST)" executed successfully
2025-08-05 20:25:37,408 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:25:37,408 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.75秒
2025-08-05 20:25:37,409 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:25:37,410 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:37,456 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:26:35 CST)" executed successfully
2025-08-05 20:25:45,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:55 CST)" (scheduled at 2025-08-05 20:25:45.845013+08:00)
2025-08-05 20:25:45,898 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:45,898 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:46,038 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:46,386 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:46,386 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:25:46,387 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:46,387 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:46,433 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:25:55 CST)" executed successfully
2025-08-05 20:25:53,371 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:25:55,855 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:05 CST)" (scheduled at 2025-08-05 20:25:55.845013+08:00)
2025-08-05 20:25:55,903 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:25:55,904 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:25:56,033 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:25:56,381 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:25:56,381 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:25:56,382 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:25:56,382 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:25:56,431 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:05 CST)" executed successfully
2025-08-05 20:26:00,083 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:26:05,733 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:26:35 CST)" (scheduled at 2025-08-05 20:26:05.722714+08:00)
2025-08-05 20:26:05,778 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:26:05,778 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:05,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:15 CST)" (scheduled at 2025-08-05 20:26:05.845013+08:00)
2025-08-05 20:26:05,902 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:05,902 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:05,907 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:26:06,038 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:06,256 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:26:06,256 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:26:06,257 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:26:06,258 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:26:06,258 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:06,304 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:26:35 CST)" executed successfully
2025-08-05 20:26:06,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:06,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:26:06,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:06,398 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:06,447 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:15 CST)" executed successfully
2025-08-05 20:26:15,850 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:25 CST)" (scheduled at 2025-08-05 20:26:15.845013+08:00)
2025-08-05 20:26:15,894 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:15,894 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:16,027 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:16,386 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:16,386 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:26:16,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:16,387 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:16,431 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:25 CST)" executed successfully
2025-08-05 20:26:23,374 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=2, 总计=4
2025-08-05 20:26:23,375 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:26:23,376 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:26:25,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:35 CST)" (scheduled at 2025-08-05 20:26:25.845013+08:00)
2025-08-05 20:26:25,904 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:25,905 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:26,038 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:26,386 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:26,386 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:26:26,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:26,387 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:26,433 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:35 CST)" executed successfully
2025-08-05 20:26:30,266 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:26:35,602 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:27:35 CST)" (scheduled at 2025-08-05 20:26:35.602103+08:00)
2025-08-05 20:26:35,648 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:26:35,649 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:35,653 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:26:35,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:27:05 CST)" (scheduled at 2025-08-05 20:26:35.722714+08:00)
2025-08-05 20:26:35,774 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:26:35,774 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:35,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:45 CST)" (scheduled at 2025-08-05 20:26:35.845013+08:00)
2025-08-05 20:26:35,902 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:35,903 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:35,905 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:26:35,964 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:26:35,967 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:26:36,037 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:36,259 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:26:36,259 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:26:36,259 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:26:36,260 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:26:36,260 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:36,309 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:27:05 CST)" executed successfully
2025-08-05 20:26:36,392 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:36,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:26:36,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:36,393 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:36,437 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:45 CST)" executed successfully
2025-08-05 20:26:36,630 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:26:36,631 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.98秒
2025-08-05 20:26:36,634 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:26:36,635 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:36,682 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:27:35 CST)" executed successfully
2025-08-05 20:26:45,849 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:55 CST)" (scheduled at 2025-08-05 20:26:45.845013+08:00)
2025-08-05 20:26:45,893 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:45,893 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:46,026 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:46,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:46,379 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:26:46,380 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:46,380 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:46,425 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:26:55 CST)" executed successfully
2025-08-05 20:26:53,379 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:26:55,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:05 CST)" (scheduled at 2025-08-05 20:26:55.845013+08:00)
2025-08-05 20:26:55,895 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:26:55,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:26:56,025 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:26:56,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:26:56,371 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:26:56,372 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:26:56,373 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:26:56,425 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:05 CST)" executed successfully
2025-08-05 20:27:00,453 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:27:05,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:27:35 CST)" (scheduled at 2025-08-05 20:27:05.722714+08:00)
2025-08-05 20:27:05,772 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:27:05,772 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:05,855 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:15 CST)" (scheduled at 2025-08-05 20:27:05.845013+08:00)
2025-08-05 20:27:05,900 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:05,901 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:05,901 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:27:06,034 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:06,253 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:27:06,254 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:27:06,254 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:27:06,255 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:27:06,256 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:06,299 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:27:35 CST)" executed successfully
2025-08-05 20:27:06,391 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:06,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:27:06,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:06,393 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:06,440 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:15 CST)" executed successfully
2025-08-05 20:27:15,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:25 CST)" (scheduled at 2025-08-05 20:27:15.845013+08:00)
2025-08-05 20:27:15,895 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:15,911 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:16,047 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:16,407 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:16,407 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:27:16,407 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:16,408 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:16,453 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:25 CST)" executed successfully
2025-08-05 20:27:23,389 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=2, 总计=4
2025-08-05 20:27:23,389 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:27:23,390 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:27:25,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:35 CST)" (scheduled at 2025-08-05 20:27:25.845013+08:00)
2025-08-05 20:27:25,892 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:25,892 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:26,024 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:26,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:26,376 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:27:26,376 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:26,377 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:26,422 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:35 CST)" executed successfully
2025-08-05 20:27:30,674 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:27:35,616 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:28:35 CST)" (scheduled at 2025-08-05 20:27:35.602103+08:00)
2025-08-05 20:27:35,661 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:27:35,661 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:35,662 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:27:35,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:28:05 CST)" (scheduled at 2025-08-05 20:27:35.722714+08:00)
2025-08-05 20:27:35,769 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:27:35,769 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:35,849 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:45 CST)" (scheduled at 2025-08-05 20:27:35.845013+08:00)
2025-08-05 20:27:35,849 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], next run at: 2025-08-05 21:27:35 CST)" (scheduled at 2025-08-05 20:27:35.847280+08:00)
2025-08-05 20:27:35,896 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:35,896 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:35,902 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:27:35,921 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:27:35,921 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:27:36,030 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:36,033 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:cleanup_expired_mail_templates
2025-08-05 20:27:36,033 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:36,035 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行清理过期邮件模板任务
2025-08-05 20:27:36,166 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 20:27:36,216 - mail_service_distributed - INFO - 清理过期邮件模板完成: 0 个
2025-08-05 20:27:36,216 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 清理过期邮件模板完成，删除了 0 个模板
2025-08-05 20:27:36,217 - scheduler_health_checks - INFO - 任务 cleanup_expired_mail_templates 执行成功，耗时: 0.18秒
2025-08-05 20:27:36,217 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 直接执行成功
2025-08-05 20:27:36,217 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:36,261 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:27:36,261 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:27:36,262 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:27:36,262 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:27:36,262 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:36,268 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], next run at: 2025-08-05 21:27:35 CST)" executed successfully
2025-08-05 20:27:36,308 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:28:05 CST)" executed successfully
2025-08-05 20:27:36,395 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:36,395 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:27:36,396 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:36,396 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:36,441 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:45 CST)" executed successfully
2025-08-05 20:27:37,636 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:27:37,637 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.98秒
2025-08-05 20:27:37,637 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:27:37,638 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:37,687 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:28:35 CST)" executed successfully
2025-08-05 20:27:45,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:55 CST)" (scheduled at 2025-08-05 20:27:45.845013+08:00)
2025-08-05 20:27:45,898 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:45,899 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:46,032 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:46,388 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:46,388 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:27:46,389 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:46,389 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:46,434 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:27:55 CST)" executed successfully
2025-08-05 20:27:53,405 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:27:55,850 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:05 CST)" (scheduled at 2025-08-05 20:27:55.845013+08:00)
2025-08-05 20:27:56,010 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:27:56,010 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:27:56,374 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:27:56,965 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:27:56,965 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.59秒
2025-08-05 20:27:56,966 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:27:56,967 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:27:57,014 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:05 CST)" executed successfully
2025-08-05 20:28:00,883 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:28:05,737 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:28:35 CST)" (scheduled at 2025-08-05 20:28:05.722714+08:00)
2025-08-05 20:28:05,781 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:28:05,782 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:05,847 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:15 CST)" (scheduled at 2025-08-05 20:28:05.845013+08:00)
2025-08-05 20:28:05,892 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:05,892 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:05,917 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:28:06,024 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:06,274 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:28:06,274 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:28:06,276 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:28:06,278 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:28:06,278 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:06,324 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:28:35 CST)" executed successfully
2025-08-05 20:28:06,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:06,372 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:28:06,372 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:06,372 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:06,417 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:15 CST)" executed successfully
2025-08-05 20:28:15,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:25 CST)" (scheduled at 2025-08-05 20:28:15.845013+08:00)
2025-08-05 20:28:15,901 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:15,902 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:16,040 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:16,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:16,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:28:16,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:16,395 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:16,439 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:25 CST)" executed successfully
2025-08-05 20:28:23,403 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=3, 总计=5
2025-08-05 20:28:23,403 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:28:23,418 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:28:25,845 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:35 CST)" (scheduled at 2025-08-05 20:28:25.845013+08:00)
2025-08-05 20:28:25,890 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:25,890 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:26,022 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:26,370 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:26,371 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:28:26,371 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:26,372 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:26,418 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:35 CST)" executed successfully
2025-08-05 20:28:30,081 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:28:35,602 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:29:35 CST)" (scheduled at 2025-08-05 20:28:35.602103+08:00)
2025-08-05 20:28:35,647 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:28:35,647 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:35,648 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:28:35,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:29:05 CST)" (scheduled at 2025-08-05 20:28:35.722714+08:00)
2025-08-05 20:28:35,775 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:28:35,776 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:35,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:45 CST)" (scheduled at 2025-08-05 20:28:35.845013+08:00)
2025-08-05 20:28:35,887 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:28:35,887 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:28:35,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:35,899 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:35,918 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:28:36,031 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:36,277 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:28:36,277 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:28:36,277 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:28:36,278 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:28:36,278 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:36,323 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:29:05 CST)" executed successfully
2025-08-05 20:28:36,389 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:36,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:28:36,390 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:36,391 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:36,435 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:45 CST)" executed successfully
2025-08-05 20:28:37,502 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:28:37,502 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.85秒
2025-08-05 20:28:37,502 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:28:37,503 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:37,548 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:29:35 CST)" executed successfully
2025-08-05 20:28:45,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:55 CST)" (scheduled at 2025-08-05 20:28:45.845013+08:00)
2025-08-05 20:28:45,900 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:45,901 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:46,032 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:46,380 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:46,380 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:28:46,380 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:46,381 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:46,425 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:28:55 CST)" executed successfully
2025-08-05 20:28:53,421 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:28:55,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:05 CST)" (scheduled at 2025-08-05 20:28:55.845013+08:00)
2025-08-05 20:28:55,900 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:28:55,901 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:28:56,035 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:28:56,390 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:28:56,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:28:56,390 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:28:56,391 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:28:56,438 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:05 CST)" executed successfully
2025-08-05 20:29:00,266 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:29:05,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:29:35 CST)" (scheduled at 2025-08-05 20:29:05.722714+08:00)
2025-08-05 20:29:05,779 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:29:05,779 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:05,857 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:15 CST)" (scheduled at 2025-08-05 20:29:05.845013+08:00)
2025-08-05 20:29:05,905 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:05,906 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:05,911 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:29:06,041 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:06,261 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:29:06,262 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:29:06,262 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:29:06,263 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:29:06,263 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:06,311 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:29:35 CST)" executed successfully
2025-08-05 20:29:06,419 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:06,419 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 20:29:06,419 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:06,420 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:06,466 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:15 CST)" executed successfully
2025-08-05 20:29:15,857 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:25 CST)" (scheduled at 2025-08-05 20:29:15.845013+08:00)
2025-08-05 20:29:15,905 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:15,906 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:16,045 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:16,411 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:16,411 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:29:16,411 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:16,412 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:16,459 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:25 CST)" executed successfully
2025-08-05 20:29:23,412 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=3, 总计=5
2025-08-05 20:29:23,413 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:29:23,428 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:29:25,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:35 CST)" (scheduled at 2025-08-05 20:29:25.845013+08:00)
2025-08-05 20:29:25,897 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:25,898 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:26,039 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:26,424 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:26,424 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.38秒
2025-08-05 20:29:26,425 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:26,425 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:26,471 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:35 CST)" executed successfully
2025-08-05 20:29:30,464 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:29:35,608 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:30:35 CST)" (scheduled at 2025-08-05 20:29:35.602103+08:00)
2025-08-05 20:29:35,654 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:29:35,654 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:35,654 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:29:35,734 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:30:05 CST)" (scheduled at 2025-08-05 20:29:35.722714+08:00)
2025-08-05 20:29:35,783 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:29:35,783 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:35,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:45 CST)" (scheduled at 2025-08-05 20:29:35.845013+08:00)
2025-08-05 20:29:35,915 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:29:35,938 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:35,939 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:35,942 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:29:35,943 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:29:36,073 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:36,269 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:29:36,269 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:29:36,270 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:29:36,270 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:29:36,270 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:36,314 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:30:05 CST)" executed successfully
2025-08-05 20:29:36,418 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:36,419 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:29:36,419 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:36,420 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:36,464 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:45 CST)" executed successfully
2025-08-05 20:29:36,940 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:29:36,940 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.29秒
2025-08-05 20:29:36,941 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:29:36,942 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:36,985 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:30:35 CST)" executed successfully
2025-08-05 20:29:45,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:55 CST)" (scheduled at 2025-08-05 20:29:45.845013+08:00)
2025-08-05 20:29:45,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:45,900 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:46,039 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:46,385 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:46,385 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:29:46,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:46,386 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:46,440 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:29:55 CST)" executed successfully
2025-08-05 20:29:53,437 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:29:55,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:05 CST)" (scheduled at 2025-08-05 20:29:55.845013+08:00)
2025-08-05 20:29:55,896 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:29:55,897 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:29:56,390 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:29:57,054 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:29:57,054 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.66秒
2025-08-05 20:29:57,054 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:29:57,055 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:29:57,209 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:05 CST)" executed successfully
2025-08-05 20:30:00,727 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:30:05,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:30:35 CST)" (scheduled at 2025-08-05 20:30:05.722714+08:00)
2025-08-05 20:30:05,809 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:30:05,810 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:05,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:15 CST)" (scheduled at 2025-08-05 20:30:05.845013+08:00)
2025-08-05 20:30:05,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:05,900 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:05,941 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:30:06,042 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:06,304 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:30:06,305 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:30:06,305 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:30:06,306 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:30:06,307 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:06,352 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:30:35 CST)" executed successfully
2025-08-05 20:30:06,396 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:06,397 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:30:06,397 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:06,398 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:06,447 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:15 CST)" executed successfully
2025-08-05 20:30:15,847 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:25 CST)" (scheduled at 2025-08-05 20:30:15.845013+08:00)
2025-08-05 20:30:15,892 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:15,893 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:16,036 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:16,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:16,383 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:30:16,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:16,386 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:16,430 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:25 CST)" executed successfully
2025-08-05 20:30:23,423 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=3, 总计=5
2025-08-05 20:30:23,424 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:30:23,439 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:30:25,857 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:35 CST)" (scheduled at 2025-08-05 20:30:25.845013+08:00)
2025-08-05 20:30:25,903 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:25,903 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:26,034 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:26,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:26,380 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:30:26,380 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:26,380 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:26,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:35 CST)" executed successfully
2025-08-05 20:30:30,316 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:30:35,607 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:31:35 CST)" (scheduled at 2025-08-05 20:30:35.602103+08:00)
2025-08-05 20:30:35,651 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:30:35,652 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:35,652 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:30:35,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:31:05 CST)" (scheduled at 2025-08-05 20:30:35.722714+08:00)
2025-08-05 20:30:35,775 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:30:35,775 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:35,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:45 CST)" (scheduled at 2025-08-05 20:30:35.845013+08:00)
2025-08-05 20:30:35,894 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:30:35,894 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:30:35,902 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:35,902 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:35,913 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:30:36,035 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:36,424 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:36,425 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.39秒
2025-08-05 20:30:36,425 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:36,426 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:36,472 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:45 CST)" executed successfully
2025-08-05 20:30:37,446 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:30:37,446 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.79秒
2025-08-05 20:30:37,447 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:30:37,447 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:37,493 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:31:35 CST)" executed successfully
2025-08-05 20:30:40,917 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 20:30:40,918 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:30:41,103 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:30:41,467 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:30:41,467 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:30:41,468 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 5.56秒
2025-08-05 20:30:41,469 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:30:41,469 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:41,516 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:31:05 CST)" executed successfully
2025-08-05 20:30:45,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:55 CST)" (scheduled at 2025-08-05 20:30:45.845013+08:00)
2025-08-05 20:30:45,914 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:45,915 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:46,052 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:46,413 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:46,413 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:30:46,413 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:46,414 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:46,462 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:30:55 CST)" executed successfully
2025-08-05 20:30:53,451 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:30:55,845 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:05 CST)" (scheduled at 2025-08-05 20:30:55.845013+08:00)
2025-08-05 20:30:55,889 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:30:55,889 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:30:56,025 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:30:56,387 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:30:56,388 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:30:56,388 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:30:56,389 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:30:56,434 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:05 CST)" executed successfully
2025-08-05 20:31:00,492 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:31:05,726 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:31:35 CST)" (scheduled at 2025-08-05 20:31:05.722714+08:00)
2025-08-05 20:31:05,770 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:31:05,771 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:05,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:15 CST)" (scheduled at 2025-08-05 20:31:05.845013+08:00)
2025-08-05 20:31:05,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:05,899 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:05,903 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:31:06,031 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:06,269 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:31:06,270 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:31:06,270 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 20:31:06,271 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:31:06,271 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:06,319 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:31:35 CST)" executed successfully
2025-08-05 20:31:06,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:06,382 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:31:06,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:06,384 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:06,432 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:15 CST)" executed successfully
2025-08-05 20:31:15,853 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:25 CST)" (scheduled at 2025-08-05 20:31:15.845013+08:00)
2025-08-05 20:31:15,899 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:15,900 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:16,035 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:16,390 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:16,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:31:16,391 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:16,391 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:16,436 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:25 CST)" executed successfully
2025-08-05 20:31:23,425 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=3, 总计=5
2025-08-05 20:31:23,426 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:31:23,456 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:31:25,845 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:35 CST)" (scheduled at 2025-08-05 20:31:25.845013+08:00)
2025-08-05 20:31:25,890 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:25,890 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:26,022 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:26,386 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:26,387 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:31:26,387 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:26,388 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:26,434 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:35 CST)" executed successfully
2025-08-05 20:31:30,705 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:31:35,617 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:32:35 CST)" (scheduled at 2025-08-05 20:31:35.602103+08:00)
2025-08-05 20:31:35,661 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:31:35,662 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:35,662 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:31:35,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:32:05 CST)" (scheduled at 2025-08-05 20:31:35.722714+08:00)
2025-08-05 20:31:35,770 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:31:35,770 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:35,849 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:45 CST)" (scheduled at 2025-08-05 20:31:35.845013+08:00)
2025-08-05 20:31:35,894 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:35,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:35,915 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:31:35,956 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:31:35,957 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:31:36,030 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:36,267 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:31:36,267 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:31:36,267 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:31:36,268 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:31:36,269 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:36,314 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:32:05 CST)" executed successfully
2025-08-05 20:31:36,387 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:36,387 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:31:36,389 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:36,390 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:36,436 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:45 CST)" executed successfully
2025-08-05 20:31:36,751 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:31:36,751 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.09秒
2025-08-05 20:31:36,753 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:31:36,753 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:36,799 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:32:35 CST)" executed successfully
2025-08-05 20:31:45,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:55 CST)" (scheduled at 2025-08-05 20:31:45.845013+08:00)
2025-08-05 20:31:45,897 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:45,897 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:46,029 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:46,391 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:46,391 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:31:46,392 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:46,392 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:46,438 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:31:55 CST)" executed successfully
2025-08-05 20:31:53,473 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:31:55,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:05 CST)" (scheduled at 2025-08-05 20:31:55.845013+08:00)
2025-08-05 20:31:55,904 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:31:55,905 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:31:56,039 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:31:56,398 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:31:56,400 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:31:56,409 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:31:56,411 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:31:56,457 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:05 CST)" executed successfully
2025-08-05 20:32:00,883 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:32:05,736 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:32:35 CST)" (scheduled at 2025-08-05 20:32:05.722714+08:00)
2025-08-05 20:32:05,782 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:32:05,783 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:05,860 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:15 CST)" (scheduled at 2025-08-05 20:32:05.845013+08:00)
2025-08-05 20:32:05,912 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:05,912 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:05,921 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:32:06,048 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:06,274 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:32:06,275 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:32:06,275 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.35秒
2025-08-05 20:32:06,276 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:32:06,276 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:06,323 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:32:35 CST)" executed successfully
2025-08-05 20:32:06,404 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:06,407 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:32:06,408 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:06,412 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:06,466 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:15 CST)" executed successfully
2025-08-05 20:32:15,858 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:25 CST)" (scheduled at 2025-08-05 20:32:15.845013+08:00)
2025-08-05 20:32:15,906 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:15,906 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:16,044 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:16,401 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:16,401 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:32:16,402 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:16,404 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:16,454 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:25 CST)" executed successfully
2025-08-05 20:32:23,434 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=2, 可用=3, 总计=5
2025-08-05 20:32:23,435 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:32:23,480 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:32:25,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:35 CST)" (scheduled at 2025-08-05 20:32:25.845013+08:00)
2025-08-05 20:32:25,908 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:25,909 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:26,040 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:26,401 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:26,402 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:32:26,402 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:26,403 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:26,447 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:35 CST)" executed successfully
2025-08-05 20:32:30,065 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:32:35,610 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:33:35 CST)" (scheduled at 2025-08-05 20:32:35.602103+08:00)
2025-08-05 20:32:35,656 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:push_online
2025-08-05 20:32:35,657 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:35,658 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行推送在线人数任务
2025-08-05 20:32:35,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:33:05 CST)" (scheduled at 2025-08-05 20:32:35.722714+08:00)
2025-08-05 20:32:35,773 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:32:35,773 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:35,847 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:45 CST)" (scheduled at 2025-08-05 20:32:35.845013+08:00)
2025-08-05 20:32:35,895 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:35,895 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:35,898 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:32:35,898 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:32:35,910 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:32:36,029 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:36,268 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:32:36,269 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:32:36,269 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:32:36,270 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:32:36,270 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:36,318 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:33:05 CST)" executed successfully
2025-08-05 20:32:36,394 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:36,394 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 20:32:36,395 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:36,396 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:36,440 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:45 CST)" executed successfully
2025-08-05 20:32:37,384 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 在线人数推送完成，当前在线: 0
2025-08-05 20:32:37,385 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.73秒
2025-08-05 20:32:37,385 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 20:32:37,385 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:37,433 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 20:33:35 CST)" executed successfully
2025-08-05 20:32:45,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:55 CST)" (scheduled at 2025-08-05 20:32:45.845013+08:00)
2025-08-05 20:32:45,894 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:45,894 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:46,029 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:46,382 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:46,383 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 20:32:46,383 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:46,384 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:46,428 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:32:55 CST)" executed successfully
2025-08-05 20:32:53,488 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:32:55,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:05 CST)" (scheduled at 2025-08-05 20:32:55.845013+08:00)
2025-08-05 20:32:55,893 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:32:55,893 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:32:56,024 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:32:56,383 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:32:56,384 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:32:56,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:32:56,386 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:32:56,435 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:05 CST)" executed successfully
2025-08-05 20:33:00,319 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:33:05,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:33:35 CST)" (scheduled at 2025-08-05 20:33:05.722714+08:00)
2025-08-05 20:33:05,775 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 20:33:05,776 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:33:05,852 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:15 CST)" (scheduled at 2025-08-05 20:33:05.845013+08:00)
2025-08-05 20:33:05,901 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:33:05,902 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:33:05,909 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 开始执行怪物冷却持久化任务
2025-08-05 20:33:06,035 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:33:06,268 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:33:06,269 - scheduler_tasks_unified - INFO - [定时任务] Worker 5464 怪物冷却持久化完成
2025-08-05 20:33:06,269 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 20:33:06,270 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 20:33:06,270 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:33:06,316 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 20:33:35 CST)" executed successfully
2025-08-05 20:33:06,393 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:33:06,393 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:33:06,394 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:33:06,394 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:33:06,440 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:15 CST)" executed successfully
2025-08-05 20:33:15,847 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:25 CST)" (scheduled at 2025-08-05 20:33:15.845013+08:00)
2025-08-05 20:33:15,893 - distributed_lock - INFO - Worker 5464 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 20:33:15,893 - distributed_task - INFO - Worker 5464 成功获取锁并执行任务: direct_wrapper
2025-08-05 20:33:16,032 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 20:33:16,391 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 20:33:16,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 20:33:16,392 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 20:33:16,392 - distributed_task - INFO - Worker 5464 任务执行完成: direct_wrapper
2025-08-05 20:33:16,437 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 20:33:25 CST)" executed successfully
2025-08-05 20:33:23,355 - game_server - INFO - 关闭服务器... (Worker: 5464)
2025-08-05 20:33:23,357 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 20:33:23,447 - ConnectionManager - INFO - Redis连接池状态 (Worker 5464): 使用中=3, 可用=2, 总计=5
2025-08-05 20:33:23,448 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 5464): 连接中
2025-08-05 20:33:23,495 - ConnectionManager - INFO - 连接状态 (Worker 5464): 活跃连接数=0, 用户数=0
2025-08-05 20:33:23,717 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 20:33:23,718 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 20:33:23,719 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 20:33:23,720 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 20:33:23,721 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 20:33:23,722 - game_server - INFO - 日志清理管理器已停止
2025-08-05 20:33:23,723 - game_server_scheduler_integration - INFO - 关闭游戏服务器集成调度器...
2025-08-05 20:33:23,724 - unified_scheduler_manager - INFO - 停止统一调度器...
2025-08-05 20:33:23,725 - scheduler_manager - INFO - [SchedulerManager] APScheduler shutdown.
2025-08-05 20:33:23,725 - unified_scheduler_manager - INFO - 统一调度器已停止
2025-08-05 20:33:23,728 - game_server_scheduler_integration - INFO - 游戏服务器集成调度器已关闭
2025-08-05 20:33:23,730 - game_server - INFO - 统一调度器已关闭
2025-08-05 20:33:23,731 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-05 20:33:23,732 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 5464)
2025-08-05 20:33:23,777 - game_server - INFO - 服务器资源已清理 (Worker: 5464)
