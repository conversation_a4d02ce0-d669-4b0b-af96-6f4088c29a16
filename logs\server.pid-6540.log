2025-08-05 21:06:25,063 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:25,937 - models - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:25,967 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:26,365 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:26,380 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:26,392 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:26,406 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:26,419 - auth - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,117 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,170 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,230 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,245 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,256 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,257 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: c0afb66f)
2025-08-05 21:06:29,269 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,313 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,329 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,343 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,356 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,455 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,467 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,479 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,489 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,500 - game_server - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:29,502 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 21:06:29,503 - msgManager - INFO - Monster handlers registered
2025-08-05 21:06:29,504 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 21:06:29,505 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 21:06:29,514 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 21:06:29,516 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 21:06:29,517 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 35cf5641)
2025-08-05 21:06:29,564 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 21:06:29,610 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 21:06:29,610 - game_server - INFO - 模板引擎初始化成功
2025-08-05 21:06:29,613 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:06:29,614 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 6540)
2025-08-05 21:06:29,615 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 21:06:29,777 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:06:29,788 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 21:06:30,312 - ConnectionManager - INFO - 后台任务已启动 (Worker 6540)
2025-08-05 21:06:30,313 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 6540)
2025-08-05 21:06:30,320 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:06:30,321 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:06:30,322 - game_server - INFO - 游戏配置加载完成 (Worker: 6540)
2025-08-05 21:06:30,323 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:06:36,329 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 21:06:36,329 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=2, 可用=0, 总计=2
2025-08-05 21:06:36,330 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 6540): 2/2 (100.0%)
2025-08-05 21:06:36,330 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:06:36,453 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 35cf5641)
2025-08-05 21:06:36,453 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 35cf5641)
2025-08-05 21:06:36,461 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:36,463 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 21:06:36,464 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 21:06:36,471 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 21:06:36,472 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 21:06:36,473 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 21:06:36,473 - ConnectionManager - INFO - Worker 6540 开始消费广播消息，消费者标签: ctag1.089f5ccb00b7493aa4b4e620e422eaec
2025-08-05 21:06:36,619 - ConnectionManager - INFO - Worker 6540 开始消费个人消息，消费者标签: ctag1.f35f588868a6485fa7bfa3deeb993a17
2025-08-05 21:06:36,711 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 6540)
2025-08-05 21:06:36,781 - distributed_lock - INFO - Worker 6540 成功获取锁: scheduler_initialization
2025-08-05 21:06:36,782 - game_server_scheduler_integration - INFO - Worker 6540 获得调度器初始化权限
2025-08-05 21:06:36,786 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 21:06:36,787 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 21:06:36,787 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 21:06:36,787 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 21:06:36,788 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 21:06:36,788 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 21:06:36,788 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 21:06:36,789 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 21:06:36,789 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 21:06:36,789 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 21:06:36,790 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 21:06:36,790 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 21:06:36,790 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 21:06:36,791 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 21:06:36,791 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 21:06:36,791 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 21:06:36,871 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 21:06:36,872 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 21:06:36,872 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 21:06:36,873 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 21:06:37,158 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 21:06:37,245 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 21:06:37,246 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 21:06:37,246 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 21:06:37,246 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 21:06:37,247 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 21:06:37,247 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:06:37,327 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 21:06:37,454 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:06:37,455 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 21:06:37,455 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 21:06:37,456 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 21:06:37,457 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 21:06:37,458 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 21:06:37,462 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,462 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:06:37,462 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 21:06:37,464 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,464 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:06:37,464 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 21:06:37,589 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,590 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:06:37,590 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 21:06:37,720 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,721 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:06:37,721 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 21:06:37,722 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,722 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:06:37,722 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 21:06:37,723 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 21:06:37,723 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:06:37,723 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 21:06:37,724 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 21:06:37,725 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,726 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,726 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,727 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,727 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,728 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 21:06:37,728 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 21:06:37,729 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 21:06:37,730 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 21:06:37,733 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 21:06:37,733 - game_server_scheduler_integration - INFO - Worker 6540 调度器初始化成功
2025-08-05 21:06:37,774 - game_server - INFO - 统一调度器初始化成功 (Worker: 6540)
2025-08-05 21:06:37,782 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:37,783 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 21:06:37,783 - game_server - INFO - 日志清理管理器已启动 (Worker: 6540)
2025-08-05 21:06:37,784 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 21:06:37,784 - game_server - INFO - Monster cooldown manager initialized (Worker: 6540)
2025-08-05 21:06:37,921 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 21:06:37,922 - game_server - INFO - 公会系统初始化成功 (Worker: 6540)
2025-08-05 21:06:37,922 - game_server - INFO - 邮件系统初始化成功 (Worker: 6540)
2025-08-05 21:06:37,924 - game_server - INFO - 商店系统初始化成功 (Worker: 6540)
2025-08-05 21:06:37,924 - game_server - INFO - 初始化完成 (Worker: 6540)
2025-08-05 21:06:47,339 - CacheInvalidationManager - INFO - 日志系统初始化成功 (进程 ID: 6540)
2025-08-05 21:06:47,381 - CacheInvalidationManager - INFO - 缓存失效通知管理器初始化完成 (Worker 6540)
2025-08-05 21:06:47,470 - game_server - INFO - 获取用户信息，用户 dsadjdj23，返回字段: ['id', 'created_at', 'nickname']
2025-08-05 21:06:47,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:06:57 CST)" (scheduled at 2025-08-05 21:06:47.720644+08:00)
2025-08-05 21:06:47,764 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:06:47,765 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:06:47,887 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:06:48,215 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:06:48,215 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:06:48,215 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:06:48,216 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:06:48,263 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:06:57 CST)" executed successfully
2025-08-05 21:06:57,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:07 CST)" (scheduled at 2025-08-05 21:06:57.720644+08:00)
2025-08-05 21:06:57,764 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:06:57,765 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:06:57,888 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:06:58,221 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:06:58,221 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:06:58,221 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:06:58,222 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:06:58,267 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:07 CST)" executed successfully
2025-08-05 21:07:00,334 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:07:00,519 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:07:07,598 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:07:37 CST)" (scheduled at 2025-08-05 21:07:07.589618+08:00)
2025-08-05 21:07:07,639 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:07:07,639 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:07,724 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:17 CST)" (scheduled at 2025-08-05 21:07:07.720644+08:00)
2025-08-05 21:07:07,770 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:07:07,894 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:07,895 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:08,019 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:08,096 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:07:08,097 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:07:08,098 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:07:08,098 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:07:08,099 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:08,138 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:07:37 CST)" executed successfully
2025-08-05 21:07:08,367 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:08,367 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:07:08,368 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:08,368 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:08,410 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:17 CST)" executed successfully
2025-08-05 21:07:11,744 - auth - INFO - 用户 dsadjdj23 登录成功，token: eyJhbGciOi...
2025-08-05 21:07:17,732 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:27 CST)" (scheduled at 2025-08-05 21:07:17.720644+08:00)
2025-08-05 21:07:17,774 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:17,774 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:17,898 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:18,237 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:18,238 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:18,239 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:18,239 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:18,280 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:27 CST)" executed successfully
2025-08-05 21:07:27,727 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:37 CST)" (scheduled at 2025-08-05 21:07:27.720644+08:00)
2025-08-05 21:07:27,770 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:27,770 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:27,894 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:28,236 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:28,237 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:28,239 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:28,241 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:28,282 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:37 CST)" executed successfully
2025-08-05 21:07:30,335 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:07:30,671 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:07:30,948 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:07:36,339 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=2, 总计=5
2025-08-05 21:07:36,339 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:07:37,479 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:08:37 CST)" (scheduled at 2025-08-05 21:07:37.463923+08:00)
2025-08-05 21:07:37,522 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:07:37,522 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:37,523 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:07:37,524 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 21:07:37,602 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:07 CST)" (scheduled at 2025-08-05 21:07:37.589618+08:00)
2025-08-05 21:07:37,646 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:07:37,646 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:37,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:47 CST)" (scheduled at 2025-08-05 21:07:37.720644+08:00)
2025-08-05 21:07:37,767 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:07:37,768 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:37,769 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:37,894 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:38,088 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:07:38,088 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:07:38,089 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.32秒
2025-08-05 21:07:38,089 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:07:38,090 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:38,135 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:07 CST)" executed successfully
2025-08-05 21:07:38,239 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:38,239 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:38,240 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:38,240 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:38,282 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:47 CST)" executed successfully
2025-08-05 21:07:38,444 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:07:38,445 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:07:38,447 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 1
2025-08-05 21:07:38,447 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 0.92秒
2025-08-05 21:07:38,447 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:07:38,448 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:38,491 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:08:37 CST)" executed successfully
2025-08-05 21:07:39,103 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:07:39,104 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:07:47,734 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:57 CST)" (scheduled at 2025-08-05 21:07:47.720644+08:00)
2025-08-05 21:07:47,774 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:47,775 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:47,900 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:48,239 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:48,239 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:48,240 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:48,241 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:48,283 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:07:57 CST)" executed successfully
2025-08-05 21:07:57,734 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:07 CST)" (scheduled at 2025-08-05 21:07:57.720644+08:00)
2025-08-05 21:07:57,774 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:07:57,774 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:07:57,891 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:07:58,231 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:07:58,231 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:07:58,232 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:07:58,232 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:07:58,276 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:07 CST)" executed successfully
2025-08-05 21:08:00,178 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:00,344 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:00,912 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:07,598 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:37 CST)" (scheduled at 2025-08-05 21:08:07.589618+08:00)
2025-08-05 21:08:07,639 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:08:07,639 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:07,721 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:17 CST)" (scheduled at 2025-08-05 21:08:07.720644+08:00)
2025-08-05 21:08:07,765 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:08:07,771 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:07,771 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:07,896 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:08,089 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:08:08,090 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:08:08,092 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:08:08,093 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:08:08,094 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:08,139 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:08:37 CST)" executed successfully
2025-08-05 21:08:08,232 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:08,233 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:08:08,234 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:08,234 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:08,282 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:17 CST)" executed successfully
2025-08-05 21:08:17,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:27 CST)" (scheduled at 2025-08-05 21:08:17.720644+08:00)
2025-08-05 21:08:17,771 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:17,772 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:17,896 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:18,251 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:18,251 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:08:18,252 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:18,253 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:18,294 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:27 CST)" executed successfully
2025-08-05 21:08:27,721 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:37 CST)" (scheduled at 2025-08-05 21:08:27.720644+08:00)
2025-08-05 21:08:27,773 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:27,773 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:27,900 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:28,242 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:28,242 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:08:28,242 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:28,244 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:28,286 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:37 CST)" executed successfully
2025-08-05 21:08:30,221 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:30,346 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:30,469 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:08:36,347 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=2, 总计=5
2025-08-05 21:08:36,348 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:08:37,466 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:09:37 CST)" (scheduled at 2025-08-05 21:08:37.463923+08:00)
2025-08-05 21:08:37,507 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:08:37,507 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:37,508 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:08:37,604 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:07 CST)" (scheduled at 2025-08-05 21:08:37.589618+08:00)
2025-08-05 21:08:37,650 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:08:37,650 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:37,682 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:08:37,682 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:08:37,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:47 CST)" (scheduled at 2025-08-05 21:08:37.720644+08:00)
2025-08-05 21:08:37,764 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:37,765 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:37,776 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:08:37,883 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:38,113 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:08:38,113 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:08:38,113 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:08:38,114 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:08:38,114 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:38,161 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:07 CST)" executed successfully
2025-08-05 21:08:38,222 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:38,222 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:08:38,222 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:38,223 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:38,270 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:47 CST)" executed successfully
2025-08-05 21:08:39,402 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 1
2025-08-05 21:08:39,402 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.89秒
2025-08-05 21:08:39,403 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:08:39,403 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:39,444 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:09:37 CST)" executed successfully
2025-08-05 21:08:47,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:57 CST)" (scheduled at 2025-08-05 21:08:47.720644+08:00)
2025-08-05 21:08:47,770 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:47,770 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:47,893 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:48,233 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:48,233 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:08:48,234 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:48,234 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:48,294 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:08:57 CST)" executed successfully
2025-08-05 21:08:57,722 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:07 CST)" (scheduled at 2025-08-05 21:08:57.720644+08:00)
2025-08-05 21:08:57,767 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:08:57,767 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:08:57,887 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:08:58,231 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:08:58,231 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:08:58,232 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:08:58,232 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:08:58,274 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:07 CST)" executed successfully
2025-08-05 21:09:00,347 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:00,470 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:00,730 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:07,602 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:37 CST)" (scheduled at 2025-08-05 21:09:07.589618+08:00)
2025-08-05 21:09:07,642 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:09:07,643 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:07,721 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:17 CST)" (scheduled at 2025-08-05 21:09:07.720644+08:00)
2025-08-05 21:09:07,761 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:09:07,769 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:07,769 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:07,895 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:08,089 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:09:08,089 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:09:08,090 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:09:08,090 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:09:08,092 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:08,133 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:09:37 CST)" executed successfully
2025-08-05 21:09:08,248 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:08,248 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:08,248 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:08,249 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:08,290 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:17 CST)" executed successfully
2025-08-05 21:09:17,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:27 CST)" (scheduled at 2025-08-05 21:09:17.720644+08:00)
2025-08-05 21:09:18,372 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:18,373 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:19,574 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:20,371 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:20,371 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.80秒
2025-08-05 21:09:20,372 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:20,373 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:20,473 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:27 CST)" executed successfully
2025-08-05 21:09:27,733 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:37 CST)" (scheduled at 2025-08-05 21:09:27.720644+08:00)
2025-08-05 21:09:27,775 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:27,776 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:27,901 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:28,246 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:28,247 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:28,247 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:28,248 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:28,289 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:37 CST)" executed successfully
2025-08-05 21:09:30,358 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:30,717 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:30,930 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:09:36,356 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=2, 总计=5
2025-08-05 21:09:36,357 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:09:37,472 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:10:37 CST)" (scheduled at 2025-08-05 21:09:37.463923+08:00)
2025-08-05 21:09:37,517 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:09:37,517 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:37,517 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:09:37,594 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:07 CST)" (scheduled at 2025-08-05 21:09:37.589618+08:00)
2025-08-05 21:09:37,640 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:09:37,641 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:37,726 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:47 CST)" (scheduled at 2025-08-05 21:09:37.720644+08:00)
2025-08-05 21:09:37,770 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:37,770 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:37,771 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:09:37,772 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:09:37,772 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:09:37,888 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:38,109 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:09:38,109 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:09:38,110 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:09:38,111 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:09:38,111 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:38,154 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:07 CST)" executed successfully
2025-08-05 21:09:38,234 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:38,234 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:09:38,234 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:38,235 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:38,276 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:47 CST)" executed successfully
2025-08-05 21:09:38,919 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 1
2025-08-05 21:09:38,919 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.40秒
2025-08-05 21:09:38,920 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:09:38,920 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:38,966 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:10:37 CST)" executed successfully
2025-08-05 21:09:39,125 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:09:39,125 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:09:47,724 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:57 CST)" (scheduled at 2025-08-05 21:09:47.720644+08:00)
2025-08-05 21:09:47,768 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:47,768 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:47,895 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:48,226 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:48,226 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:09:48,227 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:48,228 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:48,275 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:09:57 CST)" executed successfully
2025-08-05 21:09:57,722 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:07 CST)" (scheduled at 2025-08-05 21:09:57.720644+08:00)
2025-08-05 21:09:57,763 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:09:57,764 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:09:57,892 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:09:58,233 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:09:58,234 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:09:58,234 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:09:58,235 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:09:58,314 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:07 CST)" executed successfully
2025-08-05 21:10:00,373 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:00,462 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:01,239 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:07,594 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:37 CST)" (scheduled at 2025-08-05 21:10:07.589618+08:00)
2025-08-05 21:10:07,635 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:10:07,635 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:07,732 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:17 CST)" (scheduled at 2025-08-05 21:10:07.720644+08:00)
2025-08-05 21:10:07,756 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:10:07,776 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:07,777 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:07,902 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:08,226 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:10:08,226 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:10:08,227 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.47秒
2025-08-05 21:10:08,227 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:10:08,228 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:08,243 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:08,243 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:10:08,244 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:08,244 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:08,273 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:10:37 CST)" executed successfully
2025-08-05 21:10:08,285 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:17 CST)" executed successfully
2025-08-05 21:10:17,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:27 CST)" (scheduled at 2025-08-05 21:10:17.720644+08:00)
2025-08-05 21:10:17,768 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:17,768 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:17,891 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:18,227 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:18,227 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:10:18,228 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:18,228 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:18,271 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:27 CST)" executed successfully
2025-08-05 21:10:27,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:37 CST)" (scheduled at 2025-08-05 21:10:27.720644+08:00)
2025-08-05 21:10:27,767 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:27,767 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:27,904 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:28,235 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:28,235 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:10:28,235 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:28,236 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:28,279 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:37 CST)" executed successfully
2025-08-05 21:10:30,379 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:30,441 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:30,640 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:10:36,363 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=3, 总计=6
2025-08-05 21:10:36,364 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:10:37,473 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:11:37 CST)" (scheduled at 2025-08-05 21:10:37.463923+08:00)
2025-08-05 21:10:37,516 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:10:37,516 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:37,518 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:10:37,598 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:07 CST)" (scheduled at 2025-08-05 21:10:37.589618+08:00)
2025-08-05 21:10:37,709 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:10:37,709 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:37,737 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:47 CST)" (scheduled at 2025-08-05 21:10:37.720644+08:00)
2025-08-05 21:10:37,875 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:37,876 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:37,878 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:10:37,879 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:10:37,919 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:10:38,119 - scheduler_health_checks - ERROR - 怪物冷却管理器Redis操作测试失败
2025-08-05 21:10:38,119 - unified_scheduler_manager - ERROR - 任务 monster_cooldown_notify 依赖的服务 monster_cooldown_manager 不健康
2025-08-05 21:10:38,120 - unified_scheduler_manager - WARNING - 任务 monster_cooldown_notify 执行失败，第 1 次重试: 任务 monster_cooldown_notify 依赖验证失败
2025-08-05 21:10:38,735 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:10:38,735 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:10:38,736 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.82秒
2025-08-05 21:10:38,737 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:10:38,737 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:38,899 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:07 CST)" executed successfully
2025-08-05 21:10:39,140 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 1
2025-08-05 21:10:39,141 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.62秒
2025-08-05 21:10:39,143 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:10:39,143 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:39,309 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:11:37 CST)" executed successfully
2025-08-05 21:10:39,750 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:10:39,751 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:10:40,375 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:40,951 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:40,951 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.58秒
2025-08-05 21:10:40,952 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:40,953 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:40,994 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:47 CST)" executed successfully
2025-08-05 21:10:47,735 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:57 CST)" (scheduled at 2025-08-05 21:10:47.720644+08:00)
2025-08-05 21:10:47,782 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:47,783 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:47,906 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:48,247 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:48,248 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:10:48,248 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:48,249 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:48,294 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:10:57 CST)" executed successfully
2025-08-05 21:10:57,727 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:07 CST)" (scheduled at 2025-08-05 21:10:57.720644+08:00)
2025-08-05 21:10:57,769 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:10:57,769 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:10:57,897 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:10:58,253 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:10:58,253 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 21:10:58,254 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:10:58,254 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:10:58,295 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:07 CST)" executed successfully
2025-08-05 21:11:00,387 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:00,695 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:00,908 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:07,603 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:37 CST)" (scheduled at 2025-08-05 21:11:07.589618+08:00)
2025-08-05 21:11:07,649 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:11:07,649 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:07,722 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:17 CST)" (scheduled at 2025-08-05 21:11:07.720644+08:00)
2025-08-05 21:11:07,765 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:07,765 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:07,773 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:11:07,896 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:08,108 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:11:08,108 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:11:08,109 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:11:08,109 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:11:08,110 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:08,154 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:11:37 CST)" executed successfully
2025-08-05 21:11:08,245 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:08,245 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:11:08,246 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:08,247 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:08,293 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:17 CST)" executed successfully
2025-08-05 21:11:17,736 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:27 CST)" (scheduled at 2025-08-05 21:11:17.720644+08:00)
2025-08-05 21:11:17,781 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:17,781 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:17,911 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:18,252 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:18,252 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:11:18,252 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:18,253 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:18,298 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:27 CST)" executed successfully
2025-08-05 21:11:27,725 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:37 CST)" (scheduled at 2025-08-05 21:11:27.720644+08:00)
2025-08-05 21:11:27,767 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:27,768 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:27,893 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:28,246 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:28,248 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:11:28,252 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:28,252 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:28,296 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:37 CST)" executed successfully
2025-08-05 21:11:30,122 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:30,400 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:30,895 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:11:36,372 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=3, 总计=6
2025-08-05 21:11:36,373 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:11:37,471 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:12:37 CST)" (scheduled at 2025-08-05 21:11:37.463923+08:00)
2025-08-05 21:11:37,518 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:11:37,518 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:37,519 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:11:37,593 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:07 CST)" (scheduled at 2025-08-05 21:11:37.589618+08:00)
2025-08-05 21:11:37,637 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:11:37,638 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:37,710 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:11:37,710 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:11:37,724 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:47 CST)" (scheduled at 2025-08-05 21:11:37.720644+08:00)
2025-08-05 21:11:37,766 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:11:37,767 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:37,767 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:37,901 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:38,119 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:11:38,120 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:11:38,132 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.37秒
2025-08-05 21:11:38,133 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:11:38,133 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:38,182 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:07 CST)" executed successfully
2025-08-05 21:11:38,273 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:38,273 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 21:11:38,274 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:38,275 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:38,323 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:47 CST)" executed successfully
2025-08-05 21:11:39,367 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 0
2025-08-05 21:11:39,368 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.85秒
2025-08-05 21:11:39,368 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:11:39,369 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:39,412 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:12:37 CST)" executed successfully
2025-08-05 21:11:39,628 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:11:39,629 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:11:47,728 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:57 CST)" (scheduled at 2025-08-05 21:11:47.720644+08:00)
2025-08-05 21:11:47,769 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:47,770 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:47,898 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:48,229 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:48,229 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 21:11:48,230 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:48,230 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:48,274 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:11:57 CST)" executed successfully
2025-08-05 21:11:57,724 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:07 CST)" (scheduled at 2025-08-05 21:11:57.720644+08:00)
2025-08-05 21:11:57,769 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:11:57,769 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:11:57,897 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:11:58,235 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:11:58,235 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:11:58,236 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:11:58,237 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:11:58,281 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:07 CST)" executed successfully
2025-08-05 21:12:00,104 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:00,341 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:00,403 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:07,590 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:37 CST)" (scheduled at 2025-08-05 21:12:07.589618+08:00)
2025-08-05 21:12:07,633 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:12:07,633 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:07,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:17 CST)" (scheduled at 2025-08-05 21:12:07.720644+08:00)
2025-08-05 21:12:07,761 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:12:07,773 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:07,774 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:07,906 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:08,099 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:12:08,100 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:12:08,100 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 21:12:08,100 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:12:08,101 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:08,144 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:12:37 CST)" executed successfully
2025-08-05 21:12:08,260 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:08,260 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:12:08,262 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:08,262 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:08,308 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:17 CST)" executed successfully
2025-08-05 21:12:17,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:27 CST)" (scheduled at 2025-08-05 21:12:17.720644+08:00)
2025-08-05 21:12:17,774 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:17,774 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:17,910 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:18,257 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:18,257 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:12:18,258 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:18,258 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:18,308 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:27 CST)" executed successfully
2025-08-05 21:12:27,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:37 CST)" (scheduled at 2025-08-05 21:12:27.720644+08:00)
2025-08-05 21:12:27,779 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:27,779 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:27,909 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:28,257 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:28,257 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:12:28,259 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:28,259 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:28,303 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:37 CST)" executed successfully
2025-08-05 21:12:30,315 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:30,409 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:30,548 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:12:36,388 - ConnectionManager - INFO - Redis连接池状态 (Worker 6540): 使用中=3, 可用=3, 总计=6
2025-08-05 21:12:36,389 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 6540): 连接中
2025-08-05 21:12:37,469 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:13:37 CST)" (scheduled at 2025-08-05 21:12:37.463923+08:00)
2025-08-05 21:12:37,594 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:07 CST)" (scheduled at 2025-08-05 21:12:37.589618+08:00)
2025-08-05 21:12:37,693 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:push_online
2025-08-05 21:12:37,693 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:37,694 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行推送在线人数任务
2025-08-05 21:12:37,733 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:47 CST)" (scheduled at 2025-08-05 21:12:37.720644+08:00)
2025-08-05 21:12:37,997 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:12:37,998 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:38,356 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:12:38,357 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:38,357 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:38,507 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:12:38,508 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:12:38,711 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:39,322 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:39,322 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.61秒
2025-08-05 21:12:39,323 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:39,323 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:39,324 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 在线人数推送完成，当前在线: 0
2025-08-05 21:12:39,324 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.63秒
2025-08-05 21:12:39,325 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 21:12:39,325 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:39,368 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:47 CST)" executed successfully
2025-08-05 21:12:39,371 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 21:13:37 CST)" executed successfully
2025-08-05 21:12:39,383 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:12:39,384 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:12:39,385 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 1.03秒
2025-08-05 21:12:39,386 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:12:39,388 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:39,516 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:07 CST)" executed successfully
2025-08-05 21:12:39,735 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 21:12:39,736 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:12:47,724 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:57 CST)" (scheduled at 2025-08-05 21:12:47.720644+08:00)
2025-08-05 21:12:47,766 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:47,767 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:47,902 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:48,237 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:48,237 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 21:12:48,238 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:48,238 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:48,281 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:12:57 CST)" executed successfully
2025-08-05 21:12:57,732 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:07 CST)" (scheduled at 2025-08-05 21:12:57.720644+08:00)
2025-08-05 21:12:57,875 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:12:57,876 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:12:58,379 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:12:59,774 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:12:59,775 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.40秒
2025-08-05 21:12:59,775 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:12:59,776 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:12:59,875 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:07 CST)" executed successfully
2025-08-05 21:13:00,421 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:13:00,514 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:13:00,758 - ConnectionManager - INFO - 连接状态 (Worker 6540): 活跃连接数=0, 用户数=0
2025-08-05 21:13:07,599 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:37 CST)" (scheduled at 2025-08-05 21:13:07.589618+08:00)
2025-08-05 21:13:07,646 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 21:13:07,648 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:07,723 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:17 CST)" (scheduled at 2025-08-05 21:13:07.720644+08:00)
2025-08-05 21:13:07,766 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:07,767 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:07,775 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 开始执行怪物冷却持久化任务
2025-08-05 21:13:07,893 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:08,109 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:13:08,110 - scheduler_tasks_unified - INFO - [定时任务] Worker 6540 怪物冷却持久化完成
2025-08-05 21:13:08,110 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 21:13:08,111 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 21:13:08,111 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:13:08,156 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 21:13:37 CST)" executed successfully
2025-08-05 21:13:08,241 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:08,241 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:13:08,242 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:08,242 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:13:08,287 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:17 CST)" executed successfully
2025-08-05 21:13:17,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:27 CST)" (scheduled at 2025-08-05 21:13:17.720644+08:00)
2025-08-05 21:13:17,775 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:17,775 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:17,905 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 21:13:18,253 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 21:13:18,253 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 21:13:18,254 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 21:13:18,254 - distributed_task - INFO - Worker 6540 任务执行完成: direct_wrapper
2025-08-05 21:13:18,299 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:27 CST)" executed successfully
2025-08-05 21:13:27,669 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 21:13:27,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 21:13:37 CST)" (scheduled at 2025-08-05 21:13:27.720644+08:00)
2025-08-05 21:13:27,775 - distributed_lock - INFO - Worker 6540 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 21:13:27,776 - distributed_task - INFO - Worker 6540 成功获取锁并执行任务: direct_wrapper
2025-08-05 21:13:27,902 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
