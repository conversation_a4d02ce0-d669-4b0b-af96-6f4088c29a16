import motor.motor_asyncio
import asyncio
import logging
from config import config

logger = logging.getLogger(__name__)

class MongoDBManager:
    _instance = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._client = None
        self._db = None
        self._initialized = False
        self._collections = None

    @classmethod
    async def get_instance(cls):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
                await cls._instance._init_db()
            return cls._instance

    async def _init_db(self):
        mongo_config = config.get_mongodb_config()
        uri = f"mongodb://{mongo_config['host']}:{mongo_config['port']}"
        if mongo_config.get('username') and mongo_config.get('password'):
            uri = f"mongodb://{mongo_config['username']}:{mongo_config['password']}@{mongo_config['host']}:{mongo_config['port']}"
        try:
            self._client = motor.motor_asyncio.AsyncIOMotorClient(
                uri,
                serverSelectionTimeoutMS=3000,
                connectTimeoutMS=3000,
                socketTimeoutMS=3000,
                maxPoolSize=50,
                minPoolSize=10,
                maxIdleTimeMS=60000,
                waitQueueTimeoutMS=3000,
                retryWrites=True
            )
            await self._client.server_info()  # 测试连接
            self._db = self._client[mongo_config['database']]
            self._initialized = True
            logger.info("MongoDBManager: MongoDB连接池初始化成功")
        except Exception as e:
            logger.error(f"MongoDBManager: MongoDB连接初始化失败: {str(e)}")
            self._client = None
            self._db = None
            self._initialized = False

    async def get_db(self, retries=3, retry_delay=0.5):
        for attempt in range(retries):
            if self._initialized and self._db is not None:
                try:
                    await self._client.server_info()
                    return self._db
                except Exception as e:
                    logger.warning(f"MongoDBManager: ping失败，重试初始化，第{attempt+1}次: {str(e)}")
                    self._initialized = False
                    self._client = None
                    self._db = None
            try:
                await self._init_db()
                if self._initialized and self._db is not None:
                    try:
                        await self._client.server_info()
                        return self._db
                    except Exception as e:
                        logger.warning(f"MongoDBManager: ping失败，重试初始化，第{attempt+1}次: {str(e)}")
                        self._initialized = False
                        self._client = None
                        self._db = None
            except Exception as e:
                logger.warning(f"MongoDBManager: get_db初始化失败，第{attempt+1}次重试: {str(e)}")
            if attempt < retries - 1:
                await asyncio.sleep(retry_delay)
        raise Exception("MongoDBManager: MongoDB未初始化成功，重试多次后仍失败")

    def get_collections(self):
        """获取数据库集合配置"""
        if self._collections is None:
            self._collections = config.get_database_collections()
        return self._collections

    def get_collection_name(self, collection_key: str) -> str:
        """获取指定的集合名"""
        collections = self.get_collections()
        return collections.get(collection_key, collection_key)

    async def init_indexes(self):
        """初始化所有关键集合的索引"""
        db = await self.get_db()
        collections = self.get_collections()
        try:
            # 用户表索引
            users_collection = db[collections["users"]]
            await users_collection.create_index([("id", 1)], unique=True)

            # 道具表索引
            items_collection = db[collections["items"]]
            await items_collection.create_index([("owner", 1)])
            await items_collection.create_index([("defid", 1), ("owner", 1), ("type", 1)])
            await items_collection.create_index([("id", 1)], unique=True)

            # 武将表索引
            generals_collection = db[collections["generals"]]
            await generals_collection.create_index([("player_id", 1)])
            await generals_collection.create_index([("general_id", 1)], unique=True)
            # 其它集合索引可按需添加
            # await db.formation.create_index([("player_id", 1)])
            # await db.general_templates.create_index([("template_id", 1)], unique=True)
            # ...
            logger.info("MongoDBManager: 所有索引初始化完成")
        except Exception as e:
            logger.error(f"MongoDBManager: 初始化索引失败: {str(e)}")
    async def close(self):
        """优雅关闭 MongoDB 连接池"""
        if self._client is not None:
            try:
                self._client.close()
                self._client = None
                self._db = None
                self._initialized = False
                logger.info("MongoDBManager: MongoDB连接池已关闭")
            except Exception as e:
                logger.warning(f"MongoDBManager: 关闭MongoDB连接池异常: {str(e)}") 