2025-08-05 17:50:50,825 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:51,748 - models - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:51,800 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:52,254 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:52,282 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:52,312 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:52,341 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:52,366 - auth - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,061 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,107 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,174 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,208 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,214 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,215 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 73e90804)
2025-08-05 17:50:55,216 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,286 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,332 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,335 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,338 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,420 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,431 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,434 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,439 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,442 - game_server - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:50:55,443 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 17:50:55,444 - msgManager - INFO - Monster handlers registered
2025-08-05 17:50:55,445 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 17:50:55,453 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 17:50:55,503 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 17:50:55,503 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 17:50:55,504 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 12fdea22)
2025-08-05 17:50:55,552 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 17:50:55,598 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 17:50:55,599 - game_server - INFO - 模板引擎初始化成功
2025-08-05 17:50:55,601 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 17:50:55,602 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 38704)
2025-08-05 17:50:55,603 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 17:50:55,763 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 17:50:55,776 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 17:50:56,263 - ConnectionManager - INFO - 后台任务已启动 (Worker 38704)
2025-08-05 17:50:56,263 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 38704)
2025-08-05 17:50:56,264 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 17:50:56,264 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 17:50:56,265 - game_server - INFO - 游戏配置加载完成 (Worker: 38704)
2025-08-05 17:50:56,265 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:51:00,755 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 17:51:00,756 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=0, 总计=2
2025-08-05 17:51:00,756 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 38704): 2/2 (100.0%)
2025-08-05 17:51:00,756 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:51:00,757 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:51:00,799 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 12fdea22)
2025-08-05 17:51:00,799 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 12fdea22)
2025-08-05 17:51:00,804 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:51:00,806 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 17:51:00,808 - ConnectionManager - INFO - Worker 38704 开始消费广播消息，消费者标签: ctag1.ab63cf1be9ff46118f942b826faf5086
2025-08-05 17:51:00,852 - ConnectionManager - INFO - Worker 38704 开始消费个人消息，消费者标签: ctag1.3909b48806994eabb364ffa03cc3b321
2025-08-05 17:51:00,941 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 38704)
2025-08-05 17:51:01,012 - distributed_lock - INFO - Worker 38704 成功获取锁: scheduler_initialization
2025-08-05 17:51:01,012 - game_server_scheduler_integration - INFO - Worker 38704 获得调度器初始化权限
2025-08-05 17:51:01,016 - unified_scheduler_manager - INFO - 统一调度器管理器初始化，模式: integrated
2025-08-05 17:51:01,016 - unified_scheduler_manager - INFO - 注册服务依赖: Redis管理器 (redis_manager)
2025-08-05 17:51:01,016 - unified_scheduler_manager - INFO - 注册服务依赖: MongoDB管理器 (mongodb_manager)
2025-08-05 17:51:01,017 - unified_scheduler_manager - INFO - 注册服务依赖: 连接管理器 (conn_manager)
2025-08-05 17:51:01,017 - unified_scheduler_manager - INFO - 注册服务依赖: 怪物冷却管理器 (monster_cooldown_manager)
2025-08-05 17:51:01,017 - unified_scheduler_manager - INFO - 注册任务定义: daily_reset
2025-08-05 17:51:01,018 - unified_scheduler_manager - INFO - 注册任务定义: push_online_count
2025-08-05 17:51:01,018 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_persist
2025-08-05 17:51:01,018 - unified_scheduler_manager - INFO - 注册任务定义: monster_cooldown_notify
2025-08-05 17:51:01,020 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_expired_mail_templates
2025-08-05 17:51:01,020 - unified_scheduler_manager - INFO - 注册任务定义: cleanup_old_processed_records
2025-08-05 17:51:01,020 - game_server_scheduler_integration - INFO - 发现已存在的服务: 连接管理器
2025-08-05 17:51:01,021 - unified_scheduler_manager - INFO - 启动统一调度器...
2025-08-05 17:51:01,021 - unified_scheduler_manager - INFO - 开始初始化服务...
2025-08-05 17:51:01,022 - unified_scheduler_manager - INFO - 初始化服务: Redis管理器
2025-08-05 17:51:01,022 - scheduler_health_checks - INFO - 初始化Redis管理器...
2025-08-05 17:51:01,106 - scheduler_health_checks - INFO - Redis管理器初始化完成
2025-08-05 17:51:01,107 - unified_scheduler_manager - INFO - 服务 Redis管理器 初始化完成
2025-08-05 17:51:01,107 - unified_scheduler_manager - INFO - 初始化服务: MongoDB管理器
2025-08-05 17:51:01,109 - scheduler_health_checks - INFO - 初始化MongoDB管理器...
2025-08-05 17:51:01,406 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 17:51:01,492 - scheduler_health_checks - INFO - MongoDB管理器初始化完成
2025-08-05 17:51:01,493 - unified_scheduler_manager - INFO - 服务 MongoDB管理器 初始化完成
2025-08-05 17:51:01,494 - unified_scheduler_manager - INFO - 服务 连接管理器 已存在，跳过初始化
2025-08-05 17:51:01,495 - unified_scheduler_manager - INFO - 初始化服务: 怪物冷却管理器
2025-08-05 17:51:01,495 - scheduler_health_checks - INFO - 初始化怪物冷却管理器...
2025-08-05 17:51:01,496 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 17:51:01,577 - scheduler_health_checks - INFO - 怪物冷却管理器Redis连接测试成功
2025-08-05 17:51:01,706 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 17:51:01,707 - scheduler_health_checks - INFO - 怪物冷却数据恢复成功
2025-08-05 17:51:01,707 - scheduler_health_checks - INFO - 怪物冷却管理器初始化完成
2025-08-05 17:51:01,707 - unified_scheduler_manager - INFO - 服务 怪物冷却管理器 初始化完成
2025-08-05 17:51:01,708 - unified_scheduler_manager - INFO - 所有服务初始化完成
2025-08-05 17:51:01,709 - unified_scheduler_manager - INFO - 开始注册调度任务...
2025-08-05 17:51:01,713 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,713 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 17:51:01,714 - unified_scheduler_manager - INFO - 任务 daily_reset 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: cron[hour='0', minute='0'], pending)
2025-08-05 17:51:01,715 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,715 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 17:51:01,715 - unified_scheduler_manager - INFO - 任务 push_online_count 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], pending)
2025-08-05 17:51:01,843 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,844 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 17:51:01,845 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], pending)
2025-08-05 17:51:01,970 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,970 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 17:51:01,970 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], pending)
2025-08-05 17:51:01,971 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,971 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 17:51:01,972 - unified_scheduler_manager - INFO - 任务 cleanup_expired_mail_templates 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1:00:00], pending)
2025-08-05 17:51:01,972 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-05 17:51:01,973 - scheduler_manager - INFO - [SchedulerManager] Job added: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 17:51:01,973 - unified_scheduler_manager - INFO - 任务 cleanup_old_processed_records 注册成功: UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[1 day, 0:00:00], pending)
2025-08-05 17:51:01,973 - unified_scheduler_manager - INFO - 所有调度任务注册完成
2025-08-05 17:51:01,975 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,975 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,975 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,976 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,976 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,977 - apscheduler.scheduler - INFO - Added job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper" to job store "default"
2025-08-05 17:51:01,977 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 17:51:01,978 - scheduler_manager - INFO - [SchedulerManager] APScheduler started.
2025-08-05 17:51:01,978 - unified_scheduler_manager - INFO - 健康监控已启动
2025-08-05 17:51:01,978 - unified_scheduler_manager - INFO - 统一调度器启动成功
2025-08-05 17:51:01,979 - game_server_scheduler_integration - INFO - Worker 38704 调度器初始化成功
2025-08-05 17:51:02,021 - game_server - INFO - 统一调度器初始化成功 (Worker: 38704)
2025-08-05 17:51:02,023 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 38704)
2025-08-05 17:51:02,023 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 17:51:02,024 - game_server - INFO - 日志清理管理器已启动 (Worker: 38704)
2025-08-05 17:51:02,024 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 17:51:02,025 - game_server - INFO - Monster cooldown manager initialized (Worker: 38704)
2025-08-05 17:51:02,153 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 17:51:02,155 - game_server - INFO - 公会系统初始化成功 (Worker: 38704)
2025-08-05 17:51:02,156 - game_server - INFO - 邮件系统初始化成功 (Worker: 38704)
2025-08-05 17:51:02,158 - game_server - INFO - 商店系统初始化成功 (Worker: 38704)
2025-08-05 17:51:02,158 - game_server - INFO - 初始化完成 (Worker: 38704)
2025-08-05 17:51:05,300 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 17:51:06,009 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 17:51:09,522 - shop_api - INFO - [ShopAPI] 获取商店详情: shop_026666601acb
2025-08-05 17:51:09,965 - shop_database_manager - ERROR - 获取商店商品配置时发生错误: ShopItemConfig.__init__() missing 1 required positional argument: 'item_type'
2025-08-05 17:51:11,975 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:21 CST)" (scheduled at 2025-08-05 17:51:11.970095+08:00)
2025-08-05 17:51:12,019 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:51:12,019 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:51:12,150 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:51:12,500 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:51:12,501 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:51:12,501 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:51:12,504 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:51:12,547 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:21 CST)" executed successfully
2025-08-05 17:51:21,982 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:31 CST)" (scheduled at 2025-08-05 17:51:21.970095+08:00)
2025-08-05 17:51:22,030 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:51:22,030 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:51:22,162 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:51:22,503 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:51:22,503 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:51:22,504 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:51:22,504 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:51:22,548 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:31 CST)" executed successfully
2025-08-05 17:51:26,268 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:51:35,328 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:52:01 CST)" was missed by 0:00:03.484831
2025-08-05 17:51:35,329 - apscheduler.executors.default - WARNING - Run time of job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:41 CST)" was missed by 0:00:03.359034
2025-08-05 17:51:41,972 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:51 CST)" (scheduled at 2025-08-05 17:51:41.970095+08:00)
2025-08-05 17:51:42,016 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:51:42,017 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:51:42,145 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:51:42,497 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:51:42,497 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:51:42,498 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:51:42,498 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:51:42,546 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:51:51 CST)" executed successfully
2025-08-05 17:51:51,974 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:01 CST)" (scheduled at 2025-08-05 17:51:51.970095+08:00)
2025-08-05 17:51:52,020 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:51:52,021 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:51:52,151 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:51:52,492 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:51:52,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.37秒
2025-08-05 17:51:52,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:51:52,525 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:51:52,573 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:01 CST)" executed successfully
2025-08-05 17:52:00,507 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:52:00,770 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=2, 总计=4
2025-08-05 17:52:00,771 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:52:01,730 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:53:01 CST)" (scheduled at 2025-08-05 17:52:01.715228+08:00)
2025-08-05 17:52:01,775 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:push_online
2025-08-05 17:52:01,776 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:01,785 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行推送在线人数任务
2025-08-05 17:52:01,787 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 17:52:01,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:52:31 CST)" (scheduled at 2025-08-05 17:52:01.843774+08:00)
2025-08-05 17:52:01,938 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:52:01,938 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:01,977 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:11 CST)" (scheduled at 2025-08-05 17:52:01.970095+08:00)
2025-08-05 17:52:02,021 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:02,021 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:02,060 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:52:02,159 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:02,389 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:52:02,390 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:52:02,390 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 17:52:02,391 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:52:02,391 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:02,436 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:52:31 CST)" executed successfully
2025-08-05 17:52:02,508 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:02,510 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:52:02,510 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:02,510 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:02,555 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:11 CST)" executed successfully
2025-08-05 17:52:03,207 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:52:03,208 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:52:03,227 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 在线人数推送完成，当前在线: 0
2025-08-05 17:52:03,228 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.44秒
2025-08-05 17:52:03,228 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 17:52:03,229 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:03,276 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:53:01 CST)" executed successfully
2025-08-05 17:52:05,331 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:52:11,972 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:21 CST)" (scheduled at 2025-08-05 17:52:11.970095+08:00)
2025-08-05 17:52:12,016 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:12,016 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:12,146 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:12,502 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:12,502 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 17:52:12,503 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:12,504 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:12,548 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:21 CST)" executed successfully
2025-08-05 17:52:21,970 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:31 CST)" (scheduled at 2025-08-05 17:52:21.970095+08:00)
2025-08-05 17:52:22,014 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:22,015 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:22,143 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:22,498 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:22,498 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:52:22,501 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:22,501 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:22,547 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:31 CST)" executed successfully
2025-08-05 17:52:30,733 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:52:31,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:53:01 CST)" (scheduled at 2025-08-05 17:52:31.843774+08:00)
2025-08-05 17:52:31,892 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:52:31,892 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:31,972 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:41 CST)" (scheduled at 2025-08-05 17:52:31.970095+08:00)
2025-08-05 17:52:32,013 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:32,014 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:32,024 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:52:32,140 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:32,366 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:52:32,367 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:52:32,367 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 17:52:32,367 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:52:32,368 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:32,412 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:53:01 CST)" executed successfully
2025-08-05 17:52:32,479 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:32,480 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:52:32,480 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:32,480 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:32,521 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:41 CST)" executed successfully
2025-08-05 17:52:35,341 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:52:41,980 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:51 CST)" (scheduled at 2025-08-05 17:52:41.970095+08:00)
2025-08-05 17:52:42,022 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:42,022 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:42,146 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:42,478 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:42,479 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:52:42,479 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:42,479 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:42,521 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:52:51 CST)" executed successfully
2025-08-05 17:52:51,971 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:01 CST)" (scheduled at 2025-08-05 17:52:51.970095+08:00)
2025-08-05 17:52:52,013 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:52:52,014 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:52:52,134 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:52:52,465 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:52:52,465 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:52:52,466 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:52:52,466 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:52:52,507 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:01 CST)" executed successfully
2025-08-05 17:53:00,784 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=2, 总计=4
2025-08-05 17:53:00,785 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:53:00,955 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:53:01,715 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:54:01 CST)" (scheduled at 2025-08-05 17:53:01.715228+08:00)
2025-08-05 17:53:01,757 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:push_online
2025-08-05 17:53:01,757 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:01,758 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行推送在线人数任务
2025-08-05 17:53:01,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:53:31 CST)" (scheduled at 2025-08-05 17:53:01.843774+08:00)
2025-08-05 17:53:01,896 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:53:01,896 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:01,951 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:53:01,951 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:53:01,978 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:11 CST)" (scheduled at 2025-08-05 17:53:01.970095+08:00)
2025-08-05 17:53:02,020 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:53:02,023 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:02,023 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:02,153 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:02,353 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:53:02,353 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:53:02,353 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 17:53:02,354 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:53:02,354 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:02,398 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:53:31 CST)" executed successfully
2025-08-05 17:53:02,503 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:02,505 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:53:02,513 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:02,514 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:02,558 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:11 CST)" executed successfully
2025-08-05 17:53:03,754 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 在线人数推送完成，当前在线: 0
2025-08-05 17:53:03,756 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 2.00秒
2025-08-05 17:53:03,756 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 17:53:03,757 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:03,804 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:54:01 CST)" executed successfully
2025-08-05 17:53:05,358 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:53:11,976 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:21 CST)" (scheduled at 2025-08-05 17:53:11.970095+08:00)
2025-08-05 17:53:12,021 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:12,022 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:12,156 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:12,503 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:12,504 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:53:12,504 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:12,505 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:12,552 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:21 CST)" executed successfully
2025-08-05 17:53:21,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:31 CST)" (scheduled at 2025-08-05 17:53:21.970095+08:00)
2025-08-05 17:53:22,035 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:22,035 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:22,270 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:22,835 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:22,836 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.57秒
2025-08-05 17:53:22,836 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:22,836 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:22,881 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:31 CST)" executed successfully
2025-08-05 17:53:30,163 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:53:31,848 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:54:01 CST)" (scheduled at 2025-08-05 17:53:31.843774+08:00)
2025-08-05 17:53:31,892 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:53:31,892 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:31,971 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:41 CST)" (scheduled at 2025-08-05 17:53:31.970095+08:00)
2025-08-05 17:53:32,012 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:32,012 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:32,024 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:53:32,138 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:32,365 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:53:32,365 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:53:32,365 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 17:53:32,366 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:53:32,366 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:32,412 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:54:01 CST)" executed successfully
2025-08-05 17:53:32,466 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:32,466 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:53:32,466 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:32,467 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:32,509 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:41 CST)" executed successfully
2025-08-05 17:53:35,373 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:53:41,972 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:51 CST)" (scheduled at 2025-08-05 17:53:41.970095+08:00)
2025-08-05 17:53:42,013 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:42,014 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:42,141 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:42,477 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:42,478 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:53:42,479 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:42,484 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:42,529 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:53:51 CST)" executed successfully
2025-08-05 17:53:51,983 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:01 CST)" (scheduled at 2025-08-05 17:53:51.970095+08:00)
2025-08-05 17:53:52,024 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:53:52,025 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:53:52,146 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:53:52,470 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:53:52,470 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.32秒
2025-08-05 17:53:52,471 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:53:52,471 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:53:52,512 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:01 CST)" executed successfully
2025-08-05 17:54:00,343 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:54:00,793 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=2, 总计=4
2025-08-05 17:54:00,795 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:54:01,719 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:55:01 CST)" (scheduled at 2025-08-05 17:54:01.715228+08:00)
2025-08-05 17:54:01,762 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:push_online
2025-08-05 17:54:01,763 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:01,763 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行推送在线人数任务
2025-08-05 17:54:01,859 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:54:31 CST)" (scheduled at 2025-08-05 17:54:01.843774+08:00)
2025-08-05 17:54:01,902 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:54:01,903 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:01,984 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:11 CST)" (scheduled at 2025-08-05 17:54:01.970095+08:00)
2025-08-05 17:54:02,026 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:54:02,028 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:02,028 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:02,029 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:54:02,029 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:54:02,159 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:02,354 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:54:02,354 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:54:02,354 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 17:54:02,355 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:54:02,355 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:02,404 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:54:31 CST)" executed successfully
2025-08-05 17:54:02,497 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:02,497 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:54:02,498 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:02,498 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:02,542 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:11 CST)" executed successfully
2025-08-05 17:54:02,972 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 在线人数推送完成，当前在线: 0
2025-08-05 17:54:02,973 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.21秒
2025-08-05 17:54:02,973 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 17:54:02,974 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:03,017 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:55:01 CST)" executed successfully
2025-08-05 17:54:05,376 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:54:11,980 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:21 CST)" (scheduled at 2025-08-05 17:54:11.970095+08:00)
2025-08-05 17:54:12,024 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:12,025 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:12,157 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:12,506 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:12,506 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:54:12,507 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:12,507 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:12,553 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:21 CST)" executed successfully
2025-08-05 17:54:21,971 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:31 CST)" (scheduled at 2025-08-05 17:54:21.970095+08:00)
2025-08-05 17:54:22,121 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:22,121 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:22,424 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:23,523 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:23,523 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 1.10秒
2025-08-05 17:54:23,524 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:23,524 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:23,621 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:31 CST)" executed successfully
2025-08-05 17:54:30,545 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:54:31,845 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:55:01 CST)" (scheduled at 2025-08-05 17:54:31.843774+08:00)
2025-08-05 17:54:31,890 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:54:31,890 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:31,983 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:41 CST)" (scheduled at 2025-08-05 17:54:31.970095+08:00)
2025-08-05 17:54:32,024 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:32,024 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:32,026 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:54:32,165 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:32,383 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:54:32,383 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:54:32,383 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 17:54:32,384 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:54:32,384 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:32,448 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:55:01 CST)" executed successfully
2025-08-05 17:54:32,497 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:32,497 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:54:32,498 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:32,498 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:32,540 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:41 CST)" executed successfully
2025-08-05 17:54:35,390 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:54:41,979 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:51 CST)" (scheduled at 2025-08-05 17:54:41.970095+08:00)
2025-08-05 17:54:42,020 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:42,020 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:42,142 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:42,473 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:42,474 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:54:42,475 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:42,475 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:42,521 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:54:51 CST)" executed successfully
2025-08-05 17:54:51,971 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:01 CST)" (scheduled at 2025-08-05 17:54:51.970095+08:00)
2025-08-05 17:54:52,015 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:54:52,016 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:54:52,139 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:54:52,478 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:54:52,478 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:54:52,479 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:54:52,479 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:54:52,523 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:01 CST)" executed successfully
2025-08-05 17:55:00,764 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:55:00,811 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=2, 总计=4
2025-08-05 17:55:00,813 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:55:01,729 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:56:01 CST)" (scheduled at 2025-08-05 17:55:01.715228+08:00)
2025-08-05 17:55:01,771 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:push_online
2025-08-05 17:55:01,771 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:01,772 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行推送在线人数任务
2025-08-05 17:55:01,854 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:55:31 CST)" (scheduled at 2025-08-05 17:55:01.843774+08:00)
2025-08-05 17:55:01,901 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:55:01,901 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:01,976 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:11 CST)" (scheduled at 2025-08-05 17:55:01.970095+08:00)
2025-08-05 17:55:02,019 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:02,020 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:02,025 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:55:02,108 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:55:02,108 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:55:02,148 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:02,356 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:55:02,357 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:55:02,358 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 17:55:02,359 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:55:02,359 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:02,402 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:55:31 CST)" executed successfully
2025-08-05 17:55:02,500 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:02,500 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:55:02,501 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:02,501 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:02,547 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:11 CST)" executed successfully
2025-08-05 17:55:02,925 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 在线人数推送完成，当前在线: 0
2025-08-05 17:55:02,925 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.15秒
2025-08-05 17:55:02,926 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 17:55:02,926 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:02,970 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:56:01 CST)" executed successfully
2025-08-05 17:55:05,402 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:55:11,974 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:21 CST)" (scheduled at 2025-08-05 17:55:11.970095+08:00)
2025-08-05 17:55:12,019 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:12,020 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:12,153 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:12,514 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:12,515 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 17:55:12,515 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:12,516 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:12,561 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:21 CST)" executed successfully
2025-08-05 17:55:21,975 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:31 CST)" (scheduled at 2025-08-05 17:55:21.970095+08:00)
2025-08-05 17:55:22,019 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:22,019 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:22,153 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:22,503 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:22,504 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:55:22,504 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:22,505 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:22,549 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:31 CST)" executed successfully
2025-08-05 17:55:30,964 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:55:31,856 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:56:01 CST)" (scheduled at 2025-08-05 17:55:31.843774+08:00)
2025-08-05 17:55:31,903 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:55:31,904 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:31,980 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:41 CST)" (scheduled at 2025-08-05 17:55:31.970095+08:00)
2025-08-05 17:55:32,025 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:32,025 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:32,034 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:55:32,148 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:32,392 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:55:32,392 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:55:32,392 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.36秒
2025-08-05 17:55:32,393 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:55:32,393 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:32,439 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:56:01 CST)" executed successfully
2025-08-05 17:55:32,478 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:32,479 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:55:32,479 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:32,480 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:32,523 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:41 CST)" executed successfully
2025-08-05 17:55:35,418 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:55:41,981 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:51 CST)" (scheduled at 2025-08-05 17:55:41.970095+08:00)
2025-08-05 17:55:42,022 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:42,022 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:42,144 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:42,491 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:42,491 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:55:42,492 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:42,492 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:42,535 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:55:51 CST)" executed successfully
2025-08-05 17:55:51,985 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:01 CST)" (scheduled at 2025-08-05 17:55:51.970095+08:00)
2025-08-05 17:55:52,026 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:55:52,027 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:55:52,151 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:55:52,486 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:55:52,486 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:55:52,487 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:55:52,487 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:55:52,530 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:01 CST)" executed successfully
2025-08-05 17:56:00,212 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:56:00,817 - ConnectionManager - INFO - Redis连接池状态 (Worker 38704): 使用中=2, 可用=2, 总计=4
2025-08-05 17:56:00,818 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38704): 连接中
2025-08-05 17:56:01,717 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:57:01 CST)" (scheduled at 2025-08-05 17:56:01.715228+08:00)
2025-08-05 17:56:01,759 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:push_online
2025-08-05 17:56:01,760 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:01,760 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行推送在线人数任务
2025-08-05 17:56:01,855 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:56:31 CST)" (scheduled at 2025-08-05 17:56:01.843774+08:00)
2025-08-05 17:56:01,897 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:56:01,897 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:01,951 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:56:01,951 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:56:01,978 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:11 CST)" (scheduled at 2025-08-05 17:56:01.970095+08:00)
2025-08-05 17:56:02,019 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:56:02,026 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:02,026 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:02,155 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:02,357 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:56:02,357 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:56:02,358 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.34秒
2025-08-05 17:56:02,358 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:56:02,358 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:02,400 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:56:31 CST)" executed successfully
2025-08-05 17:56:02,509 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:02,511 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.36秒
2025-08-05 17:56:02,512 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:02,513 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:02,558 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:11 CST)" executed successfully
2025-08-05 17:56:03,703 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 在线人数推送完成，当前在线: 0
2025-08-05 17:56:03,703 - scheduler_health_checks - INFO - 任务 push_online_count 执行成功，耗时: 1.94秒
2025-08-05 17:56:03,703 - unified_scheduler_manager - INFO - 任务 push_online_count 直接执行成功
2025-08-05 17:56:03,704 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:03,750 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:01:00], next run at: 2025-08-05 17:57:01 CST)" executed successfully
2025-08-05 17:56:05,426 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:56:11,983 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:21 CST)" (scheduled at 2025-08-05 17:56:11.970095+08:00)
2025-08-05 17:56:12,024 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:12,025 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:12,147 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:12,480 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:12,481 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.33秒
2025-08-05 17:56:12,481 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:12,482 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:12,524 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:21 CST)" executed successfully
2025-08-05 17:56:21,984 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:31 CST)" (scheduled at 2025-08-05 17:56:21.970095+08:00)
2025-08-05 17:56:22,025 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:22,026 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:22,146 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:22,490 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:22,491 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.34秒
2025-08-05 17:56:22,493 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:22,495 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:22,541 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:31 CST)" executed successfully
2025-08-05 17:56:30,390 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:56:31,851 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:57:01 CST)" (scheduled at 2025-08-05 17:56:31.843774+08:00)
2025-08-05 17:56:31,894 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_persist
2025-08-05 17:56:31,895 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:31,971 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:41 CST)" (scheduled at 2025-08-05 17:56:31.970095+08:00)
2025-08-05 17:56:32,016 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:32,016 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:32,019 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 开始执行怪物冷却持久化任务
2025-08-05 17:56:32,149 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:32,348 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 17:56:32,348 - scheduler_tasks_unified - INFO - [定时任务] Worker 38704 怪物冷却持久化完成
2025-08-05 17:56:32,349 - scheduler_health_checks - INFO - 任务 monster_cooldown_persist 执行成功，耗时: 0.33秒
2025-08-05 17:56:32,349 - unified_scheduler_manager - INFO - 任务 monster_cooldown_persist 直接执行成功
2025-08-05 17:56:32,350 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:32,391 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:30], next run at: 2025-08-05 17:57:01 CST)" executed successfully
2025-08-05 17:56:32,495 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:32,495 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:56:32,498 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:32,498 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:32,542 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:41 CST)" executed successfully
2025-08-05 17:56:35,441 - ConnectionManager - INFO - 连接状态 (Worker 38704): 活跃连接数=0, 用户数=0
2025-08-05 17:56:41,981 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:51 CST)" (scheduled at 2025-08-05 17:56:41.970095+08:00)
2025-08-05 17:56:42,025 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:42,026 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:42,156 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:42,507 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:42,507 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:56:42,508 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:42,508 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:42,554 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:56:51 CST)" executed successfully
2025-08-05 17:56:51,972 - apscheduler.executors.default - INFO - Running job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:57:01 CST)" (scheduled at 2025-08-05 17:56:51.970095+08:00)
2025-08-05 17:56:52,015 - distributed_lock - INFO - Worker 38704 成功获取锁: lock:scheduled:monster_cooldown_notify
2025-08-05 17:56:52,016 - distributed_task - INFO - Worker 38704 成功获取锁并执行任务: direct_wrapper
2025-08-05 17:56:52,146 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知任务执行
2025-08-05 17:56:52,496 - scheduler_tasks_unified - INFO - [定时任务] 怪物冷却通知完成，已通知 0 个怪物刷新
2025-08-05 17:56:52,497 - scheduler_health_checks - INFO - 任务 monster_cooldown_notify 执行成功，耗时: 0.35秒
2025-08-05 17:56:52,509 - unified_scheduler_manager - INFO - 任务 monster_cooldown_notify 直接执行成功
2025-08-05 17:56:52,510 - distributed_task - INFO - Worker 38704 任务执行完成: direct_wrapper
2025-08-05 17:56:52,556 - apscheduler.executors.default - INFO - Job "UnifiedSchedulerManager._wrap_direct_execution.<locals>.direct_wrapper (trigger: interval[0:00:10], next run at: 2025-08-05 17:57:01 CST)" executed successfully
