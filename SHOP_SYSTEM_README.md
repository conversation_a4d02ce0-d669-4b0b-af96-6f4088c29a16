# 🏪 商店系统使用指南

## 📋 概述

商店系统已成功集成到主项目中，支持WebSocket实时通信和HTTP API，具备生产级别的性能和功能。

## 🔧 系统集成问题修复

### 问题描述
1. **Redis连接问题**: 事件循环关闭时出现 `RuntimeError: Event loop is closed`
2. **模块依赖问题**: 引用了不存在的 `item_service_distributed` 模块
3. **WebSocket处理器问题**: 参数顺序错误和返回值序列化问题

### 修复方案
1. **创建专用Redis管理器** (`shop_redis_manager.py`)
2. **使用现有道具系统** (替换为 `ItemCacheManager` 和 `Item` 类)
3. **修复WebSocket处理器** (参数顺序和返回值格式)
4. **优化货币服务** (基于现有道具系统实现)

### 修复后的使用方式
```python
# Redis连接修复
from shop_redis_manager import get_shop_redis
redis = await get_shop_redis()

# 道具系统集成
from ItemCacheManager import ItemCacheManager, Item
item_cache = await ItemCacheManager.get_instance()

# 货币服务使用现有道具系统
currency_service = CurrencyService()  # 内部使用Item类
```

## 🚀 快速开始

### 1. 系统初始化
```bash
# 初始化商店系统和示例数据
python shop_init.py

# 或使用启动器（推荐）
python start_shop_system.py
```

### 2. 测试Redis修复
```bash
# 测试Redis连接修复
python test_shop_redis_fix.py
```

### 3. 集成测试
```bash
# WebSocket集成测试
python test_shop_integration.py

# HTTP API测试
python test_shop_api.py
```

## 📡 WebSocket API

### 消息ID列表
```javascript
// 商店相关消息ID (350-361)
SHOP_GET_LIST = 350          // 获取商店列表
SHOP_GET_ITEMS = 351         // 获取商店商品
SHOP_GET_ITEM_DETAIL = 352   // 获取商品详情
SHOP_PURCHASE = 353          // 购买商品
SHOP_PREVIEW_PURCHASE = 354  // 预览购买
SHOP_GET_HISTORY = 355       // 获取购买历史
SHOP_GET_LIMITS = 356        // 获取限购状态
SHOP_PURCHASE_SUCCESS = 357  // 购买成功通知
SHOP_LIMITS_RESET = 358      // 限购重置通知
SHOP_REFRESHED = 359         // 商店刷新通知
SHOP_DISCOUNT_UPDATED = 360  // 折扣更新通知
SHOP_ITEM_UPDATED = 361      // 商品更新通知
```

### 使用示例
```javascript
// 获取商店列表
websocket.send(JSON.stringify({
    msgId: 350,
    data: {}
}));

// 购买商品
websocket.send(JSON.stringify({
    msgId: 353,
    data: {
        config_id: "shop1:gold_coin:abc123",
        quantity: 1,
        metadata: {source: "client"}
    }
}));
```

## 🌐 HTTP API

### 玩家接口
```bash
# 获取商店列表
GET /api/shop/list?player_id=player123

# 获取商店商品
GET /api/shop/{shop_id}/items?player_id=player123

# 购买商品
POST /api/shop/purchase?player_id=player123
Content-Type: application/json
{
    "config_id": "shop1:gold_coin:abc123",
    "quantity": 1
}
```

### 管理接口
```bash
# 创建商店
POST /api/shop/admin/shop
Content-Type: application/json
{
    "shop_name": "新商店",
    "shop_type": "normal",
    "description": "商店描述"
}

# 重置限购
POST /api/shop/admin/limits/reset?limit_type=daily
```

## 🔄 事件流程

### 购买流程
1. 客户端发送购买请求
2. 服务器验证条件（权限、限购、货币）
3. 执行购买事务（原子性操作）
4. 广播购买成功事件
5. 通知货币和道具变化

### 限购重置流程
1. 系统触发重置（定时或手动）
2. 更新全局重置时间戳
3. 广播重置事件
4. 玩家操作时懒加载重置

## 🛠️ 开发指南

### 添加新的商店类型
```python
# 1. 在 shop_models.py 中添加类型
class ShopType:
    NEW_TYPE = "new_type"

# 2. 在初始化脚本中添加示例
shop_data = {
    "shop_type": ShopType.NEW_TYPE,
    # ... 其他配置
}
```

### 添加新的货币类型
```python
# 在 currency_service.py 中添加映射
self.currency_item_mapping = {
    "new_currency": "new_currency_item_id",
    # ... 其他货币
}
```

### 添加新的折扣类型
```python
# 在 shop_discount_service.py 中扩展
class DiscountType:
    NEW_TYPE = "new_type"

# 在 _apply_discount 方法中添加处理逻辑
```

## 🔍 故障排除

### Redis连接问题
```bash
# 检查Redis服务状态
redis-cli ping

# 测试商店系统Redis连接
python test_shop_redis_fix.py
```

### 数据库连接问题
```bash
# 检查MongoDB连接
python -c "from mongodb_manager import MongoDBManager; import asyncio; asyncio.run(MongoDBManager.get_instance())"
```

### WebSocket连接问题
```bash
# 检查服务器是否运行
curl http://localhost:8000/health

# 测试WebSocket连接
python test_shop_integration.py
```

## 📊 性能监控

### 关键指标
- Redis连接数
- 购买事务成功率
- 限购重置时间
- WebSocket消息延迟

### 监控命令
```bash
# Redis连接状态
redis-cli info clients

# 商店系统状态
curl http://localhost:8000/api/shop/admin/statistics/shop_id
```

## 🔒 安全注意事项

1. **输入验证**: 所有用户输入都经过验证
2. **权限检查**: 每个操作都检查玩家权限
3. **事务保护**: 使用分布式锁防止并发问题
4. **数据完整性**: 购买事务支持回滚

## 📝 更新日志

### v1.0.0 (2025-08-03)
- ✅ 完整的商店系统实现
- ✅ WebSocket和HTTP API支持
- ✅ Redis连接问题修复
- ✅ 事务安全保证
- ✅ 实时事件广播

## 🤝 贡献指南

1. 遵循现有代码风格
2. 添加适当的日志记录
3. 编写单元测试
4. 更新文档

## 📞 支持

如果遇到问题，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查配置文件
4. 联系开发团队
