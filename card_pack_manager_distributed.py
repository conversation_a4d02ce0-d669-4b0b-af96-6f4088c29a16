import asyncio
import time
import uuid
import json
from typing import Dict, List, Optional, Any
from distributed_lock import DistributedLock
from config import config
from card_pack import CardPack
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from redis_manager import RedisManager
logger = logging.getLogger(__name__)

class CardPackGlobalManager:
    """
    全局卡包开关与活动卡包管理器，支持全服动态开启/关闭卡包
    """
    _redis = None
    _global_prefix = "game:v2:cardpack:global:enabled:"
    _active_key = "game:v2:cardpack:global:active"

    @classmethod
    async def _get_redis(cls):
        if cls._redis is None:
            redis_manager = await RedisManager.get_instance()
            cls._redis = await redis_manager.get_redis()
        return cls._redis

    @classmethod
    async def enable_card_pack(cls, pack_id: str):
        redis = await cls._get_redis()
        await redis.set(cls._global_prefix + str(pack_id), 1)
        logger.info(f"[GlobalCardPack] 全服开启卡包 {pack_id}")

    @classmethod
    async def disable_card_pack(cls, pack_id: str):
        redis = await cls._get_redis()
        await redis.set(cls._global_prefix + str(pack_id), 0)
        logger.info(f"[GlobalCardPack] 全服关闭卡包 {pack_id}")

    @classmethod
    async def is_card_pack_enabled(cls, pack_id: str) -> bool:
        redis = await cls._get_redis()
        value = await redis.get(cls._global_prefix + str(pack_id))
        if value is None:
            # 默认开启
            return True
        return str(value) == "1"

    @classmethod
    async def open_card_pack(cls, pack_id: str, params: Optional[dict] = None, start_time: Optional[int] = None, end_time: Optional[int] = None):
        """全服动态开启一个新卡包，可带活动参数"""
        redis = await cls._get_redis()
        active = await redis.get(cls._active_key)
        if active:
            active_data = json.loads(active)
        else:
            active_data = {}
        active_data[str(pack_id)] = {
            "start_time": start_time or int(time.time()),
            "end_time": end_time,
            "params": params or {}
        }
        await redis.set(cls._active_key, json.dumps(active_data, ensure_ascii=False))
        logger.info(f"[GlobalCardPack] 全服动态开启卡包 {pack_id} 活动参数: {active_data[str(pack_id)]}")

    @classmethod
    async def close_card_pack(cls, pack_id: str):
        """全服关闭一个活动卡包"""
        redis = await cls._get_redis()
        active = await redis.get(cls._active_key)
        if active:
            active_data = json.loads(active)
            if str(pack_id) in active_data:
                del active_data[str(pack_id)]
                await redis.set(cls._active_key, json.dumps(active_data, ensure_ascii=False))
                logger.info(f"[GlobalCardPack] 全服关闭活动卡包 {pack_id}")

    @classmethod
    async def get_active_card_packs(cls) -> Dict[str, dict]:
        """获取当前全服所有已开启活动卡包及参数"""
        redis = await cls._get_redis()
        active = await redis.get(cls._active_key)
        if active:
            return json.loads(active)
        return {}

class CardPackManagerDistributed:
    """
    分布式卡包管理器，支持多worker环境下的卡包管理，支持Redis+MongoDB双持久化
    """
    _mongo_client = None
    _mongo_db = None
    _mongo_collection = None

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.card_packs: Dict[str, CardPack] = {}  # pack_id -> CardPack
        self.lock_prefix = f"cardpack:{self.user_id}:"
        self.redis_key = f"game:v2:cardpacks:{self.user_id}"
        self._redis = None
        self._init_mongo()

    @classmethod
    def _init_mongo(cls):
        if cls._mongo_client is None:
            mongo_cfg = config.get_mongodb_config()
            uri = f"mongodb://{mongo_cfg['username']}:{mongo_cfg['password']}@{mongo_cfg['host']}:{mongo_cfg['port']}"
            cls._mongo_client = AsyncIOMotorClient(uri)
            cls._mongo_db = cls._mongo_client[mongo_cfg['database']]
            cls._mongo_collection = cls._mongo_db['cardpacks']

    async def _get_redis(self):
        if self._redis is None:
            redis_manager = await RedisManager.get_instance()
            self._redis = await redis_manager.get_redis()
        return self._redis

    def _load_from_config(self):
        packages = config.get_card_packages()
        now = int(time.time())
        for pkg in packages.values() if isinstance(packages, dict) else packages:
            pack_id = str(pkg["id"])
            start_time = now
            end_time = None
            if pkg.get("last_time", -1) > 0:
                end_time = now + int(pkg["last_time"])
            limit_count = pkg.get("limit_time", -1)
            refresh_type = None
            if pkg.get("reset_day", 0) == 1:
                refresh_type = "daily"
            free_times = pkg.get("free_times", 0)
            free_interval = pkg.get("free_time", None)
            public_luck = None
            luck_stars = []
            if pkg.get("pulic_luck"):
                parts = str(pkg["pulic_luck"]).split("|")
                if len(parts) == 2:
                    public_luck = int(parts[0])
                    luck_stars = [int(x) for x in parts[1].split(",")]
            luck_num = pkg.get("guard", None)
            card_star_expect = []
            if pkg.get("expact"):
                for seg in str(pkg["expact"]).split(","):
                    star, expect = seg.split(":")
                    card_star_expect.append([int(star), float(expect)])
            card_pack = CardPack(
                pack_id=pack_id,
                cfg_id=pkg["cfg_id"],
                user_id=self.user_id,
                pack_type=pkg.get("kindtype", "normal"),
                start_time=start_time,
                end_time=end_time,
                limit_count=limit_count,
                refresh_type=refresh_type,
                free_times=free_times,
                free_interval=free_interval,
                public_luck=public_luck,
                luck_stars=luck_stars,
                luck_num=luck_num,
                card_star_expect=card_star_expect
            )
            self.card_packs[pack_id] = card_pack

    async def sync_with_global(self):
        """同步全局活动卡包，动态增删"""
        active = await CardPackGlobalManager.get_active_card_packs()
        now = int(time.time())
        # 新增全局活动卡包
        for pack_id, info in active.items():
            # 检查时间有效性
            st = info.get("start_time", 0)
            et = info.get("end_time", None)
            if (st and now < st) or (et and et > 0 and now > et):
                continue  # 未到开始或已过期
            if pack_id not in self.card_packs:
                # 从配置创建新卡包
                packages = config.get_card_packages()
                pkg = None
                for p in packages.values() if isinstance(packages, dict) else packages:
                    if str(p["id"]) == str(pack_id):
                        pkg = p
                        break
                if pkg:
                    start_time = st or now
                    end_time = et
                    limit_count = pkg.get("limit_time", -1)         
                    refresh_type = None
                    if pkg.get("reset_day", 0) == 1:
                        refresh_type = "daily"
                    free_times = pkg.get("free_times", 0)
                    free_interval = pkg.get("free_time", None)
                    public_luck = None
                    luck_stars = []
                    if pkg.get("pulic_luck"):
                        parts = str(pkg["pulic_luck"]).split("|")
                        if len(parts) == 2:
                            public_luck = int(parts[0])
                            luck_stars = [int(x) for x in parts[1].split(",")]
                    luck_num = pkg.get("guard", None)
                    card_star_expect = []
                    if pkg.get("expact"):
                        for seg in str(pkg["expact"]).split(","):
                            star, expect = seg.split(":")
                            card_star_expect.append([int(star), float(expect)])
                    card_pack = CardPack(
                        pack_id=pack_id,
                        cfg_id=pkg["cfg_id"],
                        user_id=self.user_id,
                        pack_type=pkg.get("kindtype", "normal"),
                        start_time=start_time,
                        end_time=end_time,
                        limit_count=limit_count,
                        refresh_type=refresh_type,
                        free_times=free_times,
                        free_interval=free_interval,
                        public_luck=public_luck,
                        luck_stars=luck_stars,
                        luck_num=luck_num,
                        card_star_expect=card_star_expect
                    )
                    self.card_packs[pack_id] = card_pack
        # 移除已关闭的活动卡包
        remove_ids = []
        for pack_id in self.card_packs:
            if pack_id not in active and int(pack_id) >= 10000:  # 只移除活动动态卡包（如活动id>=10000）
                remove_ids.append(pack_id)
        for pack_id in remove_ids:
            del self.card_packs[pack_id]

    async def acquire_pack_lock(self, pack_id: str, ttl: int = 5):
        lock_key = self.lock_prefix + str(pack_id)
        return DistributedLock(lock_key, ttl)

    def get_pack(self, pack_id: str) -> Optional[CardPack]:
        return self.card_packs.get(str(pack_id))

    def get_all_packs(self) -> List[CardPack]:
        return list(self.card_packs.values())

    async def refresh_all(self):
        await self.sync_with_global()
        now = int(time.time())
        for pack in self.card_packs.values():
            pack.refresh(now)
        await self.save()

    async def draw_from_pack(self, pack_id: str, use_free: bool = False) -> bool:
        enabled = await CardPackGlobalManager.is_card_pack_enabled(pack_id)
        if not enabled:
            logger.warning(f"[CardPackManager] 卡包{pack_id}已被全局关闭，禁止抽卡")
            return False
        async with await self.acquire_pack_lock(pack_id):
            pack = self.get_pack(pack_id)
            if not pack:
                logger.warning(f"卡包不存在: {pack_id}")
                return False
            result = pack.draw(use_free=use_free)
            if result:
                await self.save()
            return result

    async def save(self):
        """
        持久化所有卡包状态到Redis和MongoDB
        """
        redis = await self._get_redis()
        data = {pack_id: pack.to_dict() for pack_id, pack in self.card_packs.items()}
        # Redis
        await redis.set(self.redis_key, json.dumps(data, ensure_ascii=False))
        # MongoDB
        await self._mongo_collection.update_one(
            {"user_id": self.user_id},
            {"$set": {"user_id": self.user_id, "data": data}},
            upsert=True
        )
        logger.debug(f"[CardPackManager] 用户{self.user_id}卡包数据已保存到Redis和MongoDB")

    async def load(self):
        """
        优先从Redis加载，无则从MongoDB加载并写回Redis
        """
        await self.sync_with_global()
        redis = await self._get_redis()
        value = await redis.get(self.redis_key)
        logger.info(f"[CardPackManager] 用户{self.user_id}卡包数据: {value}")
        if value:
            try:
                data = json.loads(value)
                self.card_packs = {pack_id: CardPack.from_dict(pack_data) for pack_id, pack_data in data.items()}
                logger.info(f"[CardPackManager] 用户{self.user_id}卡包数据已从Redis加载")
                return
            except Exception as e:
                logger.error(f"[CardPackManager] 加载卡包数据失败: {e}")
        # Redis无数据，尝试MongoDB
        doc = await self._mongo_collection.find_one({"user_id": self.user_id})
        if doc and "data" in doc:
            data = doc["data"]
            self.card_packs = {pack_id: CardPack.from_dict(pack_data) for pack_id, pack_data in data.items()}
            # 写回Redis
            await redis.set(self.redis_key, json.dumps(data, ensure_ascii=False))
            logger.info(f"[CardPackManager] 用户{self.user_id}卡包数据已从MongoDB加载并写回Redis")
        else:
            self._load_from_config()
            await self.save() 