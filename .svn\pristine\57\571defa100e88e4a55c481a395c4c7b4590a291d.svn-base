from datetime import datetime, timedelta
import json
import time
import uuid
from typing import Dict, List, Optional, Union, Any

class CardPack:
    """
    卡包基础类，表示一个可抽取的卡包实例
    支持多种类型：普通、限时、限次、每日/每周刷新等
    """
    def __init__(
        self, 
        pack_id: str,
        cfg_id: int, 
        user_id: str,
        pack_type: str = "normal",
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        limit_count: Optional[int] = None,
        refresh_type: Optional[str] = None,
        free_times: int = 0,
        free_interval: Optional[int] = None,
        last_free_time: Optional[int] = None,
        joined_packs: List[int] = None,
        public_luck: Optional[int] = None,
        luck_stars: List[int] = None,
        luck_num: Optional[int] = None,
        gift_time: int = 0,
        card_star_expect: List[List[Union[int, float]]] = None
    ):
        # 基本信息
        self.pack_id = pack_id  # 实例唯一ID
        self.cfg_id = cfg_id    # 配置ID
        self.user_id = user_id  # 所属用户ID
        self.pack_type = pack_type  # 卡包类型: normal, timed, limited, daily, weekly
        
        # 时间限制
        self.start_time = start_time or int(time.time())
        self.end_time = end_time
        
        # 次数限制
        # None 表示无限制
        self.limit_count = None if (limit_count is None or int(limit_count) == -1) else int(limit_count)
        self.remain_count = self.limit_count
        
        # 刷新机制
        self.refresh_type = refresh_type  # None, daily, weekly
        self.last_refresh_time = int(time.time())
        
        # 免费抽取
        self.free_times = free_times
        self.free_interval = free_interval  # 免费抽取间隔(秒)
        self.last_free_time = last_free_time or int(time.time())
        
        # 保底机制
        self.public_luck = public_luck  # 公开保底次数
        self.luck_stars = luck_stars or []  # 保底星级
        self.luck_num = luck_num  # 隐藏保底次数
        
        # 累计奖励
        self.gift_time = gift_time  # 累计抽取次数
        
        # 卡包内容
        self.joined_packs = joined_packs or []  # 已加入的卡包ID列表
        self.card_star_expect = card_star_expect or []  # 卡星级期望
        
        # 半价标记
        self.half_price = False
        
    def is_active(self, now: Optional[int] = None) -> bool:
        """检查卡包当前是否可用"""
        now = now or int(time.time())
        
        # 检查时间限制
        if self.end_time and now > self.end_time:
            return False
            
        # 检查次数限制
        if self.limit_count is not None and self.remain_count is not None and self.remain_count <= 0:
            return False
            
        return True
        
    def can_draw(self, now: Optional[int] = None) -> bool:
        """检查是否可以抽卡"""
        if not self.is_active(now):
            return False
        
        return True
        
    def can_draw_free(self, now: Optional[int] = None) -> bool:
        """检查是否可以免费抽卡"""
        now = now or int(time.time())
        
        # 有免费次数
        if self.free_times > 0:
            return True
            
        # 检查免费间隔
        if self.free_interval and self.last_free_time:
            next_free_time = self.last_free_time + self.free_interval
            if now >= next_free_time:
                return True
                
        return False
        
    def refresh(self, now: Optional[int] = None) -> bool:
        """根据刷新规则刷新卡包状态"""
        now = now or int(time.time())
        if not self.refresh_type:
            return False
            
        # 计算上次刷新到现在是否需要刷新
        last_refresh = datetime.fromtimestamp(self.last_refresh_time)
        current = datetime.fromtimestamp(now)
        
        refreshed = False
        
        if self.refresh_type == "daily":
            # 检查是否跨天
            if (current.year > last_refresh.year or 
                current.month > last_refresh.month or 
                current.day > last_refresh.day):
                self._do_refresh()
                refreshed = True
                
        elif self.refresh_type == "weekly":
            # 检查是否跨周
            last_week = last_refresh.isocalendar()[1]
            current_week = current.isocalendar()[1]
            if (current.year > last_refresh.year or 
                current_week > last_week):
                self._do_refresh()
                refreshed = True
                
        if refreshed:
            self.last_refresh_time = now
            
        return refreshed
        
    def _do_refresh(self):
        """执行刷新操作"""
        # 重置次数
        if self.limit_count is not None:
            self.remain_count = self.limit_count
            
        # 重置免费次数
        if hasattr(self, "cfg") and self.cfg.get("free_times"):
            self.free_times = self.cfg.get("free_times")
            
    def draw(self, use_free: bool = False, now: Optional[int] = None) -> bool:
        """
        执行抽卡，更新次数、免费次数等
        返回是否抽卡成功
        """
        now = now or int(time.time())
        
        # 检查是否可抽
        if not self.can_draw(now):
            return False
            
        # 使用免费次数
        if use_free and self.can_draw_free(now):
            if self.free_times > 0:
                self.free_times -= 1
            else:
                self.last_free_time = now
                self.half_price = True
        else:
            # 消耗次数
            if self.limit_count is not None:
                if self.remain_count is not None and self.remain_count <= 0:
                    return False
                self.remain_count -= 1
                
            self.half_price = False
            
        # 更新保底计数
        if self.luck_num is not None and self.luck_num > 0:
            self.luck_num -= 1
            
        if self.public_luck is not None and self.public_luck > 0:
            self.public_luck -= 1
            
        # 更新累计奖励次数
        self.gift_time += 1
        
        return True
        
    def join_pack(self, pack_cfg_id: int) -> bool:
        """加入其他卡包配置到此卡包"""
        if pack_cfg_id not in self.joined_packs:
            self.joined_packs.append(pack_cfg_id)
            return True
        return False
        
    def reset_luck(self, cfg_guard: Optional[int] = None):
        """重置保底次数"""
        if cfg_guard is not None:
            self.luck_num = cfg_guard
        elif hasattr(self, "cfg") and self.cfg.get("guard"):
            self.luck_num = self.cfg.get("guard")
            
    def init_public_luck(self, cfg_public_luck: Optional[str] = None):
        """初始化公开保底"""
        self.luck_stars = []
        
        if cfg_public_luck:
            parts = cfg_public_luck.split("|")
            if len(parts) >= 2:
                if self.public_luck is None or self.public_luck == 0:
                    self.public_luck = int(parts[0])
                    
                star_parts = parts[1].split(",")
                for star in star_parts:
                    self.luck_stars.append(int(star))
        
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典，limit_count/remain_count为None时对外返回-1"""
        return {
            "pack_id": self.pack_id,
            "cfg_id": self.cfg_id,
            "user_id": self.user_id,
            "pack_type": self.pack_type,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "limit_time": -1 if self.limit_count is None else self.limit_count,
            "remain_count": -1 if self.remain_count is None else self.remain_count,
            "refresh_type": self.refresh_type,
            "last_refresh_time": self.last_refresh_time,
            "free_times": self.free_times,
            "free_interval": self.free_interval,
            "last_free_time": self.last_free_time,
            "joined_packs": self.joined_packs,
            "public_luck": self.public_luck,
            "luck_stars": self.luck_stars,
            "luck_num": self.luck_num,
            "gift_time": self.gift_time,
            "half_price": self.half_price,
            "card_star_expect": self.card_star_expect
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CardPack':
        """从字典创建卡包实例"""
        # 兼容limit_time=-1的反序列化
        limit_count = data.get("limit_time", None)
        if limit_count == -1:
            limit_count = None
        return cls(
            pack_id=data.get("pack_id"),
            cfg_id=data.get("cfg_id"),
            user_id=data.get("user_id"),
            pack_type=data.get("pack_type", "normal"),
            start_time=data.get("start_time"),
            end_time=data.get("end_time"),
            limit_count=limit_count,
            refresh_type=data.get("refresh_type"),
            free_times=data.get("free_times", 0),
            free_interval=data.get("free_interval"),
            last_free_time=data.get("last_free_time"),
            joined_packs=data.get("joined_packs"),
            public_luck=data.get("public_luck"),
            luck_stars=data.get("luck_stars"),
            luck_num=data.get("luck_num"),
            gift_time=data.get("gift_time", 0),
            card_star_expect=data.get("card_star_expect")
        ) 