[{"id": 1, "key": "range", "val": 1}, {"id": 2, "key": "range", "val": 1}, {"id": 3, "key": "range", "val": 1}, {"id": 4, "key": "mindamage", "val": 6}, {"id": 5, "key": "mindamage", "val": 7}, {"id": 6, "key": "mindamage", "val": 8}, {"id": 7, "key": "mindamage", "val": 9}, {"id": 8, "key": "mindamage", "val": 10}, {"id": 9, "key": "mindamage", "val": 11}, {"id": 10, "key": "mindamage", "val": 12}, {"id": 11, "key": "mindamage", "val": 13}, {"id": 12, "key": "mindamage", "val": 14}, {"id": 13, "key": "mindamage", "val": 15}, {"id": 14, "key": "mindamage", "val": 16}, {"id": 15, "key": "mindamage", "val": 17}, {"id": 16, "key": "mindamage", "val": 18}, {"id": 17, "key": "mindamage", "val": 19}, {"id": 18, "key": "mindamage", "val": 20}, {"id": 19, "key": "maxdamage", "val": 6}, {"id": 20, "key": "maxdamage", "val": 7}, {"id": 21, "key": "maxdamage", "val": 8}, {"id": 22, "key": "maxdamage", "val": 9}, {"id": 23, "key": "maxdamage", "val": 10}, {"id": 24, "key": "maxdamage", "val": 11}, {"id": 25, "key": "maxdamage", "val": 12}, {"id": 26, "key": "maxdamage", "val": 13}, {"id": 27, "key": "maxdamage", "val": 14}, {"id": 28, "key": "maxdamage", "val": 15}, {"id": 29, "key": "maxdamage", "val": 16}, {"id": 30, "key": "maxdamage", "val": 17}, {"id": 31, "key": "maxdamage", "val": 18}, {"id": 32, "key": "maxdamage", "val": 19}, {"id": 33, "key": "maxdamage", "val": 20}, {"id": 34, "key": "maxhp", "val": 10}, {"id": 35, "key": "maxhp", "val": 11}, {"id": 36, "key": "maxhp", "val": 12}, {"id": 37, "key": "maxhp", "val": 13}, {"id": 38, "key": "maxhp", "val": 14}, {"id": 39, "key": "maxhp", "val": 15}, {"id": 40, "key": "maxhp", "val": 16}, {"id": 41, "key": "maxhp", "val": 17}, {"id": 42, "key": "maxhp", "val": 18}, {"id": 43, "key": "maxhp", "val": 19}, {"id": 44, "key": "maxhp", "val": 20}, {"id": 45, "key": "maxhp", "val": 21}, {"id": 46, "key": "maxhp", "val": 22}, {"id": 47, "key": "maxhp", "val": 23}, {"id": 48, "key": "maxhp", "val": 24}, {"id": 49, "key": "maxhp", "val": 25}, {"id": 50, "key": "maxhp", "val": 26}, {"id": 51, "key": "maxhp", "val": 27}, {"id": 52, "key": "maxhp", "val": 28}, {"id": 53, "key": "maxhp", "val": 29}, {"id": 54, "key": "maxhp", "val": 30}, {"id": 55, "key": "maxhp", "val": 31}, {"id": 56, "key": "maxhp", "val": 32}, {"id": 57, "key": "maxhp", "val": 33}, {"id": 58, "key": "maxhp", "val": 34}, {"id": 59, "key": "maxhp", "val": 35}, {"id": 60, "key": "maxhp", "val": 36}, {"id": 61, "key": "maxhp", "val": 37}, {"id": 62, "key": "maxhp", "val": 38}, {"id": 63, "key": "maxhp", "val": 39}, {"id": 64, "key": "maxhp", "val": 40}, {"id": 65, "key": "maxhp", "val": 41}, {"id": 66, "key": "maxhp", "val": 42}, {"id": 67, "key": "maxhp", "val": 43}, {"id": 68, "key": "maxhp", "val": 44}, {"id": 69, "key": "maxhp", "val": 45}, {"id": 70, "key": "maxhp", "val": 46}, {"id": 71, "key": "maxhp", "val": 47}, {"id": 72, "key": "maxhp", "val": 48}, {"id": 73, "key": "maxhp", "val": 49}, {"id": 74, "key": "maxhp", "val": 50}, {"id": 75, "key": "defence", "val": 10}, {"id": 76, "key": "defence", "val": 11}, {"id": 77, "key": "defence", "val": 12}, {"id": 78, "key": "defence", "val": 13}, {"id": 79, "key": "defence", "val": 14}, {"id": 80, "key": "defence", "val": 15}, {"id": 81, "key": "defence", "val": 16}, {"id": 82, "key": "defence", "val": 17}, {"id": 83, "key": "defence", "val": 18}, {"id": 84, "key": "defence", "val": 19}, {"id": 85, "key": "defence", "val": 20}, {"id": 86, "key": "defence", "val": 21}, {"id": 87, "key": "defence", "val": 22}, {"id": 88, "key": "defence", "val": 23}, {"id": 89, "key": "defence", "val": 24}, {"id": 90, "key": "defence", "val": 25}, {"id": 91, "key": "defence", "val": 26}, {"id": 92, "key": "defence", "val": 27}, {"id": 93, "key": "defence", "val": 28}, {"id": 94, "key": "defence", "val": 29}, {"id": 95, "key": "defence", "val": 30}]