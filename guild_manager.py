"""
公会数据库管理器
处理公会相关的MongoDB数据库操作
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from mongodb_manager import MongoDBManager
from guild_models import Guild, GuildMember, GuildApplication, GuildStatus, ApplicationStatus, GuildPosition
from guild_cache_manager import GuildCacheManager
from config import config

logger = logging.getLogger(__name__)


class GuildDatabaseManager:
    """公会数据库管理器"""
    
    def __init__(self):
        self.db_manager = None
        self.collections = config.get_database_collections()
        self.db = None        
    async def _get_db(self):
        """获取数据库连接"""
        if self.db is None:
            self.db_manager = await MongoDBManager.get_instance()
            self.db = await self.db_manager.get_db()
        return self.db

    def _clean_mongodb_data(self, data: dict) -> dict:
        """清理MongoDB数据，处理特殊类型"""
        if not data:
            return data

        from datetime import datetime
        try:
            from bson import ObjectId
        except ImportError:
            ObjectId = None

        # 处理ObjectId和datetime类型
        for key, value in list(data.items()):
            if ObjectId and isinstance(value, ObjectId):
                data[key] = str(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()

        return data

    # ==================== 公会基础操作 ====================
    
    async def create_guild(self, guild: Guild) -> bool:
        """创建公会"""
        try:
            db = await self._get_db()

            # 获取公会集合
            guilds_collection = db[self.collections["guilds"]]

            # 检查公会名称是否已存在
            existing = await guilds_collection.find_one({"name": guild.name, "status": GuildStatus.ACTIVE})
            if existing:
                logger.warning(f"公会名称已存在: {guild.name}")
                return False

            # 插入公会记录
            guild_data = guild.to_dict()
            result = await guilds_collection.insert_one(guild_data)
            
            if result.inserted_id:
                logger.info(f"公会创建成功: {guild.guild_id} - {guild.name}")
                return True
            else:
                logger.error(f"公会创建失败: {guild.guild_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建公会时发生错误: {str(e)}")
            return False

    async def get_guild_by_id(self, guild_id: str) -> Optional[Guild]:
        """根据ID获取公会信息"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]
            guild_data = await guilds_collection.find_one({"guild_id": guild_id, "status": GuildStatus.ACTIVE})
            
            if guild_data:
                # 移除MongoDB的_id字段
                guild_data.pop("_id", None)
                return Guild.from_dict(guild_data)
            return None
            
        except Exception as e:
            logger.error(f"获取公会信息时发生错误: {str(e)}")
            return None

    async def get_guild_by_name(self, name: str) -> Optional[Guild]:
        """根据名称获取公会信息"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]
            guild_data = await guilds_collection.find_one({"name": name, "status": GuildStatus.ACTIVE})
            
            if guild_data:
                guild_data.pop("_id", None)
                return Guild.from_dict(guild_data)
            return None
            
        except Exception as e:
            logger.error(f"根据名称获取公会信息时发生错误: {str(e)}")
            return None

    async def update_guild(self, guild_id: str, update_data: Dict[str, Any]) -> bool:
        """更新公会信息"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]

            # 添加更新时间
            update_data["updated_at"] = datetime.now()

            result = await guilds_collection.update_one(
                {"guild_id": guild_id, "status": GuildStatus.ACTIVE},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"公会信息更新成功: {guild_id}")
                return True
            else:
                logger.warning(f"公会信息更新失败或无变化: {guild_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新公会信息时发生错误: {str(e)}")
            return False

    async def disband_guild(self, guild_id: str) -> bool:
        """解散公会"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]

            # 更新公会状态为已解散
            result = await guilds_collection.update_one(
                {"guild_id": guild_id, "status": GuildStatus.ACTIVE},
                {"$set": {"status": GuildStatus.DISBANDED, "updated_at": datetime.now()}}
            )

            if result.modified_count > 0:
                # 删除所有成员记录
                await db.guild_members.delete_many({"guild_id": guild_id})
                # 删除所有待处理申请
                await db.guild_applications.delete_many({"guild_id": guild_id, "status": ApplicationStatus.PENDING})
                
                logger.info(f"公会解散成功: {guild_id}")
                return True
            else:
                logger.warning(f"公会解散失败: {guild_id}")
                return False
                
        except Exception as e:
            logger.error(f"解散公会时发生错误: {str(e)}")
            return False

    async def search_guilds(self, keyword: str = "", page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """搜索公会"""
        try:
            db = await self._get_db()
            
            # 构建查询条件
            query = {"status": GuildStatus.ACTIVE}
            if keyword:
                query["$or"] = [
                    {"name": {"$regex": keyword, "$options": "i"}},
                    {"description": {"$regex": keyword, "$options": "i"}}
                ]
            
            # 计算跳过的记录数
            skip = (page - 1) * page_size
            
            # 获取公会集合
            guilds_collection = db[self.collections["guilds"]]

            # 查询公会列表
            cursor = guilds_collection.find(query).sort("level", -1).sort("member_count", -1).skip(skip).limit(page_size)
            guilds = []
            
            async for guild_data in cursor:
                guild_data.pop("_id", None)
                # 处理datetime字段序列化
                guild_data = self._clean_mongodb_data(guild_data)
                guilds.append(guild_data)
            
            # 获取总数
            total = await guilds_collection.count_documents(query)
            
            return {
                "guilds": guilds,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            logger.error(f"搜索公会时发生错误: {str(e)}")
            return {"guilds": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}

    # ==================== 公会成员操作 ====================
    
    async def add_member(self, member: GuildMember) -> bool:
        """添加公会成员"""
        try:
            db = await self._get_db()
            player_id = member.player_id            
            logger.info(f"添加公会成员: {member.guild_id} - {player_id}")
            if not player_id:
                logger.error("添加公会成员失败: 玩家ID不能为空")
                return False
            # 检查成员是否已存在
            existing = await db.guild_members.find_one({"player_id": player_id})
            if existing:
                logger.warning(f"玩家已在其他公会中: {player_id}")
                return False
            
            # 插入成员记录
            member_data = member.to_dict()
            result = await db.guild_members.insert_one(member_data)            
            if result.inserted_id:
                # 更新公会成员数量
                await self._update_member_count(member.guild_id)
                logger.info(f"公会成员添加成功: {member.guild_id} - {player_id}")
                return True
            else:
                logger.error(f"公会成员添加失败: {player_id}")
                return False
                
        except Exception as e:
            logger.error(f"添加公会成员时发生错误: {str(e)}")
            return False

    async def remove_member(self, guild_id: str, player_id: str) -> bool:
        """移除公会成员"""
        try:
            db = await self._get_db()
            
            result = await db.guild_members.delete_one({"guild_id": guild_id, "player_id": player_id})
            
            if result.deleted_count > 0:
                # 更新公会成员数量
                await self._update_member_count(guild_id)
                logger.info(f"公会成员移除成功: {guild_id} - {player_id}")
                return True
            else:
                logger.warning(f"公会成员移除失败: {guild_id} - {player_id}")
                return False
                
        except Exception as e:
            logger.error(f"移除公会成员时发生错误: {str(e)}")
            return False

    async def get_member(self, guild_id: str, player_id: str) -> Optional[GuildMember]:
        """获取公会成员信息"""
        try:
            db = await self._get_db()
            member_data = await db.guild_members.find_one({"guild_id": guild_id, "player_id": player_id})
            
            if member_data:
                member_data.pop("_id", None)
                return GuildMember.from_dict(member_data)
            else:
                logger.error(f"公会成员不存在: {guild_id} - {player_id}")
            return None
            
        except Exception as e:
            logger.error(f"获取公会成员信息时发生错误: {str(e)}")
            return None

    async def get_player_guild(self, player_id: str) -> Optional[str]:
        """获取玩家所在的公会ID"""
        try:
            db = await self._get_db()
            member_data = await db.guild_members.find_one({"player_id": player_id})
            
            if member_data:
                return member_data["guild_id"]
            return None
            
        except Exception as e:
            logger.error(f"获取玩家公会信息时发生错误: {str(e)}")
            return None

    async def get_guild_members(self, guild_id: str) -> List[GuildMember]:
        """获取公会成员列表"""
        try:
            db = await self._get_db()
            cursor = db.guild_members.find({"guild_id": guild_id}).sort("position", 1).sort("joined_at", 1)
            
            members = []
            async for member_data in cursor:
                member_data.pop("_id", None)
                members.append(GuildMember.from_dict(member_data))
            
            return members
            
        except Exception as e:
            logger.error(f"获取公会成员列表时发生错误: {str(e)}")
            return []

    async def update_member_position(self, guild_id: str, player_id: str, position: GuildPosition) -> bool:
        """更新成员职位"""
        try:
            db = await self._get_db()
            
            result = await db.guild_members.update_one(
                {"guild_id": guild_id, "player_id": player_id},
                {"$set": {"position": position}}
            )
            
            if result.modified_count > 0:
                logger.info(f"成员职位更新成功: {guild_id} - {player_id} -> {position}")
                return True
            else:
                logger.warning(f"成员职位更新失败: {guild_id} - {player_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新成员职位时发生错误: {str(e)}")
            return False

    async def _update_member_count(self, guild_id: str):
        """更新公会成员数量"""
        try:
            db = await self._get_db()
            
            # 统计成员数量
            count = await db.guild_members.count_documents({"guild_id": guild_id})
            
            # 获取公会集合
            guilds_collection = db[self.collections["guilds"]]

            # 更新公会记录
            await guilds_collection.update_one(
                {"guild_id": guild_id},
                {"$set": {"member_count": count, "updated_at": datetime.now()}}
            )
            
        except Exception as e:
            logger.error(f"更新公会成员数量时发生错误: {str(e)}")

    # ==================== 公会申请操作 ====================
    
    async def create_application(self, application: GuildApplication) -> bool:
        """创建公会申请"""
        try:
            db = await self._get_db()
            
            # 检查是否已有待处理的申请
            existing = await db.guild_applications.find_one({
                "guild_id": application.guild_id,
                "player_id": application.player_id,
                "status": ApplicationStatus.PENDING
            })
            
            if existing:
                logger.warning(f"玩家已有待处理的申请: {application.player_id} -> {application.guild_id}")
                return False
            
            # 插入申请记录
            app_data = application.to_dict()
            result = await db.guild_applications.insert_one(app_data)
            
            if result.inserted_id:
                logger.info(f"公会申请创建成功: {application.application_id}")
                return True
            else:
                logger.error(f"公会申请创建失败: {application.application_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建公会申请时发生错误: {str(e)}")
            return False

    async def get_application(self, application_id: str) -> Optional[GuildApplication]:
        """获取公会申请信息"""
        try:
            db = await self._get_db()
            app_data = await db.guild_applications.find_one({"application_id": application_id})

            if app_data:
                app_data.pop("_id", None)
                return GuildApplication.from_dict(app_data)
            return None

        except Exception as e:
            logger.error(f"获取公会申请信息时发生错误: {str(e)}")
            return None

    async def get_guild_applications(self, guild_id: str, status: ApplicationStatus = None) -> List[GuildApplication]:
        """获取公会申请列表"""
        try:
            db = await self._get_db()

            query = {"guild_id": guild_id}
            if status:
                query["status"] = status

            cursor = db.guild_applications.find(query).sort("applied_at", -1)

            applications = []
            async for app_data in cursor:
                app_data.pop("_id", None)
                applications.append(GuildApplication.from_dict(app_data))

            return applications

        except Exception as e:
            logger.error(f"获取公会申请列表时发生错误: {str(e)}")
            return []

    async def get_player_applications(self, player_id: str) -> List[GuildApplication]:
        """获取玩家的申请列表"""
        try:
            db = await self._get_db()
            cursor = db.guild_applications.find({"player_id": player_id}).sort("applied_at", -1)

            applications = []
            async for app_data in cursor:
                app_data.pop("_id", None)
                applications.append(GuildApplication.from_dict(app_data))

            return applications

        except Exception as e:
            logger.error(f"获取玩家申请列表时发生错误: {str(e)}")
            return []

    async def process_application(self, application_id: str, status: ApplicationStatus,
                                processed_by: str, reason: str = "") -> bool:
        """处理公会申请"""
        try:
            db = await self._get_db()

            update_data = {
                "status": status,
                "processed_at": datetime.now(),
                "processed_by": processed_by
            }

            if reason:
                update_data["reason"] = reason

            result = await db.guild_applications.update_one(
                {"application_id": application_id, "status": ApplicationStatus.PENDING},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"公会申请处理成功: {application_id} -> {status}")
                return True
            else:
                logger.warning(f"公会申请处理失败: {application_id}")
                return False

        except Exception as e:
            logger.error(f"处理公会申请时发生错误: {str(e)}")
            return False

    async def cleanup_expired_applications(self, days: int = 7) -> int:
        """清理过期的申请"""
        try:
            db = await self._get_db()

            # 计算过期时间
            expire_time = datetime.now() - timedelta(days=days)

            # 更新过期申请状态
            result = await db.guild_applications.update_many(
                {
                    "status": ApplicationStatus.PENDING,
                    "applied_at": {"$lt": expire_time}
                },
                {"$set": {"status": ApplicationStatus.EXPIRED}}
            )

            logger.info(f"清理过期申请: {result.modified_count} 条")
            return result.modified_count

        except Exception as e:
            logger.error(f"清理过期申请时发生错误: {str(e)}")
            return 0

    # ==================== 工具方法 ====================

    def generate_guild_id(self) -> str:
        """生成公会ID"""
        return f"guild_{uuid.uuid4().hex[:12]}"

    def generate_application_id(self) -> str:
        """生成申请ID"""
        return f"app_{uuid.uuid4().hex[:12]}"

    async def is_guild_name_available(self, name: str) -> bool:
        """检查公会名称是否可用"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]
            existing = await guilds_collection.find_one({"name": name, "status": GuildStatus.ACTIVE})
            return existing is None
        except Exception as e:
            logger.error(f"检查公会名称可用性时发生错误: {str(e)}")
            return False

    async def get_guild_count(self) -> int:
        """获取活跃公会总数"""
        try:
            db = await self._get_db()
            guilds_collection = db[self.collections["guilds"]]
            return await guilds_collection.count_documents({"status": GuildStatus.ACTIVE})
        except Exception as e:
            logger.error(f"获取公会总数时发生错误: {str(e)}")
            return 0
