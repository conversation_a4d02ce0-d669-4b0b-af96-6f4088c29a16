"""
商店系统数据模型
"""

import json
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import List, Optional, Dict, Any
import math

from enums import ItemType


def sanitize_for_json(data: Any) -> Any:
    """
    清理数据以确保JSON序列化兼容性
    处理无穷大、NaN等不兼容的浮点数值
    """
    if isinstance(data, dict):
        return {key: sanitize_for_json(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_for_json(item) for item in data]
    elif isinstance(data, float):
        if math.isinf(data):
            return 0.0 if data < 0 else 1.0  # 负无穷返回0，正无穷返回1
        elif math.isnan(data):
            return 0.0  # NaN返回0
        else:
            return data
    else:
        return data


@dataclass
class Shop:
    """商店配置"""
    shop_id: str                    # 商店ID
    shop_name: str                  # 商店名称
    shop_type: str                  # 商店类型 (normal, guild, vip, event, arena)
    description: str                # 商店描述
    icon: str                       # 商店图标
    
    # 访问控制
    is_active: bool                 # 是否激活
    access_conditions: Dict         # 访问条件配置
    
    # 刷新配置
    refresh_config: Optional[Dict]  # 刷新配置
    
    # 显示配置
    sort_order: int                 # 排序权重
    ui_config: Dict                 # UI配置
    
    created_at: datetime
    updated_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = data['created_at'].isoformat()
        data['updated_at'] = data['updated_at'].isoformat()
        return sanitize_for_json(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Shop':
        """从字典创建对象"""
        data = data.copy()
        data.pop('_id', None)
        
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)


@dataclass
class ShopItemConfig:
    """商店商品配置"""
    config_id: str                  # 配置ID
    shop_id: str                    # 商店ID

    # 道具配置
    item_template_id: str           # 道具模板ID
    item_quantity: int              # 道具数量

    # 价格配置
    price_config: Dict              # 价格配置

    # 可用性配置
    availability: Dict              # 可用性配置

    # 刷新配置
    refresh_weight: int             # 刷新权重
    refresh_probability: float      # 出现概率

    # 显示配置
    is_active: bool                 # 是否激活
    sort_order: int                 # 排序
    display_config: Dict            # 显示配置

    created_at: datetime
    updated_at: datetime

    # 可选字段（有默认值的字段必须放在最后）
    slot_id: Optional[int] = None          # 商品槽位ID
    item_quality: Optional[int] = None     # 道具品质
    item_type: ItemType = ItemType.ITEM    # 道具类型，默认为普通道具
    purchase_limit: Optional[Dict] = None  # 限购配置

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = data['created_at'].isoformat()
        data['updated_at'] = data['updated_at'].isoformat()

        # 确保item_type序列化为字符串
        if isinstance(data.get('item_type'), ItemType):
            data['item_type'] = data['item_type'].value

        # 清理数据以确保JSON序列化兼容性
        return sanitize_for_json(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShopItemConfig':
        """从字典创建对象"""
        data = data.copy()
        data.pop('_id', None)

        # 处理时间字段
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])

        # 为向后兼容性处理item_type字段
        if 'item_type' not in data or data['item_type'] is None:
            # 如果数据库中没有item_type字段，则不传递该参数，使用默认值
            data.pop('item_type', None)
        else:
            # 确保item_type是ItemType枚举类型
            if isinstance(data['item_type'], str):
                data['item_type'] = ItemType(data['item_type'])

        return cls(**data)


@dataclass
class ShopDiscount:
    """商店折扣配置"""
    discount_id: str                # 折扣ID
    discount_name: str              # 折扣名称
    
    # 适用范围
    scope_type: str                 # shop, item, category, global
    scope_values: List[str]         # 适用的商店/商品/分类ID列表
    
    # 折扣规则
    discount_rule: Dict             # 折扣规则
    
    # 使用条件
    conditions: Dict                # 使用条件
    
    # 优先级和互斥
    priority: int                   # 优先级
    mutex_groups: List[str]         # 互斥组
    stackable: bool                 # 是否可叠加
    
    # 使用统计
    usage_stats: Dict               # 使用统计
    
    is_active: bool
    created_at: datetime
    updated_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = data['created_at'].isoformat()
        data['updated_at'] = data['updated_at'].isoformat()
        return sanitize_for_json(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShopDiscount':
        """从字典创建对象"""
        data = data.copy()
        data.pop('_id', None)
        
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)


@dataclass
class ShopPurchase:
    """购买记录"""
    purchase_id: str                # 购买ID
    player_id: str                  # 玩家ID
    shop_id: str                    # 商店ID
    config_id: str                  # 商品配置ID
    
    # 购买详情
    item_template_id: str           # 道具模板ID
    item_quantity: int              # 购买数量
    item_instances: List[str]       # 创建的道具实例ID列表
    
    # 价格信息
    currency_type: str              # 货币类型
    original_price: int             # 原价
    final_price: int                # 实付价格
    discount_applied: List[str]     # 应用的折扣ID列表
    
    # 限购信息
    limit_context: Optional[Dict]   # 限购上下文
    
    # 时间信息
    purchase_time: datetime         # 购买时间
    
    # 元数据
    metadata: Dict                  # 额外信息
    
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['purchase_time'] = data['purchase_time'].isoformat()
        data['created_at'] = data['created_at'].isoformat()
        return sanitize_for_json(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShopPurchase':
        """从字典创建对象"""
        data = data.copy()
        data.pop('_id', None)
        
        if isinstance(data.get('purchase_time'), str):
            data['purchase_time'] = datetime.fromisoformat(data['purchase_time'])
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        return cls(**data)


@dataclass
class PurchaseLimitCounter:
    """限购计数器"""
    counter_id: str                 # 计数器ID
    player_id: str                  # 玩家ID
    limit_scope: str                # 限购范围
    scope_value: str                # 范围值
    limit_type: str                 # 限购类型
    
    # 周期信息
    period_start: datetime          # 周期开始时间
    period_end: datetime            # 周期结束时间
    
    # 计数信息
    current_count: int              # 当前计数
    limit_count: int                # 限制数量
    
    # 元数据
    last_purchase_time: datetime    # 最后购买时间
    last_updated: datetime          # 最后更新时间
    
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['period_start'] = data['period_start'].isoformat()
        data['period_end'] = data['period_end'].isoformat()
        data['last_purchase_time'] = data['last_purchase_time'].isoformat()
        data['last_updated'] = data['last_updated'].isoformat()
        data['created_at'] = data['created_at'].isoformat()
        return sanitize_for_json(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PurchaseLimitCounter':
        """从字典创建对象"""
        data = data.copy()
        data.pop('_id', None)
        
        date_fields = ['period_start', 'period_end', 'last_purchase_time', 'last_updated', 'created_at']
        for field in date_fields:
            if isinstance(data.get(field), str):
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)


# 请求和响应模型
@dataclass
class PurchaseRequest:
    """购买请求"""
    player_id: str
    config_id: str
    quantity: int
    metadata: Optional[Dict] = None


@dataclass
class PurchaseResult:
    """购买结果"""
    success: bool
    purchase_id: Optional[str] = None
    items: Optional[List[str]] = None
    error: Optional[str] = None
    data: Optional[Dict] = None


@dataclass
class ValidationResult:
    """验证结果"""
    success: bool
    error: Optional[str] = None
    details: Optional[Dict] = None


@dataclass
class PriceInfo:
    """价格信息"""
    original_price: int
    final_price: int
    currency_type: str
    discounts_applied: List[str]
    discount_amount: int


@dataclass
class LimitCheckResult:
    """限购检查结果"""
    can_purchase: bool
    remaining: int
    current_count: int
    limit_count: int
    limit_type: str
    error: Optional[str] = None


# 常量定义
class ShopType:
    NORMAL = "normal"
    GUILD = "guild"
    VIP = "vip"
    EVENT = "event"
    ARENA = "arena"


class LimitType:
    NONE = "none"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    PERMANENT = "permanent"


class DiscountType:
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"
    FORMULA = "formula"


class ScopeType:
    ITEM = "item"
    CATEGORY = "category"
    SHOP = "shop"
    GLOBAL = "global"
