import aio_pika
import asyncio
import json
import uuid
from datetime import datetime
from typing import Callable, Dict, Any, Optional
from config import config

class TaskQueue:
    def __init__(self, queue_name: str = "task_queue"):
        """
        :param queue_name: 队列名
        """
        rabbitmq_config = config.get_rabbitmq_config()
        rabbitmq_url = f"amqp://{rabbitmq_config['username']}:{rabbitmq_config['password']}@{rabbitmq_config['host']}:{rabbitmq_config['port']}/{rabbitmq_config['virtual_host']}"
        self.rabbitmq_url = rabbitmq_url
        self.queue_name = queue_name
        self.connection: Optional[aio_pika.RobustConnection] = None
        self.channel: Optional[aio_pika.Channel] = None

    async def connect(self):
        if self.connection is None or self.connection.is_closed:
            self.connection = await aio_pika.connect_robust(self.rabbitmq_url)
        if self.channel is None or self.channel.is_closed:
            self.channel = await self.connection.channel()
            await self.channel.declare_queue(self.queue_name, durable=True)

    async def publish_task(self, task_type: str, params: Dict[str, Any]):
        if self.channel is None:
            await self.connect()
        message = {
            "type": task_type,
            "params": params,
            "task_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat()
        }
        await self.channel.default_exchange.publish(
            aio_pika.Message(
                body=json.dumps(message).encode(),
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT
            ),
            routing_key=self.queue_name
        )
        print(f"[TaskQueue] 已发布任务: {message}")

    async def consume_tasks(self, handlers: Dict[str, Callable[[Dict[str, Any]], Any]]):
        if self.channel is None:
            await self.connect()
        queue = await self.channel.declare_queue(self.queue_name, durable=True)
        print(f"[TaskQueue] 正在监听队列: {self.queue_name}")
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    try:
                        task = json.loads(message.body)
                        task_type = task.get("type")
                        params = task.get("params", {})
                        handler = handlers.get(task_type)
                        if handler:
                            result = handler(params)
                            if asyncio.iscoroutine(result):
                                await result
                        else:
                            print(f"[TaskQueue] 未知任务类型: {task_type}")
                    except Exception as e:
                        print(f"[TaskQueue] 处理任务异常: {e}")

    async def close(self):
        if self.channel and not self.channel.is_closed:
            await self.channel.close()
            self.channel = None
        if self.connection and not self.connection.is_closed:
            await self.connection.close()
            self.connection = None