"""
分布式角色WebSocket消息处理器
支持多Worker环境，处理角色相关的WebSocket消息
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from role_service_distributed import get_role_service
from role_models import RoleCreateRequest, RoleUpdateRequest, RoleState
from enums import MessageId, RoleState as RoleStateEnum
from game_database import DatabaseManager

logger = logging.getLogger(__name__)


class RoleHandlersDistributed:
    """分布式角色消息处理器"""
    
    def __init__(self):
        self.role_service = get_role_service()
        self.db_manager = DatabaseManager()
    
    async def handle_get_roles(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理获取角色列表消息"""
        try:
            logger.info(f"用户 {user_id} 请求获取角色列表")
            
            # 获取角色列表
            roles = await self.role_service.get_player_roles(user_id)
            
            return {
                "msgId": MessageId.GET_ROLES,
                "success": True,
                "data": {
                    "roles": roles,
                    "count": len(roles)
                }
            }
            
        except Exception as e:
            logger.error(f"获取角色列表失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.GET_ROLES,
                "success": False,
                "error": f"获取角色列表失败: {str(e)}"
            }
    
    async def handle_create_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理创建角色消息"""
        try:
            data = message.get("data", {})
            name = data.get("name", "").strip()
            avatar = data.get("avatar", "default")
            
            # 验证参数
            if not name:
                return {
                    "msgId": MessageId.CREATE_ROLE,
                    "success": False,
                    "error": "角色名称不能为空"
                }
            
            if len(name) > 20:
                return {
                    "msgId": MessageId.CREATE_ROLE,
                    "success": False,
                    "error": "角色名称不能超过20个字符"
                }
            
            logger.info(f"用户 {user_id} 请求创建角色: {name}")
            
            # 创建角色请求
            role_request = RoleCreateRequest(name=name, avatar=avatar)
            
            # 调用角色服务
            result = await self.role_service.create_role(user_id, role_request)
            
            return {
                "msgId": MessageId.CREATE_ROLE,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"创建角色失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.CREATE_ROLE,
                "success": False,
                "error": f"创建角色失败: {str(e)}"
            }
    
    async def handle_delete_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理删除角色消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.DELETE_ROLE,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求删除角色: {role_id}")
            
            # 调用角色服务
            result = await self.role_service.delete_role(user_id, role_id)
            
            return {
                "msgId": MessageId.DELETE_ROLE,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"删除角色失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.DELETE_ROLE,
                "success": False,
                "error": f"删除角色失败: {str(e)}"
            }
    
    async def handle_level_up_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理角色升级消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.LEVEL_UP_ROLE,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求角色升级: {role_id}")
            
            # 获取角色详情
            role_data = await self.role_service.get_role_details(user_id, role_id)
            if not role_data:
                return {
                    "msgId": MessageId.LEVEL_UP_ROLE,
                    "success": False,
                    "error": "角色不存在"
                }
            
            # 检查是否可以升级
            if role_data.get("exp", 0) < role_data.get("exp_max", 100):
                return {
                    "msgId": MessageId.LEVEL_UP_ROLE,
                    "success": False,
                    "error": "经验不足，无法升级"
                }
            
            # 添加经验触发升级
            result = await self.role_service.add_role_exp(user_id, role_id, 0)
            
            return {
                "msgId": MessageId.LEVEL_UP_ROLE,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"角色升级失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.LEVEL_UP_ROLE,
                "success": False,
                "error": f"角色升级失败: {str(e)}"
            }
    
    async def handle_add_exp_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理角色加经验消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            exp_amount = data.get("exp_amount", 0)
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.ADD_EXP_ROLE,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            if exp_amount <= 0:
                return {
                    "msgId": MessageId.ADD_EXP_ROLE,
                    "success": False,
                    "error": "经验值必须大于0"
                }
            
            logger.info(f"用户 {user_id} 请求给角色 {role_id} 加经验: {exp_amount}")
            
            # 调用角色服务
            result = await self.role_service.add_role_exp(user_id, role_id, exp_amount)
            
            return {
                "msgId": MessageId.ADD_EXP_ROLE,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"角色加经验失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.ADD_EXP_ROLE,
                "success": False,
                "error": f"角色加经验失败: {str(e)}"
            }
    
    async def handle_equip_weapon(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理装备武器消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            weapon_id = data.get("weapon_id")
            weapon_name = data.get("weapon_name", "")
            weapon_level = data.get("weapon_level", 0)
            weapon_star = data.get("weapon_star", 0)
            
            # 验证参数
            if not role_id or not weapon_id:
                return {
                    "msgId": MessageId.EQUIP_WEAPON,
                    "success": False,
                    "error": "角色ID和武器ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求装备武器: 角色{role_id}, 武器{weapon_id}")
            
            # 调用角色服务
            result = await self.role_service.equip_weapon(
                user_id, role_id, weapon_id, weapon_name, weapon_level, weapon_star
            )
            
            return {
                "msgId": MessageId.EQUIP_WEAPON,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"装备武器失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.EQUIP_WEAPON,
                "success": False,
                "error": f"装备武器失败: {str(e)}"
            }
    
    async def handle_unequip_weapon(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理卸下武器消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.UNEQUIP_WEAPON,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求卸下武器: 角色{role_id}")
            
            # 调用角色服务
            result = await self.role_service.unequip_weapon(user_id, role_id)
            
            return {
                "msgId": MessageId.UNEQUIP_WEAPON,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"卸下武器失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.UNEQUIP_WEAPON,
                "success": False,
                "error": f"卸下武器失败: {str(e)}"
            }
    
    async def handle_equip_armor(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理装备防具消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            armor_id = data.get("armor_id")
            armor_name = data.get("armor_name", "")
            armor_level = data.get("armor_level", 0)
            armor_star = data.get("armor_star", 0)
            
            # 验证参数
            if not role_id or not armor_id:
                return {
                    "msgId": MessageId.EQUIP_ARMOR,
                    "success": False,
                    "error": "角色ID和防具ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求装备防具: 角色{role_id}, 防具{armor_id}")
            
            # 调用角色服务
            result = await self.role_service.equip_armor(
                user_id, role_id, armor_id, armor_name, armor_level, armor_star
            )
            
            return {
                "msgId": MessageId.EQUIP_ARMOR,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"装备防具失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.EQUIP_ARMOR,
                "success": False,
                "error": f"装备防具失败: {str(e)}"
            }
    
    async def handle_unequip_armor(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理卸下防具消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.UNEQUIP_ARMOR,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求卸下防具: 角色{role_id}")
            
            # 调用角色服务
            result = await self.role_service.unequip_armor(user_id, role_id)
            
            return {
                "msgId": MessageId.UNEQUIP_ARMOR,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"卸下防具失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.UNEQUIP_ARMOR,
                "success": False,
                "error": f"卸下防具失败: {str(e)}"
            }
    
    async def handle_change_role_state(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理改变角色状态消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            new_state_str = data.get("state")
            
            # 验证参数
            if not role_id or not new_state_str:
                return {
                    "msgId": MessageId.CHANGE_ROLE_STATE,
                    "success": False,
                    "error": "角色ID和状态不能为空"
                }
            
            # 验证状态值
            try:
                new_state = RoleStateEnum(new_state_str)
            except ValueError:
                return {
                    "msgId": MessageId.CHANGE_ROLE_STATE,
                    "success": False,
                    "error": f"无效的角色状态: {new_state_str}"
                }
            
            logger.info(f"用户 {user_id} 请求改变角色状态: 角色{role_id}, 状态{new_state.value}")
            
            # 调用角色服务
            result = await self.role_service.change_role_state(user_id, role_id, new_state)
            
            return {
                "msgId": MessageId.CHANGE_ROLE_STATE,
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"改变角色状态失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.CHANGE_ROLE_STATE,
                "success": False,
                "error": f"改变角色状态失败: {str(e)}"
            }
    
    async def handle_lock_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理锁定角色消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.LOCK_ROLE,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求锁定角色: {role_id}")
            
            # 创建更新请求
            update_data = RoleUpdateRequest()
            # 这里需要扩展RoleUpdateRequest来支持锁定状态
            # 暂时使用change_role_state来实现
            
            return {
                "msgId": MessageId.LOCK_ROLE,
                "success": False,
                "error": "锁定功能暂未实现"
            }
            
        except Exception as e:
            logger.error(f"锁定角色失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.LOCK_ROLE,
                "success": False,
                "error": f"锁定角色失败: {str(e)}"
            }
    
    async def handle_unlock_role(self, message: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """处理解锁角色消息"""
        try:
            data = message.get("data", {})
            role_id = data.get("role_id")
            
            # 验证参数
            if not role_id:
                return {
                    "msgId": MessageId.UNLOCK_ROLE,
                    "success": False,
                    "error": "角色ID不能为空"
                }
            
            logger.info(f"用户 {user_id} 请求解锁角色: {role_id}")
            
            return {
                "msgId": MessageId.UNLOCK_ROLE,
                "success": False,
                "error": "解锁功能暂未实现"
            }
            
        except Exception as e:
            logger.error(f"解锁角色失败 {user_id}: {str(e)}")
            return {
                "msgId": MessageId.UNLOCK_ROLE,
                "success": False,
                "error": f"解锁角色失败: {str(e)}"
            }


# 全局处理器实例
_role_handlers = None

def get_role_handlers() -> RoleHandlersDistributed:
    """获取全局角色处理器实例"""
    global _role_handlers
    if _role_handlers is None:
        _role_handlers = RoleHandlersDistributed()
    return _role_handlers


# 消息处理器映射
ROLE_HANDLERS = {
    MessageId.GET_ROLES: "handle_get_roles",
    MessageId.CREATE_ROLE: "handle_create_role",
    MessageId.DELETE_ROLE: "handle_delete_role",
    MessageId.LEVEL_UP_ROLE: "handle_level_up_role",
    MessageId.ADD_EXP_ROLE: "handle_add_exp_role",
    MessageId.EQUIP_WEAPON: "handle_equip_weapon",
    MessageId.UNEQUIP_WEAPON: "handle_unequip_weapon",
    MessageId.EQUIP_ARMOR: "handle_equip_armor",
    MessageId.UNEQUIP_ARMOR: "handle_unequip_armor",
    MessageId.CHANGE_ROLE_STATE: "handle_change_role_state",
    MessageId.LOCK_ROLE: "handle_lock_role",
    MessageId.UNLOCK_ROLE: "handle_unlock_role",
}


def register_role_handlers_distributed(message_manager):
    """注册分布式角色消息处理器到消息管理器"""
    role_handlers = get_role_handlers()
    
    # 创建包装函数来处理WebSocket消息格式
    async def create_role_wrapper(websocket, username, token, data, connection_manager):
        """创建角色的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_create_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"创建角色包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.CREATE_ROLE,
                "success": False,
                "error": f"创建角色失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def get_roles_wrapper(websocket, username, token, data, connection_manager):
        """获取角色列表的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_get_roles(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"获取角色列表包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.GET_ROLES,
                "success": False,
                "error": f"获取角色列表失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def delete_role_wrapper(websocket, username, token, data, connection_manager):
        """删除角色的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_delete_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"删除角色包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.DELETE_ROLE,
                "success": False,
                "error": f"删除角色失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def level_up_role_wrapper(websocket, username, token, data, connection_manager):
        """角色升级的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_level_up_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"角色升级包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.LEVEL_UP_ROLE,
                "success": False,
                "error": f"角色升级失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def add_exp_role_wrapper(websocket, username, token, data, connection_manager):
        """角色加经验的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_add_exp_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"角色加经验包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.ADD_EXP_ROLE,
                "success": False,
                "error": f"角色加经验失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def equip_weapon_wrapper(websocket, username, token, data, connection_manager):
        """装备武器的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_equip_weapon(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"装备武器包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.EQUIP_WEAPON,
                "success": False,
                "error": f"装备武器失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def unequip_weapon_wrapper(websocket, username, token, data, connection_manager):
        """卸下武器的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_unequip_weapon(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"卸下武器包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.UNEQUIP_WEAPON,
                "success": False,
                "error": f"卸下武器失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def equip_armor_wrapper(websocket, username, token, data, connection_manager):
        """装备防具的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_equip_armor(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"装备防具包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.EQUIP_ARMOR,
                "success": False,
                "error": f"装备防具失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def unequip_armor_wrapper(websocket, username, token, data, connection_manager):
        """卸下防具的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_unequip_armor(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"卸下防具包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.UNEQUIP_ARMOR,
                "success": False,
                "error": f"卸下防具失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def change_role_state_wrapper(websocket, username, token, data, connection_manager):
        """改变角色状态的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_change_role_state(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"改变角色状态包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.CHANGE_ROLE_STATE,
                "success": False,
                "error": f"改变角色状态失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def lock_role_wrapper(websocket, username, token, data, connection_manager):
        """锁定角色的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_lock_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"锁定角色包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.LOCK_ROLE,
                "success": False,
                "error": f"锁定角色失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    async def unlock_role_wrapper(websocket, username, token, data, connection_manager):
        """解锁角色的包装函数"""
        try:
            message = {"data": data}
            result = await role_handlers.handle_unlock_role(message, username)
            
            # 发送响应
            await connection_manager.send_personal_message(result, token)
            return result
        except Exception as e:
            logger.error(f"解锁角色包装函数失败: {str(e)}")
            error_result = {
                "msgId": MessageId.UNLOCK_ROLE,
                "success": False,
                "error": f"解锁角色失败: {str(e)}"
            }
            await connection_manager.send_personal_message(error_result, token)
            return error_result
    
    # 注册所有角色处理器
    message_manager.register_handler(MessageId.GET_ROLES, get_roles_wrapper)
    message_manager.register_handler(MessageId.CREATE_ROLE, create_role_wrapper)
    message_manager.register_handler(MessageId.DELETE_ROLE, delete_role_wrapper)
    message_manager.register_handler(MessageId.LEVEL_UP_ROLE, level_up_role_wrapper)
    message_manager.register_handler(MessageId.ADD_EXP_ROLE, add_exp_role_wrapper)
    message_manager.register_handler(MessageId.EQUIP_WEAPON, equip_weapon_wrapper)
    message_manager.register_handler(MessageId.UNEQUIP_WEAPON, unequip_weapon_wrapper)
    message_manager.register_handler(MessageId.EQUIP_ARMOR, equip_armor_wrapper)
    message_manager.register_handler(MessageId.UNEQUIP_ARMOR, unequip_armor_wrapper)
    message_manager.register_handler(MessageId.CHANGE_ROLE_STATE, change_role_state_wrapper)
    message_manager.register_handler(MessageId.LOCK_ROLE, lock_role_wrapper)
    message_manager.register_handler(MessageId.UNLOCK_ROLE, unlock_role_wrapper)
    
    logger.info("分布式角色消息处理器注册完成") 