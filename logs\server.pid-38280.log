2025-08-05 20:55:19,898 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:20,641 - models - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:20,673 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:21,100 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:21,116 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:21,132 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:21,153 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:21,168 - auth - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,840 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,881 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,904 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,915 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,928 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,929 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: b9250ad4)
2025-08-05 20:55:23,936 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,970 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,984 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:23,992 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,000 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,081 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,093 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,102 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,111 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,119 - game_server - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:24,121 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 20:55:24,123 - msgManager - INFO - Monster handlers registered
2025-08-05 20:55:24,124 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 20:55:24,125 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 20:55:24,135 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 20:55:24,136 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 20:55:24,136 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 0dd2bc5e)
2025-08-05 20:55:24,184 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 20:55:24,231 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 20:55:24,231 - game_server - INFO - 模板引擎初始化成功
2025-08-05 20:55:24,236 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 20:55:24,239 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 38280)
2025-08-05 20:55:24,239 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 20:55:24,469 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 20:55:24,471 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 20:55:24,998 - ConnectionManager - INFO - 后台任务已启动 (Worker 38280)
2025-08-05 20:55:24,998 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 38280)
2025-08-05 20:55:25,007 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:55:25,007 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:55:25,009 - game_server - INFO - 游戏配置加载完成 (Worker: 38280)
2025-08-05 20:55:25,010 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:55:29,647 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 20:55:29,648 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=0, 总计=2
2025-08-05 20:55:29,648 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 38280): 2/2 (100.0%)
2025-08-05 20:55:29,648 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 20:55:29,698 - ConnectionManager - INFO - Worker 38280 开始消费广播消息，消费者标签: ctag1.753ba6108a7f41c99741f1c1dae32dd1
2025-08-05 20:55:29,699 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 0dd2bc5e)
2025-08-05 20:55:29,700 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 0dd2bc5e)
2025-08-05 20:55:29,707 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:29,709 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 20:55:29,710 - config - INFO - 检测到游戏配置文件变更: cfg_item
2025-08-05 20:55:29,718 - config - INFO - 成功加载游戏配置 cfg_item: 1924 项
2025-08-05 20:55:29,718 - config - INFO - 检测到游戏配置文件变更: monsters
2025-08-05 20:55:29,719 - config - INFO - 成功加载游戏配置 monsters: 5 项
2025-08-05 20:55:29,747 - ConnectionManager - INFO - Worker 38280 开始消费个人消息，消费者标签: ctag1.d9b6990197654d9c9f5d773cf2b0a78f
2025-08-05 20:55:29,819 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 38280)
2025-08-05 20:55:29,934 - distributed_lock - INFO - Worker 38280 获取锁超时: scheduler_initialization
2025-08-05 20:55:29,934 - game_server_scheduler_integration - INFO - Worker 38280 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 20:55:29,935 - game_server - INFO - 统一调度器初始化成功 (Worker: 38280)
2025-08-05 20:55:29,943 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 20:55:29,945 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 20:55:29,946 - game_server - INFO - 日志清理管理器已启动 (Worker: 38280)
2025-08-05 20:55:29,947 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 20:55:29,948 - game_server - INFO - Monster cooldown manager initialized (Worker: 38280)
2025-08-05 20:55:30,308 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 20:55:30,400 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 20:55:30,401 - game_server - INFO - 公会系统初始化成功 (Worker: 38280)
2025-08-05 20:55:30,403 - game_server - INFO - 邮件系统初始化成功 (Worker: 38280)
2025-08-05 20:55:30,414 - game_server - INFO - 商店系统初始化成功 (Worker: 38280)
2025-08-05 20:55:30,414 - game_server - INFO - 初始化完成 (Worker: 38280)
2025-08-05 20:55:30,654 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:56:09,500 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:56:09,591 - distributed_lock - INFO - Worker 38280 成功获取锁: shop:create:32
2025-08-05 20:56:09,592 - shop_service - INFO - Worker 38280 获取商店创建锁: 32
2025-08-05 20:56:10,312 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 20:56:10,356 - shop_database_manager - INFO - 商店创建成功: shop_6f5c8d6413fe
2025-08-05 20:56:10,444 - shop_service - INFO - Worker 38280 商店创建成功: shop_6f5c8d6413fe
2025-08-05 20:56:10,492 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 20:56:14,903 - shop_api - INFO - [ShopAPI] 获取商店详情: shop_6f5c8d6413fe
2025-08-05 20:56:29,656 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 20:56:29,657 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 20:56:30,644 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:56:31,496 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:56:31,498 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:56:39,504 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:57:00,827 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:57:09,518 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:57:29,667 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 20:57:29,668 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 20:57:30,040 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:57:30,725 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 20:57:30,725 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 20:57:39,523 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:58:00,251 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:58:09,537 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:58:29,679 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 20:58:29,680 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 20:58:30,543 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:58:39,542 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:59:00,819 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 20:59:09,544 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 21:01:01,073 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 21:01:01,074 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 21:01:01,078 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=0, 用户数=0
2025-08-05 21:01:01,078 - aiormq.connection - WARNING - Server connection <Connection: "amqp://admin:******@*************:5672/bthost?heartbeat=30" at 0x2374b746bc0> was stuck. No frames were received in 93 seconds.
2025-08-05 21:01:01,080 - aio_pika.robust_connection - INFO - Connection to amqp://admin:******@*************:5672/bthost?heartbeat=30 closed. Reconnecting after 5 seconds.
2025-08-05 21:01:01,084 - game_server - INFO - WebSocket 连接尝试，token: eyJhbGciOi..., worker: 38280
2025-08-05 21:01:01,687 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:01:01,687 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 21:01:06,091 - redis_manager - WARNING - RedisManager: ping失败，重试初始化，第1次: Timeout reading from *************:6379
2025-08-05 21:01:06,092 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 21:01:06,270 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 21:01:06,358 - ConnectionManager - INFO - 已发布用户登录通知到Redis (用户: dsadjdj23, Worker: 38280)
2025-08-05 21:01:06,409 - CacheInvalidationManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 21:01:06,453 - CacheInvalidationManager - INFO - 缓存失效通知管理器初始化完成 (Worker 38280)
2025-08-05 21:01:07,005 - game_manager - INFO - 开始初始化游戏管理器 (Worker: 38280)
2025-08-05 21:01:07,006 - game_manager - INFO - 创建PlayerSessionManager...
2025-08-05 21:01:07,008 - player_session_manager - INFO - class PlayerSessionManager:实例已创建
2025-08-05 21:01:07,009 - game_manager - INFO - 创建GameNotificationManager...
2025-08-05 21:01:07,010 - game_manager - INFO - 获取UserCacheManager...
2025-08-05 21:01:07,011 - game_manager - INFO - 获取ItemCacheManager...
2025-08-05 21:01:07,060 - game_manager - INFO - 获取GuildServiceDistributed...
2025-08-05 21:01:07,063 - game_manager - INFO - 获取ConnectionManager...
2025-08-05 21:01:07,069 - game_manager - INFO - 游戏管理器初始化完成 (Worker: 38280)
2025-08-05 21:01:07,071 - game_manager - INFO - 处理玩家登录: dsadjdj23 (Worker: 38280)
2025-08-05 21:01:07,114 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 1754353521.3秒)
2025-08-05 21:01:07,327 - player_session_manager - INFO - 创建玩家会话: dsadjdj23 (Worker: 38280)
2025-08-05 21:01:07,878 - guild_cache_manager - INFO - GuildCacheManager实例已创建
2025-08-05 21:01:09,438 - mail_database_manager - INFO - 邮件模板数据库索引检查完成
2025-08-05 21:01:09,483 - mail_database_manager - INFO - 数据库中共有 12 个未过期的邮件模板
2025-08-05 21:01:09,530 - mail_database_manager - INFO - 用户 dsadjdj23 已处理的模板ID: {'template_7fd5d6e6b55c4142', 'template_6e47793807b84b13', 'template_e827e75c8beb4d5d', 'template_46f1fcab221b4a38', 'template_575fc5088c474399', 'template_c999a774d9014253', 'template_14981f8399fe4572', 'template_c466e0ddaa8c44f2', 'template_b0a095478f034b26', 'template_0acd29605c994701', 'template_62e4594c1bf04b4b', 'template_0738ee4b4a2d4983'}
2025-08-05 21:01:09,589 - mail_database_manager - INFO - 模板 template_6e47793807b84b13 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,589 - mail_database_manager - INFO - 模板 template_c466e0ddaa8c44f2 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,590 - mail_database_manager - INFO - 模板 template_c999a774d9014253 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,590 - mail_database_manager - INFO - 模板 template_575fc5088c474399 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,590 - mail_database_manager - INFO - 模板 template_0acd29605c994701 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,591 - mail_database_manager - INFO - 模板 template_14981f8399fe4572 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,591 - mail_database_manager - INFO - 模板 template_46f1fcab221b4a38 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,592 - mail_database_manager - INFO - 模板 template_7fd5d6e6b55c4142 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,592 - mail_database_manager - INFO - 模板 template_0738ee4b4a2d4983 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,592 - mail_database_manager - INFO - 模板 template_62e4594c1bf04b4b 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,593 - mail_database_manager - INFO - 模板 template_e827e75c8beb4d5d 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,593 - mail_database_manager - INFO - 模板 template_b0a095478f034b26 已被用户 dsadjdj23 处理，跳过
2025-08-05 21:01:09,594 - mail_database_manager - INFO - 用户 dsadjdj23 有 0 个未处理的邮件模板
2025-08-05 21:01:09,594 - mail_service_distributed - INFO - 用户 dsadjdj23 登录时发现 0 个未处理的邮件模板
2025-08-05 21:01:09,595 - mail_cache_manager - INFO - MailCacheManager实例已创建
2025-08-05 21:01:09,719 - game_manager - INFO - 玩家登录处理完成: dsadjdj23
2025-08-05 21:01:09,720 - ConnectionManager - INFO - 用户 dsadjdj23 连接成功 (Token: eyJhbGciOi, Worker: 38280)
2025-08-05 21:01:11,620 - MessageIdempotencyManager - INFO - 日志系统初始化成功 (进程 ID: 38280)
2025-08-05 21:01:11,620 - MessageIdempotencyManager - INFO - 消息幂等性管理器初始化完成
2025-08-05 21:01:24,540 - distributed_lock - INFO - Worker 38280 成功获取锁: purchase:lock:dsadjdj23:shop_6f5c8d6413fe:82026:743b7a23
2025-08-05 21:01:25,243 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:01:26,003 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:01:26,768 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 21:01:28,413 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 38280, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODUyNjF9.BAmUSVyNndHgj9oRx7GqAWy27vrijhlqK0rvkDE0DjY', 'last_active': '2025-08-05T21:01:06.453314'}
2025-08-05 21:01:28,414 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:01:28,414 - currency_service - INFO - 扣除货币成功: dsadjdj23 -80 gold
2025-08-05 21:01:29,553 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 38280, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODUyNjF9.BAmUSVyNndHgj9oRx7GqAWy27vrijhlqK0rvkDE0DjY', 'last_active': '2025-08-05T21:01:06.453314'}
2025-08-05 21:01:29,553 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: add, 资产类型: ItemType.ITEM
2025-08-05 21:01:29,554 - shop_purchase_service - ERROR - 创建道具实例时发生错误: 'dict' object has no attribute 'success'
2025-08-05 21:01:29,554 - shop_purchase_service - WARNING - 开始回滚购买事务: purchase_d98bda172fe44f95
2025-08-05 21:01:29,933 - GlobalDBUtils - INFO - 用户 dsadjdj23 连接信息: {'worker_id': 38280, 'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkc2FkamRqMjMiLCJleHAiOjE3NTQ0ODUyNjF9.BAmUSVyNndHgj9oRx7GqAWy27vrijhlqK0rvkDE0DjY', 'last_active': '2025-08-05T21:01:06.453314'}
2025-08-05 21:01:29,933 - GlobalDBUtils - INFO - 已发送资产变更通知给用户 dsadjdj23，操作: update, 资产类型: ItemType.ITEM
2025-08-05 21:01:29,934 - currency_service - INFO - 增加货币成功: dsadjdj23 +80 gold
2025-08-05 21:01:29,934 - shop_purchase_service - WARNING - 购买事务回滚完成: purchase_d98bda172fe44f95
2025-08-05 21:01:29,979 - utils - ERROR - Error: 购买失败: 道具创建失败
2025-08-05 21:01:29,979 - base_handlers - ERROR - ShopPurchaseHandler 处理错误: 道具创建失败, 数据: {'config_id': 'shop_6f5c8d6413fe:82026:743b7a23', 'quantity': 1}
2025-08-05 21:01:29,980 - base_handlers - ERROR - NoneType: None

2025-08-05 21:01:30,339 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:01:30,762 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:01:30,762 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:01:31,093 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:02:00,619 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:02:03,112 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 21:02:03,115 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 21:02:03,116 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:02:30,368 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:02:30,721 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:02:30,722 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:02:33,121 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:03:00,634 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:03:03,126 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 21:03:03,130 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 21:03:03,147 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:03:30,914 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:03:30,934 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 1'}}
2025-08-05 21:03:30,935 - ConnectionManager - INFO - 广播消息已发送给 1 个用户，失败 0 个
2025-08-05 21:03:32,939 - ConnectionManager - INFO - 连接心跳超时，已发送ping (Token: eyJhbGciOi, 超时: 120.0秒)
2025-08-05 21:03:33,154 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:04:00,211 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:04:03,159 - ConnectionManager - INFO - Redis连接池状态 (Worker 38280): 使用中=2, 可用=1, 总计=3
2025-08-05 21:04:03,161 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38280): 连接中
2025-08-05 21:04:03,162 - ConnectionManager - INFO - 连接状态 (Worker 38280): 活跃连接数=1, 用户数=1
2025-08-05 21:04:22,811 - game_server - INFO - WebSocket 断开，用户: dsadjdj23, token: eyJhbGciOi..., 代码: 1006, 原因: , worker: 38280
2025-08-05 21:04:23,064 - ConnectionManager - INFO - 正在关闭WebSocket连接 (Token: eyJhbGciOi, 状态: WebSocketState.CONNECTED)
2025-08-05 21:04:23,064 - ConnectionManager - WARNING - 关闭WebSocket连接失败: Unexpected ASGI message 'websocket.close', after sending 'websocket.close' or response already completed.
2025-08-05 21:04:23,065 - ConnectionManager - INFO - 用户 dsadjdj23 断开连接 (Token: eyJhbGciOi, Worker: 38280)
2025-08-05 21:04:23,065 - game_manager - INFO - 处理玩家登出: dsadjdj23, 原因: disconnect (Worker: 38280)
2025-08-05 21:04:23,190 - player_session_manager - INFO - 移除玩家会话: dsadjdj23 (Worker: 38280)
2025-08-05 21:04:23,235 - game_manager - INFO - 玩家登出处理完成: dsadjdj23
2025-08-05 21:04:25,742 - game_server - INFO - 关闭服务器... (Worker: 38280)
2025-08-05 21:04:25,742 - xxsg.monster_cooldown - INFO - 正在关闭怪物冷却管理器...
2025-08-05 21:04:26,120 - xxsg.monster_cooldown - INFO - No cooldowns to persist
2025-08-05 21:04:26,120 - xxsg.monster_cooldown - INFO - 冷却数据已成功持久化到MongoDB
2025-08-05 21:04:26,121 - xxsg.monster_cooldown - INFO - 怪物冷却管理器已关闭
2025-08-05 21:04:26,123 - game_server - INFO - 怪物冷却管理器已关闭
2025-08-05 21:04:26,124 - LogCleanupManager - INFO - 日志清理任务已停止
2025-08-05 21:04:26,124 - game_server - INFO - 日志清理管理器已停止
2025-08-05 21:04:26,125 - game_server - INFO - 统一调度器已关闭
2025-08-05 21:04:26,126 - ConnectionManager - INFO - ConnectionManager资源清理完成 (Worker 38280)
2025-08-05 21:04:26,172 - game_server - INFO - 服务器资源已清理 (Worker: 38280)
