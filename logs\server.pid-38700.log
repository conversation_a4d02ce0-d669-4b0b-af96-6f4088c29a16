2025-08-05 17:50:50,830 - __mp_main__ - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:51,744 - models - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:51,795 - CacheKeyBuilder - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:52,260 - GlobalDBUtils - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:52,294 - CacheManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:52,318 - ItemCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:52,347 - UserCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:52,367 - auth - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,020 - ConnectionManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,105 - game_manager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,174 - websocket_handlers - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,205 - equipment_v2 - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,215 - equipment_service_distributed - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,216 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: a1c4a1ab)
2025-08-05 17:50:55,217 - equipment_handlers_distributed - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,283 - GeneralCacheManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,331 - xxsg.monster_cooldown - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,334 - xxsg.monster_handlers - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,336 - msgManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,417 - unified_scheduler_manager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,430 - scheduler_health_checks - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,432 - scheduler_tasks_unified - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,438 - game_server_scheduler_integration - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,441 - game_server - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:50:55,443 - equipment_handlers_distributed - INFO - Distributed equipment handlers registered successfully
2025-08-05 17:50:55,443 - msgManager - INFO - Monster handlers registered
2025-08-05 17:50:55,444 - msgManager - INFO - 武将相关消息处理器注册完成
2025-08-05 17:50:55,451 - msgManager - INFO - 公会相关消息处理器注册完成
2025-08-05 17:50:55,499 - msgManager - INFO - 邮件相关消息处理器注册完成
2025-08-05 17:50:55,500 - shop_websocket_handlers - INFO - 商店相关消息处理器注册完成
2025-08-05 17:50:55,501 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 421b7f92)
2025-08-05 17:50:55,558 - game_server - INFO - 邮件管理API路由注册成功
2025-08-05 17:50:55,615 - game_server - INFO - 商店系统API路由注册成功
2025-08-05 17:50:55,615 - game_server - INFO - 模板引擎初始化成功
2025-08-05 17:50:55,617 - redis_manager - INFO - RedisManager: 动态分配连接池大小: 120
2025-08-05 17:50:55,618 - game_server - INFO - 启动服务器，初始化数据库和连接管理器... (Worker: 38700)
2025-08-05 17:50:55,618 - ConnectionManager - INFO - RabbitMQ配置: host=*************, port=5672, virtual_host=bthost
2025-08-05 17:50:55,792 - redis_manager - INFO - RedisManager: Redis连接池初始化成功
2025-08-05 17:50:55,794 - ConnectionManager - INFO - 成功连接到RabbitMQ服务器: *************:5672
2025-08-05 17:50:56,303 - ConnectionManager - INFO - 后台任务已启动 (Worker 38700)
2025-08-05 17:50:56,303 - ConnectionManager - INFO - ConnectionManager 初始化完成 (Worker 38700)
2025-08-05 17:50:56,304 - config - WARNING - 游戏配置文件不存在: config/game/cfg_item.json
2025-08-05 17:50:56,304 - config - WARNING - 游戏配置文件不存在: config/game/monsters.json
2025-08-05 17:50:56,305 - game_server - INFO - 游戏配置加载完成 (Worker: 38700)
2025-08-05 17:50:56,305 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:51:00,763 - ConnectionManager - INFO - 自动清理队列和连接完成
2025-08-05 17:51:00,764 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=0, 总计=2
2025-08-05 17:51:00,764 - ConnectionManager - WARNING - Redis连接池使用率过高 (Worker 38700): 2/2 (100.0%)
2025-08-05 17:51:00,765 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:51:00,765 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:51:00,808 - equipment_service_distributed - INFO - Subscribed to equipment cache invalidation (Worker: 421b7f92)
2025-08-05 17:51:00,808 - equipment_service_distributed - INFO - DistributedEquipmentService initialized (Worker: 421b7f92)
2025-08-05 17:51:00,811 - simple_scheduler_api - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:51:00,812 - game_server - INFO - 调度器监控API路由已注册
2025-08-05 17:51:00,814 - ConnectionManager - INFO - Worker 38700 开始消费广播消息，消费者标签: ctag1.77353f1ba14a437287aab2e114a069cb
2025-08-05 17:51:00,859 - ConnectionManager - INFO - Worker 38700 开始消费个人消息，消费者标签: ctag1.6301cc05f15f4a16874bc4bd18e292e8
2025-08-05 17:51:00,935 - ConnectionManager - INFO - 已订阅Redis用户登录通道 (Worker 38700)
2025-08-05 17:51:01,033 - distributed_lock - INFO - Worker 38700 获取锁超时: scheduler_initialization
2025-08-05 17:51:01,033 - game_server_scheduler_integration - INFO - Worker 38700 未获得调度器初始化权限，跳过调度器初始化
2025-08-05 17:51:01,034 - game_server - INFO - 统一调度器初始化成功 (Worker: 38700)
2025-08-05 17:51:01,039 - LogCleanupManager - INFO - 日志系统初始化成功 (进程 ID: 38700)
2025-08-05 17:51:01,043 - LogCleanupManager - INFO - 日志清理任务已启动
2025-08-05 17:51:01,043 - game_server - INFO - 日志清理管理器已启动 (Worker: 38700)
2025-08-05 17:51:01,043 - xxsg.monster_cooldown - INFO - Monster cooldown manager initialized
2025-08-05 17:51:01,044 - game_server - INFO - Monster cooldown manager initialized (Worker: 38700)
2025-08-05 17:51:01,402 - mongodb_manager - INFO - MongoDBManager: MongoDB连接池初始化成功
2025-08-05 17:51:01,492 - xxsg.monster_cooldown - INFO - Restored 0 cooldowns from MongoDB
2025-08-05 17:51:01,493 - game_server - INFO - 公会系统初始化成功 (Worker: 38700)
2025-08-05 17:51:01,493 - game_server - INFO - 邮件系统初始化成功 (Worker: 38700)
2025-08-05 17:51:01,495 - game_server - INFO - 商店系统初始化成功 (Worker: 38700)
2025-08-05 17:51:01,496 - game_server - INFO - 初始化完成 (Worker: 38700)
2025-08-05 17:51:15,123 - shop_api - INFO - [ShopAPI] 获取所有商店列表
2025-08-05 17:51:15,834 - shop_database_manager - INFO - 商店系统数据库索引创建完成
2025-08-05 17:51:18,050 - shop_api - INFO - [ShopAPI] 获取商店详情: shop_8697f73416d6
2025-08-05 17:51:18,473 - shop_database_manager - ERROR - 获取商店商品配置时发生错误: ShopItemConfig.__init__() missing 1 required positional argument: 'item_type'
2025-08-05 17:51:26,315 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:52:00,491 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:52:00,770 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=1, 总计=3
2025-08-05 17:52:00,775 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:52:03,189 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:52:03,190 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:52:05,331 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:52:30,733 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:52:35,341 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:53:00,784 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=1, 总计=3
2025-08-05 17:53:00,785 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:53:00,955 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:53:01,901 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:53:01,902 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:53:05,342 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:53:30,178 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:53:35,357 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:54:00,374 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:54:00,793 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=1, 总计=3
2025-08-05 17:54:00,799 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:54:01,994 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:54:01,994 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:54:05,361 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:54:30,576 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:54:35,375 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:55:00,796 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:55:00,811 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=1, 总计=3
2025-08-05 17:55:00,814 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:55:01,941 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:55:01,941 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:55:05,387 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:55:30,006 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:55:35,403 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:56:00,228 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:56:00,817 - ConnectionManager - INFO - Redis连接池状态 (Worker 38700): 使用中=2, 可用=1, 总计=3
2025-08-05 17:56:00,818 - ConnectionManager - INFO - RabbitMQ连接状态 (Worker 38700): 连接中
2025-08-05 17:56:01,921 - ConnectionManager - INFO - 收到广播消息: {'msgId': 1, 'success': True, 'data': {'content': '当前在线人数: 0'}}
2025-08-05 17:56:01,921 - ConnectionManager - INFO - 广播消息已发送给 0 个用户，失败 0 个
2025-08-05 17:56:05,409 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:56:30,405 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
2025-08-05 17:56:35,425 - ConnectionManager - INFO - 连接状态 (Worker 38700): 活跃连接数=0, 用户数=0
