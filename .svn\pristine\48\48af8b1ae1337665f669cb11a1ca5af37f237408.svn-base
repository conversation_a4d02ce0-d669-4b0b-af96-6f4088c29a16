# models.py
from pydantic import BaseModel
from typing import Optional, Any
from enums import MessageId
from logger_config import setup_logger
# 初始化日志系统
logger = setup_logger(__name__)

# 响应模型
class ResponseModel(BaseModel):
    success: bool
    code: int
    data: Optional[Any] = None
    message: Optional[str] = None
# 消息模型
class MessageModel(BaseModel):
    msgId: int
    success: bool = True
    data: dict
class MessageModelError(BaseModel):
    msgId: int = MessageId.ERROR
    success: bool = False
    message: str = "操作失败"