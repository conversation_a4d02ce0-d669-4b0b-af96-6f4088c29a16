from typing import Dict, Any
from fastapi import WebSocket
import logging
import json
from datetime import datetime
import traceback
from ConnectionManager import ConnectionManager
import asyncio
from utils import handle_error
from enums import MessageId, ItemType
from logger_config import setup_logger
from models import MessageModel
from ItemCacheManager import Item
from service_locator import ServiceLocator
from GlobalDBUtils import GlobalDBUtils
from UserCacheManager import UserCacheManager
from ItemCacheManager import ItemCacheManager
from game_manager import GameManager
# 初始化日志系统
logger = setup_logger(__name__)

class MessageHandler:
    """消息处理器基类"""
    def __init__(self):
        self.name = self.__class__.__name__
        
    async def validate(self, data: dict) -> bool:
        """验证消息数据"""
        return True
        
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理消息"""
        raise NotImplementedError
        
    async def error_handler(self, error: Exception, data: dict) -> dict:
        """处理错误"""
        error_msg = str(error)
        logger.error(f"{self.name} 处理错误: {error_msg}, 数据: {data}")
        logger.error(traceback.format_exc())
        return {
            "msgId": MessageId.ERROR,
            "data": {"error": error_msg}
        }

class ChatHandler(MessageHandler):
    async def validate(self, data: dict) -> bool:
        """验证聊天消息"""
        if not isinstance(data.get("content"), str):
            return False
        if len(data.get("content", "").strip()) == 0:
            return False
        return True

    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理聊天消息"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid chat message format")
                return self.error_handler(ValueError("Invalid chat message format"), data)

            message = MessageModel(msgId=MessageId.CHAT, data={
                    "sender": username,
                    "content": data.get("content", "").strip(),
                    "timestamp": datetime.now().isoformat()
            }).model_dump()
            
            # 使用改进的广播机制发送消息
            await connection_manager.broadcast(message)
            logger.debug(f"Chat message broadcasted: {message}")
            
            return message
        except Exception as e:
            logger.error(f"Error in ChatHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

class BroadcastHandler(MessageHandler):
    async def validate(self, data: dict) -> bool:
        """验证广播消息"""
        if not isinstance(data.get("message"), (dict, str)):
            return False
        return True

    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理广播消息"""
        try:
            if not await self.validate(data):
                await handle_error(websocket, "Invalid broadcast message format")
                return self.error_handler(ValueError("Invalid broadcast message format"), data)

            message = MessageModel(msgId=MessageId.BROADCAST_MESSAGE, data={
                    "sender": username,
                    "content": data.get("message"),
                    "timestamp": datetime.now().isoformat()
            }).model_dump()
            
            # 使用改进的广播机制发送消息
            await connection_manager.broadcast(message, priority=1)  # 广播消息优先级更高
            logger.debug(f"System broadcast sent: {message}")
            
            return message
        except Exception as e:
            logger.error(f"Error in BroadcastHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

class HeartbeatHandler(MessageHandler):
    async def handle(self, data: dict, websocket: WebSocket, 
                    username: str, token: str, 
                    connection_manager) -> dict:
        """处理心跳消息"""
        try:
            await websocket.send_json(MessageModel(msgId=MessageId.HEARTBEAT, data={
                    "status": "ok",
                    "timestamp": datetime.now().isoformat()
                }).model_dump())
            return MessageModel(msgId=MessageId.HEARTBEAT, data={
                "status": "ok",
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        except Exception as e:
            logger.error(f"Error in HeartbeatHandler: {str(e)}")
            logger.error(traceback.format_exc())
            return self.error_handler(e, data)

# 消息处理器映射
handlers: Dict[str, MessageHandler] = {
    "chat": ChatHandler(),
    "broadcast": BroadcastHandler(),
    "heartbeat": HeartbeatHandler()
}

async def handle_message(data: dict, websocket: WebSocket, 
                        username: str, token: str, 
                        connection_manager) -> dict:
    """处理接收到的消息"""
    try:
        message_type = data.get("type")
        if not message_type:
            await handle_error(websocket, "Message type not specified")
            return MessageModel(msgId=MessageId.ERROR, data={"error": "Message type not specified"}).model_dump()

        handler = handlers.get(message_type)
        if not handler:
            await handle_error(websocket, f"Unknown message type: {message_type}")
            return MessageModel(msgId=MessageId.ERROR, data={"error": f"Unknown message type: {message_type}"}).model_dump()

        return await handler.handle(data, websocket, username, token, connection_manager)
        
    except Exception as e:
        logger.error(f"Error handling message: {str(e)}")
        logger.error(traceback.format_exc())
        return MessageModel(msgId=MessageId.ERROR, data={"error": f"Internal error while processing message: {str(e)}"}).model_dump()

async def heartbeat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理心跳消息，确保高可靠性"""
    try:
        # 心跳消息应当是最高优先级处理
        # 直接调用ConnectionManager的handle_heartbeat方法
        success = await manager.handle_heartbeat(websocket, token, data)
        
        if not success and token in manager.active_connections:
            # 如果心跳处理失败但连接仍然存在，尝试一次重试
            await asyncio.sleep(0.05)  # 短暂等待，减少延迟
            success = await manager.handle_heartbeat(websocket, token, data)
        
        # 不返回完整响应，响应已经在handle_heartbeat中发送
        # 这样可以减少一次消息序列化和网络传输
        return True
    except Exception as e:
        # 记录错误但不中断连接
        logger.error(f"心跳处理错误 (用户: {username}, Token: {token[:10]}): {str(e)}")
        # 不返回详细错误信息，减少攻击面
        return False
async def gm_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理GM命令"""
    try:
        command = data.get("command")
        if not command:
            await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"error": "命令不能为空"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "命令不能为空"}).model_dump()
        if command == "add_item":
            item_id = data.get("item_id")
            quantity = data.get("quantity", 1)
            item_type = data.get("item_type", ItemType.ITEM)
            attributes = data.get("attributes", {})
            item_data = {
                "defid": item_id,
                "quantity": quantity,
                "attributes": attributes
            }
            user_cache = await UserCacheManager.get_instance()
            result = await user_cache.add_user_asset(username, item_type, item_data,False,True)
            if result["success"]:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"message": f"添加物品 {item_id} 成功"}).model_dump(), token)
            else:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"error": f"添加物品 {item_id} 失败: {result['message']}"}).model_dump(), token)
            return MessageModel(msgId=MessageId.GM, data={"message": f"添加物品 {item_id} 成功"}).model_dump()
        elif command == "delete_item":
            item_id = data.get("item_id")
            item_type = data.get("item_type", ItemType.ITEM)
            result = await Item.delete(username, item_id, manager.db_manager.db, manager.db_manager.redis_client)
            if result:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"message": f"删除物品 {item_id} 成功"}).model_dump(), token)
            else:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"error": f"删除物品 {item_id} 失败"}).model_dump(), token)
        elif command == "add_equipment":
            defid = data.get("item_id")
            level = data.get("level", 1)
            quality = data.get("quality", 1)
            holes = data.get("holes", 0)
            attributes = data.get("attributes", {})
            item_data = {
                "defid": defid,
                "level": 1,
                "quality": 1,
                "holes": 3,
                "attributes": attributes
            }
            distributed_equipment_service = ServiceLocator.get("distributed_equipment_service")
            success, msg, equipment = await distributed_equipment_service.add_equipment(
                username, defid=defid, level=level, quality=quality, holes=holes, attributes=attributes
            )
            if success:
                await GlobalDBUtils.notify_asset_change(username, ItemType.EQUIPMENT, equipment)
            else:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"error": f"添加装备 {defid} 失败: {msg}"}).model_dump(), token)
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.GM, data={"error": "未知命令"}).model_dump(), token)
            return True
    except Exception as e:
        logger.error(f"处理GM命令失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理GM命令失败: {str(traceback.format_exc())}"}).model_dump(), token)
        return False

async def chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理公共聊天消息"""
    try:
        content = data.get("content")
        if not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.CHAT, data={"error": "内容不能为空"}).model_dump(), token)
            return MessageModel(msgId=MessageId.ERROR, data={"error": "内容不能为空"}).model_dump()

        # 创建广播消息
        message = MessageModel(msgId=MessageId.CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 使用改进的广播机制
        await manager.broadcast(message)
        logger.info(f"用户 {username} 发送公共聊天消息: {content[:30]}...")
        return True
    except Exception as e:
        logger.error(f"处理聊天消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理聊天消息失败: {str(e)}"}).model_dump(), token)
        return False

async def private_chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理私聊消息"""
    try:
        target = data.get("target")
        content = data.get("content")
        if not target or not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.PRIVATE_CHAT, data={"error": "目标或内容不能为空"}).model_dump(), token)
            return False
        
        target_token = manager.user_tokens.get(target)
        message = MessageModel(msgId=MessageId.PRIVATE_CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        if target_token:
            # 发送给目标用户
            await manager.send_personal_message(message, target_token)
            # 发送给发送者自己
            await manager.send_personal_message(message, token)
            logger.info(f"用户 {username} 发送私聊消息给 {target}: {content[:30]}...")
            return True
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.PRIVATE_CHAT, data={"error": "目标用户不在线"}).model_dump(), token)
            return False
    except Exception as e:
        logger.error(f"处理私聊消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理私聊消息失败: {str(e)}"}).model_dump(), token)
        return False

async def group_chat_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """处理群聊消息"""
    try:
        targets = data.get("targets", [])
        content = data.get("content")
        if not targets or not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.GROUP_CHAT, data={"error": "目标或内容不能为空"}).model_dump(), token)
            return False
        
        message = MessageModel(msgId=MessageId.GROUP_CHAT, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 发送给所有目标用户
        await manager.broadcast_to_users(message, targets)
        # 发送给发送者自己
        await manager.send_personal_message(message, token)
        logger.info(f"用户 {username} 发送群聊消息给 {len(targets)} 个用户: {content[:30]}...")
        return True
    except Exception as e:
        logger.error(f"处理群聊消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"处理群聊消息失败: {str(e)}"}).model_dump(), token)
        return False

async def enter_game_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """进入游戏"""
    try:
        # 先检查ServiceLocator中是否有GameManager
        game_manager = GameManager.get_instance()
        if game_manager:                
            await game_manager.on_player_login(username, token, websocket)                
        else:              
            from game_manager import GameManager
            game_manager = await GameManager.get_instance()
            await game_manager.on_player_login(username, token, websocket)
    except Exception as e:
        logger.error(f"GameManager登录处理失败，用户: {username}, 错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
async def get_items_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """获取用户道具列表"""
    try:
        skip = data.get("skip", 0)
        limit = data.get("limit", 300)     
        item_type = data.get("item_type", ItemType.ITEM)
        item_cache = await ItemCacheManager.get_instance()
        items = await item_cache.get_user_items_by_type(username, item_type, skip, limit)        
        match item_type:
            case ItemType.ITEM:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data= items).model_dump(), token)
            case ItemType.EQUIPMENT:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GET_EQUIPMENT, data= items).model_dump(), token)
            case ItemType.RUNE:
                await manager.send_personal_message(MessageModel(msgId=MessageId.GET_RUNE, data= items).model_dump(), token)
        return True
    except ValueError as e:
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": str(e)}).model_dump(), token)
        return False
    except Exception as e:
        logger.error(f"获取道具列表失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"获取道具失败: {str(e)}"}).model_dump(), token)
        return False

async def delete_item_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """删除道具"""
    item_id = data.get("item_id")
    if not item_id:
        await manager.send_personal_message(MessageModel(msgId=MessageId.DELETE_ITEM, data={"error": "道具 ID 不能为空"}).model_dump(), token)
        return False
    try:
        success = await Item.delete(username, item_id, manager.db_manager.db, manager.db_manager.redis_client)
        if success:
            item_cache = await ItemCacheManager.get_instance()
            await manager.send_personal_message(MessageModel(msgId=MessageId.DELETE_ITEM, data={"message": f"道具 {item_id} 删除成功"}).model_dump(), token)
            items = await item_cache.get_user_items_by_type(username, ItemType.ITEM, 0, 300)
            await manager.send_personal_message(MessageModel(msgId=MessageId.GET_ITEMS, data= items).model_dump(), token)
            return True
        else:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"道具 {item_id} 未找到"}).model_dump(), token)
            return False
    except Exception as e:
        logger.error(f"删除道具失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"删除失败: {str(e)}"}).model_dump(), token)
        return False

# 角色信息
async def role_info_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """角色信息"""
    try:
        user_cache = await UserCacheManager.get_instance()
        user = await user_cache.get_user_by_username(username)
        await manager.send_personal_message(MessageModel(msgId=MessageId.ROLE_INFO, data=user.serialize(exclude_fields=["password"])).model_dump(), token)
        return True
    except Exception as e:
        logger.error(f"获取角色信息失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"获取角色信息失败: {str(e)}"}).model_dump(), token)
        return False

# 设置昵称
async def set_nickname_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """设置昵称"""
    nickname = data.get("nickname")
    if not nickname:
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "昵称不能为空"}).model_dump(), token)
        return False
    try:
        user_cache = await UserCacheManager.get_instance()
        await user_cache.update_user_fields(username, "nickname", nickname)
        await manager.send_personal_message(MessageModel(msgId=MessageId.SET_NICKNAME, data={"message": f"昵称设置成功: {nickname}"}).model_dump(), token) 
        return True
    except Exception as e:
        logger.error(f"设置昵称失败，用户: {username}, 错误: {str(e)}")
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"设置昵称失败: {str(e)}"}).model_dump(), token)
        return False

async def broadcast_message_handler(websocket: WebSocket, username: str, token: str, data: dict, manager: ConnectionManager):
    """广播消息"""
    try:
        content = data.get("content")
        if not content:
            await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": "广播内容不能为空"}).model_dump(), token)
            return False
        
        # 创建广播消息
        message = MessageModel(msgId=MessageId.BROADCAST_MESSAGE, data={
                "sender": username,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }).model_dump()
        
        # 使用改进的广播机制
        await manager.broadcast(message, priority=1)  # 广播消息使用更高优先级
        logger.info(f"用户 {username} 发送广播消息: {content[:30]}...")
        return True
    except Exception as e:
        logger.error(f"发送广播消息失败: {str(e)}")
        logger.error(traceback.format_exc())
        await manager.send_personal_message(MessageModel(msgId=MessageId.ERROR, data={"error": f"广播失败: {str(e)}"}).model_dump(), token)
        return False
