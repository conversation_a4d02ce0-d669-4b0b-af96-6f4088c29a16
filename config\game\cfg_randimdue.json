[{"id": 1, "imdue": "pow", "base": 20, "step": 20}, {"id": 2, "imdue": "int", "base": 20, "step": 20}, {"id": 3, "imdue": "con", "base": 20, "step": 20}, {"id": 4, "imdue": "dex", "base": 20, "step": 20}, {"id": 5, "imdue": "hp", "base": 100, "step": 100}, {"id": 6, "imdue": "def", "base": 20, "step": 20}, {"id": 7, "imdue": "dmg_p", "base": 2, "step": 2}, {"id": 8, "imdue": "red", "base": 2, "step": 2}, {"id": 9, "imdue": "trt", "base": 20, "step": 20}, {"id": 10, "imdue": "pow_p", "base": 2, "step": 2}, {"id": 11, "imdue": "def_p", "base": 2, "step": 2}, {"id": 12, "imdue": "int_p", "base": 2, "step": 2}, {"id": 13, "imdue": "dex_p", "base": 2, "step": 2}, {"id": 14, "imdue": "con_p", "base": 2, "step": 2}]