[{"id": 1, "name": "接见张绣", "limit": 1, "opens": "2,3", "whosay": "98.png", "say": "在下张绣，闻将军仁德，愿追随将军效犬马之劳。", "desc": "<txt0012,1A1A1A>请将军点击&<txt0012,D98515>【前往】&<txt0012,1A1A1A>接见张绣&", "kind": "click", "isnum": 0, "parmas": "goBtn", "task_type": 1, "gift": "4:82036|1"}, {"id": 2, "name": "招募武将", "limit": 1, "btn": "Button_kShop", "say": "将军，请使用元宝招募一名武将。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【招募】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【名将卡包】&<txt0012,1A1A1A>进行招募&。", "kind": "rec", "isnum": 0, "parmas": "1.0", "task_type": 1, "gift": "4:82001|200,82182|20"}, {"id": 3, "name": "查看武将", "limit": 1, "opens": "4.0", "btn": "But<PERSON>_Role", "say": "将军，请在武将界面，查看武将详情", "desc": "<txt0012,1A1A1A>打开&<txt0012,D98515>【武将】&<txt0012,1A1A1A>菜单，点击&<txt0012,D98515>【武将卡片】&<txt0012,1A1A1A>来查看武将详情&。", "kind": "look_card", "isnum": 0, "task_type": 1, "gift": "4:82001|300,82021|1"}, {"id": 4, "name": "武将上阵", "limit": 1, "opens": "5.0", "btn": "But<PERSON>_Legatus", "say": "将军，请打开阵容，把两个武将拖动到阵上。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【布阵】&<txt0012,1A1A1A>拖动武将到阵上&。", "kind": "rec_battle", "isnum": 0, "task_type": 1, "gift": "4:82001|200,82000|20000,82182|20"}, {"id": 5, "name": "黄巾-1", "limit": 1, "opens": "6.0", "btn": "Button_Map", "say": "将军，请通关或者扫荡关卡黄巾-1。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【黄巾1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-1】&。", "kind": "battle", "isnum": 0, "var": 1, "task_type": 1, "gift": "4:82001|200&2:3"}, {"id": 6, "name": "装备武器", "limit": 1, "opens": "80,9", "btn": "But<PERSON>_Role", "say": "将军，请为您的武将装备一个符文", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将】&<txt0012,1A1A1A>中，点击&<txt0012,D98515>【武将卡片】&<txt0012,1A1A1A>然后在武将详-符文界面装备&<txt0012,D98515>符文&", "kind": "e_armor", "isnum": 0, "task_type": 1, "gift": "4:82001|200,82000|20001,82021|1,82170|1"}, {"id": 7, "name": "黄巾-10", "limit": 1, "opens": "10.0", "btn": "Button_Map", "say": "将军，请通过章节黄巾-10。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【黄巾2】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-10】&。", "kind": "battle", "isnum": 0, "var": 10, "task_type": 1, "gift": "4:82001|200,82000|10000,82182|20"}, {"id": 8, "name": "招募武将", "limit": 1, "opens": "11.0", "btn": "Button_kShop", "say": "将军，请使用铜钱招募一名武将。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【招募】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【良将卡包】&<txt0012,1A1A1A>进行招募&。", "kind": "rec", "isnum": 0, "parmas": "2.0", "task_type": 1, "gift": "4:82001|200,82182|20"}, {"id": 9, "name": "使用双倍符", "limit": 1, "opens": "8.0", "btn": "Button_Package", "say": "将军，请打开仓库，在道具栏中使用双倍经验符。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【仓库】&<txt0012,1A1A1A>菜单中选择&<txt0012,D98515>【道具】&<txt0012,1A1A1A>并使用&<txt0012,D98515>【双倍经验符】&。", "kind": "u_item", "isnum": 0, "var": 82170, "task_type": 1, "gift": "4:82001|300,82008|1,82021|1"}, {"id": 10, "name": "扫荡", "limit": 1, "opens": "12.0", "btn": "Button_Map", "say": "将军，已经通关的关卡可以使用扫荡卷快速战斗获得经验和物品。", "desc": "<txt0012,D98515>扫荡【黄巾1】中的黄巾-10&<txt0012,D98515>【5】&<txt0012,D98515>次&。", "kind": "quick", "isnum": 1, "parmas": "10.0", "var": 5, "task_type": 1, "gift": "4:82001|300,82038|1,82182|20"}, {"id": 11, "name": "发现黑市", "limit": 1, "btn": "Button_zShop", "say": "将军，请去黑市看一看。", "desc": "<txt0012,1A1A1A>请去&<txt0012,D98515>【集市】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黑市】&<txt0012,1A1A1A>看一看&", "kind": "backmark", "isnum": 0, "task_type": 1, "gift": "4:82001|200,82000|5000"}, {"id": 12, "name": "研究战法I", "limit": 1, "opens": "13.0", "btn": "<PERSON><PERSON>_<PERSON>", "say": "将军，请在战法界面开启治疗战法的研究。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【战法】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【治疗】&<txt0012,1A1A1A>并拖动材料武将卡来开启&<txt0012,D98515>研究&。", "kind": "study1", "isnum": 0, "var": 2104, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 13, "name": "研究战法II", "limit": 1, "opens": "14,15", "btn": "<PERSON><PERSON>_<PERSON>", "say": "将军，请在战法界面使治疗战法的研究进度达到100%。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【战法】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【治疗】&<txt0012,1A1A1A>并拖动材料武将卡来使&<txt0012,D98515>研究进度达到100%&。", "kind": "study2", "isnum": 0, "var": 2104, "task_type": 1, "gift": "4:82001|300"}, {"id": 14, "name": "主公等级", "limit": 1, "opens": "82.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【10】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 10, "task_type": 1, "gift": "4:82001|500,82021|1"}, {"id": 15, "name": "武将等级", "limit": 1, "opens": "81,16", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【10】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 10, "task_type": 1, "gift": "4:82001|200,82028|1,82020|1"}, {"id": 16, "name": "装备技能", "limit": 1, "btn": "But<PERSON>_Role", "say": "将军，请为武将装备治疗战法", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面中，点击&<txt0012,D98515>【技能栏】&<txt0012,1A1A1A>装备战法&<txt0012,D98515>【治疗】&", "kind": "e_skill", "isnum": 0, "var": 2104, "task_type": 1, "gift": "4:82001|200"}, {"id": 17, "name": "关卡黄巾-30", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节关卡黄巾-30。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【黄巾6】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-30】&。", "kind": "battle", "isnum": 0, "var": 30, "task_type": 1, "gift": "4:82001|300,82000|10000,82020|1"}, {"id": 18, "name": "上阵3武将", "limit": 1, "opens": "17.0", "btn": "But<PERSON>_Legatus", "say": "将军，请上阵第三个武将。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【布阵】&<txt0012,1A1A1A>中，上阵&<txt0012,D98515>【三个武将】&", "kind": "rec_battle", "isnum": 0, "var": 3, "task_type": 1, "gift": "4:82001|200"}, {"id": 19, "name": "武将觉醒", "limit": 1, "opens": "20.0", "btn": "But<PERSON>_Role", "say": "将军，请觉醒一个武将，觉醒武将需要达到20级。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，点击&<txt0012,D98515>【觉醒】&<txt0012,1A1A1A>消耗同阶武将卡来觉醒&。", "kind": "card_awake", "isnum": 0, "task_type": 1, "gift": "4:82001|200,82036|1,82021|1"}, {"id": 20, "name": "武将进阶", "limit": 1, "opens": "21.0", "btn": "But<PERSON>_Role", "say": "将军，请进阶一个武将,进阶需要消耗同名武将.", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，选择&<txt0012,D98515>【进阶】&<txt0012,1A1A1A>消耗同名武将卡来进阶武将&", "kind": "card_star", "isnum": 0, "task_type": 1, "gift": "4:82000|10000,82020|1"}, {"id": 21, "name": "配置属性", "limit": 1, "opens": "22.0", "btn": "But<PERSON>_Role", "say": "将军，请为进阶后的武将分配属性点。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，选择&<txt0012,D98515>【配点】&<txt0012,1A1A1A>为武将配置属性点&", "kind": "card_pro", "isnum": 0, "var": 1, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 22, "name": "升级武将", "limit": 1, "opens": "24.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【武将等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【25】&<txt0012,1A1A1A>级&。", "kind": "level_up", "isnum": 1, "var": 25, "task_type": 1, "gift": "4:82001|100,82020|1"}, {"id": 23, "name": "关卡黄巾-31", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节汉室衰微，关卡2-10。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【黄巾6】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-31】&。", "kind": "battle", "isnum": 0, "var": 31, "task_type": 1, "gift": "4:82001|100,82000|10000"}, {"id": 24, "name": "试炼", "limit": 1, "opens": "25.0", "btn": "Button_Elite", "say": "将军，试炼有各种奖励，请参加一次试炼", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【试炼殿】&<txt0012,1A1A1A>中参加一次试炼&", "kind": "elite", "isnum": 0, "task_type": 1, "gift": "4:82001|100,82000|20000,82020|1"}, {"id": 25, "name": "武将等级", "limit": 1, "opens": "26.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【30】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 30, "task_type": 1, "gift": "4:82009|1,82010|1,82011|1"}, {"id": 26, "name": "武将等级", "limit": 1, "opens": "33,38,27", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【35】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 35, "task_type": 1, "gift": "4:82001|100,82021|1"}, {"id": 27, "name": "荆州1", "limit": 1, "opens": "28.0", "btn": "Button_Map", "say": "将军，请通过章节荆州1，荆州-5。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【荆州1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【荆州-5】&。", "kind": "battle", "isnum": 0, "var": 36, "task_type": 1, "gift": "4:82001|300,82000|10000"}, {"id": 28, "name": "荆州2", "limit": 1, "opens": "29.0", "btn": "Button_Map", "say": "将军，请通过章节荆州2，荆州-10。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【荆州2】&<txt0012,1A1A1A>中的&<txt0012,D98515>【荆州-10】&。", "kind": "battle", "isnum": 0, "var": 41, "task_type": 1, "gift": "4:82001|300,82000|10000,82021|1"}, {"id": 29, "name": "荆州4", "limit": 1, "opens": "30.0", "btn": "Button_Map", "say": "将军，请通过章节荆州4，荆州-23。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【荆州4】&<txt0012,1A1A1A>中的&<txt0012,D98515>【荆州-23】&。", "kind": "battle", "isnum": 0, "var": 54, "task_type": 1, "gift": "4:82001|300,82000|10000"}, {"id": 30, "name": "南阳4", "limit": 1, "opens": "31.0", "btn": "Button_Map", "say": "将军，请通过章节南阳4，南阳-22。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【南阳4】&<txt0012,1A1A1A>中的&<txt0012,D98515>【南阳-22】&。", "kind": "battle", "isnum": 0, "var": 76, "task_type": 1, "gift": "4:82001|300,82000|10000,82021|1"}, {"id": 31, "name": "益州4", "limit": 1, "opens": "32.0", "btn": "Button_Map", "say": "将军，请通过章节益州4，益州-21。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【益州4】&<txt0012,1A1A1A>中的&<txt0012,D98515>【益州-21】&。", "kind": "battle", "isnum": 0, "var": 97, "task_type": 1, "gift": "4:82001|300,82000|10000"}, {"id": 32, "name": "冀州4", "limit": 1, "opens": "46.0", "btn": "Button_Map", "say": "将军，请通过章节冀州1，冀州-3。", "desc": "<txt0012,1A1A1A>通关或扫荡&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中&<txt0012,D98515>【冀州1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【冀州-3】&。", "kind": "battle", "isnum": 0, "var": 100, "task_type": 1, "gift": "4:82001|300,82000|10000,82021|1"}, {"id": 33, "name": "武将等级", "limit": 1, "opens": "34.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【40】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 40, "task_type": 1, "gift": "4:82001|200"}, {"id": 34, "name": "武将等级", "limit": 1, "opens": "35.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【45】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 45, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 35, "name": "武将等级", "limit": 1, "opens": "36.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【50】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 50, "task_type": 1, "gift": "4:82001|200"}, {"id": 36, "name": "武将等级", "limit": 1, "opens": "37.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【55】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 55, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 37, "name": "武将等级", "limit": 1, "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【60】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 60, "task_type": 1, "gift": "4:82001|200"}, {"id": 38, "name": "武将等级", "limit": 1, "opens": "39.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【25】&<txt0012,1A1A1A>级&", "kind": "card_level_up", "isnum": 1, "var": 25, "task_type": 1, "gift": "4:82001|200"}, {"id": 39, "name": "主公等级", "limit": 1, "opens": "40.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【30】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 30, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 40, "name": "主公等级", "limit": 1, "opens": "41.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【35】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 35, "task_type": 1, "gift": "4:82001|200"}, {"id": 41, "name": "主公等级", "limit": 1, "opens": "42.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【40】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 40, "task_type": 1, "gift": "4:82001|200"}, {"id": 42, "name": "主公等级", "limit": 1, "opens": "43.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【45】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 45, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 43, "name": "主公等级", "limit": 1, "opens": "44.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【50】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 50, "task_type": 1, "gift": "4:82001|200"}, {"id": 44, "name": "主公等级", "limit": 1, "opens": "45.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【55】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 55, "task_type": 1, "gift": "4:82001|200,82021|1"}, {"id": 45, "name": "主公等级", "limit": 1, "opens": "46.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【60】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 60, "task_type": 1, "gift": "4:82001|200"}, {"id": 46, "name": "主公等级", "limit": 1, "opens": "89,88", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【70】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 70, "task_type": 1, "gift": "4:82001|200"}, {"id": 47, "name": "参加试炼", "limit": 1, "btn": "Button_Elite", "say": "将军，试炼有各种奖励，请参加一次精英副本", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【试炼殿】&<txt0012,1A1A1A>中参加一次试炼&", "kind": "elite", "isnum": 0, "task_type": 2, "gift": "4:82001|100"}, {"id": 48, "name": "武将等级", "limit": 1, "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级1级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>提升&<txt0012,D98515>【1】&<txt0012,1A1A1A>级&。", "kind": "level_up_by", "isnum": 1, "var": 1, "task_type": 2, "gift": "4:82001|50"}, {"id": 49, "name": "关卡1-10", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节黄巾2，黄巾-10。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【黄巾2】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-10】&。", "kind": "battle", "isnum": 0, "var": 10, "task_type": 2, "gift": "4:82001|50"}, {"id": 50, "name": "关卡2-10", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节荆州2，荆州-10。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【荆州2】&<txt0012,1A1A1A>中的&<txt0012,D98515>【荆州-10】&。", "kind": "battle", "isnum": 0, "var": 41, "task_type": 2, "gift": "4:82001|50"}, {"id": 51, "name": "关卡3-10", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节南阳1，南阳-5。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【南阳1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【南阳-5】&。", "kind": "battle", "isnum": 0, "var": 59, "task_type": 2, "gift": "4:82001|50"}, {"id": 52, "name": "关卡4-10", "limit": 1, "btn": "Button_Map", "say": "将军，请通过章节益州1，益州-5。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【益州1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【益州-5】&。", "kind": "battle", "isnum": 0, "var": 81, "task_type": 2, "gift": "4:82001|50"}, {"id": 53, "name": "消耗元宝I", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【500】&", "kind": "red_glod", "isnum": 1, "var": 500, "task_type": 2, "gift": "4:82001|100"}, {"id": 54, "name": "消耗元宝II", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【1000】&", "kind": "red_glod", "isnum": 1, "var": 1000, "task_type": 2, "gift": "4:82001|200"}, {"id": 55, "name": "消耗元宝III", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【2000】&", "kind": "red_glod", "isnum": 1, "var": 2000, "task_type": 2, "gift": "4:82001|400"}, {"id": 56, "name": "消耗元宝IV", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【3000】&", "kind": "red_glod", "isnum": 1, "var": 3000, "task_type": 2, "gift": "4:82001|600"}, {"id": 57, "name": "消耗元宝V", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【5000】&", "kind": "red_glod", "isnum": 1, "var": 5000, "task_type": 2, "gift": "4:82001|1000"}, {"id": 58, "name": "消耗铜钱I", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【50000】&", "kind": "red_coin", "isnum": 1, "var": 50000, "task_type": 2, "gift": "4:82001|50"}, {"id": 59, "name": "消耗铜钱II", "limit": 1, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【100000】&", "kind": "red_coin", "isnum": 1, "var": 100000, "task_type": 2, "gift": "4:82001|100"}, {"id": 60, "name": "消耗铜钱III", "limit": 50, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【150000】&", "kind": "red_coin", "isnum": 1, "var": 150000, "task_type": 2, "gift": "4:82001|150"}, {"id": 61, "name": "消耗铜钱IV", "limit": 80, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【200000】&", "kind": "red_coin", "isnum": 1, "var": 200000, "task_type": 2, "gift": "4:82001|200"}, {"id": 62, "name": "消耗铜钱V", "limit": 100, "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【300000】&", "kind": "red_coin", "isnum": 1, "var": 300000, "task_type": 2, "gift": "4:82001|250"}, {"id": 63, "name": "配置属性", "limit": 1, "btn": "But<PERSON>_Role", "say": "将军，请为进阶后的武将分配属性点。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，选择&<txt0012,D98515>【配点】&<txt0012,1A1A1A>为武将配置属性点&", "kind": "card_pro", "isnum": 0, "var": 1, "task_type": 2, "gift": "4:82001|50"}, {"id": 64, "name": "招募武将", "limit": 1, "btn": "Button_kShop", "say": "将军，请使用铜钱招募一名武将。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【招募】&<txt0012,1A1A1A>中进行1次招募&。", "kind": "rec", "isnum": 0, "parmas": "2.0", "task_type": 2, "gift": "4:82001|50"}, {"id": 65, "name": "武将觉醒", "limit": 1, "btn": "But<PERSON>_Role", "say": "将军，请觉醒一个武将，觉醒武将需要达到10级。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，点击&<txt0012,D98515>【觉醒】&<txt0012,1A1A1A>消耗同阶武将卡来觉醒&。", "kind": "card_awake", "isnum": 0, "task_type": 2, "gift": "4:82001|50"}, {"id": 66, "name": "武将进阶", "limit": 1, "btn": "But<PERSON>_Role", "say": "将军，请进阶一个武将,进阶需要消耗同名武将.", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【武将详情】&<txt0012,1A1A1A>界面，选择&<txt0012,D98515>【进阶】&<txt0012,1A1A1A>消耗同名武将卡来进阶武将&", "kind": "card_star", "isnum": 0, "task_type": 2, "gift": "4:82000|10000"}, {"id": 67, "name": "扫荡", "limit": 1, "btn": "Button_Map", "say": "将军，已经通关的关卡可以使用扫荡卷快速战斗获得经验和物品。", "desc": "<txt0012,D98515>扫荡任意关卡&<txt0012,D98515>【10】&<txt0012,D98515>次&。", "kind": "quick_num", "isnum": 1, "var": 10, "task_type": 2, "gift": "4:82001|50"}, {"id": 68, "name": "镶嵌", "limit": 1, "btn": "Button_sShop", "say": "将军，请进行一次镶嵌", "desc": "<txt0012,D98515>进行&<txt0012,D98515>【1】&<txt0012,D98515>次镶嵌&。", "kind": "inlay", "isnum": 0, "task_type": 2, "gift": "4:82001|50"}, {"id": 69, "name": "累计输出I", "limit": 1, "btn": "Button_Map", "say": "将军，请去尽情输出吧", "desc": "<txt0012,D98515>在副本中输出累计达到&<txt0012,D98515>【200000】&", "kind": "output", "isnum": 1, "var": 200000, "task_type": 2, "gift": "4:82001|50"}, {"id": 70, "name": "累计输出II", "limit": 50, "btn": "Button_Map", "say": "将军，请去尽情输出吧", "desc": "<txt0012,D98515>在副本中输出累计达到&<txt0012,D98515>【500000】&", "kind": "output", "isnum": 1, "var": 500000, "task_type": 2, "gift": "4:82001|100"}, {"id": 71, "name": "累计输出III", "limit": 50, "btn": "Button_Map", "say": "将军，请去尽情输出吧", "desc": "<txt0012,D98515>在副本中输出累计达到&<txt0012,D98515>【1000000】&", "kind": "output", "isnum": 1, "var": 1000000, "task_type": 2, "gift": "4:82001|150"}, {"id": 72, "name": "累计治疗I", "limit": 1, "btn": "Button_Map", "say": "将军，请治疗您的伙伴吧", "desc": "<txt0012,D98515>在副本中治疗累计达到&<txt0012,D98515>【100000】&", "kind": "treat", "isnum": 1, "var": 100000, "task_type": 2, "gift": "4:82001|50"}, {"id": 73, "name": "累计治疗I", "limit": 50, "btn": "Button_Map", "say": "将军，请治疗您的伙伴吧", "desc": "<txt0012,D98515>在副本中治疗累计达到&<txt0012,D98515>【200000】&", "kind": "treat", "isnum": 1, "var": 200000, "task_type": 2, "gift": "4:82001|100"}, {"id": 74, "name": "累计治疗I", "limit": 50, "btn": "Button_Map", "say": "将军，请治疗您的伙伴吧", "desc": "<txt0012,D98515>在副本中治疗累计达到&<txt0012,D98515>【300000】&", "kind": "treat", "isnum": 1, "var": 300000, "task_type": 2, "gift": "4:82001|150"}, {"id": 75, "name": "累计受伤I", "limit": 1, "btn": "Button_Map", "say": "将军，请帮您的伙伴抗伤害吧", "desc": "<txt0012,D98515>在副本中受到伤害累计达到&<txt0012,D98515>【200000】&", "kind": "hurt", "isnum": 1, "var": 200000, "task_type": 2, "gift": "4:82001|50"}, {"id": 76, "name": "累计受伤II", "limit": 50, "btn": "Button_Map", "say": "将军，请帮您的伙伴抗伤害吧", "desc": "<txt0012,D98515>在副本中受到伤害累计达到&<txt0012,D98515>【500000】&", "kind": "hurt", "isnum": 1, "var": 500000, "task_type": 2, "gift": "4:82001|100"}, {"id": 77, "name": "累计受伤III", "limit": 50, "btn": "Button_Map", "say": "将军，请帮您的伙伴抗伤害吧", "desc": "<txt0012,D98515>在副本中受到伤害累计达到&<txt0012,D98515>【1000000】&", "kind": "hurt", "isnum": 1, "var": 1000000, "task_type": 2, "gift": "4:82001|150"}, {"id": 78, "name": "镶嵌", "limit": 1, "btn": "Button_sShop", "say": "将军，请进行一次镶嵌", "desc": "<txt0012,D98515>进行&<txt0012,D98515>【1】&<txt0012,D98515>次镶嵌&。", "kind": "inlay", "isnum": 0, "task_type": 2, "gift": "4:82001|50"}, {"id": 79, "name": "黑市购买", "limit": 1, "btn": "Button_zShop", "say": "将军，请在黑市中购买一件物品", "desc": "<txt0012,D98515>请在&<txt0012,D98515>【黑市】&<txt0012,D98515>中购买1次物品&", "kind": "back_buy", "isnum": 0, "task_type": 2, "gift": "4:82001|50"}, {"id": 80, "name": "黄巾-5", "limit": 1, "opens": "7.0", "btn": "Button_Map", "say": "将军，请通过章节黄巾-5。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【章节】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【黄巾1】&<txt0012,1A1A1A>中的&<txt0012,D98515>【黄巾-5】&。", "kind": "battle", "isnum": 0, "var": 5, "task_type": 1, "gift": "4:82001|200"}, {"id": 81, "name": "武将等级", "limit": 1, "opens": "19.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【20】&<txt0012,1A1A1A>级&。", "kind": "card_level_up", "isnum": 1, "var": 20, "task_type": 1, "gift": "4:82001|200,82028|1,82020|1"}, {"id": 82, "name": "主公等级", "limit": 1, "opens": "18.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【20】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 20, "task_type": 1, "gift": "4:82001|100"}, {"id": 83, "name": "主公等级", "limit": 1, "opens": "18.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【20】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 20, "task_type": 1, "gift": "4:82001|100"}, {"id": 84, "name": "武备I", "limit": 50, "say": "将军，请上缴资源", "desc": "<txt0012,D98515>【将军请上缴以下资源】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 2, "gift": "4:82001|100", "needid": 82225, "neednum": 200}, {"id": 85, "name": "武备II", "limit": 50, "say": "将军，请上缴资源", "desc": "<txt0012,D98515>【将军请上缴以下资源】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 2, "gift": "4:82001|100", "needid": 82226, "neednum": 100}, {"id": 86, "name": "武备III", "limit": 50, "say": "将军，请上缴资源", "desc": "<txt0012,D98515>【将军请上缴以下资源】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 2, "gift": "4:82001|100", "needid": 82227, "neednum": 50}, {"id": 87, "name": "武备IV", "limit": 50, "say": "将军，请上缴资源", "desc": "<txt0012,D98515>【将军请上缴以下资源】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 2, "gift": "4:82001|100", "needid": 82228, "neednum": 5}, {"id": 88, "name": "讨伐黄巾", "limit": 1, "btn": "Button_Map", "say": "将军，黄巾贼军猖獗，请速速前去讨伐", "desc": "<txt0012,D98515>【讨伐黄巾头目，有几率掉落-黄头巾，上缴黄头巾换取奖励】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 1, "gift": "4:82916|1", "needid": 82914, "neednum": 30}, {"id": 89, "name": "主公等级", "limit": 1, "opens": "90.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【80】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 80, "task_type": 1, "gift": "4:82001|200"}, {"id": 90, "name": "主公等级", "limit": 1, "opens": "91.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【90】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 90, "task_type": 1, "gift": "4:82001|300"}, {"id": 91, "name": "主公等级", "limit": 1, "opens": "92.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【100】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 100, "task_type": 1, "gift": "4:82001|400"}, {"id": 92, "name": "主公等级", "limit": 1, "opens": "93.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【110】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 110, "task_type": 1, "gift": "4:82001|500"}, {"id": 93, "name": "主公等级", "limit": 1, "opens": "94.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【120】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 120, "task_type": 1, "gift": "4:82001|600"}, {"id": 94, "name": "主公等级", "limit": 1, "opens": "95.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【130】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 130, "task_type": 1, "gift": "4:82001|700"}, {"id": 95, "name": "主公等级", "limit": 1, "opens": "96.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【140】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 140, "task_type": 1, "gift": "4:82001|800"}, {"id": 96, "name": "主公等级", "limit": 1, "opens": "97.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【150】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 150, "task_type": 1, "gift": "4:82001|900"}, {"id": 97, "name": "主公等级", "limit": 1, "opens": "98.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【160】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 160, "task_type": 1, "gift": "4:82001|1000"}, {"id": 98, "name": "主公等级", "limit": 1, "opens": "99.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【170】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 170, "task_type": 1, "gift": "4:82001|1000"}, {"id": 99, "name": "主公等级", "limit": 1, "opens": "100.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【180】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 180, "task_type": 1, "gift": "4:82001|1000"}, {"id": 100, "name": "主公等级", "limit": 1, "opens": "101.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【190】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 190, "task_type": 1, "gift": "4:82001|1000"}, {"id": 101, "name": "主公等级", "limit": 1, "opens": "102.0", "btn": "Button_Map", "say": "将军，请去击杀敌军提升等级。", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【200】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 200, "task_type": 1, "gift": "4:82001|1000"}, {"id": 102, "name": "主公等级", "limit": 1, "btn": "Button_Map", "say": "预留任务", "desc": "<txt0012,D98515>【主公等级】&<txt0012,1A1A1A>达到&<txt0012,D98515>【300】&<txt0012,1A1A1A>级&", "kind": "level_up", "isnum": 1, "var": 300, "task_type": 1, "gift": "4:82001|1000"}, {"id": 103, "name": "秘境探险", "limit": 1, "btn": "Button_Map", "say": "汝南出现秘境，将军可以去探险，有机会获得各种宝物", "desc": "<txt0012,D98515>【探索大兴山秘境，有几率获得任务物品】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 0, "gift": "4:82001|100", "needid": 82917, "neednum": 5}, {"id": 104, "name": "困难试炼", "limit": 1, "btn": "Button_Map", "say": "通关云川副本困难可以解锁困难模式", "desc": "<txt0012,D98515>【通关云川副本困难可以解锁困难模式】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 1, "gift": "4:82953|1", "needid": 82956, "neednum": 1}, {"id": 105, "name": "雷惑线索1", "limit": 1, "opens": "106.0", "btn": "Button_Map", "say": "据传甘宁知晓雷惑坐骑的线索", "desc": "<txt0012,D98515>【击败甘宁，有几率掉落-雷惑线索】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82001|100", "needid": 83373, "neednum": 20}, {"id": 106, "name": "雷惑线索2", "limit": 1, "opens": "107.0", "btn": "Button_Map", "say": "据传在云梦泽出现过雷惑坐骑", "desc": "<txt0012,D98515>【探索云梦泽秘境，有较低几率获得雷惑马鞍】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82001|100", "needid": 83374, "neednum": 1}, {"id": 107, "name": "雷惑线索3", "limit": 1, "opens": "108.0", "btn": "Button_Map", "say": "据传诸葛亮知晓雷惑的打造方法", "desc": "<txt0012,D98515>【击败蜀之智，有几率掉落-雷惑蓝图残卷】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82514|1", "needid": 83375, "neednum": 20}, {"id": 108, "name": "雷惑线索4", "limit": 1, "opens": "109.0", "btn": "Button_Map", "say": "据传在猛虎洞穴可以获得猛虎魄", "desc": "<txt0012,D98515>【探索猛虎洞穴秘境，有较低几率获得猛虎残魄】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83377|1", "needid": 83376, "neednum": 5}, {"id": 109, "name": "雷惑线索5", "limit": 1, "btn": "Button_Map", "say": "据传云川王拥有雷玉", "desc": "<txt0012,D98515>【击败云川王，有几率掉落-雷玉石】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83379|1", "needid": 83378, "neednum": 5}, {"id": 110, "name": "阴阳鱼线索1", "limit": 1, "opens": "111.0", "btn": "Button_Map", "say": "据传吕布家有一个阴阳鱼玉佩", "desc": "<txt0012,D98515>【击败吕布，有几率掉落-阴阳鱼线索】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82001|100", "needid": 83382, "neednum": 20}, {"id": 111, "name": "阴阳鱼线索2", "limit": 1, "opens": "112.0", "btn": "<PERSON><PERSON>_<PERSON>", "say": "想要拥有阴阳鱼玉佩必须先研究群雄逐鹿战法", "desc": "<txt0012,D98515>【想要拥有阴阳鱼玉佩，必须先获得群雄逐鹿战法】&", "kind": "study2", "isnum": 0, "var": 4426, "task_type": 3, "gift": "4:82001|100"}, {"id": 112, "name": "阴阳鱼线索3", "limit": 1, "opens": "113.0", "btn": "Button_Map", "say": "据传吕布将阴阳鱼蓝图交予了张辽", "desc": "<txt0012,D98515>【击败张辽，有几率掉落-阴阳鱼蓝图线索】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82515|1", "needid": 83383, "neednum": 20}, {"id": 113, "name": "阴阳鱼线索4", "limit": 1, "opens": "114.0", "btn": "Button_Map", "say": "据传西凉秘宝有太极玉", "desc": "<txt0012,D98515>【探索西凉秘宝秘境，有较低几率获得太极玉石】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83385|1", "needid": 83384, "neednum": 5}, {"id": 114, "name": "阴阳鱼线索5", "limit": 1, "btn": "Button_Map", "say": "据传云川公主拥有速度秘要", "desc": "<txt0012,D98515>【击败云川公主，有几率掉落-速度秘诀】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83387|1", "needid": 83386, "neednum": 5}, {"id": 115, "name": "飞焰白鸽线索1", "limit": 1, "opens": "116.0", "btn": "Button_Map", "say": "据传江东都督有飞焰白鸽的线索", "desc": "<txt0012,D98515>【击败江东都督，有几率掉落-飞焰白鸽线索】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82001|100", "needid": 83394, "neednum": 10}, {"id": 116, "name": "飞焰白鸽线索2", "limit": 1, "opens": "117.0", "btn": "<PERSON><PERSON>_<PERSON>", "say": "想要知晓飞焰白鸽线索必须先研究烈火袭营战法", "desc": "<txt0012,D98515>【想要知晓飞焰白鸽线索，必须先获得烈火袭营战法】&", "kind": "study2", "isnum": 0, "var": 4562, "task_type": 3, "gift": "4:82001|100"}, {"id": 117, "name": "飞焰白鸽线索3", "limit": 1, "opens": "118.0", "btn": "Button_Map", "say": "想要飞焰白鸽蓝图拿10个火烧连营之计来换", "desc": "<txt0012,D98515>【想要飞焰白鸽蓝图拿10个火烧连营之计来换】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:82517|1", "needid": 82832, "neednum": 10}, {"id": 118, "name": "飞焰白鸽线索4", "limit": 1, "opens": "119.0", "btn": "Button_Map", "say": "据传陆逊将飞焰白鸽马鞍交给了孙策", "desc": "<txt0012,D98515>【击败孙策，有极低几率掉落-飞焰白鸽马鞍线索】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83392|1", "needid": 83391, "neednum": 20}, {"id": 119, "name": "飞焰白鸽线索5", "limit": 1, "btn": "Button_Map", "say": "据传内宫地牢有熔岩玉", "desc": "<txt0012,D98515>【探索内宫地牢秘境，有较低几率获得熔岩玉】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 3, "gift": "4:83390|1", "needid": 83389, "neednum": 5}, {"id": 120, "name": "完成签到", "limit": 1, "wolong": "week", "btn": "Button_Activity", "say": "完成签到", "desc": "<txt0012,D98515>【完成签到次数达到5次】&", "kind": "sign", "isnum": 1, "var": 5, "task_type": 4, "gift": "4:83395|10"}, {"id": 121, "name": "进行装备合成(四合一)", "limit": 1, "wolong": "week", "btn": "Button_sShop", "say": "进行装备合成(四合一)", "desc": "<txt0012,D98515>【进行装备合成（四合一）达到30次】&", "kind": "nextbox1", "isnum": 1, "var": 30, "task_type": 4, "gift": "4:83395|5"}, {"id": 122, "name": "消耗铜钱", "limit": 1, "wolong": "week", "desc": "<txt0012,1A1A1A>累计消费&<txt0012,D98515>【铜钱】&<txt0012,1A1A1A>达到&<txt0012,D98515>【5000000】&", "kind": "red_coin", "isnum": 1, "var": 5000000, "task_type": 4, "gift": "4:83395|2"}, {"id": 123, "name": "世界头目", "limit": 1, "wolong": "week", "btn": "<PERSON><PERSON>_<PERSON>rold", "say": "击杀世界头目", "desc": "<txt0012,D98515>【累计击杀世界头目达到210次】&", "kind": "killboos", "isnum": 1, "var": 210, "task_type": 4, "gift": "4:83395|5"}, {"id": 124, "name": "军团boss", "limit": 1, "wolong": "week", "btn": "Button_Legion", "say": "参加军团头目", "desc": "<txt0012,D98515>【累计参加军团头目达到18次】&", "kind": "<PERSON><PERSON><PERSON><PERSON>", "isnum": 1, "var": 18, "task_type": 4, "gift": "4:83395|5"}, {"id": 125, "name": "蛮族入侵", "limit": 1, "wolong": "week", "btn": "Button_Activity", "say": "参加蛮族入侵活动", "desc": "<txt0012,D98515>【累计参加蛮族入侵达到35次】&", "kind": "turnboss", "isnum": 1, "var": 35, "task_type": 4, "gift": "4:83395|10"}, {"id": 126, "name": "副本", "limit": 1, "wolong": "week", "btn": "But<PERSON>_Raid", "say": "通关副本殿任意副本", "desc": "<txt0012,D98515>【累计通过副本殿任意副本达到7次】&", "kind": "raidboss", "isnum": 1, "var": 7, "task_type": 4, "gift": "4:83395|5"}, {"id": 127, "name": "激活神纹", "limit": 1, "wolong": "week", "btn": "Button_sShop", "say": "激活任意神纹", "desc": "<txt0012,D98515>【累计激活任意神纹达到10次】&", "kind": "runeworld", "isnum": 1, "var": 10, "task_type": 4, "gift": "4:83395|15"}, {"id": 128, "name": "使用神纹", "limit": 1, "wolong": "month", "btn": "Button_sShop", "say": "使用神纹19#", "desc": "<txt0012,D98515>【累计使用19#神纹达到1次】&", "kind": "costrune", "isnum": 1, "parmas": "40066.0", "var": 1, "task_type": 4, "gift": "4:83395|5"}, {"id": 129, "name": "使用神纹", "limit": 1, "wolong": "month", "btn": "Button_sShop", "say": "使用神纹18#", "desc": "<txt0012,D98515>【累计使用18#神纹达到2次】&", "kind": "costrune", "isnum": 1, "parmas": "40065.0", "var": 2, "task_type": 4, "gift": "4:83395|5"}, {"id": 130, "name": "增孔", "limit": 1, "wolong": "month", "btn": "Button_sShop", "say": "消耗卧龙石进行增孔合成", "desc": "<txt0012,D98515>【累计消耗卧龙石增孔达到2次】&", "kind": "nextbox2", "isnum": 1, "var": 2, "task_type": 4, "gift": "4:83395|10"}, {"id": 131, "name": "开孔", "limit": 1, "wolong": "month", "btn": "Button_sShop", "say": "消耗钻石开孔", "desc": "<txt0012,D98515>【累计消耗钻石开孔达到2次】&", "kind": "nextbox3", "isnum": 1, "var": 2, "task_type": 4, "gift": "4:83395|15"}, {"id": 132, "name": "转移属性", "limit": 1, "wolong": "month", "btn": "Button_sShop", "say": "消耗多彩宝石进行转移属性", "desc": "<txt0012,D98515>【累计消耗多彩宝石转移属性达到2次】&", "kind": "nextbox4", "isnum": 1, "var": 2, "task_type": 4, "gift": "4:83395|15"}, {"id": 133, "name": "探索", "limit": 1, "wolong": "week", "btn": "But<PERSON>_Raid", "say": "获得云川秘宝数", "desc": "<txt0012,D98515>【累计获得云川秘宝5个】&", "kind": "getitem", "isnum": 1, "parmas": "82743.0", "var": 5, "task_type": 4, "gift": "4:83395|5"}, {"id": 134, "name": "招募武将", "limit": 1, "wolong": "week", "btn": "Button_kShop", "say": "将军，请在名将中招募一名武将。", "desc": "<txt0012,1A1A1A>在&<txt0012,D98515>【招募】&<txt0012,1A1A1A>中选择&<txt0012,D98515>【名将卡包】&<txt0012,1A1A1A>进行招募达到100次&。", "kind": "rec", "isnum": 1, "parmas": "1.0", "var": 100, "task_type": 4, "gift": "4:83395|10"}, {"id": 135, "name": "名将挑战", "limit": 1, "wolong": "week", "btn": "<PERSON><PERSON>_Master", "say": "参加名将马超挑战次数", "desc": "<txt0012,D98515>【参加名将马超挑战达到15次】&", "kind": "master", "isnum": 1, "var": 15, "task_type": 4, "gift": "4:83395|5"}, {"id": 136, "name": "累计消耗政令", "limit": 1, "wolong": "week", "btn": "Button_Map", "say": "累计消耗政令", "desc": "<txt0012,D98515>【消耗政令达到1000】&", "kind": "costitem", "isnum": 1, "parmas": "82003.0", "var": 1000, "task_type": 4, "gift": "4:83395|5"}, {"id": 137, "name": "通关试炼殿", "limit": 1, "wolong": "week", "btn": "Button_Elite", "say": "通关试炼殿阵营试炼次数", "desc": "<txt0012,D98515>【通关试炼殿阵营试炼次数达到15次】&", "kind": "eliteboss", "isnum": 1, "var": 15, "task_type": 4, "gift": "4:83395|5"}, {"id": 138, "name": "探索秘境", "limit": 1, "wolong": "week", "btn": "<PERSON><PERSON>_<PERSON>rold", "say": "探索世界秘境次数", "desc": "<txt0012,D98515>【探索世界秘境次数达到7次】&", "kind": "offlinecity", "isnum": 1, "var": 7, "task_type": 4, "gift": "4:83395|5"}, {"id": 139, "name": "离线扫荡世界头目", "limit": 1, "wolong": "week", "btn": "<PERSON><PERSON>_<PERSON>rold", "say": "离线扫荡世界头目个数", "desc": "<txt0012,D98515>【累计扫荡世界头目个数达到120个】&", "kind": "offlineboss", "isnum": 1, "var": 120, "task_type": 4, "gift": "4:83395|5"}, {"id": 140, "name": "消耗玉符", "limit": 1, "wolong": "week", "btn": "Button_wShop", "say": "消耗玉符", "desc": "<txt0012,D98515>【累计消耗玉符达到100个】&", "kind": "costitem", "isnum": 1, "parmas": "81999.0", "var": 100, "task_type": 4, "gift": "4:83395|20"}, {"id": 141, "name": "完成签到", "limit": 1, "wolong": "day", "btn": "Button_Activity", "say": "完成签到", "desc": "<txt0012,D98515>【完成签到次数达到1次】&", "kind": "sign", "isnum": 1, "var": 1, "task_type": 4, "gift": "4:83395|1"}, {"id": 142, "name": "军团boss", "limit": 1, "wolong": "day", "btn": "Button_Legion", "say": "参加军团头目", "desc": "<txt0012,D98515>【累计参加军团头目达到3次】&", "kind": "<PERSON><PERSON><PERSON><PERSON>", "isnum": 1, "var": 3, "task_type": 4, "gift": "4:83395|1"}, {"id": 143, "name": "蛮族入侵", "limit": 1, "wolong": "day", "btn": "Button_Activity", "say": "参加蛮族入侵活动", "desc": "<txt0012,D98515>【累计参加蛮族入侵达到5次】&", "kind": "turnboss", "isnum": 1, "var": 5, "task_type": 4, "gift": "4:83395|1"}, {"id": 144, "name": "累计消耗政令", "limit": 1, "wolong": "day", "btn": "Button_Map", "say": "累计消耗政令", "desc": "<txt0012,D98515>【消耗政令达到120】&", "kind": "costitem", "isnum": 1, "parmas": "82003.0", "var": 120, "task_type": 4, "gift": "4:83395|1"}, {"id": 145, "name": "消耗玉符", "limit": 1, "wolong": "day", "btn": "Button_wShop", "say": "消耗玉符", "desc": "<txt0012,D98515>【累计消耗玉符达到6个】&", "kind": "costitem", "isnum": 1, "parmas": "81999.0", "var": 6, "task_type": 4, "gift": "4:83395|2"}, {"id": 146, "name": "世界头目", "limit": 1, "wolong": "day", "btn": "<PERSON><PERSON>_<PERSON>rold", "say": "击杀世界头目", "desc": "<txt0012,D98515>【累计击杀世界头目达到30次】&", "kind": "killboos", "isnum": 1, "var": 30, "task_type": 4, "gift": "4:83395|1"}, {"id": 147, "name": "消耗元宝", "limit": 1, "wolong": "day", "desc": "<txt0012,1A1A1A>今日累计消费&<txt0012,D98515>【元宝】&<txt0012,1A1A1A>达到&<txt0012,D98515>【500】&", "kind": "red_glod", "isnum": 1, "var": 500, "task_type": 4, "gift": "4:83395|1"}, {"id": 148, "name": "名将挑战", "limit": 1, "wolong": "day", "btn": "<PERSON><PERSON>_Master", "say": "参加名将马超挑战次数", "desc": "<txt0012,D98515>【参加名将马超挑战达到2次】&", "kind": "master", "isnum": 1, "var": 2, "task_type": 4, "gift": "4:83395|1"}, {"id": 149, "name": "消耗玉符", "limit": 1, "wolong": "month", "btn": "Button_wShop", "say": "消耗玉符", "desc": "<txt0012,D98515>【累计消耗玉符达到500个】&", "kind": "costitem", "isnum": 1, "parmas": "81999.0", "var": 500, "task_type": 4, "gift": "4:83395|30"}, {"id": 150, "name": "地狱试炼", "limit": 1, "btn": "Button_Map", "say": "通关困难模式云川副本的困难副本可以解锁地狱模式", "desc": "<txt0012,D98515>【通关困难模式云川副本的困难副本可以解锁地狱模式】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 1, "gift": "4:82954|1", "needid": 83524, "neednum": 1}, {"id": 151, "name": "噩梦试炼", "limit": 1, "btn": "Button_Map", "say": "通关地狱云川副本的地狱副本可以解锁噩梦模式", "desc": "<txt0012,D98515>【通关地狱云川副本的地狱副本可以解锁噩梦模式】&", "kind": "payitem", "isnum": 1, "var": 1, "task_type": 1, "gift": "4:82955|1", "needid": 83525, "neednum": 1}]