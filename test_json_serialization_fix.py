#!/usr/bin/env python3
"""
测试JSON序列化修复
验证无穷大和NaN值的处理
"""

import asyncio
import logging
import sys
import json
import math
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_sanitize_function():
    """测试JSON清理函数"""
    print("=" * 60)
    print("🧪 测试JSON清理函数")
    print("=" * 60)
    
    try:
        from shop_models import sanitize_for_json
        
        # 测试数据
        test_cases = [
            {
                "name": "正常数据",
                "data": {"price": 100, "probability": 0.5, "name": "test"},
                "expected_serializable": True
            },
            {
                "name": "包含正无穷大",
                "data": {"price": 100, "probability": float('inf'), "name": "test"},
                "expected_serializable": True
            },
            {
                "name": "包含负无穷大",
                "data": {"price": 100, "probability": float('-inf'), "name": "test"},
                "expected_serializable": True
            },
            {
                "name": "包含NaN",
                "data": {"price": 100, "probability": float('nan'), "name": "test"},
                "expected_serializable": True
            },
            {
                "name": "嵌套数据",
                "data": {
                    "config": {
                        "price": float('inf'),
                        "items": [1, 2, float('nan'), 4]
                    }
                },
                "expected_serializable": True
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n{i+1}. 测试 {test_case['name']}")
            
            # 清理数据
            cleaned_data = sanitize_for_json(test_case['data'])
            print(f"   原始数据: {test_case['data']}")
            print(f"   清理后: {cleaned_data}")
            
            # 测试JSON序列化
            try:
                json_str = json.dumps(cleaned_data)
                print(f"   ✅ JSON序列化成功")
                
                # 测试反序列化
                parsed_data = json.loads(json_str)
                print(f"   ✅ JSON反序列化成功")
                
            except Exception as e:
                print(f"   ❌ JSON序列化失败: {str(e)}")
                return False
        
        print("\n✅ JSON清理函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_shop_item_model():
    """测试商品模型的JSON序列化"""
    print("\n" + "=" * 60)
    print("🧪 测试商品模型JSON序列化")
    print("=" * 60)
    
    try:
        from shop_models import ShopItemConfig
        
        # 创建包含异常值的商品配置
        test_configs = [
            {
                "name": "正常配置",
                "refresh_probability": 0.5
            },
            {
                "name": "无穷大概率",
                "refresh_probability": float('inf')
            },
            {
                "name": "NaN概率",
                "refresh_probability": float('nan')
            },
            {
                "name": "负无穷大概率",
                "refresh_probability": float('-inf')
            }
        ]
        
        for i, test_case in enumerate(test_configs):
            print(f"\n{i+1}. 测试 {test_case['name']}")
            print(f"   refresh_probability: {test_case['refresh_probability']}")
            
            # 创建商品配置
            config = ShopItemConfig(
                config_id=f"test_config_{i}",
                shop_id="test_shop",
                slot_id=None,
                item_template_id="test_item",
                item_quantity=1,
                item_quality=None,
                price_config={"currency_type": "gold", "base_price": 100},
                purchase_limit=None,
                availability={},
                refresh_weight=100,
                refresh_probability=test_case['refresh_probability'],
                is_active=True,
                sort_order=0,
                display_config={},
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            try:
                # 转换为字典
                config_dict = config.to_dict()
                print(f"   转换后的refresh_probability: {config_dict['refresh_probability']}")
                
                # 测试JSON序列化
                json_str = json.dumps(config_dict)
                print(f"   ✅ JSON序列化成功")
                
                # 验证值的合理性
                if math.isinf(test_case['refresh_probability']) or math.isnan(test_case['refresh_probability']):
                    if 0.0 <= config_dict['refresh_probability'] <= 1.0:
                        print(f"   ✅ 异常值已正确处理")
                    else:
                        print(f"   ⚠️ 异常值处理可能不正确")
                
            except Exception as e:
                print(f"   ❌ 处理失败: {str(e)}")
                return False
        
        print("\n✅ 商品模型JSON序列化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_api_response():
    """测试API响应的JSON安全性"""
    print("\n" + "=" * 60)
    print("🧪 测试API响应JSON安全性")
    print("=" * 60)
    
    try:
        from shop_models import sanitize_for_json
        
        # 模拟API响应数据
        mock_response = {
            "success": True,
            "data": {
                "shop_id": "test_shop",
                "shop_name": "测试商店",
                "items": [
                    {
                        "config_id": "item_1",
                        "original_price": 100,
                        "final_price": 80,
                        "refresh_probability": float('inf'),  # 异常值
                        "discount_amount": 20
                    },
                    {
                        "config_id": "item_2",
                        "original_price": 200,
                        "final_price": 200,
                        "refresh_probability": float('nan'),  # 异常值
                        "discount_amount": 0
                    }
                ],
                "total": 2
            }
        }
        
        print("1. 原始响应数据包含异常值")
        print(f"   item_1.refresh_probability: {mock_response['data']['items'][0]['refresh_probability']}")
        print(f"   item_2.refresh_probability: {mock_response['data']['items'][1]['refresh_probability']}")
        
        print("2. 清理响应数据...")
        cleaned_response = sanitize_for_json(mock_response)
        
        print("3. 清理后的数据:")
        print(f"   item_1.refresh_probability: {cleaned_response['data']['items'][0]['refresh_probability']}")
        print(f"   item_2.refresh_probability: {cleaned_response['data']['items'][1]['refresh_probability']}")
        
        print("4. 测试JSON序列化...")
        try:
            json_str = json.dumps(cleaned_response)
            print("   ✅ JSON序列化成功")
            
            # 验证数据大小合理
            if len(json_str) > 0:
                print(f"   ✅ JSON数据大小: {len(json_str)} 字符")
            
        except Exception as e:
            print(f"   ❌ JSON序列化失败: {str(e)}")
            return False
        
        print("\n✅ API响应JSON安全性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始JSON序列化修复测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试清理函数
    print("🧹 测试JSON清理函数...")
    sanitize_result = test_sanitize_function()
    results.append(("JSON清理函数测试", sanitize_result))
    
    # 2. 测试商品模型
    print("\n🏷️ 测试商品模型JSON序列化...")
    model_result = await test_shop_item_model()
    results.append(("商品模型序列化测试", model_result))
    
    # 3. 测试API响应
    print("\n🌐 测试API响应JSON安全性...")
    api_result = await test_api_response()
    results.append(("API响应安全性测试", api_result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！JSON序列化修复成功！")
        print("现在API应该不会再出现 'Out of range float values' 错误")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行失败: {str(e)}")
        sys.exit(1)
