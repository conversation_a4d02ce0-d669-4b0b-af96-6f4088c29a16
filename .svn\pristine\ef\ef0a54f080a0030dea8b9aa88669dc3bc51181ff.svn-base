from enum import IntEnum, Enum

class MessageId(IntEnum):
    GM = 1001 # GM命令
    ERROR = 1000 # 错误
    HEARTBEAT = 0 # 心跳
    PING = -1 # Ping消息，用于连接检测
    CHAT = 1 # 聊天
    PRIVATE_CHAT =2 # 私聊
    GROUP_CHAT = 3 # 群聊
    GET_ITEMS = 4 # 获取道具
    DELETE_ITEM = 5 # 删除道具
    BROADCAST_MESSAGE = 6 # 广播消息
    SET_NICKNAME = 7 # 设置昵称
    ROLE_INFO = 8 # 角色信息 
    ENTER_GAME = 9 # 进入游戏
    CREATE_ROLE = 10 # 创建角色
    ASSET_CHANGED = 11       # 单个资产变更
    ASSET_BATCH_CHANGED = 12 # 批量资产变更
    GET_EQUIPMENT = 13 # 获取装备
    GET_RUNE = 14 # 获取符文    
    
    # Monster related message IDs
    CHECK_COOLDOWN = 100     # Check monster cooldown
    MONSTER_KILLED = 101     # Monster killed notification
    GET_ALL_COOLDOWNS = 102  # Get all active cooldowns
    RESET_COOLDOWN = 103     # Reset cooldown (GM only)
    MONSTER_RESPAWNED = 104  # Monster respawned notification
    GET_MONSTERS = 105       # Get monster list
    
class ItemType(str):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文

class CooldownType(str, Enum):
    PERSONAL = "personal"  # Personal cooldown
    GUILD = "guild"        # Guild cooldown
    GLOBAL = "global"      # Global cooldown