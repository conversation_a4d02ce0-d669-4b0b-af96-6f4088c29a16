"""
商店事件广播服务
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from enums import MessageId
from models import MessageModel

logger = logging.getLogger(__name__)


class ShopEventBroadcaster:
    """商店事件广播器"""
    
    def __init__(self):
        self.connection_manager = None
    
    def set_connection_manager(self, connection_manager):
        """设置连接管理器"""
        self.connection_manager = connection_manager
    
    async def broadcast_purchase_success(self, player_id: str, purchase_data: Dict[str, Any]):
        """广播购买成功事件"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播购买成功事件")
                return
            
            message = MessageModel(
                msgId=MessageId.SHOP_PURCHASE_SUCCESS,
                data={
                    "event_type": "purchase_success",
                    "player_id": player_id,
                    "purchase_id": purchase_data.get("purchase_id"),
                    "item_template_id": purchase_data.get("item_template_id"),
                    "quantity": purchase_data.get("quantity"),
                    "final_price": purchase_data.get("final_price"),
                    "currency_type": purchase_data.get("currency_type"),
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            # 发送给购买者
            await self.connection_manager.send_personal_message(str(message), player_id)
            
            logger.info(f"购买成功事件已发送给玩家: {player_id}")
            
        except Exception as e:
            logger.error(f"广播购买成功事件失败: {str(e)}")
    
    async def broadcast_limits_reset(self, limit_type: str, affected_players: Optional[List[str]] = None):
        """广播限购重置事件"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播限购重置事件")
                return
            
            message = MessageModel(
                msgId=MessageId.SHOP_LIMITS_RESET,
                data={
                    "event_type": "limits_reset",
                    "limit_type": limit_type,
                    "reset_time": datetime.now().isoformat(),
                    "message": f"{limit_type} 限购已重置"
                }
            ).model_dump()
            
            if affected_players:
                # 发送给指定玩家
                for player_id in affected_players:
                    await self.connection_manager.send_personal_message(str(message), player_id)
                logger.info(f"限购重置事件已发送给 {len(affected_players)} 个玩家")
            else:
                # 广播给所有在线玩家
                await self.connection_manager.broadcast(str(message))
                logger.info(f"限购重置事件已广播给所有在线玩家")
            
        except Exception as e:
            logger.error(f"广播限购重置事件失败: {str(e)}")
    
    async def broadcast_shop_refreshed(self, shop_id: str, shop_name: str, affected_players: Optional[List[str]] = None):
        """广播商店刷新事件"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播商店刷新事件")
                return
            
            message = MessageModel(
                msgId=MessageId.SHOP_REFRESHED,
                data={
                    "event_type": "shop_refreshed",
                    "shop_id": shop_id,
                    "shop_name": shop_name,
                    "refresh_time": datetime.now().isoformat(),
                    "message": f"商店 {shop_name} 已刷新"
                }
            ).model_dump()
            
            if affected_players:
                # 发送给指定玩家（个人商店刷新）
                for player_id in affected_players:
                    await self.connection_manager.send_personal_message(str(message), player_id)
                logger.info(f"商店刷新事件已发送给 {len(affected_players)} 个玩家")
            else:
                # 广播给所有在线玩家（全服商店刷新）
                await self.connection_manager.broadcast(str(message))
                logger.info(f"商店刷新事件已广播给所有在线玩家")
            
        except Exception as e:
            logger.error(f"广播商店刷新事件失败: {str(e)}")
    
    async def broadcast_discount_updated(self, discount_data: Dict[str, Any], affected_players: Optional[List[str]] = None):
        """广播折扣更新事件"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播折扣更新事件")
                return
            
            message = MessageModel(
                msgId=MessageId.SHOP_DISCOUNT_UPDATED,
                data={
                    "event_type": "discount_updated",
                    "discount_id": discount_data.get("discount_id"),
                    "discount_name": discount_data.get("discount_name"),
                    "scope_type": discount_data.get("scope_type"),
                    "scope_values": discount_data.get("scope_values", []),
                    "update_time": datetime.now().isoformat(),
                    "message": f"折扣 {discount_data.get('discount_name')} 已更新"
                }
            ).model_dump()
            
            if affected_players:
                # 发送给指定玩家
                for player_id in affected_players:
                    await self.connection_manager.send_personal_message(str(message), player_id)
                logger.info(f"折扣更新事件已发送给 {len(affected_players)} 个玩家")
            else:
                # 广播给所有在线玩家
                await self.connection_manager.broadcast(str(message))
                logger.info(f"折扣更新事件已广播给所有在线玩家")
            
        except Exception as e:
            logger.error(f"广播折扣更新事件失败: {str(e)}")
    
    async def broadcast_item_updated(self, item_data: Dict[str, Any], affected_players: Optional[List[str]] = None):
        """广播商品更新事件"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播商品更新事件")
                return
            
            message = MessageModel(
                msgId=MessageId.SHOP_ITEM_UPDATED,
                data={
                    "event_type": "item_updated",
                    "config_id": item_data.get("config_id"),
                    "shop_id": item_data.get("shop_id"),
                    "item_template_id": item_data.get("item_template_id"),
                    "update_type": item_data.get("update_type", "modified"),  # added, modified, removed
                    "update_time": datetime.now().isoformat(),
                    "message": f"商品 {item_data.get('item_template_id')} 已更新"
                }
            ).model_dump()
            
            if affected_players:
                # 发送给指定玩家
                for player_id in affected_players:
                    await self.connection_manager.send_personal_message(str(message), player_id)
                logger.info(f"商品更新事件已发送给 {len(affected_players)} 个玩家")
            else:
                # 广播给所有在线玩家
                await self.connection_manager.broadcast(str(message))
                logger.info(f"商品更新事件已广播给所有在线玩家")
            
        except Exception as e:
            logger.error(f"广播商品更新事件失败: {str(e)}")
    
    async def notify_currency_change(self, player_id: str, currency_changes: Dict[str, int]):
        """通知玩家货币变化"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法通知货币变化")
                return
            
            message = MessageModel(
                msgId=MessageId.PLAYER_DATA_UPDATE,
                data={
                    "event_type": "currency_change",
                    "player_id": player_id,
                    "currency_changes": currency_changes,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            # 发送给指定玩家
            await self.connection_manager.send_personal_message(str(message), player_id)
            
            logger.debug(f"货币变化通知已发送给玩家: {player_id}")
            
        except Exception as e:
            logger.error(f"通知货币变化失败: {str(e)}")
    
    async def notify_item_received(self, player_id: str, items: List[str]):
        """通知玩家获得道具"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法通知道具获得")
                return
            
            message = MessageModel(
                msgId=MessageId.PLAYER_DATA_UPDATE,
                data={
                    "event_type": "items_received",
                    "player_id": player_id,
                    "items": items,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            # 发送给指定玩家
            await self.connection_manager.send_personal_message(str(message), player_id)
            
            logger.debug(f"道具获得通知已发送给玩家: {player_id}")
            
        except Exception as e:
            logger.error(f"通知道具获得失败: {str(e)}")
    
    async def broadcast_system_notification(self, message_text: str, notification_type: str = "info"):
        """广播系统通知"""
        try:
            if not self.connection_manager:
                logger.warning("连接管理器未设置，无法广播系统通知")
                return
            
            message = MessageModel(
                msgId=MessageId.SYSTEM_NOTIFICATION,
                data={
                    "type": notification_type,
                    "message": message_text,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()
            
            # 广播给所有在线玩家
            await self.connection_manager.broadcast(str(message))
            
            logger.info(f"系统通知已广播: {message_text}")
            
        except Exception as e:
            logger.error(f"广播系统通知失败: {str(e)}")


# 全局事件广播器实例
shop_event_broadcaster = ShopEventBroadcaster()


def get_shop_event_broadcaster() -> ShopEventBroadcaster:
    """获取商店事件广播器实例"""
    return shop_event_broadcaster


def set_shop_event_broadcaster_connection_manager(connection_manager):
    """设置商店事件广播器的连接管理器"""
    shop_event_broadcaster.set_connection_manager(connection_manager)
