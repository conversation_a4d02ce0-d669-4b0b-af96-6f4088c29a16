# 🚀 商店管理系统 - 快速启动指南

## ❌ **CORS错误解决方案**

如果您遇到以下错误：
```
Access to fetch at 'file:///D:/api/shop/admin/shop' from origin 'null' has been blocked by CORS policy
```

这是因为直接用文件协议打开HTML文件导致的。请按以下方法解决：

## 🔧 **解决方案**

### **方案1: 使用Python内置服务器（推荐）**

#### **步骤1: 打开命令行**
- Windows: 按 `Win + R`，输入 `cmd`，回车
- Mac/Linux: 打开终端

#### **步骤2: 进入项目目录**
```bash
cd admin/shopadmin
```

#### **步骤3: 启动HTTP服务器**

**Python 3 用户:**
```bash
python -m http.server 8080
```

**Python 2 用户:**
```bash
python -m SimpleHTTPServer 8080
```

#### **步骤4: 访问系统**
在浏览器中打开：`http://localhost:8080/index.html`

---

### **方案2: 使用Node.js服务器**

#### **步骤1: 安装Node.js**
从 https://nodejs.org/ 下载并安装Node.js

#### **步骤2: 安装http-server**
```bash
npm install -g http-server
```

#### **步骤3: 启动服务器**
```bash
cd admin/shopadmin
http-server -p 8080
```

#### **步骤4: 访问系统**
在浏览器中打开：`http://localhost:8080/index.html`

---

### **方案3: 使用VS Code Live Server**

#### **步骤1: 安装VS Code**
从 https://code.visualstudio.com/ 下载并安装

#### **步骤2: 安装Live Server扩展**
1. 打开VS Code
2. 点击左侧扩展图标
3. 搜索"Live Server"
4. 安装第一个结果

#### **步骤3: 启动服务器**
1. 在VS Code中打开 `admin/shopadmin` 文件夹
2. 右键点击 `index.html`
3. 选择"Open with Live Server"

---

### **方案4: 使用其他Web服务器**

#### **Apache/Nginx**
将 `admin/shopadmin` 目录放到Web服务器的文档根目录下

#### **IIS (Windows)**
在IIS中创建新站点，指向 `admin/shopadmin` 目录

## ⚙️ **配置API地址**

### **修改配置文件**

编辑 `js/config.js` 文件，修改API地址：

```javascript
// 开发环境API地址
development: {
    baseURL: 'http://localhost:8000',  // 修改为您的后端地址
    timeout: 10000
},

// 生产环境API地址  
production: {
    baseURL: 'https://your-api-server.com',  // 修改为生产环境地址
    timeout: 10000
}
```

### **常见API地址配置**

| 环境 | API地址示例 |
|------|-------------|
| 本地开发 | `http://localhost:8000` |
| 本地开发(其他端口) | `http://localhost:5000` |
| 局域网服务器 | `http://*************:8000` |
| 测试服务器 | `http://test-server.com:8000` |
| 生产服务器 | `https://api.yoursite.com` |

## 🧪 **测试连接**

### **1. 检查后端服务**
确保您的FastAPI服务器正在运行：
```bash
# 检查服务器是否响应
curl http://localhost:8000/api/shop/health
```

### **2. 检查API接口**
在浏览器中访问：
```
http://localhost:8000/docs
```
查看API文档，确认管理接口已添加。

### **3. 测试前端连接**
1. 打开浏览器开发者工具 (F12)
2. 访问管理界面
3. 查看Console标签页的日志
4. 查看Network标签页的请求

## 🔍 **故障排除**

### **常见错误及解决方案**

#### **1. 端口被占用**
```
Error: listen EADDRINUSE: address already in use :::8080
```
**解决方案**: 更换端口号
```bash
python -m http.server 8081
```

#### **2. Python命令不存在**
```
'python' is not recognized as an internal or external command
```
**解决方案**: 
- 安装Python: https://python.org/downloads/
- 或使用 `python3` 命令

#### **3. 后端连接失败**
```
Failed to fetch
```
**解决方案**:
1. 检查后端服务是否启动
2. 检查API地址配置是否正确
3. 检查防火墙设置

#### **4. 跨域问题**
```
CORS policy error
```
**解决方案**:
1. 确保使用HTTP服务器访问，不要直接打开文件
2. 检查后端CORS配置

## 📱 **浏览器兼容性**

### **推荐浏览器**
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### **不支持的浏览器**
- ❌ Internet Explorer
- ❌ 过旧版本的浏览器

## 🎯 **快速验证**

### **验证步骤**
1. ✅ 启动HTTP服务器
2. ✅ 访问 `http://localhost:8080/index.html`
3. ✅ 看到商店管理界面
4. ✅ 点击"新增商店"按钮
5. ✅ 填写表单并保存
6. ✅ 检查是否成功创建

### **成功标志**
- 界面正常显示
- 没有控制台错误
- API请求正常发送
- 数据正确保存

## 📞 **获取帮助**

如果仍然遇到问题：

1. **检查控制台错误**: 按F12查看详细错误信息
2. **查看网络请求**: 在Network标签页检查API调用
3. **检查后端日志**: 查看FastAPI服务器的日志输出
4. **验证配置**: 确认 `js/config.js` 中的API地址正确

---

**🎉 祝您使用愉快！**
