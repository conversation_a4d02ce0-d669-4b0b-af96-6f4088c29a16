import redis.asyncio as redis
import asyncio
import logging
import os
from redis import exceptions as redis_exceptions
from config import config

logger = logging.getLogger(__name__)

class RedisManager:
    _instance = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._redis = None
        self._initialized = False

    @classmethod
    async def get_instance(cls):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
                await cls._instance._init_redis()
            return cls._instance

    async def _init_redis(self):
        redis_config = config.get_redis_config()
        base_connections = 10
        cpu_count = os.cpu_count() or 4
        workers = config.get_server_config().get("workers", 4)
        max_connections = int(base_connections * min(cpu_count, workers) * 1.5)
        max_connections = min(max_connections, 200)
        config_max_connections = redis_config.get('max_connections')
        if config_max_connections:
            max_connections = min(int(config_max_connections), max_connections * 2)
        logger.info(f"RedisManager: 动态分配连接池大小: {max_connections}")
        try:
            self._redis = redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config.get('password'),
                db=redis_config.get('db', 0),
                max_connections=max_connections,
                decode_responses=True,
                socket_timeout=redis_config.get('socket_timeout', 5.0),
                socket_connect_timeout=redis_config.get('socket_connect_timeout', 5.0),
                socket_keepalive=True,
                health_check_interval=30
            )
            await self._redis.ping()
            self._initialized = True
            logger.info("RedisManager: Redis连接池初始化成功")
        except redis_exceptions.TimeoutError as e:
            logger.error(f"RedisManager: Redis连接超时: {str(e)}")
            self._redis = None
            self._initialized = False
        except Exception as e:
            logger.error(f"RedisManager: Redis连接初始化失败: {str(e)}")
            self._redis = None
            self._initialized = False

    async def get_redis(self, retries=3, retry_delay=0.5):
        for attempt in range(retries):
            if self._initialized and self._redis is not None:
                try:
                    await self._redis.ping()
                    return self._redis
                except Exception as e:
                    logger.warning(f"RedisManager: ping失败，重试初始化，第{attempt+1}次: {str(e)}")
                    self._initialized = False
                    self._redis = None
            try:
                await self._init_redis()
                if self._initialized and self._redis is not None:
                    try:
                        await self._redis.ping()
                        return self._redis
                    except Exception as e:
                        logger.warning(f"RedisManager: ping失败，重试初始化，第{attempt+1}次: {str(e)}")
                        self._initialized = False
                        self._redis = None
            except Exception as e:
                logger.warning(f"RedisManager: get_redis初始化失败，第{attempt+1}次重试: {str(e)}")
            if attempt < retries - 1:
                await asyncio.sleep(retry_delay)
        raise Exception("RedisManager: Redis未初始化成功，重试多次后仍失败")

    async def close(self):
        """优雅关闭 Redis 连接池"""
        if self._redis is not None:
            try:
                await self._redis.close()
                self._redis = None
                self._initialized = False
                logger.info("RedisManager: Redis连接池已关闭")
            except Exception as e:
                logger.warning(f"RedisManager: 关闭Redis连接池异常: {str(e)}")

    # 辅助方法：安全get/set，自动捕获TimeoutError并重试
    async def safe_get(self, key, default=None, retries=3, retry_delay=0.2):
        for attempt in range(retries):
            try:
                return await self._redis.get(key)
            except redis_exceptions.TimeoutError:
                logger.warning(f"RedisManager: get超时: {key}, 第{attempt+1}次重试")
                if attempt < retries - 1:
                    await asyncio.sleep(retry_delay)
                else:
                    return default
            except Exception as e:
                logger.error(f"RedisManager: get异常: {key}, {str(e)}")
                return default

    async def safe_set(self, key, value, ex=None, retries=3, retry_delay=0.2):
        for attempt in range(retries):
            try:
                return await self._redis.set(key, value, ex=ex)
            except redis_exceptions.TimeoutError:
                logger.warning(f"RedisManager: set超时: {key}, 第{attempt+1}次重试")
                if attempt < retries - 1:
                    await asyncio.sleep(retry_delay)
                else:
                    return False
            except Exception as e:
                logger.error(f"RedisManager: set异常: {key}, {str(e)}")
                return False 