# Python代码规范指南

## 📋 概述

本文档定义了游戏服务器项目的Python代码规范，基于PEP 8扩展，针对异步编程、游戏服务器特性和团队协作进行了优化。

## 🎯 核心原则

1. **可读性优于简洁性** - 代码应该易于理解和维护
2. **一致性** - 整个项目保持统一的编码风格
3. **性能意识** - 考虑游戏服务器的性能要求
4. **安全第一** - 编写安全的代码，防范常见漏洞

## 📝 代码格式规范

### 缩进和空格
```python
# ✅ 正确：使用4个空格缩进
def process_user_action(user_id: int, action_type: str) -> bool:
    if action_type == "login":
        return await authenticate_user(user_id)
    return False

# ❌ 错误：使用Tab或不一致的缩进
def process_user_action(user_id: int, action_type: str) -> bool:
	if action_type == "login":  # Tab缩进
      return await authenticate_user(user_id)  # 6个空格
    return False
```

### 行长度
- **最大行长度**: 88字符（Black格式化器标准）
- **文档字符串**: 72字符
- **注释**: 72字符

```python
# ✅ 正确：合理换行
user_data = await database_manager.get_user_comprehensive_data(
    user_id=user_id,
    include_inventory=True,
    include_achievements=True
)

# ❌ 错误：行过长
user_data = await database_manager.get_user_comprehensive_data(user_id=user_id, include_inventory=True, include_achievements=True, include_friends=True)
```

### 空行规则
```python
# ✅ 正确：类和函数间的空行
import asyncio
from typing import Dict, List, Optional


class UserManager:
    """用户管理器类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self._user_cache: Dict[int, User] = {}
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """获取用户信息"""
        if user_id in self._user_cache:
            return self._user_cache[user_id]
        
        user_data = await self.db_manager.get_user(user_id)
        if user_data:
            user = User.from_dict(user_data)
            self._user_cache[user_id] = user
            return user
        
        return None
    
    async def update_user(self, user: User) -> bool:
        """更新用户信息"""
        try:
            await self.db_manager.update_user(user.to_dict())
            self._user_cache[user.id] = user
            return True
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return False


async def main():
    """主函数"""
    user_manager = UserManager(db_manager)
    await user_manager.initialize()
```

## 🏷️ 命名约定

### 变量和函数命名
```python
# ✅ 正确：snake_case，描述性命名
user_id = 12345
player_inventory = {}
max_connection_count = 1000

async def authenticate_user(username: str, password: str) -> bool:
    """用户认证"""
    pass

async def get_player_equipment_list(player_id: int) -> List[Equipment]:
    """获取玩家装备列表"""
    pass

# ❌ 错误：不规范的命名
userId = 12345  # 应该用snake_case
playerInv = {}  # 缩写不清晰
MAX_CONN = 1000  # 常量应该更描述性

def authUser(u: str, p: str) -> bool:  # 函数名和参数名不清晰
    pass
```

### 类命名
```python
# ✅ 正确：PascalCase，清晰的类名
class UserManager:
    """用户管理器"""
    pass

class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    pass

class GameDatabaseManager:
    """游戏数据库管理器"""
    pass

# ❌ 错误：不规范的类名
class userManager:  # 应该用PascalCase
    pass

class WSConnMgr:  # 缩写不清晰
    pass
```

### 常量命名
```python
# ✅ 正确：UPPER_SNAKE_CASE
MAX_PLAYERS_PER_ROOM = 100
DEFAULT_TIMEOUT_SECONDS = 30
REDIS_KEY_PREFIX = "game_server:"
DATABASE_CONNECTION_POOL_SIZE = 20

# 游戏相关常量
EQUIPMENT_MAX_LEVEL = 100
PLAYER_MAX_INVENTORY_SIZE = 200
MONSTER_RESPAWN_TIME_SECONDS = 300

# ❌ 错误：不规范的常量名
maxPlayers = 100  # 应该用UPPER_SNAKE_CASE
default_timeout = 30  # 应该用UPPER_SNAKE_CASE
```

## 🔤 类型注解

### 基础类型注解
```python
from typing import Dict, List, Optional, Union, Tuple, Any
from datetime import datetime

# ✅ 正确：完整的类型注解
def calculate_damage(
    base_damage: int,
    multiplier: float,
    critical_hit: bool = False
) -> int:
    """计算伤害值"""
    damage = int(base_damage * multiplier)
    if critical_hit:
        damage *= 2
    return damage

async def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户数据"""
    try:
        return await database.get_user(user_id)
    except Exception:
        return None

# 复杂类型注解
PlayerStats = Dict[str, Union[int, float]]
InventoryItem = Tuple[int, str, int]  # (item_id, item_name, quantity)

class Player:
    def __init__(
        self,
        player_id: int,
        username: str,
        stats: PlayerStats,
        inventory: List[InventoryItem]
    ) -> None:
        self.player_id = player_id
        self.username = username
        self.stats = stats
        self.inventory = inventory
```

### 异步函数类型注解
```python
from typing import Awaitable, Coroutine

# ✅ 正确：异步函数类型注解
async def process_game_event(
    event_type: str,
    event_data: Dict[str, Any]
) -> bool:
    """处理游戏事件"""
    pass

async def batch_update_players(
    player_updates: List[Dict[str, Any]]
) -> List[bool]:
    """批量更新玩家数据"""
    results = []
    for update in player_updates:
        result = await update_player(update)
        results.append(result)
    return results

# 回调函数类型
EventCallback = Callable[[str, Dict[str, Any]], Awaitable[None]]

class EventBus:
    def __init__(self) -> None:
        self._callbacks: Dict[str, List[EventCallback]] = {}
    
    def register_callback(
        self,
        event_type: str,
        callback: EventCallback
    ) -> None:
        """注册事件回调"""
        if event_type not in self._callbacks:
            self._callbacks[event_type] = []
        self._callbacks[event_type].append(callback)
```

## 📚 文档字符串规范

### 函数文档字符串
```python
def calculate_player_level(experience_points: int) -> int:
    """根据经验值计算玩家等级
    
    Args:
        experience_points: 玩家当前经验值
        
    Returns:
        玩家等级（1-100）
        
    Raises:
        ValueError: 当经验值为负数时
        
    Example:
        >>> calculate_player_level(1000)
        5
        >>> calculate_player_level(10000)
        15
    """
    if experience_points < 0:
        raise ValueError("经验值不能为负数")
    
    # 经验值到等级的转换逻辑
    level = int((experience_points / 1000) ** 0.5) + 1
    return min(level, 100)

async def authenticate_user(
    username: str,
    password: str,
    ip_address: str
) -> Tuple[bool, Optional[str]]:
    """用户认证
    
    验证用户名和密码，检查IP地址是否被封禁，
    返回认证结果和可能的错误信息。
    
    Args:
        username: 用户名
        password: 密码（明文）
        ip_address: 客户端IP地址
        
    Returns:
        认证结果元组：(是否成功, 错误信息)
        成功时错误信息为None
        
    Raises:
        DatabaseError: 数据库连接失败时
        
    Note:
        密码会在函数内部进行哈希处理，
        不会以明文形式存储或传输。
    """
    pass
```

### 类文档字符串
```python
class GameSessionManager:
    """游戏会话管理器
    
    负责管理玩家的游戏会话，包括会话创建、维护、
    清理和状态同步。支持多房间、多玩家的并发管理。
    
    Attributes:
        max_sessions: 最大会话数量
        active_sessions: 当前活跃会话字典
        session_timeout: 会话超时时间（秒）
        
    Example:
        >>> session_manager = GameSessionManager(max_sessions=1000)
        >>> await session_manager.create_session(user_id=123, room_id=456)
        >>> session = await session_manager.get_session(user_id=123)
    """
    
    def __init__(
        self,
        max_sessions: int = 1000,
        session_timeout: int = 3600
    ) -> None:
        """初始化会话管理器
        
        Args:
            max_sessions: 最大会话数量，默认1000
            session_timeout: 会话超时时间（秒），默认3600
        """
        self.max_sessions = max_sessions
        self.session_timeout = session_timeout
        self.active_sessions: Dict[int, GameSession] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
```

## 🚫 代码质量要求

### 避免的反模式
```python
# ❌ 错误：过长的函数
def process_user_login(username, password, ip, device_info, session_data):
    # 100+ 行代码...
    pass

# ✅ 正确：拆分为多个小函数
async def process_user_login(
    username: str,
    password: str,
    login_context: LoginContext
) -> LoginResult:
    """处理用户登录"""
    # 验证用户凭据
    auth_result = await authenticate_user(username, password)
    if not auth_result.success:
        return LoginResult.failed(auth_result.error)
    
    # 检查安全限制
    security_check = await check_security_restrictions(
        username, login_context.ip_address
    )
    if not security_check.passed:
        return LoginResult.failed(security_check.reason)
    
    # 创建会话
    session = await create_user_session(auth_result.user, login_context)
    return LoginResult.success(session)

# ❌ 错误：深层嵌套
def process_game_action(action):
    if action.type == "move":
        if action.target:
            if action.target.is_valid():
                if action.player.can_move():
                    # 执行移动
                    pass

# ✅ 正确：早期返回，减少嵌套
def process_game_action(action: GameAction) -> ActionResult:
    """处理游戏动作"""
    if action.type != "move":
        return ActionResult.invalid("不支持的动作类型")
    
    if not action.target:
        return ActionResult.invalid("缺少目标位置")
    
    if not action.target.is_valid():
        return ActionResult.invalid("无效的目标位置")
    
    if not action.player.can_move():
        return ActionResult.invalid("玩家当前无法移动")
    
    # 执行移动逻辑
    return execute_player_move(action.player, action.target)
```

## 🔧 工具配置

### pyproject.toml 配置
```toml
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.git
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["game_server", "models", "utils"]

[tool.pylint.messages_control]
disable = [
    "too-few-public-methods",
    "too-many-arguments",
    "too-many-instance-attributes"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

## ✅ 检查清单

### 代码提交前检查
- [ ] 代码通过black格式化
- [ ] 导入语句通过isort排序
- [ ] 通过flake8检查
- [ ] 通过mypy类型检查
- [ ] 所有函数都有类型注解
- [ ] 公共函数都有文档字符串
- [ ] 变量和函数命名符合规范
- [ ] 没有超过88字符的行
- [ ] 没有未使用的导入和变量

---

**注意**: 使用pre-commit钩子可以自动执行这些检查，确保代码质量。
