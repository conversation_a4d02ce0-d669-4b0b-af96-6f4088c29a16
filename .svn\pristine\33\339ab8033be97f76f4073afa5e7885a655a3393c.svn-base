/**
 * 商店管理系统 - 商品管理逻辑
 * 负责商品配置的增删改查操作和界面交互
 */

class ItemManager {
    constructor() {
        this.items = [];
        this.filteredItems = [];
        this.currentItem = null;
        this.shopId = null;
        this.shopInfo = null;
        this.isLoading = false;
        this.isSubmitting = false;  // 防止重复提交

        // 初始化
        this.init();
    }

    /**
     * 初始化管理器
     */
    async init() {
        try {
            // 获取URL参数中的商店ID
            const urlParams = new URLSearchParams(window.location.search);
            this.shopId = urlParams.get('shop_id');
            
            if (!this.shopId) {
                throw new Error('缺少商店ID参数');
            }
            
            await this.loadShopInfo();
            await this.loadItems();
            this.bindEvents();
            
            console.log('[ItemManager] 初始化完成');
        } catch (error) {
            console.error('[ItemManager] 初始化失败:', error);
            this.showMessage('系统初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 注意：不在这里绑定表单提交事件，因为HTML中已经有onsubmit属性
        // 避免重复绑定导致表单被提交两次

        // 移除点击空白区域关闭模态框的功能，避免误操作
        // 用户必须点击关闭按钮或按ESC键才能关闭

        // ESC键关闭模态框（保留此功能，因为这是用户主动操作）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        console.log('[ItemManager] 事件绑定完成 - 已禁用点击空白关闭功能');
    }

    // ==================== 数据加载方法 ====================

    /**
     * 加载商店信息
     */
    async loadShopInfo() {
        try {
            const response = await shopAPI.getShop(this.shopId);
            
            if (response.success) {
                this.shopInfo = response.data;
                this.updateShopInfo();
                console.log(`[ItemManager] 加载商店信息: ${this.shopInfo.shop_name}`);
            } else {
                throw new Error(response.message || '加载商店信息失败');
            }
            
        } catch (error) {
            console.error('[ItemManager] 加载商店信息失败:', error);
            this.showMessage('加载商店信息失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载商品配置列表
     */
    async loadItems() {
        try {
            this.setLoading(true);
            
            const response = await shopAPI.getShopItems(this.shopId);
            
            if (response.success) {
                this.items = response.data || [];
                this.filteredItems = [...this.items];
                this.renderItemList();
                this.updateItemCount();
                console.log(`[ItemManager] 加载了 ${this.items.length} 个商品配置`);
            } else {
                throw new Error(response.message || '加载商品配置失败');
            }
            
        } catch (error) {
            console.error('[ItemManager] 加载商品配置失败:', error);
            this.showMessage('加载商品配置失败: ' + error.message, 'error');
            this.showEmptyState();
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 刷新商品列表
     */
    async refreshItemList() {
        console.log('[ItemManager] 刷新商品列表');
        await this.loadItems();
        this.showMessage('商品列表已刷新', 'success');
    }

    // ==================== 界面渲染方法 ====================

    /**
     * 更新商店信息显示
     */
    updateShopInfo() {
        const currentShopName = document.getElementById('currentShopName');
        if (currentShopName && this.shopInfo) {
            currentShopName.textContent = `${this.shopInfo.shop_name} - 商品管理`;
        }
    }

    /**
     * 渲染商品列表
     */
    renderItemList() {
        const itemList = document.getElementById('itemList');
        const emptyState = document.getElementById('emptyState');
        
        if (!itemList) return;

        if (this.filteredItems.length === 0) {
            itemList.innerHTML = '';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';

        itemList.innerHTML = this.filteredItems.map(item => this.createItemCard(item)).join('');
    }

    /**
     * 创建商品卡片HTML
     * @param {Object} item - 商品配置数据
     * @returns {string} HTML字符串
     */
    createItemCard(item) {
        const statusClass = item.is_active ? 'active' : 'inactive';
        const statusText = item.is_active ? '激活' : '禁用';
        const qualityText = this.getQualityText(item.item_quality);
        const priceText = this.formatPrice(item.price_config);
        
        return `
            <div class="item-card" data-config-id="${item.config_id}">
                <div class="item-card-header">
                    <div class="item-info">
                        <h3 class="item-template-id">模板ID: ${this.escapeHtml(item.item_template_id)}</h3>
                        <div class="item-config-id">${this.escapeHtml(item.config_id)}</div>
                    </div>
                    <div class="item-status">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                        ${qualityText ? `<span class="quality-badge quality-${item.item_quality}">${qualityText}</span>` : ''}
                    </div>
                </div>
                
                <div class="item-details">
                    <div class="item-detail-row">
                        <span class="item-detail-label">数量:</span>
                        <span class="item-detail-value">${item.item_quantity}</span>
                    </div>
                    ${item.slot_id ? `
                    <div class="item-detail-row">
                        <span class="item-detail-label">槽位:</span>
                        <span class="item-detail-value">${item.slot_id}</span>
                    </div>
                    ` : ''}
                    <div class="item-detail-row">
                        <span class="item-detail-label">权重:</span>
                        <span class="item-detail-value">${item.refresh_weight}</span>
                    </div>
                    <div class="item-detail-row">
                        <span class="item-detail-label">概率:</span>
                        <span class="item-detail-value">${(item.refresh_probability * 100).toFixed(1)}%</span>
                    </div>
                </div>
                
                <div class="item-price">
                    <div class="item-price-label">价格</div>
                    <div class="item-price-value">${priceText}</div>
                </div>
                
                <div class="item-actions">
                    <button class="btn btn-sm btn-secondary" onclick="itemManager.editItem('${item.config_id}')">
                        <i class="icon-edit"></i>
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="itemManager.deleteItem('${item.config_id}')">
                        <i class="icon-delete"></i>
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const itemList = document.getElementById('itemList');
        const emptyState = document.getElementById('emptyState');
        
        if (itemList) itemList.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
    }

    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.isLoading = loading;
        const loadingState = document.getElementById('loadingState');
        const itemList = document.getElementById('itemList');
        
        if (loadingState) {
            loadingState.style.display = loading ? 'flex' : 'none';
        }
        
        if (itemList && loading) {
            itemList.innerHTML = '';
        }
    }

    /**
     * 更新商品数量显示
     */
    updateItemCount() {
        const itemCount = document.getElementById('itemCount');
        if (itemCount) {
            itemCount.textContent = this.filteredItems.length;
        }
    }

    // ==================== 商品操作方法 ====================

    /**
     * 打开新增商品表单
     */
    openItemForm() {
        this.currentItem = null;
        this.resetForm();
        
        const modal = document.getElementById('itemModal');
        const modalTitle = document.getElementById('modalTitle');
        
        if (modalTitle) modalTitle.textContent = '新增商品';
        if (modal) {
            modal.classList.add('show');
            modal.style.display = 'flex';
        }
        
        // 聚焦到第一个输入框
        setTimeout(() => {
            const firstInput = document.getElementById('itemTemplateId');
            if (firstInput) firstInput.focus();
        }, 100);
        
        console.log('[ItemManager] 打开新增商品表单');
    }

    /**
     * 编辑商品配置
     * @param {string} configId - 配置ID
     */
    async editItem(configId) {
        try {
            console.log('[ItemManager] 开始编辑商品:', configId);

            const item = this.items.find(i => i.config_id === configId);
            if (!item) {
                throw new Error('商品配置不存在');
            }

            console.log('[ItemManager] 找到商品配置:', item);
            this.currentItem = item;
            console.log('[ItemManager] 设置currentItem:', this.currentItem);

            this.fillForm(item);

            const modal = document.getElementById('itemModal');
            const modalTitle = document.getElementById('modalTitle');

            if (modalTitle) modalTitle.textContent = '编辑商品';
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }

            console.log('[ItemManager] 编辑商品表单已打开:', configId);

        } catch (error) {
            console.error('[ItemManager] 编辑商品失败:', error);
            this.showMessage('编辑商品失败: ' + error.message, 'error');
        }
    }

    /**
     * 删除商品配置
     * @param {string} configId - 配置ID
     */
    deleteItem(configId) {
        const item = this.items.find(i => i.config_id === configId);
        if (!item) {
            this.showMessage('商品配置不存在', 'error');
            return;
        }

        // 显示确认删除对话框
        const deleteModal = document.getElementById('deleteModal');
        const deleteItemName = document.getElementById('deleteItemName');
        
        if (deleteItemName) deleteItemName.textContent = `模板ID: ${item.item_template_id}`;
        if (deleteModal) {
            deleteModal.classList.add('show');
            deleteModal.style.display = 'flex';
            deleteModal.dataset.configId = configId;
        }
        
        console.log('[ItemManager] 准备删除商品配置:', configId);
    }

    /**
     * 确认删除商品配置
     */
    async confirmDelete() {
        const deleteModal = document.getElementById('deleteModal');
        const configId = deleteModal?.dataset.configId;
        
        if (!configId) return;

        try {
            const response = await shopAPI.deleteShopItem(this.shopId, configId);
            
            if (response.success) {
                this.closeDeleteModal();
                await this.loadItems();
                this.showMessage('商品配置删除成功', 'success');
                console.log('[ItemManager] 商品配置删除成功:', configId);
            } else {
                throw new Error(response.message || '删除失败');
            }
            
        } catch (error) {
            console.error('[ItemManager] 删除商品配置失败:', error);
            this.showMessage('删除商品配置失败: ' + error.message, 'error');
        }
    }

    /**
     * 返回商店列表
     */
    goBackToShops() {
        console.log('[ItemManager] 返回商店列表');
        window.location.href = 'index.html';
    }

    // ==================== 工具方法 ====================

    /**
     * 获取品质文本
     * @param {number} quality - 品质等级
     * @returns {string} 品质文本
     */
    getQualityText(quality) {
        const qualityMap = {
            1: '白色',
            2: '绿色',
            3: '蓝色',
            4: '紫色',
            5: '橙色',
            6: '红色'
        };
        return qualityMap[quality] || '';
    }

    /**
     * 格式化价格显示
     * @param {Object} priceConfig - 价格配置
     * @returns {string} 格式化后的价格
     */
    formatPrice(priceConfig) {
        console.log('[ItemManager] 格式化价格:', priceConfig);

        if (!priceConfig) {
            console.log('[ItemManager] 价格配置为空');
            return '未设置';
        }

        // 支持多种价格配置格式
        let currency, amount;

        if (priceConfig.currency_type !== undefined && priceConfig.base_price !== undefined) {
            // 后端格式: {currency_type: "gold", base_price: 100}
            currency = priceConfig.currency_type;
            amount = priceConfig.base_price;
            console.log('[ItemManager] 使用后端格式:', { currency, amount });
        } else if (priceConfig.currency !== undefined && priceConfig.amount !== undefined) {
            // 前端格式: {currency: "gold", amount: 100}
            currency = priceConfig.currency;
            amount = priceConfig.amount;
            console.log('[ItemManager] 使用前端格式:', { currency, amount });
        } else {
            console.error('[ItemManager] 价格配置格式错误:', priceConfig);
            return '格式错误';
        }

        // 确保金额是数字
        const numAmount = parseInt(amount);
        if (isNaN(numAmount)) {
            console.error('[ItemManager] 价格金额无效:', amount);
            return '金额无效';
        }

        const currencyMap = {
            'gold': '金币',
            'diamond': '钻石',
            'arena_coin': '竞技场币',
            'guild_coin': '公会币',
            'honor_point': '荣誉点'
        };

        const currencyText = currencyMap[currency] || currency;
        const result = `${numAmount} ${currencyText}`;
        console.log('[ItemManager] 格式化结果:', result);
        return result;
    }

    /**
     * HTML转义
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        if (!text) return '';
        
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;
        
        container.appendChild(messageEl);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }

    // ==================== 表单处理方法 ====================

    /**
     * 处理表单提交
     * @param {Event} event - 表单提交事件
     */
    async handleFormSubmit(event) {
        event.preventDefault();

        // 防止重复提交
        if (this.isSubmitting) {
            console.log('[ItemManager] 表单正在提交中，忽略重复提交');
            return;
        }

        this.isSubmitting = true;

        try {
            console.log('[ItemManager] 表单提交开始，currentItem:', this.currentItem);

            // 验证currentItem状态
            if (this.currentItem && !this.currentItem.config_id) {
                console.error('[ItemManager] currentItem存在但缺少config_id:', this.currentItem);
                throw new Error('商品配置数据异常，请重新打开编辑表单');
            }

            const formData = this.getFormData();
            this.validateFormData(formData);

            let response;
            let configId = null;
            let isUpdate = false;

            if (this.currentItem && this.currentItem.config_id) {
                // 更新商品配置
                isUpdate = true;
                configId = this.currentItem.config_id;
                console.log('[ItemManager] 准备更新商品配置:', configId);
                response = await shopAPI.updateShopItem(this.shopId, configId, formData);
                console.log('[ItemManager] 更新商品配置响应:', response);
            } else {
                // 创建商品配置
                isUpdate = false;
                console.log('[ItemManager] 准备创建商品配置:', formData.item_template_id);
                response = await shopAPI.createShopItem(this.shopId, formData);
                console.log('[ItemManager] 创建商品配置响应:', response);
            }

            if (response && response.success) {
                const action = isUpdate ? '更新' : '创建';

                this.closeItemForm();
                await this.loadItems();
                this.showMessage(`商品配置${action}成功`, 'success');
            } else {
                throw new Error(response?.message || '操作失败');
            }

        } catch (error) {
            console.error('[ItemManager] 表单提交失败:', error);
            this.showMessage('操作失败: ' + error.message, 'error');
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
        const form = document.getElementById('itemForm');
        const formData = new FormData(form);

        // 解析价格配置
        const priceConfigRaw = this.parseJSONField(formData.get('price_config'));
        const priceConfig = this.normalizePriceConfig(priceConfigRaw);

        // 安全解析数值，避免无穷大或NaN
        const refreshProbability = parseFloat(formData.get('refresh_probability')) || 1.0;
        const safeRefreshProbability = this.sanitizeFloat(refreshProbability, 1.0, 0.0, 1.0);

        const data = {
            item_template_id: formData.get('item_template_id')?.trim(),
            item_quantity: parseInt(formData.get('item_quantity')) || 1,
            item_quality: formData.get('item_quality') ? parseInt(formData.get('item_quality')) : null,
            slot_id: formData.get('slot_id') ? parseInt(formData.get('slot_id')) : null,
            price_config: priceConfig,
            purchase_limit: this.parseJSONField(formData.get('purchase_limit')),
            availability: this.parseJSONField(formData.get('availability')),
            refresh_weight: parseInt(formData.get('refresh_weight')) || 100,
            refresh_probability: safeRefreshProbability,
            sort_order: parseInt(formData.get('sort_order')) || 0,
            is_active: formData.has('is_active'),
            display_config: this.parseJSONField(formData.get('display_config'))
        };

        return data;
    }

    /**
     * 标准化价格配置格式（发送到后端）
     * @param {Object} priceConfig - 原始价格配置
     * @returns {Object} 标准化后的价格配置
     */
    normalizePriceConfig(priceConfig) {
        console.log('[ItemManager] 标准化价格配置:', priceConfig);

        if (!priceConfig) {
            console.log('[ItemManager] 价格配置为空');
            return null;
        }

        let result;

        // 支持多种输入格式，统一转换为后端期望的格式
        if (priceConfig.currency !== undefined && priceConfig.amount !== undefined) {
            // 前端格式: {currency: "gold", amount: 100}
            const amount = parseInt(priceConfig.amount);
            if (isNaN(amount)) {
                console.error('[ItemManager] 价格金额无效:', priceConfig.amount);
                return null;
            }
            result = {
                currency_type: priceConfig.currency,
                base_price: amount
            };
            console.log('[ItemManager] 前端格式转换结果:', result);
        } else if (priceConfig.currency_type !== undefined && priceConfig.base_price !== undefined) {
            // 后端格式: {currency_type: "gold", base_price: 100}
            const basePrice = parseInt(priceConfig.base_price);
            if (isNaN(basePrice)) {
                console.error('[ItemManager] 基础价格无效:', priceConfig.base_price);
                return null;
            }
            result = {
                currency_type: priceConfig.currency_type,
                base_price: basePrice
            };
            console.log('[ItemManager] 后端格式保持原样:', result);
        } else {
            // 尝试自动推断
            console.warn('[ItemManager] 价格配置格式不标准，尝试自动推断:', priceConfig);
            const currency = priceConfig.currency_type || priceConfig.currency || "gold";
            const amount = parseInt(priceConfig.base_price || priceConfig.amount || 0);

            if (isNaN(amount)) {
                console.error('[ItemManager] 无法解析价格金额');
                return null;
            }

            result = {
                currency_type: currency,
                base_price: amount
            };
            console.log('[ItemManager] 自动推断结果:', result);
        }

        return result;
    }

    /**
     * 转换价格配置为显示格式（从后端到前端）
     * @param {Object} priceConfig - 后端价格配置
     * @returns {Object} 前端显示格式
     */
    convertPriceConfigForDisplay(priceConfig) {
        console.log('[ItemManager] 转换价格配置为显示格式:', priceConfig);

        if (!priceConfig) {
            console.log('[ItemManager] 价格配置为空，返回默认值');
            return { currency: "gold", amount: 0 };
        }

        let result;

        // 将后端格式转换为前端友好的格式
        if (priceConfig.currency_type !== undefined && priceConfig.base_price !== undefined) {
            // 后端格式: {currency_type: "gold", base_price: 111}
            result = {
                currency: priceConfig.currency_type,
                amount: parseInt(priceConfig.base_price) || 0
            };
            console.log('[ItemManager] 检测到后端格式，转换结果:', result);
        } else if (priceConfig.currency !== undefined && priceConfig.amount !== undefined) {
            // 前端格式: {currency: "gold", amount: 111}
            result = {
                currency: priceConfig.currency,
                amount: parseInt(priceConfig.amount) || 0
            };
            console.log('[ItemManager] 检测到前端格式，保持原样:', result);
        } else {
            // 兼容其他格式或缺失字段
            const currency = priceConfig.currency_type || priceConfig.currency || "gold";
            const amount = parseInt(priceConfig.base_price || priceConfig.amount || 0);
            result = { currency, amount };
            console.log('[ItemManager] 使用兼容模式，结果:', result);
        }

        return result;
    }

    /**
     * 清理浮点数值，确保不包含无穷大或NaN
     * @param {number} value - 原始值
     * @param {number} defaultValue - 默认值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 清理后的值
     */
    sanitizeFloat(value, defaultValue = 0, min = null, max = null) {
        // 检查是否为有效数字
        if (typeof value !== 'number' || !isFinite(value) || isNaN(value)) {
            return defaultValue;
        }

        // 应用范围限制
        if (min !== null && value < min) {
            return min;
        }
        if (max !== null && value > max) {
            return max;
        }

        return value;
    }

    /**
     * 验证表单数据
     * @param {Object} data - 表单数据
     */
    validateFormData(data) {
        if (!data.item_template_id) {
            throw new Error('道具模板ID不能为空');
        }

        if (!data.item_quantity || data.item_quantity <= 0) {
            throw new Error('道具数量必须大于0');
        }

        if (!data.price_config || !data.price_config.currency_type || data.price_config.base_price === undefined) {
            throw new Error('价格配置不能为空，需要包含货币类型和基础价格');
        }

        if (data.price_config.base_price <= 0) {
            throw new Error('基础价格必须大于0');
        }

        if (!data.availability) {
            throw new Error('可用性配置不能为空');
        }

        // 验证道具模板ID格式
        if (!/^\d+$/.test(data.item_template_id)) {
            throw new Error('道具模板ID必须是数字');
        }

        // 验证品质范围
        if (data.item_quality !== null && (data.item_quality < 1 || data.item_quality > 6)) {
            throw new Error('道具品质必须在1-6之间');
        }

        // 验证概率范围
        if (data.refresh_probability < 0 || data.refresh_probability > 1) {
            throw new Error('出现概率必须在0-1之间');
        }
    }

    /**
     * 解析JSON字段
     * @param {string} value - JSON字符串
     * @returns {Object|null} 解析后的对象
     */
    parseJSONField(value) {
        if (!value || !value.trim()) return {};

        try {
            return JSON.parse(value.trim());
        } catch (error) {
            throw new Error('JSON格式错误: ' + value);
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        const form = document.getElementById('itemForm');
        if (form) {
            form.reset();

            // 设置默认值
            const defaultValues = {
                'itemQuantity': '1',
                'refreshWeight': '100',
                'refreshProbability': '1.0',
                'sortOrder': '0',
                'priceConfig': '{"currency": "gold", "amount": 100}',
                'availability': '{"start_time": null, "end_time": null, "conditions": {}}'
            };

            Object.entries(defaultValues).forEach(([fieldId, value]) => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = value;
                }
            });

            const isActiveInput = document.getElementById('isActive');
            if (isActiveInput) isActiveInput.checked = true;
        }
    }

    /**
     * 填充表单数据
     * @param {Object} item - 商品配置数据
     */
    fillForm(item) {
        const fields = [
            'itemTemplateId', 'itemQuantity', 'itemQuality', 'slotId',
            'refreshWeight', 'refreshProbability', 'sortOrder'
        ];

        const mapping = {
            'itemTemplateId': 'item_template_id',
            'itemQuantity': 'item_quantity',
            'itemQuality': 'item_quality',
            'slotId': 'slot_id',
            'refreshWeight': 'refresh_weight',
            'refreshProbability': 'refresh_probability',
            'sortOrder': 'sort_order'
        };

        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            const dataKey = mapping[fieldId];

            if (element && item[dataKey] !== undefined && item[dataKey] !== null) {
                element.value = item[dataKey];
            }
        });

        // 处理复选框
        const isActiveInput = document.getElementById('isActive');
        if (isActiveInput) {
            isActiveInput.checked = item.is_active;
        }

        // 处理JSON字段
        const jsonFields = [
            { id: 'purchaseLimit', key: 'purchase_limit' },
            { id: 'availability', key: 'availability' },
            { id: 'displayConfig', key: 'display_config' }
        ];

        jsonFields.forEach(({ id, key }) => {
            const element = document.getElementById(id);
            if (element && item[key]) {
                element.value = JSON.stringify(item[key], null, 2);
            }
        });

        // 特殊处理价格配置，转换为前端友好的格式
        const priceConfigElement = document.getElementById('priceConfig');
        if (priceConfigElement && item.price_config) {
            const priceConfig = this.convertPriceConfigForDisplay(item.price_config);
            priceConfigElement.value = JSON.stringify(priceConfig, null, 2);
        }
    }

    /**
     * 关闭商品表单
     */
    closeItemForm() {
        console.log('[ItemManager] 关闭商品表单，当前currentItem:', this.currentItem);

        const modal = document.getElementById('itemModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
        }
        this.resetForm();
        this.currentItem = null;

        console.log('[ItemManager] 商品表单已关闭，currentItem已重置');
    }

    /**
     * 关闭删除确认对话框
     */
    closeDeleteModal() {
        const modal = document.getElementById('deleteModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
            delete modal.dataset.configId;
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        this.closeItemForm();
        this.closeDeleteModal();
    }

    // ==================== 筛选和搜索方法 ====================

    /**
     * 筛选商品
     */
    filterItems() {
        const statusFilter = document.getElementById('statusFilter')?.value;
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim();

        this.filteredItems = this.items.filter(item => {
            // 状态筛选
            if (statusFilter !== '') {
                const isActive = statusFilter === 'true';
                if (item.is_active !== isActive) {
                    return false;
                }
            }

            // 搜索筛选
            if (searchTerm) {
                const searchFields = [
                    item.item_template_id,
                    item.config_id
                ].filter(Boolean);

                const matchesSearch = searchFields.some(field =>
                    field.toString().toLowerCase().includes(searchTerm)
                );

                if (!matchesSearch) {
                    return false;
                }
            }

            return true;
        });

        this.renderItemList();
        this.updateItemCount();

        console.log(`[ItemManager] 筛选结果: ${this.filteredItems.length}/${this.items.length}`);
    }

    /**
     * 搜索商品
     */
    searchItems() {
        // 防抖处理
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.filterItems();
        }, 300);
    }

    /**
     * 排序商品
     */
    sortItems() {
        const sortBy = document.getElementById('sortBy')?.value || 'sort_order';

        this.filteredItems.sort((a, b) => {
            switch (sortBy) {
                case 'item_template_id':
                    return a.item_template_id.localeCompare(b.item_template_id);
                case 'created_at':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'sort_order':
                default:
                    return (a.sort_order || 0) - (b.sort_order || 0);
            }
        });

        this.renderItemList();
        console.log(`[ItemManager] 按 ${sortBy} 排序`);
    }
}

// ==================== 全局函数 ====================

/**
 * 打开商品表单 - 全局函数
 */
function openItemForm() {
    if (window.itemManager) {
        window.itemManager.openItemForm();
    }
}

/**
 * 关闭商品表单 - 全局函数
 */
function closeItemForm() {
    if (window.itemManager) {
        window.itemManager.closeItemForm();
    }
}

/**
 * 保存商品 - 全局函数
 * @param {Event} event - 表单提交事件
 */
function saveItem(event) {
    console.log('[Global] saveItem 被调用，event:', event);

    if (window.itemManager) {
        // 确保事件被正确处理
        event.preventDefault();
        window.itemManager.handleFormSubmit(event);
    } else {
        console.error('[Global] itemManager 未初始化');
    }
}

/**
 * 关闭删除确认对话框 - 全局函数
 */
function closeDeleteModal() {
    if (window.itemManager) {
        window.itemManager.closeDeleteModal();
    }
}

/**
 * 确认删除 - 全局函数
 */
function confirmDelete() {
    if (window.itemManager) {
        window.itemManager.confirmDelete();
    }
}

/**
 * 刷新商品列表 - 全局函数
 */
function refreshItemList() {
    if (window.itemManager) {
        window.itemManager.refreshItemList();
    }
}

/**
 * 筛选商品 - 全局函数
 */
function filterItems() {
    if (window.itemManager) {
        window.itemManager.filterItems();
    }
}

/**
 * 搜索商品 - 全局函数
 */
function searchItems() {
    if (window.itemManager) {
        window.itemManager.searchItems();
    }
}

/**
 * 排序商品 - 全局函数
 */
function sortItems() {
    if (window.itemManager) {
        window.itemManager.sortItems();
    }
}

/**
 * 返回商店列表 - 全局函数
 */
function goBackToShops() {
    if (window.itemManager) {
        window.itemManager.goBackToShops();
    }
}

// 创建全局实例
window.itemManager = new ItemManager();
