import traceback
import time
import json
import logging
from typing import Dict, List, Optional, Union, Tuple
from enum import Enum
from datetime import datetime, timed<PERSON><PERSON>
from game_database import DatabaseManager
from service_locator import ServiceLocator
from distributed_lock import DistributedLock
from logger_config import setup_logger
from config import config

# Initialize logger
logger = setup_logger(__name__)

class CooldownType(str, Enum):
    """Cooldown type enum"""
    PERSONAL = "personal"  # Personal cooldown
    GUILD = "guild"        # Guild cooldown
    GLOBAL = "global"      # Global cooldown


class MonsterCooldownManager:
    """Monster cooldown management system"""
    
    def __init__(self,db_manager: DatabaseManager):
        """Initialize the monster cooldown manager"""
        # Get database and redis from service locator
        self.db_manager = db_manager
        self.redis = self.db_manager.redis_client if self.db_manager else None
        
        # Redis key prefixes
        self.key_prefix = "game:v2:monster_cooldown"
        self.personal_key = f"{self.key_prefix}:personal:{{username}}:{{monster_id}}"
        self.guild_key = f"{self.key_prefix}:guild:{{guild_id}}:{{monster_id}}"
        self.global_key = f"{self.key_prefix}:global:{{monster_id}}"
        
        # Redis hash fields
        self.field_cooldown_end = "cooldown_end"
        self.field_killed_by = "killed_by"
        self.field_killed_at = "killed_at"
        self.field_cooldown_time = "cooldown_time"
        self.field_guild_id = "guild_id"
        
        # Lock prefix for distributed operations
        self.lock_prefix = f"{self.key_prefix}:lock"
        
        # 通知追踪集合，用于防止重复通知
        self.notified_key = f"{self.key_prefix}:notified:{{monster_id}}:{{cooldown_type}}"
        
        # 用于管理后台任务的变量
        self._running = True
        self._background_tasks = set()
        
        logger.info("Monster cooldown manager initialized")
    
    async def set_cooldown(self, 
                         monster_id: str, 
                         username: str, 
                         cooldown_type: CooldownType,
                         cooldown_time: int = None,
                         guild_id: str = None) -> bool:
        """
        Set cooldown for a monster
        
        Args:
            monster_id: Monster ID
            username: Username of the player who killed the monster
            cooldown_type: Type of cooldown (personal, guild, global)
            cooldown_time: Cooldown time in seconds (if None, will use config)
            guild_id: Guild ID (required for guild cooldown)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.redis:
                logger.error("Redis is not available")
                return False
            
            logger.info(f"设置怪物 {monster_id} 冷却 - 参数: username={username}, cooldown_type={cooldown_type}, cooldown_time={cooldown_time}, guild_id={guild_id}")
            
            # If cooldown_time is not provided, get from config
            if cooldown_time is None:
                cooldown_time = config.get_monster_cooldown(monster_id, cooldown_type)
                logger.info(f"从配置获取冷却时间: {cooldown_time}")
                if cooldown_time is None:
                    logger.error(f"No cooldown time configured for monster {monster_id}, type {cooldown_type}")
                    return False
            
            now = int(time.time())
            cooldown_end = now + cooldown_time
            logger.info(f"冷却开始时间: {now}, 冷却结束时间: {cooldown_end}, 冷却时长: {cooldown_time}秒")
            
            # Prepare cooldown data
            cooldown_data = {
                self.field_cooldown_end: cooldown_end,
                self.field_killed_by: username,
                self.field_killed_at: now,
                self.field_cooldown_time: cooldown_time
            }
            logger.info(f"冷却数据: {cooldown_data}")
            
            # Add guild_id for guild cooldown
            if cooldown_type == CooldownType.GUILD and guild_id:
                cooldown_data[self.field_guild_id] = guild_id
                logger.info(f"添加公会ID到冷却数据: {guild_id}")
            
            # Set cooldown based on type
            if cooldown_type == CooldownType.PERSONAL:
                key = self.personal_key.format(username=username, monster_id=monster_id)
                logger.info(f"设置个人冷却键: {key}")
            elif cooldown_type == CooldownType.GUILD:
                if not guild_id:
                    logger.error(f"Guild ID is required for guild cooldown: monster_id={monster_id}, username={username}")
                    return False
                key = self.guild_key.format(guild_id=guild_id, monster_id=monster_id)
                logger.info(f"设置公会冷却键: {key}")
            elif cooldown_type == CooldownType.GLOBAL:
                key = self.global_key.format(monster_id=monster_id)
                logger.info(f"设置全局冷却键: {key}")
            else:
                logger.error(f"Invalid cooldown type: {cooldown_type}")
                return False
            
            # Use Redis HSET to store cooldown data
            await self.redis.hset(key, mapping=cooldown_data)
            logger.info(f"已设置Redis哈希: {key}")
            
            # Set expiration for the key
            await self.redis.expire(key, cooldown_time * 2)  # Set expiry to twice the cooldown time for safety
            logger.info(f"已设置键过期时间: {cooldown_time * 2}秒")
            
            # 清除通知标记，以便在冷却结束后可以重新通知
            notified_key = self.notified_key.format(monster_id=monster_id, cooldown_type=cooldown_type)
            await self.redis.delete(notified_key)
            logger.info(f"已清除通知标记: {notified_key}")
            
            # Get monster name from config
            monster_name = monster_id
            monster_info = config.get_monster_by_id(monster_id)
            if monster_info:
                monster_name = monster_info.get("name", monster_id)
            
            logger.info(f"Set {cooldown_type} cooldown for monster {monster_name} ({monster_id}), user {username}, expires in {cooldown_time}s")
            return True
            
        except Exception as e:
            logger.error(f"Error setting cooldown: {str(traceback.format_exc())}")
            return False
    
    async def check_cooldown(self, 
                           monster_id: str, 
                           username: str = None, 
                           guild_id: str = None) -> Dict[str, Union[bool, int, str]]:
        """
        Check if a monster is on cooldown
        
        Args:
            monster_id: Monster ID
            username: Username of the player (for personal cooldown)
            guild_id: Guild ID (for guild cooldown)
            
        Returns:
            Dict with cooldown status:
            {
                "on_cooldown": bool,
                "remaining_time": int,  # seconds remaining
                "cooldown_type": str,   # which cooldown type is active
                "killed_by": str,       # who killed the monster
                "killed_at": int        # when the monster was killed (timestamp)
            }
        """
        try:
            if not self.redis:
                logger.error("Redis is not available")
                return {
                    "on_cooldown": False,
                    "remaining_time": 0,
                    "cooldown_type": None,
                    "killed_by": None,
                    "killed_at": None,
                    "error": "Redis is not available"
                }
            
            now = int(time.time())
            logger.info(f"检查怪物 {monster_id} 冷却状态 - 参数: username={username}, guild_id={guild_id}, 当前时间={now}")
            
            result = {
                "on_cooldown": False,
                "remaining_time": 0,
                "cooldown_type": None,
                "killed_by": None,
                "killed_at": None,
                "monster_id": monster_id
            }
            
            # Get monster info from config
            monster_info = config.get_monster_by_id(monster_id)
            if monster_info:
                result["monster_name"] = monster_info.get("name", monster_id)
                result["monster_level"] = monster_info.get("level", 1)
                result["monster_type"] = monster_info.get("type", "normal")
            
            # Check personal cooldown if username provided
            if username:
                personal_key = self.personal_key.format(username=username, monster_id=monster_id)
                logger.info(f"检查个人冷却键: {personal_key}")
                personal_data = await self.redis.hgetall(personal_key)
                logger.info(f"个人冷却数据: {personal_data}")
                
                if personal_data and self.field_cooldown_end in personal_data:
                    # 确保将字节字符串转换为整数
                    cooldown_end_value = personal_data[self.field_cooldown_end]
                    if isinstance(cooldown_end_value, bytes):
                        cooldown_end = int(cooldown_end_value.decode('utf-8'))
                    else:
                        cooldown_end = int(cooldown_end_value)
                    
                    logger.info(f"个人冷却结束时间: {cooldown_end}, 当前时间: {now}, 剩余时间: {cooldown_end - now}秒")
                    if cooldown_end > now:
                        result["on_cooldown"] = True
                        result["remaining_time"] = cooldown_end - now
                        result["cooldown_type"] = CooldownType.PERSONAL
                        
                        # 处理killed_by字段
                        killed_by = personal_data.get(self.field_killed_by)
                        if isinstance(killed_by, bytes):
                            result["killed_by"] = killed_by.decode('utf-8')
                        else:
                            result["killed_by"] = killed_by
                        
                        # 处理killed_at字段
                        killed_at = personal_data.get(self.field_killed_at, 0)
                        if isinstance(killed_at, bytes):
                            result["killed_at"] = int(killed_at.decode('utf-8'))
                        else:
                            result["killed_at"] = int(killed_at)
                        
                        logger.info(f"怪物 {monster_id} 处于个人冷却中，剩余时间: {result['remaining_time']}秒")
                        return result
                    else:
                        logger.info(f"怪物 {monster_id} 的个人冷却已过期")
            
            # Check guild cooldown if guild_id provided
            if guild_id:
                guild_key = self.guild_key.format(guild_id=guild_id, monster_id=monster_id)
                logger.info(f"检查公会冷却键: {guild_key}")
                guild_data = await self.redis.hgetall(guild_key)
                logger.info(f"公会冷却数据: {guild_data}")
                
                if guild_data and self.field_cooldown_end in guild_data:
                    # 确保将字节字符串转换为整数
                    cooldown_end_value = guild_data[self.field_cooldown_end]
                    if isinstance(cooldown_end_value, bytes):
                        cooldown_end = int(cooldown_end_value.decode('utf-8'))
                    else:
                        cooldown_end = int(cooldown_end_value)
                    
                    logger.info(f"公会冷却结束时间: {cooldown_end}, 当前时间: {now}, 剩余时间: {cooldown_end - now}秒")
                    if cooldown_end > now:
                        result["on_cooldown"] = True
                        result["remaining_time"] = cooldown_end - now
                        result["cooldown_type"] = CooldownType.GUILD
                        
                        # 处理killed_by字段
                        killed_by = guild_data.get(self.field_killed_by)
                        if isinstance(killed_by, bytes):
                            result["killed_by"] = killed_by.decode('utf-8')
                        else:
                            result["killed_by"] = killed_by
                        
                        # 处理killed_at字段
                        killed_at = guild_data.get(self.field_killed_at, 0)
                        if isinstance(killed_at, bytes):
                            result["killed_at"] = int(killed_at.decode('utf-8'))
                        else:
                            result["killed_at"] = int(killed_at)
                        
                        logger.info(f"怪物 {monster_id} 处于公会冷却中，剩余时间: {result['remaining_time']}秒")
                        return result
                    else:
                        logger.info(f"怪物 {monster_id} 的公会冷却已过期")
            
            # Always check global cooldown
            global_key = self.global_key.format(monster_id=monster_id)
            logger.info(f"检查全局冷却键: {global_key}")
            global_data = await self.redis.hgetall(global_key)
            logger.info(f"全局冷却数据: {global_data}")
            
            if global_data and self.field_cooldown_end in global_data:
                # 确保将字节字符串转换为整数
                cooldown_end_value = global_data[self.field_cooldown_end]
                if isinstance(cooldown_end_value, bytes):
                    cooldown_end = int(cooldown_end_value.decode('utf-8'))
                else:
                    cooldown_end = int(cooldown_end_value)
                
                logger.info(f"全局冷却结束时间: {cooldown_end}, 当前时间: {now}, 剩余时间: {cooldown_end - now}秒")
                if cooldown_end > now:
                    result["on_cooldown"] = True
                    result["remaining_time"] = cooldown_end - now
                    result["cooldown_type"] = CooldownType.GLOBAL
                    
                    # 处理killed_by字段
                    killed_by = global_data.get(self.field_killed_by)
                    if isinstance(killed_by, bytes):
                        result["killed_by"] = killed_by.decode('utf-8')
                    else:
                        result["killed_by"] = killed_by
                    
                    # 处理killed_at字段
                    killed_at = global_data.get(self.field_killed_at, 0)
                    if isinstance(killed_at, bytes):
                        result["killed_at"] = int(killed_at.decode('utf-8'))
                    else:
                        result["killed_at"] = int(killed_at)
                    
                    logger.info(f"怪物 {monster_id} 处于全局冷却中，剩余时间: {result['remaining_time']}秒")
                else:
                    logger.info(f"怪物 {monster_id} 的全局冷却已过期")
            
            logger.info(f"怪物 {monster_id} 没有处于任何冷却状态")
            return result
            
        except Exception as e:
            logger.error(f"Error checking cooldown: {str(traceback.format_exc())}")
            return {
                "on_cooldown": False,
                "remaining_time": 0,
                "cooldown_type": None,
                "killed_by": None,
                "killed_at": None,
                "error": str(e)
            }
    
    async def reset_cooldown(self, 
                           monster_id: str, 
                           cooldown_type: CooldownType,
                           username: str = None,
                           guild_id: str = None) -> bool:
        """
        Reset cooldown for a monster
        
        Args:
            monster_id: Monster ID
            cooldown_type: Type of cooldown to reset
            username: Username (for personal cooldown)
            guild_id: Guild ID (for guild cooldown)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.redis:
                logger.error("Redis is not available")
                return False
                
            if cooldown_type == CooldownType.PERSONAL:
                if not username:
                    logger.error("Username is required for personal cooldown reset")
                    return False
                key = self.personal_key.format(username=username, monster_id=monster_id)
            elif cooldown_type == CooldownType.GUILD:
                if not guild_id:
                    logger.error("Guild ID is required for guild cooldown reset")
                    return False
                key = self.guild_key.format(guild_id=guild_id, monster_id=monster_id)
            elif cooldown_type == CooldownType.GLOBAL:
                key = self.global_key.format(monster_id=monster_id)
            else:
                logger.error(f"Invalid cooldown type: {cooldown_type}")
                return False
            
            # Delete the key to reset cooldown
            await self.redis.delete(key)
            logger.info(f"Reset {cooldown_type} cooldown for monster {monster_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error resetting cooldown: {str(e)}")
            return False
    
    async def get_all_cooldowns(self, 
                              username: str = None, 
                              guild_id: str = None,
                              monster_ids: List[str] = None) -> Dict[str, Dict]:
        """
        Get all active cooldowns for a user, guild, or specific monsters
        
        Args:
            username: Username to get personal cooldowns
            guild_id: Guild ID to get guild cooldowns
            monster_ids: List of monster IDs to check (optional)
            
        Returns:
            Dict mapping monster_id to cooldown info
        """
        try:
            if not self.redis:
                logger.error("Redis is not available")
                return {}
                
            now = int(time.time())
            result = {}
            
            # 记录输入参数
            logger.info(f"get_all_cooldowns - 参数: username={username}, guild_id={guild_id}, monster_ids={monster_ids}")
            
            # If specific monster IDs provided, check them individually
            if monster_ids:
                logger.info(f"检查指定怪物ID: {monster_ids}")
                for monster_id in monster_ids:
                    cooldown = await self.check_cooldown(monster_id, username, guild_id)
                    logger.info(f"怪物 {monster_id} 冷却状态: {cooldown}")
                    if cooldown["on_cooldown"]:
                        result[monster_id] = cooldown
                    else:
                        logger.info(f"怪物 {monster_id} 没有处于冷却状态")
                return result
            
            # Otherwise, scan Redis for all cooldowns
            # This is more efficient than checking each monster individually
            
            # Check personal cooldowns
            if username:
                pattern = self.personal_key.format(username=username, monster_id="*")
                logger.info(f"扫描个人冷却键: {pattern}")
                personal_keys = await self._scan_keys(pattern)
                logger.info(f"找到个人冷却键: {personal_keys}")
                
                for key in personal_keys:
                    monster_id = key.split(":")[-1]
                    data = await self.redis.hgetall(key)
                    logger.info(f"怪物 {monster_id} 个人冷却数据: {data}")
                    
                    if data and self.field_cooldown_end in data:
                        # 确保将字节字符串转换为整数
                        cooldown_end_value = data[self.field_cooldown_end]
                        if isinstance(cooldown_end_value, bytes):
                            cooldown_end = int(cooldown_end_value.decode('utf-8'))
                        else:
                            cooldown_end = int(cooldown_end_value)
                            
                        logger.info(f"怪物 {monster_id} 冷却结束时间: {cooldown_end}, 当前时间: {now}")
                        if cooldown_end > now:
                            # Get monster info from config
                            monster_info = config.get_monster_by_id(monster_id)
                            monster_name = monster_id
                            monster_level = 1
                            monster_type = "normal"
                            if monster_info:
                                monster_name = monster_info.get("name", monster_id)
                                monster_level = monster_info.get("level", 1)
                                monster_type = monster_info.get("type", "normal")
                                
                            # 处理killed_by字段
                            killed_by = data.get(self.field_killed_by)
                            if isinstance(killed_by, bytes):
                                killed_by = killed_by.decode('utf-8')
                                
                            # 处理killed_at字段
                            killed_at = data.get(self.field_killed_at, 0)
                            if isinstance(killed_at, bytes):
                                killed_at = int(killed_at.decode('utf-8'))
                            else:
                                killed_at = int(killed_at)
                                
                            result[monster_id] = {
                                "on_cooldown": True,
                                "remaining_time": cooldown_end - now,
                                "cooldown_type": CooldownType.PERSONAL,
                                "killed_by": killed_by,
                                "killed_at": killed_at,
                                "monster_id": monster_id,
                                "monster_name": monster_name,
                                "monster_level": monster_level,
                                "monster_type": monster_type
                            }
                            logger.info(f"添加怪物 {monster_id} 的个人冷却信息到结果")
                        else:
                            logger.info(f"怪物 {monster_id} 的个人冷却已过期")
            
            # Check guild cooldowns
            if guild_id:
                pattern = self.guild_key.format(guild_id=guild_id, monster_id="*")
                logger.info(f"扫描公会冷却键: {pattern}")
                guild_keys = await self._scan_keys(pattern)
                logger.info(f"找到公会冷却键: {guild_keys}")
                
                for key in guild_keys:
                    monster_id = key.split(":")[-1]
                    data = await self.redis.hgetall(key)
                    logger.info(f"怪物 {monster_id} 公会冷却数据: {data}")
                    
                    if data and self.field_cooldown_end in data:
                        # 确保将字节字符串转换为整数
                        cooldown_end_value = data[self.field_cooldown_end]
                        if isinstance(cooldown_end_value, bytes):
                            cooldown_end = int(cooldown_end_value.decode('utf-8'))
                        else:
                            cooldown_end = int(cooldown_end_value)
                            
                        logger.info(f"怪物 {monster_id} 冷却结束时间: {cooldown_end}, 当前时间: {now}")
                        if cooldown_end > now:
                            # Only add if not already added or has higher priority
                            if monster_id not in result:
                                # Get monster info from config
                                monster_info = config.get_monster_by_id(monster_id)
                                monster_name = monster_id
                                monster_level = 1
                                monster_type = "normal"
                                if monster_info:
                                    monster_name = monster_info.get("name", monster_id)
                                    monster_level = monster_info.get("level", 1)
                                    monster_type = monster_info.get("type", "normal")
                                    
                                # 处理killed_by字段
                                killed_by = data.get(self.field_killed_by)
                                if isinstance(killed_by, bytes):
                                    killed_by = killed_by.decode('utf-8')
                                    
                                # 处理killed_at字段
                                killed_at = data.get(self.field_killed_at, 0)
                                if isinstance(killed_at, bytes):
                                    killed_at = int(killed_at.decode('utf-8'))
                                else:
                                    killed_at = int(killed_at)
                                    
                                result[monster_id] = {
                                    "on_cooldown": True,
                                    "remaining_time": cooldown_end - now,
                                    "cooldown_type": CooldownType.GUILD,
                                    "killed_by": killed_by,
                                    "killed_at": killed_at,
                                    "monster_id": monster_id,
                                    "monster_name": monster_name,
                                    "monster_level": monster_level,
                                    "monster_type": monster_type
                                }
                                logger.info(f"添加怪物 {monster_id} 的公会冷却信息到结果")
                            else:
                                logger.info(f"怪物 {monster_id} 已经有更高优先级的冷却信息")
                        else:
                            logger.info(f"怪物 {monster_id} 的公会冷却已过期")
            
            # Check global cooldowns
            pattern = self.global_key.format(monster_id="*")
            logger.info(f"扫描全局冷却键: {pattern}")
            global_keys = await self._scan_keys(pattern)
            logger.info(f"找到全局冷却键: {global_keys}")
            
            for key in global_keys:
                monster_id = key.split(":")[-1]
                data = await self.redis.hgetall(key)
                logger.info(f"怪物 {monster_id} 全局冷却数据: {data}")
                
                if data and self.field_cooldown_end in data:
                    # 确保将字节字符串转换为整数
                    cooldown_end_value = data[self.field_cooldown_end]
                    if isinstance(cooldown_end_value, bytes):
                        cooldown_end = int(cooldown_end_value.decode('utf-8'))
                    else:
                        cooldown_end = int(cooldown_end_value)
                        
                    logger.info(f"怪物 {monster_id} 冷却结束时间: {cooldown_end}, 当前时间: {now}")
                    if cooldown_end > now:
                        # Only add if not already added or has higher priority
                        if monster_id not in result:
                            # Get monster info from config
                            monster_info = config.get_monster_by_id(monster_id)
                            monster_name = monster_id
                            monster_level = 1
                            monster_type = "normal"
                            if monster_info:
                                monster_name = monster_info.get("name", monster_id)
                                monster_level = monster_info.get("level", 1)
                                monster_type = monster_info.get("type", "normal")
                                
                            # 处理killed_by字段
                            killed_by = data.get(self.field_killed_by)
                            if isinstance(killed_by, bytes):
                                killed_by = killed_by.decode('utf-8')
                                
                            # 处理killed_at字段
                            killed_at = data.get(self.field_killed_at, 0)
                            if isinstance(killed_at, bytes):
                                killed_at = int(killed_at.decode('utf-8'))
                            else:
                                killed_at = int(killed_at)
                                
                            result[monster_id] = {
                                "on_cooldown": True,
                                "remaining_time": cooldown_end - now,
                                "cooldown_type": CooldownType.GLOBAL,
                                "killed_by": killed_by,
                                "killed_at": killed_at,
                                "monster_id": monster_id,
                                "monster_name": monster_name,
                                "monster_level": monster_level,
                                "monster_type": monster_type
                            }
                            logger.info(f"添加怪物 {monster_id} 的全局冷却信息到结果")
                        else:
                            logger.info(f"怪物 {monster_id} 已经有更高优先级的冷却信息")
                    else:
                        logger.info(f"怪物 {monster_id} 的全局冷却已过期")
            
            logger.info(f"最终返回的冷却结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting all cooldowns: {str(traceback.format_exc())}")
            return {}
    
    async def _scan_keys(self, pattern: str) -> List[str]:
        """Helper method to scan Redis keys matching a pattern"""
        if not self.redis:
            return []
            
        keys = []
        cursor = 0
        while True:
            cursor, partial_keys = await self.redis.scan(cursor, match=pattern, count=100)
            keys.extend(partial_keys)
            if cursor == 0:
                break
        return keys
    
    async def persist_to_mongodb(self) -> bool:
        """
        Persist all active cooldowns to MongoDB for backup/recovery
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.redis or not self.db_manager:
                logger.error("Redis or DB manager is not available")
                return False
                
            # Get all cooldown keys
            all_keys = []
            for pattern in [
                self.personal_key.format(username="*", monster_id="*"),
                self.guild_key.format(guild_id="*", monster_id="*"),
                self.global_key.format(monster_id="*")
            ]:
                keys = await self._scan_keys(pattern)
                all_keys.extend(keys)
            
            # No cooldowns to persist
            if not all_keys:
                logger.info("No cooldowns to persist")
                return True
            
            # Prepare bulk data
            cooldown_docs = []
            
            for key in all_keys:
                data = await self.redis.hgetall(key)
                if not data:
                    continue
                
                # Parse key to get type and IDs
                parts = key.split(":")
                cooldown_type = parts[3]  # personal, guild, or global
                
                doc = {
                    "key": key,
                    "cooldown_type": cooldown_type,
                    "monster_id": parts[-1],
                    "cooldown_end": int(data.get(self.field_cooldown_end, 0)),
                    "killed_by": data.get(self.field_killed_by),
                    "killed_at": int(data.get(self.field_killed_at, 0)),
                    "cooldown_time": int(data.get(self.field_cooldown_time, 0)),
                    "persisted_at": int(time.time())
                }
                
                # Add username or guild_id based on type
                if cooldown_type == CooldownType.PERSONAL:
                    doc["username"] = parts[4]
                elif cooldown_type == CooldownType.GUILD:
                    doc["guild_id"] = parts[4]
                
                cooldown_docs.append(doc)
            
            # Bulk insert/update to MongoDB
            if cooldown_docs:
                # Use bulk operations for efficiency
                bulk_ops = []
                for doc in cooldown_docs:
                    bulk_ops.append({
                        "update_one": {
                            "filter": {"key": doc["key"]},
                            "update": {"$set": doc},
                            "upsert": True
                        }
                    })
                
                if bulk_ops:
                    await self.db_manager.db.monster_cooldowns.bulk_write(bulk_ops)
                    logger.info(f"Persisted {len(cooldown_docs)} cooldowns to MongoDB")
            
            return True
            
        except Exception as e:
            logger.error(f"Error persisting cooldowns: {str(traceback.format_exc())}")
            return False
    
    async def restore_from_mongodb(self) -> bool:
        """
        Restore cooldowns from MongoDB to Redis
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.redis or not self.db_manager:
                logger.error("Redis or DB manager is not available")
                return False
                
            now = int(time.time())
            
            # Get all active cooldowns from MongoDB
            cursor = self.db_manager.db.monster_cooldowns.find({
                "cooldown_end": {"$gt": now}  # Only restore active cooldowns
            })
            
            count = 0
            async for doc in cursor:
                key = doc["key"]
                
                # Prepare cooldown data
                cooldown_data = {
                    self.field_cooldown_end: doc["cooldown_end"],
                    self.field_killed_by: doc["killed_by"],
                    self.field_killed_at: doc["killed_at"],
                    self.field_cooldown_time: doc["cooldown_time"]
                }
                
                # Add guild_id for guild cooldown
                if doc["cooldown_type"] == CooldownType.GUILD and "guild_id" in doc:
                    cooldown_data[self.field_guild_id] = doc["guild_id"]
                
                # Calculate remaining time
                remaining_time = doc["cooldown_end"] - now
                if remaining_time <= 0:
                    continue
                
                # Set cooldown in Redis
                await self.redis.hset(key, mapping=cooldown_data)
                await self.redis.expire(key, remaining_time * 2)  # Set expiry to twice the remaining time
                count += 1
            
            logger.info(f"Restored {count} cooldowns from MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring cooldowns: {str(e)}")
            return False
    
    async def calculate_dynamic_cooldown(self, 
                                       base_cooldown: int,
                                       username: str,
                                       monster_id: str,
                                       modifiers: Dict[str, float] = None) -> int:
        """
        Calculate dynamic cooldown time based on various modifiers
        
        Args:
            base_cooldown: Base cooldown time in seconds
            username: Username of the player
            monster_id: Monster ID
            modifiers: Dict of modifier names and values (multipliers)
            
        Returns:
            int: Calculated cooldown time in seconds
        """
        try:
            # Start with base cooldown
            cooldown = base_cooldown
            
            # Apply modifiers if provided
            if modifiers:
                for modifier, value in modifiers.items():
                    cooldown *= value
            
            # Get user data for VIP level, buffs, etc.
            if self.db_manager:
                user = await self.db_manager.get_user_by_username(username)
                if user:
                    # Apply VIP reduction if applicable
                    vip_level = user.profile.get("vip", {}).get("level", 0) if user.profile else 0
                    if vip_level > 0:
                        # Example: 5% reduction per VIP level
                        vip_reduction = 1.0 - (vip_level * 0.05)
                        cooldown *= max(0.5, vip_reduction)  # Cap at 50% reduction
                    
                    # Check for active buffs that affect cooldown
                    buffs = user.profile.get("buffs", []) if user.profile else []
                    for buff in buffs:
                        if buff.get("type") == "cooldown_reduction" and buff.get("active", False):
                            cooldown_reduction = buff.get("value", 1.0)
                            cooldown *= cooldown_reduction
            
            # Ensure minimum cooldown (1 second)
            return max(1, int(cooldown))
            
        except Exception as e:
            logger.error(f"Error calculating dynamic cooldown: {str(e)}")
            return base_cooldown  # Return base cooldown on error
    
    async def get_monster_drops(self, monster_id: str, username: str = None) -> List[Dict]:
        """
        Get monster drops based on configuration and randomization
        
        Args:
            monster_id: Monster ID
            username: Username of the player (optional, for personalized drops)
            
        Returns:
            List of drop items with quantities
        """
        try:
            # Get monster drops from config
            drops_config = config.get_monster_drops(monster_id)
            if not drops_config:
                return []
            
            import random
            
            # Calculate actual drops based on chance and quantity
            actual_drops = []
            for drop in drops_config:
                # Check if item drops based on chance
                if random.random() <= drop.get("chance", 0):
                    # Calculate quantity
                    quantity = 1
                    quantity_config = drop.get("quantity")
                    if isinstance(quantity_config, dict):
                        # Range quantity
                        min_qty = quantity_config.get("min", 1)
                        max_qty = quantity_config.get("max", min_qty)
                        quantity = random.randint(min_qty, max_qty)
                    elif isinstance(quantity_config, int):
                        # Fixed quantity
                        quantity = quantity_config
                    
                    # Add to drops
                    actual_drops.append({
                        "item_id": drop.get("item_id"),
                        "name": drop.get("name"),
                        "quantity": quantity
                    })
            
            return actual_drops
            
        except Exception as e:
            logger.error(f"Error getting monster drops: {str(e)}")
            return [] 

    async def notify_expired_cooldowns(self) -> int:
        """
        检查并通知刚刚过期的冷却（怪物刚刚刷新）
        
        Returns:
            int: 通知的冷却数量
        """
        try:
            if not self.redis or not self.db_manager:
                logger.error("Redis or DB manager is not available")
                return 0
                
            now = int(time.time())
            connection_manager = ServiceLocator.get("conn_manager")
            if not connection_manager:
                logger.error("Connection manager is not available")
                return 0
            
            from models import MessageModel
            from enums import MessageId
            
            # 获取所有冷却键
            all_keys = []
            for pattern in [
                self.personal_key.format(username="*", monster_id="*"),
                self.guild_key.format(guild_id="*", monster_id="*"),
                self.global_key.format(monster_id="*")
            ]:
                keys = await self._scan_keys(pattern)
                all_keys.extend(keys)
            
            # 没有冷却需要检查
            if not all_keys:
                return 0
            
            notify_count = 0
            
            for key in all_keys:
                data = await self.redis.hgetall(key)
                if not data or self.field_cooldown_end not in data:
                    continue
                
                cooldown_end = int(data[self.field_cooldown_end])
                
                # 检查冷却是否已过期
                if cooldown_end <= now:
                    # 解析键以获取类型和ID
                    parts = key.split(":")
                    cooldown_type_str = parts[3]  # personal, guild, 或 global
                    monster_id = parts[-1]
                    
                    try:
                        cooldown_type = CooldownType(cooldown_type_str)
                    except ValueError:
                        logger.error(f"无效的冷却类型: {cooldown_type_str}")
                        continue
                    
                    # 检查是否已经通知过
                    notified_key = self.notified_key.format(monster_id=monster_id, cooldown_type=cooldown_type_str)
                    if await self.redis.exists(notified_key):
                        # 已经通知过，跳过
                        continue
                    
                    # 获取怪物信息
                    monster_info = config.get_monster_by_id(monster_id)
                    monster_name = monster_id
                    if monster_info:
                        monster_name = monster_info.get("name", monster_id)
                    
                    # 准备通知数据
                    notification_data = {
                        "monster_id": monster_id,
                        "monster_name": monster_name,
                        "cooldown_type": cooldown_type_str,
                        "respawn_time": cooldown_end,
                        "message": f"怪物 {monster_name} 已经重生"
                    }
                    
                    if monster_info:
                        notification_data["monster_level"] = monster_info.get("level", 1)
                        notification_data["monster_type"] = monster_info.get("type", "normal")
                        notification_data["monster_area"] = monster_info.get("area", "unknown")
                    
                    # 根据冷却类型发送通知
                    if cooldown_type == CooldownType.GLOBAL:
                        # 全局广播
                        await connection_manager.broadcast(
                            MessageModel(
                                msgId=MessageId.MONSTER_RESPAWNED,
                                data=notification_data
                            ).model_dump()
                        )
                        logger.info(f"全服通知: 怪物 {monster_name} ({monster_id}) 已重生")
                    
                    elif cooldown_type == CooldownType.GUILD:
                        # 获取公会ID
                        guild_id = data.get(self.field_guild_id)
                        if guild_id:
                            # 获取公会成员
                            guild = await self.db_manager.db.guilds.find_one({"guild_id": guild_id})
                            if guild and "members" in guild:
                                # 向公会成员广播
                                await connection_manager.broadcast_to_users(
                                    MessageModel(
                                        msgId=MessageId.MONSTER_RESPAWNED,
                                        data=notification_data
                                    ).model_dump(),
                                    guild["members"]
                                )
                                logger.info(f"公会通知: 怪物 {monster_name} ({monster_id}) 已重生，公会 {guild_id}")
                    
                    elif cooldown_type == CooldownType.PERSONAL:
                        # 获取用户名
                        username = parts[4]
                        # 向特定用户发送消息
                        await connection_manager.send_personal_message_to_user(
                            MessageModel(
                                msgId=MessageId.MONSTER_RESPAWNED,
                                data=notification_data
                            ).model_dump(),
                            username
                        )
                        logger.info(f"个人通知: 怪物 {monster_name} ({monster_id}) 已重生，用户 {username}")
                    
                    # 标记为已通知，永久保存直到怪物被再次击杀
                    # 这确保只有在怪物被击杀并重新刷新时才会再次通知
                    await self.redis.set(notified_key, "1")
                    
                    notify_count += 1
            
            return notify_count
            
        except Exception as e:
            logger.error(f"通知过期冷却时出错: {str(e)}")
            return 0 

    async def shutdown(self) -> bool:
        """
        关闭怪物冷却管理器，清理资源
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("正在关闭怪物冷却管理器...")
            
            # 标记为不再运行，以便后台任务可以退出
            self._running = False
            
            # 等待所有后台任务完成
            if self._background_tasks:
                logger.info(f"等待 {len(self._background_tasks)} 个后台任务完成...")
                for task in self._background_tasks:
                    if not task.done():
                        try:
                            # 取消任务
                            task.cancel()
                            try:
                                await asyncio.wait_for(task, timeout=2.0)
                            except asyncio.TimeoutError:
                                logger.warning("任务取消超时")
                            except asyncio.CancelledError:
                                pass
                        except Exception as e:
                            logger.error(f"取消任务时出错: {str(e)}")
            
            # 持久化当前的冷却数据到MongoDB
            try:
                persist_success = await self.persist_to_mongodb()
                if persist_success:
                    logger.info("冷却数据已成功持久化到MongoDB")
                else:
                    logger.warning("冷却数据持久化失败")
            except Exception as e:
                logger.error(f"持久化冷却数据时出错: {str(e)}")
            
            logger.info("怪物冷却管理器已关闭")
            return True
            
        except Exception as e:
            logger.error(f"关闭怪物冷却管理器时出错: {str(e)}")
            return False
    
    def start_background_task(self, coro):
        """
        启动后台任务并跟踪它
        
        Args:
            coro: 协程函数
        """
        task = asyncio.create_task(coro)
        self._background_tasks.add(task)
        task.add_done_callback(self._background_tasks.discard) 