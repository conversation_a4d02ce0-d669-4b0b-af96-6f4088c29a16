#coding:utf-8
import uvicorn
import os
import argparse
import logging
import traceback
from logger_config import setup_logger
from config import config

# 初始化日志系统
logger = setup_logger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Game Server')
    parser.add_argument('--host', type=str, help='服务器主机地址')
    parser.add_argument('--port', type=int, help='服务器端口')
    parser.add_argument('--workers', type=int, help='工作进程数量')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    return parser.parse_args()

if __name__ == "__main__":
    # 获取配置
    server_config = config.get_server_config()
    
    # 记录启动信息
    logger.info(f"启动游戏服务器: host={server_config['host']}, port={server_config['port']}, workers={server_config['workers']}, debug={server_config['debug']}")
    
    # 启动服务器
    uvicorn.run(
        "game_server:app",
        host=server_config["host"],
        port=int(server_config["port"]),
        workers=int(server_config["workers"]),  # 始终使用配置的worker数量
        reload=True,  # 禁用热重载
        reload_includes=["*.py"],  # 只监控 Python 文件
        reload_excludes=["*.log", "*.txt", "*.json", "*.tmp", "logs/*"],  # 排除常见无关文件
        log_level=config.get_log_config()["level"].lower()
    )