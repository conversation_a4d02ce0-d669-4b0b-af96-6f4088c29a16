import asyncio
import time
import uuid
from typing import Optional

class DistributedLock:
    def __init__(self, redis_client, key: str, ttl: int = 30):
        self.redis = redis_client
        self.key = key
        self.ttl = ttl  # 锁超时时间（秒）
        self.lock_value = str(uuid.uuid4())

    async def acquire(self, blocking: bool = True, timeout: int = 10) -> bool:
        """
        获取分布式锁。
        :param blocking: 是否阻塞等待
        :param timeout: 最大等待时间（秒）
        :return: 是否获取成功
        """
        start = time.time()
        while True:
            # SETNX + EXPIRE 原子操作
            result = await self.redis.set(self.key, self.lock_value, ex=self.ttl, nx=True)
            if result:
                return True
            if not blocking or (time.time() - start) > timeout:
                return False
            await asyncio.sleep(0.1)

    async def release(self):
        """
        释放分布式锁（仅持有者可释放）。
        """
        # Lua脚本保证原子性
        lua = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """
        await self.redis.eval(lua, 1, self.key, self.lock_value)

    async def __aenter__(self):
        acquired = await self.acquire()
        if not acquired:
            raise TimeoutError(f"Failed to acquire lock: {self.key}")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.release() 