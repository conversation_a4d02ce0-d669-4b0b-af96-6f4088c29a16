# 角色系统使用指南

## 概述

本角色系统是基于现有项目架构设计的分布式角色管理系统，支持多Worker环境，具有以下特性：

- **分布式架构**：支持多Worker环境，使用Redis进行分布式缓存和锁管理
- **服务器权威**：所有角色数据由服务器管理，确保数据一致性
- **高性能缓存**：使用Redis缓存提升读取性能
- **并发安全**：使用分布式锁确保并发操作的安全性
- **完整功能**：支持角色的创建、删除、升级、装备、技能等完整功能

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    分布式角色服务架构                        │
├─────────────────────────────────────────────────────────────┤
│  WebSocket Handlers  │  HTTP API  │  Internal Services      │
├─────────────────────────────────────────────────────────────┤
│              分布式角色服务 (DistributedRoleService)         │
├───────────────────────────────┬─────────────────────────────┤
│        角色缓存管理器          │        角色数据模型          │
│      (RoleCacheManager)       │        (Role Models)        │
├───────────────────────────────┼─────────────────────────────┤
│         Redis 缓存            │       MongoDB 数据库         │
└───────────────────────────────┴─────────────────────────────┘
```

## 核心组件

### 1. 角色数据模型 (`role_models.py`)

定义了完整的角色数据结构：

- **RoleProperties**: 角色属性（力量、敏捷、智力、体质、精神、幸运等）
- **RoleSkill**: 角色技能
- **RoleSoldier**: 角色士兵
- **RoleEquipment**: 角色装备
- **Role**: 主角色模型

### 2. 分布式角色服务 (`role_service_distributed.py`)

提供角色管理的核心服务：

- 角色CRUD操作
- 角色升级和经验管理
- 装备管理
- 状态管理
- 分布式锁管理

### 3. 角色缓存管理器 (`game_database.py`)

基于现有缓存系统架构的角色缓存管理：

- 角色列表缓存
- 角色详情缓存
- 角色属性计算结果缓存
- 角色战斗力缓存

### 4. 角色消息处理器 (`role_handlers_distributed.py`)

处理WebSocket消息的角色相关操作：

- 获取角色列表
- 创建角色
- 删除角色
- 角色升级
- 装备管理
- 状态管理

## 消息ID定义

角色系统使用以下消息ID：

```python
# 角色相关消息ID
GET_ROLES = 20           # 获取角色列表
CREATE_ROLE = 21         # 创建角色
DELETE_ROLE = 22         # 删除角色
LEVEL_UP_ROLE = 23       # 角色升级
STAR_UP_ROLE = 24        # 角色升星
ADD_EXP_ROLE = 25        # 角色加经验
EQUIP_WEAPON = 26        # 装备武器
UNEQUIP_WEAPON = 27      # 卸下武器
EQUIP_ARMOR = 28         # 装备防具
UNEQUIP_ARMOR = 29       # 卸下防具
LEARN_SKILL = 30         # 学习技能
EQUIP_SKILL = 31         # 装备技能
UNEQUIP_SKILL = 32       # 卸下技能
CHANGE_ROLE_STATE = 33   # 改变角色状态
LOCK_ROLE = 34           # 锁定角色
UNLOCK_ROLE = 35         # 解锁角色
```

## 角色状态枚举

```python
class RoleState(str, Enum):
    IDLE = "idle"                   # 空闲
    RETINUE = "retinue"             # 上阵
    MANOR_TASK = "manor_task"       # 任务
    MANOR_STATION = "manor_station" # 驻守
    MANOR_BATTLE = "manor_battle"   # 出征
    MANOR_REC = "manor_rec"         # 募兵
    MANOR_TRAIN = "manor_train"     # 训练
    MANOR_TECH = "manor_tech"       # 研究中
    MANOR_LEARN = "manor_learn"     # 进修
    MANOR_LEVELUP = "manor_levelup" # 升级建筑
    MANOR_MASTER = "manor_master"   # 军团长
    MANOR_TRIM = "manor_trim"       # 整备
```

## 使用示例

### 1. 获取角色列表

**客户端请求：**
```json
{
    "msgId": 20,
    "data": {}
}
```

**服务器响应：**
```json
{
    "msgId": 20,
    "success": true,
    "data": {
        "roles": [
            {
                "role_id": "uuid-1",
                "owner": "player_001",
                "name": "关羽",
                "level": 10,
                "exp": 500,
                "exp_max": 1000,
                "star": 3,
                "state": "idle",
                "fight_power": 1500,
                "properties": {...},
                "equipment": {...},
                "skills": [...],
                "created_at": "2024-01-01T00:00:00"
            }
        ],
        "count": 1
    }
}
```

### 2. 创建角色

**客户端请求：**
```json
{
    "msgId": 21,
    "data": {
        "name": "张飞",
        "avatar": "zhangfei"
    }
}
```

**服务器响应：**
```json
{
    "msgId": 21,
    "success": true,
    "data": {
        "role_id": "uuid-2",
        "owner": "player_001",
        "name": "张飞",
        "level": 1,
        "exp": 0,
        "exp_max": 100,
        "star": 1,
        "state": "idle",
        "fight_power": 100,
        "properties": {...},
        "equipment": {...},
        "skills": [],
        "created_at": "2024-01-01T00:00:00"
    },
    "message": "创建角色成功"
}
```

### 3. 角色升级

**客户端请求：**
```json
{
    "msgId": 23,
    "data": {
        "role_id": "uuid-1"
    }
}
```

**服务器响应：**
```json
{
    "msgId": 23,
    "success": true,
    "data": {
        "role": {...},
        "exp_result": {
            "leveled_up": true,
            "new_level": 11,
            "exp_gained": 0,
            "current_exp": 400,
            "exp_max": 1100
        }
    },
    "message": "角色升级成功"
}
```

### 4. 装备武器

**客户端请求：**
```json
{
    "msgId": 26,
    "data": {
        "role_id": "uuid-1",
        "weapon_id": "weapon_001",
        "weapon_name": "青龙偃月刀",
        "weapon_level": 5,
        "weapon_star": 3
    }
}
```

**服务器响应：**
```json
{
    "msgId": 26,
    "success": true,
    "data": {
        "role_id": "uuid-1",
        "equipment": {
            "weapon_id": "weapon_001",
            "weapon_name": "青龙偃月刀",
            "weapon_level": 5,
            "weapon_star": 3
        },
        "fight_power": 1800
    },
    "message": "装备武器成功"
}
```

### 5. 改变角色状态

**客户端请求：**
```json
{
    "msgId": 33,
    "data": {
        "role_id": "uuid-1",
        "state": "retinue"
    }
}
```

**服务器响应：**
```json
{
    "msgId": 33,
    "success": true,
    "data": {
        "role_id": "uuid-1",
        "state": "retinue"
    },
    "message": "改变角色状态成功"
}
```

## 缓存策略

### 缓存键设计

```python
# 用户角色列表
"game:v2:roles:{owner}"

# 角色详情
"game:v2:roles:{owner}:{role_id}"

# 角色属性计算结果
"game:v2:roles:{owner}:{role_id}:properties"

# 角色战斗力
"game:v2:roles:{owner}:{role_id}:fight_power"
```

### 缓存过期时间

- **角色列表**: 1小时
- **角色详情**: 24小时
- **角色属性**: 10分钟
- **角色战斗力**: 10分钟

### 缓存失效策略

- 角色数据更新时，自动使相关缓存失效
- 使用分布式锁确保缓存一致性
- 支持批量缓存操作

## 分布式锁

角色系统使用分布式锁确保并发安全：

```python
# 角色操作锁
"role_lock:{owner}:{role_id}"

# 用户角色列表锁
"role_lock:{owner}"
```

锁配置：
- 超时时间：30秒
- 重试延迟：0.1秒
- 最大重试次数：10次

## 数据库设计

### MongoDB集合：roles

```javascript
{
    "_id": ObjectId,
    "role_id": "uuid-string",
    "owner": "player_001",
    "name": "关羽",
    "avatar": "guanyu",
    "level": 10,
    "exp": 500,
    "exp_max": 1000,
    "star": 3,
    "rebirth": 0,
    "state": "idle",
    "is_locked": false,
    "properties": {
        "hp": 1000,
        "bp": 100,
        "pow": 50,
        "dex": 30,
        "intelligence": 20,
        "con": 40,
        "men": 25,
        "luck": 15,
        // 装备加成属性
        "weapon_pow": 20,
        "weapon_dex": 10,
        "weapon_intelligence": 5,
        // 称号加成属性
        "title_pow": 5,
        "title_dex": 3,
        // 套装加成属性
        "set_pow": 10,
        "set_dex": 8
    },
    "equipment": {
        "weapon_id": "weapon_001",
        "weapon_name": "青龙偃月刀",
        "weapon_level": 5,
        "weapon_star": 3,
        "armor_id": "armor_001",
        "armor_name": "青龙战甲",
        "armor_level": 3,
        "armor_star": 2
    },
    "skills": [
        {
            "skill_id": "skill_001",
            "skill_name": "青龙斩",
            "skill_type": "active",
            "skill_level": 3,
            "is_equipped": true,
            "cooldown": 30,
            "description": "对敌人造成大量伤害"
        }
    ],
    "soldiers": [
        {
            "soldier_type": "infantry",
            "soldier_name": "步兵",
            "soldier_level": 5,
            "soldier_count": 100,
            "soldier_max": 200
        }
    ],
    "title": "五虎上将",
    "titles": ["五虎上将", "忠义无双"],
    "current_set": "青龙套装",
    "set_bonus": {
        "pow": 20,
        "dex": 15
    },
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T12:00:00"
}
```

## 性能优化

### 1. 缓存优化

- 使用Redis缓存热点数据
- 合理的缓存过期时间
- 批量缓存操作

### 2. 数据库优化

- 创建索引：`{owner: 1, role_id: 1}`
- 使用upsert操作减少查询
- 批量操作减少网络开销

### 3. 并发优化

- 使用分布式锁避免竞态条件
- 异步操作提升响应速度
- 合理的锁粒度

## 错误处理

### 常见错误码

- **角色不存在**: 角色ID无效或角色已被删除
- **角色数量已达上限**: 玩家角色数量超过100个
- **角色名称已存在**: 同一玩家下角色名称重复
- **角色正在使用中**: 角色状态不允许删除
- **经验不足**: 角色经验不足以升级
- **获取锁失败**: 分布式锁获取失败

### 错误响应格式

```json
{
    "msgId": 21,
    "success": false,
    "error": "角色名称已存在"
}
```

## 部署说明

### 1. 环境要求

- Python 3.8+
- Redis 6.0+
- MongoDB 4.4+
- 支持多Worker部署

### 2. 配置要求

在 `config.py` 中确保以下配置：

```python
# 缓存键配置
"roles_data": "game:v2:roles:{owner}",
"role_data": "game:v2:roles:{owner}:{role_id}",
"role_properties": "game:v2:roles:{owner}:{role_id}:properties",
"role_fight_power": "game:v2:roles:{owner}:{role_id}:fight_power"
```

### 3. 数据库初始化

确保MongoDB中存在 `roles` 集合，并创建必要的索引：

```javascript
db.roles.createIndex({"owner": 1, "role_id": 1}, {unique: true})
db.roles.createIndex({"owner": 1})
```

### 4. 多Worker部署

角色系统完全支持多Worker部署：

- 使用Redis进行分布式缓存
- 使用分布式锁确保并发安全
- 使用Redis Pub/Sub进行缓存失效通知

## 监控和日志

### 日志级别

- **DEBUG**: 详细的调试信息
- **INFO**: 重要的业务操作
- **WARNING**: 警告信息
- **ERROR**: 错误信息

### 关键监控指标

- 角色操作成功率
- 缓存命中率
- 数据库查询性能
- 分布式锁获取时间

## 扩展性

角色系统设计具有良好的扩展性：

### 1. 功能扩展

- 可以轻松添加新的角色属性
- 支持新的装备类型
- 可以扩展技能系统
- 支持新的角色状态

### 2. 性能扩展

- 支持水平扩展（增加Worker）
- 支持缓存集群
- 支持数据库分片

### 3. 业务扩展

- 支持角色交易
- 支持角色租赁
- 支持角色拍卖
- 支持角色公会

## 总结

本角色系统是一个完整的、可扩展的、高性能的分布式角色管理系统，完全适配现有的多Worker架构，提供了丰富的角色管理功能，具有良好的性能和可维护性。 