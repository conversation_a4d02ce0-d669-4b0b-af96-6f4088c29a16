"""
邮件系统分布式服务
处理邮件相关的业务逻辑，支持分布式环境
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Set
from mail_models import (
    Mail, MailAttachment, MailType, MailStatus, AttachmentStatus, AttachmentType,
    SendMailRequest, SystemMailRequest, BroadcastMailRequest, MailResponse,
    MailAttachmentData, MailListItem, MailConstants, MailTemplate
)
from mail_database_manager import MailDatabaseManager
from mail_cache_manager import MailCacheManager
from service_locator import ServiceLocator
from enums import MessageId, ItemType
from UserCacheManager import UserCacheManager
from player_session_manager import PlayerSessionManager

logger = logging.getLogger(__name__)


class MailServiceDistributed:
    """邮件系统分布式服务"""
    
    def __init__(self):
        self.db_manager = MailDatabaseManager()
        
        # 邮件配置
        self.config = {
            "max_title_length": MailConstants.MAX_TITLE_LENGTH,
            "max_content_length": MailConstants.MAX_CONTENT_LENGTH,
            "max_attachments": MailConstants.MAX_ATTACHMENTS,
            "default_expire_days": MailConstants.DEFAULT_EXPIRE_DAYS,
            "system_mail_expire_days": MailConstants.SYSTEM_MAIL_EXPIRE_DAYS,
            "default_page_size": MailConstants.DEFAULT_PAGE_SIZE,
            "max_page_size": MailConstants.MAX_PAGE_SIZE
        }
    
    # ==================== 内部辅助方法 ====================

    async def _get_online_users(self) -> Set[str]:
        """获取当前在线用户列表"""
        try:
            session_manager = await PlayerSessionManager.get_instance()
            if session_manager:
                online_users = await session_manager.get_online_players()
                logger.error(f"在线用户数量: {len(online_users)},{online_users}")
                return online_users
            logger.error("会话管理器不可用")
            return set()

        except Exception as e:
            logger.error(f"获取在线用户列表失败: {str(e)}")
            return set()

    async def _get_user_display_name(self, user_id: str) -> str:
        """获取用户显示名称"""
        try:
            user_cache = await UserCacheManager.get_instance()
            user_data = await user_cache.get_user_by_username(user_id)
            if user_data:
                # 优先使用昵称，其次用户名，最后用ID
                return user_data.nickname or user_data.username or user_id
            return user_id
        except Exception as e:
            logger.error(f"获取用户显示名称失败: {str(e)}")
            return user_id

    async def _grant_items_to_inventory(self, player_id: str, attachments: List[MailAttachment]) -> tuple[bool, List[dict]]:
        """发放附件到背包"""
        try:
            user_cache = await UserCacheManager.get_instance()
            granted_items = []

            for attachment in attachments:
                if attachment.claimed:
                    continue

                success = False

                # 根据附件类型调用不同的发放方法
                if attachment.attachment_type == AttachmentType.ITEM:
                    # 发放道具
                    asset_data = {
                        "defid": int(attachment.item_id),
                        "quantity": attachment.quantity,
                        "attributes": attachment.extra_data or {}
                    }
                    result = await user_cache.add_user_asset(
                        player_id,
                        ItemType.ITEM,
                        asset_data,
                        batch=False,
                        notify=True
                    )
                    success = result.get("success", False)

                elif attachment.attachment_type == AttachmentType.EQUIPMENT:
                    # 发放装备
                    asset_data = {
                        "defid": int(attachment.item_id),
                        "level": attachment.quality,
                        "attributes": attachment.extra_data or {}
                    }
                    result = await user_cache.add_user_asset(
                        player_id,
                        ItemType.EQUIPMENT,
                        asset_data,
                        batch=False,
                        notify=True
                    )
                    success = result.get("success", False)

                elif attachment.attachment_type == AttachmentType.CURRENCY:
                    # 发放货币 - 预留接口
                    # TODO: 实现货币发放逻辑
                    logger.warning(f"货币类型附件暂未实现: {attachment.item_id}")
                    success = False

                elif attachment.attachment_type == AttachmentType.GENERAL:
                    # 发放武将 - 预留接口
                    # TODO: 实现武将发放逻辑
                    logger.warning(f"武将类型附件暂未实现: {attachment.item_id}")
                    success = False

                else:
                    logger.error(f"未知的附件类型: {attachment.attachment_type}")
                    success = False

                if success:
                    granted_items.append({
                        "item_id": attachment.item_id,
                        "item_name": attachment.item_name,
                        "quantity": attachment.quantity,
                        "quality": attachment.quality,
                        "type": attachment.attachment_type.value
                    })
                else:
                    logger.error(f"发放附件失败: {attachment.item_id} to {player_id}")

            return len(granted_items) > 0, granted_items

        except Exception as e:
            logger.error(f"发放附件到背包失败: {str(e)}")
            return False, []

    # ==================== 内部通知方法 ====================
    
    async def _send_mail_notification(self, player_id: str, notification_type: str, mail_data: dict = None) -> bool:
        """发送邮件通知给单个玩家"""
        try:
            connection_manager = ServiceLocator.get("conn_manager")
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False
            
            message = {
                "msgId": MessageId.MAIL_NOTIFICATION,
                "success": True,
                "data": {
                    "type": notification_type,
                    "mail_data": mail_data,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            return await connection_manager.send_personal_message_to_user(message, player_id)
            
        except Exception as e:
            logger.error(f"发送邮件通知失败: {player_id}, 错误: {str(e)}")
            return False
    
    # ==================== 邮件基础操作 ====================
    
    async def send_mail(self, sender_id: str, request: SendMailRequest) -> MailResponse:
        """发送邮件"""
        try:
            # 参数验证
            if not request.receiver_id or not request.title or not request.content:
                return MailResponse(
                    success=False,
                    error="收件人、标题和内容不能为空"
                )
            
            if len(request.title) > self.config["max_title_length"]:
                return MailResponse(
                    success=False,
                    error=f"标题长度不能超过{self.config['max_title_length']}字符"
                )
            
            if len(request.content) > self.config["max_content_length"]:
                return MailResponse(
                    success=False,
                    error=f"内容长度不能超过{self.config['max_content_length']}字符"
                )
            
            if request.attachments and len(request.attachments) > self.config["max_attachments"]:
                return MailResponse(
                    success=False,
                    error=f"附件数量不能超过{self.config['max_attachments']}个"
                )
            
            # 检查发送者和接收者是否存在（这里需要根据实际用户系统实现）
            # TODO: 验证用户存在性
            
            cache_manager = await MailCacheManager.get_instance()
            
            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(sender_id, "send_mail"):
                # 创建邮件
                mail_id = self.db_manager.generate_mail_id()
                now = datetime.now()
                expire_at = now + timedelta(days=self.config["default_expire_days"])
                
                # 获取真实用户名
                sender_name = await self._get_user_display_name(sender_id)
                receiver_name = await self._get_user_display_name(request.receiver_id)

                mail = Mail(
                    mail_id=mail_id,
                    sender_id=sender_id,
                    sender_name=sender_name,
                    receiver_id=request.receiver_id,
                    receiver_name=receiver_name,
                    title=request.title,
                    content=request.content,
                    mail_type=MailType.PLAYER,
                    status=MailStatus.UNREAD,
                    has_attachments=bool(request.attachments),
                    attachment_status=AttachmentStatus.UNCLAIMED if request.attachments else AttachmentStatus.NONE,
                    created_at=now,
                    expire_at=expire_at
                )
                
                # 保存邮件到数据库
                if not await self.db_manager.create_mail(mail):
                    return MailResponse(
                        success=False,
                        error="创建邮件失败"
                    )
                
                # 创建附件
                if request.attachments:
                    attachments = []
                    for attachment_data in request.attachments:
                        attachment = MailAttachment(
                            attachment_id=self.db_manager.generate_attachment_id(),
                            mail_id=mail_id,
                            attachment_type=attachment_data.attachment_type,
                            item_id=attachment_data.item_id,
                            item_name=attachment_data.item_name,
                            quantity=attachment_data.quantity,
                            quality=attachment_data.quality,
                            extra_data=attachment_data.extra_data
                        )
                        attachments.append(attachment)
                    
                    if not await self.db_manager.create_attachments(attachments):
                        return MailResponse(
                            success=False,
                            error="创建附件失败"
                        )
                
                # 清除接收者相关缓存
                await cache_manager.invalidate_all_mail_cache(request.receiver_id)
                
                # 推送新邮件通知
                await self._send_mail_notification(
                    request.receiver_id, 
                    "new_mail", 
                    {"mail_id": mail_id, "title": request.title, "sender_name": mail.sender_name}
                )
                
                logger.info(f"邮件发送成功: {sender_id} -> {request.receiver_id}")
                
                return MailResponse(
                    success=True,
                    message="邮件发送成功",
                    data={"mail_id": mail_id}
                )
                
        except Exception as e:
            logger.error(f"发送邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="发送邮件时发生内部错误"
            )
    
    async def send_system_mail(self, receiver_id: str, request: SystemMailRequest) -> MailResponse:
        """发送系统邮件"""
        try:
            # 参数验证
            if not receiver_id or not request.title or not request.content:
                return MailResponse(
                    success=False,
                    error="收件人、标题和内容不能为空"
                )
            
            cache_manager = await MailCacheManager.get_instance()
            
            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock("system", "send_system_mail"):
                # 创建系统邮件
                mail_id = self.db_manager.generate_mail_id()
                now = datetime.now()
                expire_at = now + timedelta(days=request.expire_days)
                
                # 获取接收者真实用户名
                receiver_name = await self._get_user_display_name(receiver_id)

                mail = Mail(
                    mail_id=mail_id,
                    sender_id=MailConstants.SYSTEM_SENDER_ID,
                    sender_name=MailConstants.SYSTEM_SENDER_NAME,
                    receiver_id=receiver_id,
                    receiver_name=receiver_name,
                    title=request.title,
                    content=request.content,
                    mail_type=MailType.SYSTEM,
                    status=MailStatus.UNREAD,
                    has_attachments=bool(request.attachments),
                    attachment_status=AttachmentStatus.UNCLAIMED if request.attachments else AttachmentStatus.NONE,
                    created_at=now,
                    expire_at=expire_at
                )
                
                # 保存邮件到数据库
                if not await self.db_manager.create_mail(mail):
                    return MailResponse(
                        success=False,
                        error="创建系统邮件失败"
                    )
                
                # 创建附件
                if request.attachments:
                    attachments = []
                    for attachment_data in request.attachments:
                        attachment = MailAttachment(
                            attachment_id=self.db_manager.generate_attachment_id(),
                            mail_id=mail_id,
                            attachment_type=attachment_data.attachment_type,
                            item_id=attachment_data.item_id,
                            item_name=attachment_data.item_name,
                            quantity=attachment_data.quantity,
                            quality=attachment_data.quality,
                            extra_data=attachment_data.extra_data
                        )
                        attachments.append(attachment)
                    
                    if not await self.db_manager.create_attachments(attachments):
                        return MailResponse(
                            success=False,
                            error="创建系统邮件附件失败"
                        )
                
                # 清除接收者相关缓存
                await cache_manager.invalidate_all_mail_cache(receiver_id)
                
                # 推送新邮件通知
                await self._send_mail_notification(
                    receiver_id, 
                    "new_system_mail", 
                    {"mail_id": mail_id, "title": request.title}
                )
                
                logger.info(f"系统邮件发送成功: -> {receiver_id}")
                
                return MailResponse(
                    success=True,
                    message="系统邮件发送成功",
                    data={"mail_id": mail_id}
                )
                
        except Exception as e:
            logger.error(f"发送系统邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="发送系统邮件时发生内部错误"
            )

    async def get_mail_list(self, player_id: str, page: int = 1, limit: int = 20) -> MailResponse:
        """获取邮件列表"""
        try:
            # 参数验证
            if page < 1:
                page = 1
            if limit < 1 or limit > self.config["max_page_size"]:
                limit = self.config["default_page_size"]

            cache_manager = await MailCacheManager.get_instance()

            # 先从缓存获取
            mails = await cache_manager.get_cached_mail_list(player_id, page, limit)

            # 缓存未命中，从数据库获取
            if mails is None:
                mails = await self.db_manager.get_player_mails(player_id, page, limit)
                if mails:
                    # 缓存结果
                    await cache_manager.cache_mail_list(player_id, mails, page, limit)

            # 获取未读数量
            unread_count = await self.get_unread_count_internal(player_id)

            return MailResponse(
                success=True,
                data={
                    "mails": [mail.to_dict() for mail in mails],
                    "page": page,
                    "limit": limit,
                    "count": len(mails),
                    "unread_count": unread_count
                }
            )

        except Exception as e:
            logger.error(f"获取邮件列表时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="获取邮件列表时发生内部错误"
            )

    async def get_mail_detail(self, player_id: str, mail_id: str) -> MailResponse:
        """获取邮件详情"""
        try:
            cache_manager = await MailCacheManager.get_instance()

            # 先从缓存获取
            mail = await cache_manager.get_cached_mail_info(mail_id)

            # 缓存未命中，从数据库获取
            if not mail:
                mail = await self.db_manager.get_mail_by_id(mail_id)
                if mail:
                    # 缓存结果
                    await cache_manager.cache_mail_info(mail)

            if not mail:
                return MailResponse(
                    success=False,
                    error="邮件不存在"
                )

            # 验证邮件所有权
            if mail.receiver_id != player_id:
                return MailResponse(
                    success=False,
                    error="无权访问此邮件"
                )

            # 获取附件信息
            attachments = []
            if mail.has_attachments:
                # 先从缓存获取附件
                cached_attachments = await cache_manager.get_cached_mail_attachments(mail_id)
                if cached_attachments is not None:
                    attachments = cached_attachments
                else:
                    # 从数据库获取附件
                    attachments = await self.db_manager.get_mail_attachments(mail_id)
                    if attachments:
                        # 缓存附件
                        await cache_manager.cache_mail_attachments(mail_id, attachments)

            return MailResponse(
                success=True,
                data={
                    "mail": mail.to_dict(),
                    "attachments": [attachment.to_dict() for attachment in attachments]
                }
            )

        except Exception as e:
            logger.error(f"获取邮件详情时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="获取邮件详情时发生内部错误"
            )

    async def mark_mail_read(self, player_id: str, mail_id: str) -> MailResponse:
        """标记邮件为已读"""
        try:
            # 获取邮件信息
            mail = await self.db_manager.get_mail_by_id(mail_id)
            if not mail:
                return MailResponse(
                    success=False,
                    error="邮件不存在"
                )

            # 验证邮件所有权
            if mail.receiver_id != player_id:
                return MailResponse(
                    success=False,
                    error="无权操作此邮件"
                )

            # 如果已经是已读状态，直接返回成功
            if mail.status == MailStatus.READ:
                return MailResponse(
                    success=True,
                    message="邮件已是已读状态"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(player_id, "mark_read"):
                # 更新邮件状态
                if await self.db_manager.update_mail_status(mail_id, MailStatus.READ, datetime.now()):
                    # 清除相关缓存
                    await cache_manager.invalidate_mail_info(mail_id)
                    await cache_manager.invalidate_mail_list(player_id)
                    await cache_manager.invalidate_unread_count(player_id)

                    logger.info(f"邮件标记为已读: {mail_id} by {player_id}")

                    return MailResponse(
                        success=True,
                        message="邮件已标记为已读"
                    )
                else:
                    return MailResponse(
                        success=False,
                        error="标记邮件已读失败"
                    )

        except Exception as e:
            logger.error(f"标记邮件已读时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="标记邮件已读时发生内部错误"
            )

    async def delete_mail(self, player_id: str, mail_id: str) -> MailResponse:
        """删除邮件"""
        try:
            # 获取邮件信息
            mail = await self.db_manager.get_mail_by_id(mail_id)
            if not mail:
                return MailResponse(
                    success=False,
                    error="邮件不存在"
                )

            # 验证邮件所有权
            if mail.receiver_id != player_id:
                return MailResponse(
                    success=False,
                    error="无权操作此邮件"
                )

            # 如果已经是删除状态，直接返回成功
            if mail.status == MailStatus.DELETED:
                return MailResponse(
                    success=True,
                    message="邮件已是删除状态"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(player_id, "delete_mail"):
                # 删除邮件（软删除）
                if await self.db_manager.delete_mails([mail_id]) > 0:
                    # 清除相关缓存
                    await cache_manager.invalidate_mail_and_attachments(mail_id)
                    await cache_manager.invalidate_mail_list(player_id)
                    await cache_manager.invalidate_unread_count(player_id)

                    logger.info(f"邮件删除成功: {mail_id} by {player_id}")

                    return MailResponse(
                        success=True,
                        message="邮件删除成功"
                    )
                else:
                    return MailResponse(
                        success=False,
                        error="删除邮件失败"
                    )

        except Exception as e:
            logger.error(f"删除邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="删除邮件时发生内部错误"
            )

    async def get_unread_count(self, player_id: str) -> MailResponse:
        """获取未读邮件数量"""
        try:
            count = await self.get_unread_count_internal(player_id)

            return MailResponse(
                success=True,
                data={"unread_count": count}
            )

        except Exception as e:
            logger.error(f"获取未读邮件数量时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="获取未读邮件数量时发生内部错误"
            )

    async def get_unread_count_internal(self, player_id: str) -> int:
        """内部方法：获取未读邮件数量"""
        try:
            cache_manager = await MailCacheManager.get_instance()

            # 先从缓存获取
            count = await cache_manager.get_cached_unread_count(player_id)

            # 缓存未命中，从数据库获取
            if count is None:
                count = await self.db_manager.get_unread_count(player_id)
                # 缓存结果
                await cache_manager.cache_unread_count(player_id, count)

            return count

        except Exception as e:
            logger.error(f"获取未读邮件数量时发生错误: {str(e)}")
            return 0

    # ==================== 附件管理 ====================

    async def claim_attachments(self, player_id: str, mail_id: str) -> MailResponse:
        """领取邮件附件"""
        try:
            # 获取邮件信息
            mail = await self.db_manager.get_mail_by_id(mail_id)
            if not mail:
                return MailResponse(
                    success=False,
                    error="邮件不存在"
                )

            # 验证邮件所有权
            if mail.receiver_id != player_id:
                return MailResponse(
                    success=False,
                    error="无权操作此邮件"
                )

            # 检查是否有附件
            if not mail.has_attachments:
                return MailResponse(
                    success=False,
                    error="此邮件没有附件"
                )

            # 检查附件是否已领取
            if mail.attachment_status == AttachmentStatus.CLAIMED:
                return MailResponse(
                    success=False,
                    error="附件已经被领取"
                )

            # 获取附件列表
            attachments = await self.db_manager.get_mail_attachments(mail_id)
            if not attachments:
                return MailResponse(
                    success=False,
                    error="附件不存在"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁防止重复领取
            async with await cache_manager.acquire_mail_lock(player_id, "claim_attachments"):
                # 再次检查附件状态（双重检查）
                current_mail = await self.db_manager.get_mail_by_id(mail_id)
                if current_mail.attachment_status == AttachmentStatus.CLAIMED:
                    return MailResponse(
                        success=False,
                        error="附件已经被领取"
                    )

                # 发放附件到背包
                grant_success, items_granted = await self._grant_items_to_inventory(player_id, attachments)

                if not grant_success:
                    return MailResponse(
                        success=False,
                        error="发放附件到背包失败"
                    )

                # 更新附件状态
                if await self.db_manager.claim_attachments(mail_id):
                    # 更新邮件附件状态
                    await self.db_manager.update_attachment_status(mail_id, AttachmentStatus.CLAIMED)

                    # 清除相关缓存
                    await cache_manager.invalidate_mail_and_attachments(mail_id)
                    await cache_manager.invalidate_mail_list(player_id)

                    # 推送附件领取通知
                    await self._send_mail_notification(
                        player_id,
                        "attachments_claimed",
                        {"mail_id": mail_id, "items": items_granted}
                    )

                    logger.info(f"附件领取成功: {mail_id} by {player_id}")

                    return MailResponse(
                        success=True,
                        message="附件领取成功",
                        data={"items": items_granted}
                    )
                else:
                    return MailResponse(
                        success=False,
                        error="领取附件失败"
                    )

        except Exception as e:
            logger.error(f"领取附件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="领取附件时发生内部错误"
            )

    async def claim_all_attachments(self, player_id: str) -> MailResponse:
        """领取所有邮件的附件"""
        try:
            # 获取有未领取附件的邮件列表
            # 这里简化实现，实际可能需要专门的查询方法
            mails = await self.db_manager.get_player_mails(player_id, 1, 100)

            claimed_count = 0
            failed_count = 0
            all_items = []

            for mail_item in mails:
                # 获取完整邮件信息
                mail = await self.db_manager.get_mail_by_id(mail_item.mail_id)
                if (mail and mail.has_attachments and
                    mail.attachment_status == AttachmentStatus.UNCLAIMED):

                    # 尝试领取附件
                    result = await self.claim_attachments(player_id, mail.mail_id)
                    if result.success:
                        claimed_count += 1
                        if result.data and result.data.get("items"):
                            all_items.extend(result.data["items"])
                    else:
                        failed_count += 1

            if claimed_count > 0:
                return MailResponse(
                    success=True,
                    message=f"成功领取{claimed_count}封邮件的附件",
                    data={
                        "claimed_count": claimed_count,
                        "failed_count": failed_count,
                        "items": all_items
                    }
                )
            else:
                return MailResponse(
                    success=False,
                    error="没有可领取的附件"
                )

        except Exception as e:
            logger.error(f"批量领取附件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="批量领取附件时发生内部错误"
            )

    # ==================== 批量操作 ====================

    async def batch_delete_mails(self, player_id: str, mail_ids: List[str]) -> MailResponse:
        """批量删除邮件"""
        try:
            if not mail_ids:
                return MailResponse(
                    success=False,
                    error="邮件ID列表不能为空"
                )

            # 验证所有邮件的所有权
            valid_mail_ids = []
            for mail_id in mail_ids:
                mail = await self.db_manager.get_mail_by_id(mail_id)
                if mail and mail.receiver_id == player_id and mail.status != MailStatus.DELETED:
                    valid_mail_ids.append(mail_id)

            if not valid_mail_ids:
                return MailResponse(
                    success=False,
                    error="没有可删除的邮件"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(player_id, "batch_delete"):
                # 批量删除邮件
                deleted_count = await self.db_manager.delete_mails(valid_mail_ids)

                if deleted_count > 0:
                    # 清除相关缓存
                    for mail_id in valid_mail_ids:
                        await cache_manager.invalidate_mail_and_attachments(mail_id)
                    await cache_manager.invalidate_mail_list(player_id)
                    await cache_manager.invalidate_unread_count(player_id)

                    logger.info(f"批量删除邮件成功: {deleted_count} 封，by {player_id}")

                    return MailResponse(
                        success=True,
                        message=f"成功删除{deleted_count}封邮件",
                        data={"deleted_count": deleted_count}
                    )
                else:
                    return MailResponse(
                        success=False,
                        error="删除邮件失败"
                    )

        except Exception as e:
            logger.error(f"批量删除邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="批量删除邮件时发生内部错误"
            )

    async def mark_all_read(self, player_id: str) -> MailResponse:
        """标记所有邮件为已读"""
        try:
            # 获取所有未读邮件
            mails = await self.db_manager.get_player_mails(player_id, 1, 1000)  # 假设最多1000封

            unread_mail_ids = []
            for mail_item in mails:
                if mail_item.status == MailStatus.UNREAD:
                    unread_mail_ids.append(mail_item.mail_id)

            if not unread_mail_ids:
                return MailResponse(
                    success=True,
                    message="没有未读邮件"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(player_id, "mark_all_read"):
                # 批量标记为已读
                read_count = 0
                for mail_id in unread_mail_ids:
                    if await self.db_manager.update_mail_status(mail_id, MailStatus.READ, datetime.now()):
                        read_count += 1

                if read_count > 0:
                    # 清除相关缓存
                    await cache_manager.invalidate_mail_list(player_id)
                    await cache_manager.invalidate_unread_count(player_id)

                    logger.info(f"批量标记已读成功: {read_count} 封，by {player_id}")

                    return MailResponse(
                        success=True,
                        message=f"成功标记{read_count}封邮件为已读",
                        data={"read_count": read_count}
                    )
                else:
                    return MailResponse(
                        success=False,
                        error="标记邮件已读失败"
                    )

        except Exception as e:
            logger.error(f"批量标记已读时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="批量标记已读时发生内部错误"
            )

    # ==================== 系统管理 ====================

    async def send_broadcast_mail(self, request: BroadcastMailRequest) -> MailResponse:
        """发送广播邮件（优化版本）"""
        try:
            # 参数验证
            if not request.title or not request.content:
                return MailResponse(
                    success=False,
                    error="标题和内容不能为空"
                )

            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock("system", "broadcast_mail"):
                # 1. 创建邮件模板
                template_id = self.db_manager.generate_template_id()
                now = datetime.now()
                expire_at = now + timedelta(days=request.expire_days)

                template = MailTemplate(
                    template_id=template_id,
                    title=request.title,
                    content=request.content,
                    attachments=request.attachments,
                    created_at=now,
                    expire_at=expire_at,
                    target_type=request.target_type,
                    target_users=request.target_players
                )

                # 2. 保存模板到数据库
                if not await self.db_manager.create_mail_template(template):
                    return MailResponse(
                        success=False,
                        error="创建邮件模板失败"
                    )

                # 3. 获取在线用户并立即创建邮件
                online_users = await self._get_online_users()
                online_processed = 0
                online_failed = 0
                logger.error(f"在线用户数量: {len(online_users)},{online_users}")
                for user_id in online_users:
                    try:
                        # 检查用户是否应该接收此邮件
                        if self._should_user_receive_template(user_id, template):
                            # 为在线用户立即创建邮件
                            mail = await self._create_mail_from_template(user_id, template)
                            if await self.db_manager.create_mail(mail):
                                # 创建附件
                                if template.attachments:
                                    attachments = await self._create_attachments_from_template(mail.mail_id, template)
                                    await self.db_manager.create_attachments(attachments)

                                # 标记用户已处理此模板
                                await self.db_manager.mark_template_processed(user_id, template_id)

                                # 推送通知
                                await self._send_mail_notification(user_id, "new_system_mail", {
                                    "mail_id": mail.mail_id,
                                    "title": template.title
                                })

                                online_processed += 1
                            else:
                                online_failed += 1

                    except Exception as e:
                        logger.error(f"为在线用户 {user_id} 创建邮件失败: {str(e)}")
                        online_failed += 1

                # 4. 批量标记在线用户已处理（防止重复处理）
                if online_users:
                    await self.db_manager.mark_templates_processed_batch(online_users, template_id)

                logger.info(f"广播邮件处理完成: 模板ID {template_id}, 在线用户处理 {online_processed} 个，失败 {online_failed} 个")

                return MailResponse(
                    success=True,
                    message="广播邮件发送完成",
                    data={
                        "template_id": template_id,
                        "online_processed": online_processed,
                        "online_failed": online_failed,
                        "total_online": len(online_users),
                        "offline_users_note": "离线用户将在登录时收到邮件"
                    }
                )

        except Exception as e:
            logger.error(f"发送广播邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="发送广播邮件时发生内部错误"
            )

    def _should_user_receive_template(self, user_id: str, template: MailTemplate) -> bool:
        """检查用户是否应该接收此模板"""
        if template.target_type == "all":
            return True
        elif template.target_type == "specific" and template.target_users:
            return user_id in template.target_users
        return False

    async def _create_mail_from_template(self, user_id: str, template: MailTemplate) -> Mail:
        """从模板创建邮件"""
        mail_id = self.db_manager.generate_mail_id()
        receiver_name = await self._get_user_display_name(user_id)

        return Mail(
            mail_id=mail_id,
            sender_id=MailConstants.SYSTEM_SENDER_ID,
            sender_name=MailConstants.SYSTEM_SENDER_NAME,
            receiver_id=user_id,
            receiver_name=receiver_name,
            title=template.title,
            content=template.content,
            mail_type=MailType.SYSTEM,
            status=MailStatus.UNREAD,
            has_attachments=bool(template.attachments),
            attachment_status=AttachmentStatus.UNCLAIMED if template.attachments else AttachmentStatus.NONE,
            created_at=datetime.now(),  # 使用当前时间作为创建时间
            expire_at=template.expire_at
        )

    async def _create_attachments_from_template(self, mail_id: str, template: MailTemplate) -> List[MailAttachment]:
        """从模板创建附件"""
        attachments = []
        if template.attachments:
            for attachment_data in template.attachments:
                attachment = MailAttachment(
                    attachment_id=self.db_manager.generate_attachment_id(),
                    mail_id=mail_id,
                    attachment_type=attachment_data.attachment_type,
                    item_id=attachment_data.item_id,
                    item_name=attachment_data.item_name,
                    quantity=attachment_data.quantity,
                    quality=attachment_data.quality,
                    extra_data=attachment_data.extra_data
                )
                attachments.append(attachment)
        return attachments

    async def process_login_mail_templates(self, user_id: str) -> int:
        """用户登录时处理邮件模板"""
        try:
            # 获取用户未处理的邮件模板
            templates = await self.db_manager.get_unprocessed_templates(user_id)

            if not templates:
                return 0

            created_count = 0
            cache_manager = await MailCacheManager.get_instance()

            # 使用分布式锁
            async with await cache_manager.acquire_mail_lock(user_id, "process_templates"):
                for template in templates:
                    try:
                        # 创建邮件
                        mail = await self._create_mail_from_template(user_id, template)
                        if await self.db_manager.create_mail(mail):
                            # 创建附件
                            if template.attachments:
                                attachments = await self._create_attachments_from_template(mail.mail_id, template)
                                await self.db_manager.create_attachments(attachments)

                            # 标记模板已处理
                            await self.db_manager.mark_template_processed(user_id, template.template_id)

                            # 清除相关缓存
                            await cache_manager.invalidate_all_mail_cache(user_id)

                            # 推送通知
                            await self._send_mail_notification(user_id, "new_system_mail", {
                                "mail_id": mail.mail_id,
                                "title": template.title
                            })

                            created_count += 1

                    except Exception as e:
                        logger.error(f"为用户 {user_id} 处理模板 {template.template_id} 失败: {str(e)}")
                        continue

            if created_count > 0:
                logger.info(f"为用户 {user_id} 从模板创建了 {created_count} 封邮件")

            return created_count

        except Exception as e:
            logger.error(f"处理用户登录邮件模板时发生错误: {str(e)}")
            return 0

    async def cleanup_expired_mails(self) -> MailResponse:
        """清理过期邮件"""
        try:
            deleted_count = await self.db_manager.cleanup_expired_mails()

            logger.info(f"清理过期邮件完成: {deleted_count} 封")

            return MailResponse(
                success=True,
                message=f"清理过期邮件完成",
                data={"deleted_count": deleted_count}
            )

        except Exception as e:
            logger.error(f"清理过期邮件时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="清理过期邮件时发生内部错误"
            )

    async def cleanup_expired_templates(self) -> MailResponse:
        """清理过期的邮件模板"""
        try:
            deleted_count = await self.db_manager.cleanup_expired_templates()

            logger.info(f"清理过期邮件模板完成: {deleted_count} 个")

            return MailResponse(
                success=True,
                message=f"清理过期邮件模板完成",
                data={"deleted_count": deleted_count}
            )

        except Exception as e:
            logger.error(f"清理过期邮件模板时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="清理过期邮件模板时发生内部错误"
            )

    async def cleanup_old_processed_records(self, days_old: int = 30) -> MailResponse:
        """清理旧的处理记录"""
        try:
            deleted_count = await self.db_manager.cleanup_old_processed_records(days_old)

            logger.info(f"清理旧处理记录完成: {deleted_count} 条")

            return MailResponse(
                success=True,
                message=f"清理旧处理记录完成",
                data={"deleted_count": deleted_count}
            )

        except Exception as e:
            logger.error(f"清理旧处理记录时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="清理旧处理记录时发生内部错误"
            )

    async def get_mail_system_statistics(self) -> MailResponse:
        """获取邮件系统统计信息"""
        try:
            # 获取模板统计
            template_stats = await self.db_manager.get_template_statistics()

            # 获取邮件统计
            # 这里可以添加更多统计信息

            return MailResponse(
                success=True,
                data={
                    "template_statistics": template_stats,
                    "timestamp": datetime.now().isoformat()
                }
            )

        except Exception as e:
            logger.error(f"获取邮件系统统计信息时发生错误: {str(e)}")
            return MailResponse(
                success=False,
                error="获取邮件系统统计信息时发生内部错误"
            )
