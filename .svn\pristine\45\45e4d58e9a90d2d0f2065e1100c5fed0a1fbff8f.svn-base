# 多Worker环境下游戏装备系统的改进方案

## 问题分析

在分析代码后，我发现当前实现在多Worker环境下存在以下关键问题：

### 1. 异步初始化问题
- `InventoryManager`的`__init__`方法使用`loop.run_until_complete(self._load_from_db())`同步执行异步方法
- 这会导致在多Worker环境中可能引发事件循环冲突
- 不符合异步编程最佳实践

### 2. 数据一致性风险
- 在`equip`和`unequip`方法中，先更新内存状态，再更新数据库
- 若数据库操作失败，会导致内存与数据库状态不一致
- 缺乏事务管理机制

### 3. 分布式锁缺失
- 关键操作（如装备强化、升星、镶嵌符文等）没有分布式锁保护
- 多Worker环境下可能产生竞态条件
- 可能导致数据错误或重复操作

### 4. 唯一ID生成机制不可靠
- `Equipment`类的`_generate_item_id`方法使用时间戳+随机数
- 高并发环境下有冲突风险
- 不适合分布式系统

### 5. 缓存一致性问题
- 缓存更新策略不明确
- 多Worker环境下可能读取到过期缓存
- 缺乏缓存失效机制

## 改进方案

### 1. 异步初始化改进
```python
class InventoryManager:
    def __init__(self, player_id):
        self.player_id = player_id
        self.equips = {}
        self.equipped = {}
        # 初始化标志
        self.initialized = False
        self._initialization_task = None

    async def initialize(self):
        if not self.initialized and self._initialization_task is None:
            self._initialization_task = asyncio.create_task(self._load_from_db())
            await self._initialization_task
            self.initialized = True
            self._initialization_task = None
        elif self._initialization_task is not None:
            await self._initialization_task
        return self.initialized
```

### 2. 事务管理实现
```python
async def equip(self, item_id):
    equip_to_wear = self.get_equipment(item_id)
    if not equip_to_wear:
        return False, "装备不存在"

    part = equip_to_wear.attributes.get("part", "unknown")
    db_manager = ServiceLocator.get("db_manager")

    # 使用事务确保一致性
    async with db_manager.transaction() as txn:
        try:
            # 如果该部位已经有装备，先卸下
            if part in self.equipped:
                current_item_id = self.equipped[part]
                await self.unequip(current_item_id, txn=txn)

            # 更新装备状态
            result, msg = equip_to_wear.equip()
            if result:
                self.equipped[part] = item_id
                print(f"Player {self.player_id} equipped {item_id} to {part}.")

                # 更新数据库中的装备状态
                await Item.update(
                    self.player_id, 
                    item_id, 
                    {"attributes": {**equip_to_wear.attributes, "state": EquipState.EQUIPPED.value}},
                    db_manager.db, 
                    db_manager.redis_client,
                    txn=txn
                )
                await txn.commit()
                return True, msg
            else:
                await txn.rollback()
                return False, msg
        except Exception as e:
            await txn.rollback()
            print(f"Error updating equipment state in database: {str(e)}")
            return False, f"装备失败: {str(e)}"
```

### 3. 分布式锁机制
```python
# 在game_database.py中实现分布式锁
class DistributedLock:
    def __init__(self, redis_client, lock_name, expire=10, retry=3, retry_interval=0.5):
        self.redis_client = redis_client
        self.lock_name = f"lock:{lock_name}"
        self.expire = expire
        self.retry = retry
        self.retry_interval = retry_interval
        self.lock_value = None

    async def acquire(self):
        for _ in range(self.retry):
            self.lock_value = str(uuid.uuid4())
            if await self.redis_client.set(self.lock_name, self.lock_value, ex=self.expire, nx=True):
                return True
            await asyncio.sleep(self.retry_interval)
        return False

    async def release(self):
        if self.lock_value:
            script = """
            if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
            else
                return 0
            end
            """
            await self.redis_client.eval(script, 1, self.lock_name, self.lock_value)
            self.lock_value = None

# 使用示例
async def intensify_equipment(self, item_id):
    db_manager = ServiceLocator.get("db_manager")
    lock = DistributedLock(
        db_manager.redis_client,
        f"equipment:intensify:{item_id}"
    )

    if not await lock.acquire():
        return False, "操作过于频繁，请稍后再试"

    try:
        equip = self.get_equipment(item_id)
        if not equip:
            return False, "装备不存在"

        result, msg = equip.intensify()
        return result, msg
    finally:
        await lock.release()
```

### 4. 可靠的唯一ID生成
```python
# 在game_database.py中实现
async def generate_unique_id(redis_client, prefix):
    # 使用Redis的INCR命令生成唯一ID
    counter = await redis_client.incr(f"{prefix}:counter")
    # 结合时间戳确保唯一性
    timestamp = int(time.time() * 1000)
    # 生成64位ID: 时间戳(40位) + 计数器(24位)
    unique_id = (timestamp << 24) | (counter & 0xFFFFFF)
    return unique_id

# 在Equipment类中使用
def _generate_item_id(self):
    # 实际使用时应从数据库管理器获取Redis客户端
    db_manager = ServiceLocator.get("db_manager")
    return asyncio.run(generate_unique_id(db_manager.redis_client, "equipment"))
```

### 5. 缓存一致性改进
```python
# 在ItemCacheManager中添加
async def invalidate_item_cache(self, user_id, item_id):
    #  invalidate single item cache
    await self.redis_client.delete(f"user:{user_id}:item:{item_id}")
    #  invalidate item list cache
    await self.redis_client.delete(f"user:{user_id}:items")
    #  可选：发送缓存失效通知到其他节点
    await self.publish_cache_invalidation(user_id, item_id)

async def publish_cache_invalidation(self, user_id, item_id):
    # 使用Redis Pub/Sub通知其他worker
    await self.redis_client.publish(
        "cache_invalidation",
        json.dumps({
            "type": "item",
            "user_id": user_id,
            "item_id": item_id
        })
    )

# 在各worker中订阅缓存失效通知
async def subscribe_to_cache_invalidation(redis_client):
    pubsub = redis_client.pubsub()
    await pubsub.subscribe("cache_invalidation")
    while True:
        message = await pubsub.get_message(ignore_subscribe_messages=True)
        if message:
            data = json.loads(message["data"])
            if data["type"] == "item":
                # 清除本地缓存
                await invalidate_local_item_cache(data["user_id"], data["item_id"])
```

## 实施步骤

1. **修改InventoryManager初始化**：将同步初始化改为异步初始化模式
2. **实现事务管理**：在所有修改数据的操作中使用事务
3. **添加分布式锁**：为关键操作添加分布式锁保护
4. **改进ID生成**：使用Redis INCR生成可靠的唯一ID
5. **优化缓存策略**：实现缓存失效和通知机制
6. **测试**：在多Worker环境下进行压力测试，验证数据一致性

## 总结

这些改进将解决多Worker环境下的并发问题，确保数据一致性，提高系统的可扩展性和稳定性。通过异步初始化、事务管理、分布式锁、可靠ID生成和缓存一致性策略的综合应用，可以构建一个健壮的分布式游戏装备系统。