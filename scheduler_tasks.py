import asyncio
from scheduler_manager import SchedulerManager
from distributed_task import distributed_task
from service_locator import ServiceLocator
from logger_config import setup_logger
from ConnectionManager import ConnectionManager
import asyncio
from config import config
from task_queue import TaskQueue
from redis_manager import RedisManager
from mongodb_manager import MongoDBManager
scheduler_manager = SchedulerManager()





logger = setup_logger(__name__)



# 示例1：每日0点重置任务
async def daily_reset():
    logger.info("[定时任务] 每日0点重置任务执行")
    task_queue = ServiceLocator.get("task_queue")
    await task_queue.publish_task("reset_daily", {})
    logger.info("[定时任务] 每日重置完成")

# 示例2：每5分钟推送在线人数
async def push_online_count():
    logger.info("[定时任务] 推送在线人数任务执行")
    task_queue = ServiceLocator.get("task_queue")
    await task_queue.publish_task("push_online_count", {})
    logger.info("[定时任务] 在线人数推送完成")
async def monster_cooldown_persist_cooldowns():
    logger.info("[定时任务] 怪物冷却持久化任务执行")
    task_queue = ServiceLocator.get("task_queue")
    await task_queue.publish_task("monster_cooldown_persist_cooldowns", {})
    logger.info("[定时任务] 怪物冷却持久化完成")
async def monster_cooldown_notify_expired_cooldowns():
    logger.info("[定时任务] 怪物冷却通知任务执行")
    task_queue = ServiceLocator.get("task_queue")
    await task_queue.publish_task("monster_cooldown_notify_expired_cooldowns", {})
    logger.info("[定时任务] 怪物冷却通知完成")
# 注册任务到调度器
async def register_tasks():
    redis_manager = await RedisManager.get_instance()
    redis_client = await redis_manager.get_redis()
    daily_reset_task = distributed_task(redis_client, lock_key="lock:scheduled:daily_reset", ttl=60)(daily_reset)
    push_online_count_task = distributed_task(redis_client, lock_key="lock:scheduled:push_online", ttl=60)(push_online_count)
    monster_cooldown_persist_cooldowns_task = distributed_task(redis_client, lock_key="lock:scheduled:monster_cooldown_persist_cooldowns", ttl=60)(monster_cooldown_persist_cooldowns)
    monster_cooldown_notify_expired_cooldowns_task = distributed_task(redis_client, lock_key="lock:scheduled:monster_cooldown_notify_expired_cooldowns", ttl=60)(monster_cooldown_notify_expired_cooldowns)
    scheduler_manager.add_job(daily_reset_task, trigger='cron', id='daily_reset', hour=0, minute=0)
    scheduler_manager.add_job(push_online_count_task, trigger='interval', id='push_online', minutes=1)
    scheduler_manager.add_job(monster_cooldown_persist_cooldowns_task, trigger='interval', id='monster_cooldown_persist_cooldowns', seconds=30)
    scheduler_manager.add_job(monster_cooldown_notify_expired_cooldowns_task, trigger='interval', id='monster_cooldown_notify_expired_cooldowns', seconds=10)

async def main():
    task_queue = TaskQueue()
    ServiceLocator.register("scheduler_manager", scheduler_manager)
    ServiceLocator.register("task_queue", task_queue)
    await register_tasks()
    scheduler_manager.start()
    logger.info("[Scheduler] 所有定时任务已注册并启动")
    while True:
        await asyncio.sleep(3600)

if __name__ == "__main__":
    asyncio.run(main()) 