/**
 * 商店管理系统 - 主逻辑文件
 * 负责系统初始化和全局事件处理
 */

class ShopAdminApp {
    constructor() {
        this.isInitialized = false;
        this.currentPage = this.getCurrentPage();
        
        // 初始化应用
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            console.log('[ShopAdminApp] 开始初始化...');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
            } else {
                this.onDOMReady();
            }
            
        } catch (error) {
            console.error('[ShopAdminApp] 初始化失败:', error);
            this.showGlobalError('系统初始化失败: ' + error.message);
        }
    }

    /**
     * DOM加载完成后的处理
     */
    async onDOMReady() {
        try {
            // 设置全局错误处理
            this.setupGlobalErrorHandling();
            
            // 初始化页面特定功能
            await this.initPageSpecificFeatures();
            
            // 设置导航高亮
            this.updateNavigation();
            
            // 设置全局快捷键
            this.setupGlobalKeyboardShortcuts();
            
            this.isInitialized = true;
            console.log('[ShopAdminApp] 初始化完成');
            
        } catch (error) {
            console.error('[ShopAdminApp] DOM初始化失败:', error);
            this.showGlobalError('页面初始化失败: ' + error.message);
        }
    }

    /**
     * 获取当前页面
     * @returns {string} 页面名称
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        if (filename === 'items.html') return 'items';
        if (filename === 'index.html' || filename === '') return 'shops';
        
        return 'unknown';
    }

    /**
     * 初始化页面特定功能
     */
    async initPageSpecificFeatures() {
        switch (this.currentPage) {
            case 'shops':
                await this.initShopsPage();
                break;
            case 'items':
                await this.initItemsPage();
                break;
            default:
                console.warn('[ShopAdminApp] 未知页面:', this.currentPage);
        }
    }

    /**
     * 初始化商店页面
     */
    async initShopsPage() {
        console.log('[ShopAdminApp] 初始化商店管理页面');
        
        // 商店管理器会自动初始化
        // 这里可以添加页面特定的初始化逻辑
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const action = urlParams.get('action');
        
        if (action === 'create') {
            // 如果URL包含创建参数，自动打开创建表单
            setTimeout(() => {
                if (window.shopManager) {
                    window.shopManager.openShopForm();
                }
            }, 500);
        }
    }

    /**
     * 初始化商品页面
     */
    async initItemsPage() {
        console.log('[ShopAdminApp] 初始化商品管理页面');
        
        // 检查是否有商店ID参数
        const urlParams = new URLSearchParams(window.location.search);
        const shopId = urlParams.get('shop_id');
        
        if (!shopId) {
            this.showGlobalError('缺少商店ID参数，请从商店列表页面进入');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }
        
        // 这里将来会初始化商品管理器
        console.log('[ShopAdminApp] 商店ID:', shopId);
    }

    /**
     * 更新导航高亮
     */
    updateNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (
                (this.currentPage === 'shops' && (href === 'index.html' || href === '')) ||
                (this.currentPage === 'items' && href === 'items.html')
            ) {
                link.classList.add('active');
            }
        });
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('[ShopAdminApp] 未处理的Promise错误:', event.reason);
            this.showGlobalError('系统错误: ' + (event.reason?.message || event.reason));
            event.preventDefault();
        });

        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('[ShopAdminApp] JavaScript错误:', event.error);
            this.showGlobalError('脚本错误: ' + event.error?.message);
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                console.error('[ShopAdminApp] 资源加载错误:', event.target.src || event.target.href);
            }
        }, true);
    }

    /**
     * 设置全局快捷键
     */
    setupGlobalKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + N: 新增商店
            if ((event.ctrlKey || event.metaKey) && event.key === 'n' && this.currentPage === 'shops') {
                event.preventDefault();
                if (window.shopManager) {
                    window.shopManager.openShopForm();
                }
            }
            
            // Ctrl/Cmd + R: 刷新列表
            if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
                event.preventDefault();
                if (this.currentPage === 'shops' && window.shopManager) {
                    window.shopManager.refreshShopList();
                }
            }
            
            // F5: 刷新页面
            if (event.key === 'F5') {
                // 允许默认行为，但记录日志
                console.log('[ShopAdminApp] 用户刷新页面');
            }
        });
    }

    /**
     * 显示全局错误消息
     * @param {string} message - 错误消息
     */
    showGlobalError(message) {
        // 尝试使用现有的消息系统
        if (window.shopManager && typeof window.shopManager.showMessage === 'function') {
            window.shopManager.showMessage(message, 'error');
            return;
        }
        
        // 回退到简单的alert
        alert('系统错误: ' + message);
    }

    /**
     * 显示全局成功消息
     * @param {string} message - 成功消息
     */
    showGlobalSuccess(message) {
        if (window.shopManager && typeof window.shopManager.showMessage === 'function') {
            window.shopManager.showMessage(message, 'success');
            return;
        }
        
        console.log('[ShopAdminApp] 成功:', message);
    }

    /**
     * 获取应用状态信息
     * @returns {Object} 状态信息
     */
    getAppStatus() {
        return {
            isInitialized: this.isInitialized,
            currentPage: this.currentPage,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 检查浏览器兼容性
     * @returns {boolean} 是否兼容
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'URLSearchParams',
            'addEventListener'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            return typeof window[feature] === 'undefined';
        });
        
        if (missingFeatures.length > 0) {
            console.error('[ShopAdminApp] 浏览器不兼容，缺少功能:', missingFeatures);
            this.showGlobalError(`浏览器不兼容，缺少以下功能: ${missingFeatures.join(', ')}`);
            return false;
        }
        
        return true;
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('[ShopAdminApp] 清理资源');
        
        // 移除事件监听器
        // 清理定时器
        // 其他清理工作
    }
}

// ==================== 全局工具函数 ====================

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 是否成功
 */
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 回退方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            textArea.remove();
            return result;
        }
    } catch (error) {
        console.error('复制到剪贴板失败:', error);
        return false;
    }
}

// ==================== 应用启动 ====================

// 创建全局应用实例
window.shopAdminApp = new ShopAdminApp();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.shopAdminApp) {
        window.shopAdminApp.cleanup();
    }
});

console.log('[ShopAdminApp] 应用脚本加载完成');
