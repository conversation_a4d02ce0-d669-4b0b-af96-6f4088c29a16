from enum import IntEnum, Enum

class MessageId(IntEnum):
    GM = 1001 # GM命令
    ERROR = 1000 # 错误
    HEARTBEAT = 0 # 心跳
    PING = -1 # Ping消息，用于连接检测
    CHAT = 1 # 聊天
    PRIVATE_CHAT =2 # 私聊
    GROUP_CHAT = 3 # 群聊
    GET_ITEMS = 4 # 获取道具
    DELETE_ITEM = 5 # 删除道具
    BROADCAST_MESSAGE = 6 # 广播消息
    SET_NICKNAME = 7 # 设置昵称
    ROLE_INFO = 8 # 角色信息 
    ENTER_GAME = 9 # 进入游戏
    CREATE_ROLE = 10 # 创建角色
    ASSET_CHANGED = 11       # 单个资产变更
    ASSET_BATCH_CHANGED = 12 # 批量资产变更
    GET_EQUIPMENT = 13 # 获取装备
    GET_RUNE = 14 # 获取符文        
    USER_CURRENCY = 15 # 用户货币信息
    
    # General related message IDs (武将相关消息ID)
    GET_GENERALS = 20           # 获取武将列表
    DRAW_GENERAL = 21           # 抽卡
    SYNTHESIS_GENERAL = 22      # 合成武将
    LEVEL_UP_GENERAL = 23       # 武将升级
    STAR_UP_GENERAL = 24        # 武将升星
    AWAKEN_GENERAL = 25         # 武将觉醒
    TRAIN_GENERAL = 26          # 武将训练
    DEPLOY_GENERAL = 27         # 上阵武将
    RETREAT_GENERAL = 28        # 下阵武将
    GET_FORMATION = 29          # 获取阵容
    SAVE_FORMATION = 30         # 保存阵容
    EQUIP_GENERAL = 31          # 装备武将
    UNEQUIP_GENERAL = 32        # 卸下装备
    CHANGE_GENERAL_STATE = 33   # 改变武将状态
    LOCK_GENERAL = 34           # 锁定武将
    UNLOCK_GENERAL = 35         # 解锁武将
    ADD_GENERAL = 36            # 添加武将
    REMOVE_GENERAL = 37         # 移除武将
    UPDATE_GENERAL = 38         # 更新武将
    GET_CARD_PACKS = 39         # 获取卡包列表
    RSP_CARD_PACK = 40         # 刷新卡包
    # Monster related message IDs
    CHECK_COOLDOWN = 100     # Check monster cooldown
    MONSTER_KILLED = 101     # Monster killed notification
    GET_ALL_COOLDOWNS = 102  # Get all active cooldowns
    RESET_COOLDOWN = 103     # Reset cooldown (GM only)
    MONSTER_RESPAWNED = 104  # Monster respawned notification

    # Guild related message IDs (公会相关消息ID)
    GUILD_CREATE = 200           # 创建公会
    GUILD_INFO = 201             # 获取公会信息
    GUILD_UPDATE = 202           # 更新公会信息
    GUILD_DISBAND = 203          # 解散公会
    GUILD_SEARCH = 204           # 搜索公会
    GUILD_APPLY = 205            # 申请加入公会
    GUILD_MEMBER_LIST = 206      # 获取公会成员列表
    GUILD_LEAVE = 207            # 离开公会
    GUILD_APPLICATIONS = 208     # 获取公会申请列表
    GUILD_PROCESS_APPLICATION = 209  # 处理公会申请
    GUILD_REMOVE_MEMBER = 210    # 移除公会成员
    GUILD_CHANGE_POSITION = 211  # 变更成员职位
    GUILD_CHAT = 212             # 公会聊天
    GUILD_NOTIFICATION = 213     # 公会通知
    GET_MY_GUILD = 214           # 获取我的公会信息

    # Mail related message IDs (邮件相关消息ID)
    MAIL_LIST = 300              # 获取邮件列表
    MAIL_DETAIL = 301            # 获取邮件详情
    MAIL_SEND = 302              # 发送邮件
    MAIL_READ = 303              # 标记邮件已读
    MAIL_DELETE = 304            # 删除邮件
    MAIL_CLAIM_ATTACHMENTS = 305 # 领取附件
    MAIL_CLAIM_ALL_ATTACHMENTS = 306 # 一键领取所有附件
    MAIL_UNREAD_COUNT = 307      # 获取未读邮件数量
    MAIL_NOTIFICATION = 308      # 邮件通知

    # Shop related message IDs (商店相关消息ID)
    SHOP_GET_LIST = 350          # 获取商店列表
    SHOP_GET_ITEMS = 351         # 获取商店商品
    SHOP_GET_ITEM_DETAIL = 352   # 获取商品详情
    SHOP_PURCHASE = 353          # 购买商品
    SHOP_PREVIEW_PURCHASE = 354  # 预览购买
    SHOP_GET_HISTORY = 355       # 获取购买历史
    SHOP_GET_LIMITS = 356        # 获取限购状态
    SHOP_PURCHASE_SUCCESS = 357  # 购买成功通知
    SHOP_LIMITS_RESET = 358      # 限购重置通知
    SHOP_REFRESHED = 359         # 商店刷新通知
    SHOP_DISCOUNT_UPDATED = 360  # 折扣更新通知
    SHOP_ITEM_UPDATED = 361      # 商品更新通知

    # 系统消息ID (系统相关消息ID)
    SYSTEM_NOTIFICATION = 410   # 系统通知
    PLAYER_DATA_UPDATE = 411    # 玩家数据更新
    GAME_EVENT = 412            # 游戏事件
    WORKER_STATUS = 413         # Worker状态
    GET_MONSTERS = 105       # Get monster list
    
class ItemType(str, Enum):
    ITEM = "item" # 道具
    EQUIPMENT = "equipment" # 装备
    RUNE = "rune" # 符文

class GeneralState(str, Enum):
    """武将状态枚举"""
    IDLE = "idle"                   # 空闲
    DEPLOYED = "deployed"           # 已上阵
    MISSION = "mission"             # 任务中
    TRAINING = "training"           # 训练中
    RESTING = "resting"             # 休息中
    LOCKED = "locked"               # 锁定状态

class CooldownType(str, Enum):
    PERSONAL = "personal"  # Personal cooldown
    GUILD = "guild"        # Guild cooldown
    GLOBAL = "global"      # Global cooldown