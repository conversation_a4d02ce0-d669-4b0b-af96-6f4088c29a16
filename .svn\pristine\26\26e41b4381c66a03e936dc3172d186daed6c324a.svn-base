"""
邮件系统数据模型
定义邮件相关的数据结构和枚举
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import IntEnum
from dataclasses import dataclass, asdict
import json


# ==================== 枚举定义 ====================

class MailType(IntEnum):
    """邮件类型"""
    SYSTEM = 1    # 系统邮件
    PLAYER = 2    # 玩家邮件


class MailStatus(IntEnum):
    """邮件状态"""
    UNREAD = 1    # 未读
    READ = 2      # 已读
    DELETED = 3   # 已删除


class AttachmentStatus(IntEnum):
    """附件状态"""
    NONE = 0      # 无附件
    UNCLAIMED = 1 # 未领取
    CLAIMED = 2   # 已领取


class AttachmentType(IntEnum):
    """附件类型"""
    ITEM = 1      # 道具
    EQUIPMENT = 2 # 装备
    CURRENCY = 3  # 货币
    GENERAL = 4   # 武将


# ==================== 数据模型 ====================

@dataclass
class Mail:
    """邮件数据模型"""
    mail_id: str
    sender_id: str
    sender_name: str
    receiver_id: str
    receiver_name: str
    title: str
    content: str
    mail_type: MailType
    status: MailStatus
    has_attachments: bool
    attachment_status: AttachmentStatus
    created_at: datetime
    read_at: Optional[datetime] = None
    expire_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime为ISO字符串
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif value is None:
                data[key] = None
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Mail':
        """从字典创建对象"""
        # 转换ISO字符串为datetime
        datetime_fields = ['created_at', 'read_at', 'expire_at', 'deleted_at']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)


@dataclass
class MailAttachment:
    """邮件附件数据模型"""
    attachment_id: str
    mail_id: str
    attachment_type: AttachmentType
    item_id: str
    item_name: str
    quantity: int
    quality: int = 1
    extra_data: Optional[Dict[str, Any]] = None
    claimed: bool = False
    claimed_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime为ISO字符串
        if data.get('claimed_at'):
            data['claimed_at'] = data['claimed_at'].isoformat()
        # 转换extra_data为JSON字符串
        if data.get('extra_data'):
            data['extra_data'] = json.dumps(data['extra_data'])
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MailAttachment':
        """从字典创建对象"""
        # 转换ISO字符串为datetime
        if data.get('claimed_at'):
            data['claimed_at'] = datetime.fromisoformat(data['claimed_at'])
        # 转换JSON字符串为字典
        if data.get('extra_data') and isinstance(data['extra_data'], str):
            data['extra_data'] = json.loads(data['extra_data'])
        
        return cls(**data)


# ==================== 请求/响应模型 ====================

@dataclass
class MailAttachmentData:
    """邮件附件数据"""
    attachment_type: AttachmentType
    item_id: str
    item_name: str
    quantity: int
    quality: int = 1
    extra_data: Optional[Dict[str, Any]] = None


@dataclass
class SendMailRequest:
    """发送邮件请求"""
    receiver_id: str
    title: str
    content: str
    attachments: Optional[List[MailAttachmentData]] = None


@dataclass
class SystemMailRequest:
    """系统邮件请求"""
    title: str
    content: str
    attachments: Optional[List[MailAttachmentData]] = None
    expire_days: int = 7  # 过期天数


@dataclass
class BroadcastMailRequest:
    """广播邮件请求"""
    title: str
    content: str
    attachments: Optional[List[MailAttachmentData]] = None
    target_players: Optional[List[str]] = None  # 目标玩家列表，None表示全服
    expire_days: int = 7
    target_type: str = "all"  # "all" 或 "specific"


@dataclass
class MailResponse:
    """邮件操作响应"""
    success: bool
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class MailTemplate:
    """邮件模板数据模型"""
    template_id: str
    title: str
    content: str
    attachments: Optional[List[MailAttachmentData]]
    created_at: datetime
    expire_at: datetime
    target_type: str  # "all" 或 "specific"
    target_users: Optional[List[str]] = None  # 指定用户列表（仅当target_type="specific"时使用）

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime为ISO字符串
        data['created_at'] = data['created_at'].isoformat()
        data['expire_at'] = data['expire_at'].isoformat()
        # 转换附件数据
        if data.get('attachments'):
            data['attachments'] = [asdict(att) for att in data['attachments']]
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MailTemplate':
        """从字典创建对象"""
        # 转换ISO字符串为datetime
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['expire_at'] = datetime.fromisoformat(data['expire_at'])
        # 转换附件数据
        if data.get('attachments'):
            data['attachments'] = [MailAttachmentData(**att) for att in data['attachments']]
        return cls(**data)


@dataclass
class UserTemplateProcessed:
    """用户模板处理记录"""
    user_id: str
    template_id: str
    processed_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['processed_at'] = data['processed_at'].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserTemplateProcessed':
        """从字典创建对象"""
        data['processed_at'] = datetime.fromisoformat(data['processed_at'])
        return cls(**data)


# ==================== 邮件列表项 ====================

@dataclass
class MailListItem:
    """邮件列表项（简化版邮件信息）"""
    mail_id: str
    sender_name: str
    title: str
    status: MailStatus
    has_attachments: bool
    attachment_status: AttachmentStatus
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = data['created_at'].isoformat()
        return data

    @classmethod
    def from_mail(cls, mail: Mail) -> 'MailListItem':
        """从Mail对象创建列表项"""
        return cls(
            mail_id=mail.mail_id,
            sender_name=mail.sender_name,
            title=mail.title,
            status=mail.status,
            has_attachments=mail.has_attachments,
            attachment_status=mail.attachment_status,
            created_at=mail.created_at
        )


# ==================== 常量定义 ====================

class MailConstants:
    """邮件系统常量"""
    
    # 邮件限制
    MAX_TITLE_LENGTH = 50        # 最大标题长度
    MAX_CONTENT_LENGTH = 500     # 最大内容长度
    MAX_ATTACHMENTS = 10         # 最大附件数量
    
    # 默认过期时间（天）
    DEFAULT_EXPIRE_DAYS = 7      # 默认7天过期
    SYSTEM_MAIL_EXPIRE_DAYS = 30 # 系统邮件30天过期
    
    # 分页限制
    DEFAULT_PAGE_SIZE = 20       # 默认分页大小
    MAX_PAGE_SIZE = 100          # 最大分页大小
    
    # 系统发送者
    SYSTEM_SENDER_ID = "system"
    SYSTEM_SENDER_NAME = "系统"
