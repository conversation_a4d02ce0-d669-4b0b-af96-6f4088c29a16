from logger_config import setup_logger
from config import config
# 初始化日志系统
logger = setup_logger(__name__)
class CacheKeyBuilder:
    """缓存键构建器，确保键命名一致性"""
    
    @staticmethod
    def build(pattern: str, **kwargs) -> str:
        """从模式和参数构建缓存键"""
        try:
            key = pattern
            for k, v in kwargs.items():
                key = key.replace(f"{{{k}}}", str(v))
            return key
        except Exception as e:
            logger.warning(f"构建缓存键失败: {str(e)}, 模式: {pattern}, 参数: {kwargs}")
            # 返回原始模式而不是None，确保在错误情况下仍有返回值
            return pattern
    
    @staticmethod
    def user_data(username: str) -> str:
        """用户数据缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_data", "game:v2:users:{username}"), username=username)
    
    @staticmethod
    def user_status(username: str) -> str:
        """用户状态缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_status", "game:v2:users:{username}:status"), username=username)
    
    @staticmethod
    def user_connection(username: str) -> str:
        """用户连接信息缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("user_connection", "game:v2:users:{username}:connection"), username=username)
    
    @staticmethod
    def token_to_user(token: str) -> str:
        """Token到用户的映射缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("token_to_user", "game:v2:token_to_user:{token}"), token=token)
    
    @staticmethod
    def items_by_owner(owner: str, item_type: str = None) -> str:
        """用户物品列表缓存键"""
        return CacheKeyBuilder.build(config.get_item_cache_key(item_type), username=owner, owner=owner)
    
    @staticmethod
    def item_details(owner: str, item_id: str) -> str:
        """物品详情缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("item_data", "game:v2:items:{owner}:{item_id}"), owner=owner, item_id=item_id)
    
    @staticmethod
    def roles_by_owner(owner: str) -> str:
        """用户角色列表缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("roles_data", "game:v2:roles:{owner}"), owner=owner)
    
    @staticmethod
    def role_details(owner: str, role_id: str) -> str:
        """角色详情缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("role_data", "game:v2:roles:{owner}:{role_id}"), owner=owner, role_id=role_id)
    
    @staticmethod
    def role_properties(owner: str, role_id: str) -> str:
        """角色属性计算结果缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("role_properties", "game:v2:roles:{owner}:{role_id}:properties"), owner=owner, role_id=role_id)
    
    @staticmethod
    def role_fight_power(owner: str, role_id: str) -> str:
        """角色战斗力缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("role_fight_power", "game:v2:roles:{owner}:{role_id}:fight_power"), owner=owner, role_id=role_id)

    @staticmethod
    def generals_by_player(player_id: str) -> str:
        """玩家武将列表缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("generals_data", "game:v2:generals:{player_id}"), player_id=player_id)

    @staticmethod
    def general_details(player_id: str, general_id: str) -> str:
        """武将详情缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("general_data", "game:v2:generals:{player_id}:{general_id}"), player_id=player_id, general_id=general_id)

    @staticmethod
    def general_properties(player_id: str, general_id: str) -> str:
        """武将属性计算结果缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("general_properties", "game:v2:generals:{player_id}:{general_id}:properties"), player_id=player_id, general_id=general_id)

    @staticmethod
    def general_fight_power(player_id: str, general_id: str) -> str:
        """武将战斗力缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("general_fight_power", "game:v2:generals:{player_id}:{general_id}:fight_power"), player_id=player_id, general_id=general_id)

    @staticmethod
    def formation_data(player_id: str) -> str:
        """玩家阵容缓存键"""
        cache_keys = config.get_cache_keys()
        return CacheKeyBuilder.build(cache_keys.get("formation_data", "game:v2:formation:{player_id}"), player_id=player_id)
