"""
测试邮件系统分布式锁修复
"""

import asyncio
import logging
from mail_cache_manager import MailCacheManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_mail_lock():
    """测试邮件锁功能"""
    try:
        # 获取缓存管理器实例
        cache_manager = await MailCacheManager.get_instance()
        logger.info("MailCacheManager实例获取成功")
        
        # 测试获取锁
        lock = await cache_manager.acquire_mail_lock("test_player", "test_operation")
        logger.info(f"锁对象创建成功: {lock}")
        
        # 测试使用锁
        async with lock:
            logger.info("成功获取并使用分布式锁")
            await asyncio.sleep(1)  # 模拟一些操作
            logger.info("锁内操作完成")
        
        logger.info("锁已释放")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    logger.info("开始测试邮件系统分布式锁...")
    
    success = await test_mail_lock()
    
    if success:
        logger.info("✅ 测试成功！分布式锁修复完成")
    else:
        logger.error("❌ 测试失败！需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
