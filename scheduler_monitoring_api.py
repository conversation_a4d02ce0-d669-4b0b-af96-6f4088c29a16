"""
调度器监控和管理API
提供调度器状态监控、任务管理和健康检查的HTTP API接口
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from logger_config import setup_logger
from game_server_scheduler_integration import get_game_server_scheduler
from scheduler_health_checks import task_metrics

logger = setup_logger(__name__)

# 创建路由器
scheduler_router = APIRouter(prefix="/api/scheduler", tags=["调度器管理"])

class TaskStatsResponse(BaseModel):
    """任务统计响应模型"""
    task_name: str
    execution_count: int
    failure_count: int
    success_rate: float
    last_execution_time: float
    avg_execution_time: float

class ServiceHealthResponse(BaseModel):
    """服务健康状态响应模型"""
    service_name: str
    service_key: str
    is_healthy: bool
    last_check_time: Optional[str] = None

class SchedulerStatusResponse(BaseModel):
    """调度器状态响应模型"""
    is_running: bool
    mode: str
    total_tasks: int
    active_tasks: int
    service_health: List[ServiceHealthResponse]
    task_stats: List[TaskStatsResponse]

class TaskControlRequest(BaseModel):
    """任务控制请求模型"""
    task_name: str
    action: str  # "pause", "resume", "trigger"

async def get_scheduler_integration():
    """获取调度器集成实例"""
    return await get_game_server_scheduler()

@scheduler_router.get("/test")
async def test_scheduler_api():
    """测试调度器API是否可用"""
    return {"message": "调度器API正常工作", "status": "ok"}

@scheduler_router.get("/ping")
async def ping():
    """简单的ping接口"""
    return {"status": "ok", "message": "pong"}

@scheduler_router.get("/status")
async def get_scheduler_status():
    """获取调度器状态"""
    try:
        # 尝试获取调度器实例
        try:
            scheduler_integration = await get_game_server_scheduler()
        except Exception as e:
            return {
                "error": f"无法获取调度器实例: {str(e)}",
                "is_running": False,
                "scheduler_available": False
            }

        if not scheduler_integration:
            return {
                "error": "调度器集成实例不存在",
                "is_running": False,
                "scheduler_available": False
            }

        # 获取基本状态
        is_running = scheduler_integration.is_running()
        
        # 获取服务健康状态
        health_status = await scheduler_integration.get_health_status()
        service_health = []
        
        if scheduler_integration.scheduler:
            for service_key, is_healthy in health_status.items():
                dependency = scheduler_integration.scheduler.service_dependencies.get(service_key)
                service_health.append(ServiceHealthResponse(
                    service_name=dependency.name if dependency else service_key,
                    service_key=service_key,
                    is_healthy=is_healthy
                ))
        
        # 获取任务统计
        task_stats_data = scheduler_integration.get_task_metrics()
        task_stats = [
            TaskStatsResponse(
                task_name=task_name,
                **stats
            )
            for task_name, stats in task_stats_data.items()
        ]
        
        # 获取任务信息
        total_tasks = 0
        active_tasks = 0
        if scheduler_integration.scheduler:
            total_tasks = len(scheduler_integration.scheduler.task_definitions)
            active_tasks = len([
                task for task in scheduler_integration.scheduler.task_definitions.values()
                if task.enabled
            ])
        
        return SchedulerStatusResponse(
            is_running=is_running,
            mode=scheduler_integration.scheduler.mode.value if scheduler_integration.scheduler else "unknown",
            total_tasks=total_tasks,
            active_tasks=active_tasks,
            service_health=service_health,
            task_stats=task_stats
        )
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取调度器状态失败: {str(e)}")

@scheduler_router.get("/tasks")
async def get_tasks(
    scheduler_integration = Depends(get_scheduler_integration)
):
    """获取所有任务列表"""
    try:
        if not scheduler_integration.scheduler:
            raise HTTPException(status_code=404, detail="调度器未初始化")
        
        tasks = []
        for task_name, task_def in scheduler_integration.scheduler.task_definitions.items():
            # 获取APScheduler中的任务状态
            job = scheduler_integration.scheduler.scheduler_manager.get_job(task_name)
            
            task_info = {
                "name": task_name,
                "enabled": task_def.enabled,
                "trigger": task_def.trigger,
                "trigger_args": task_def.trigger_args,
                "dependencies": task_def.dependencies,
                "lock_key": task_def.lock_key,
                "lock_ttl": task_def.lock_ttl,
                "next_run_time": str(job.next_run_time) if job and job.next_run_time else None,
                "job_id": job.id if job else None
            }
            tasks.append(task_info)
        
        return {"tasks": tasks}
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@scheduler_router.post("/tasks/control")
async def control_task(
    request: TaskControlRequest,
    scheduler_integration = Depends(get_scheduler_integration)
):
    """控制任务执行"""
    try:
        if not scheduler_integration.scheduler:
            raise HTTPException(status_code=404, detail="调度器未初始化")
        
        task_name = request.task_name
        action = request.action
        
        # 检查任务是否存在
        if task_name not in scheduler_integration.scheduler.task_definitions:
            raise HTTPException(status_code=404, detail=f"任务 {task_name} 不存在")
        
        scheduler_manager = scheduler_integration.scheduler.scheduler_manager
        
        if action == "pause":
            scheduler_manager.pause_job(task_name)
            message = f"任务 {task_name} 已暂停"
            
        elif action == "resume":
            scheduler_manager.resume_job(task_name)
            message = f"任务 {task_name} 已恢复"
            
        elif action == "trigger":
            # 手动触发任务
            job = scheduler_manager.get_job(task_name)
            if job:
                job.modify(next_run_time=None)  # 立即执行
                message = f"任务 {task_name} 已手动触发"
            else:
                raise HTTPException(status_code=404, detail=f"任务 {task_name} 的作业不存在")
                
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action}")
        
        logger.info(message)
        return {"message": message}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"控制任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"控制任务失败: {str(e)}")

@scheduler_router.get("/health")
async def get_health_check():
    """获取详细的健康检查信息"""
    try:
        # 尝试获取调度器实例，但不强制依赖
        try:
            scheduler_integration = await get_game_server_scheduler()
        except Exception as e:
            return {
                "status": "error",
                "message": f"无法获取调度器实例: {str(e)}",
                "scheduler_available": False
            }

        if not scheduler_integration or not scheduler_integration.scheduler:
            return {
                "status": "warning",
                "message": "调度器未初始化",
                "scheduler_available": False,
                "basic_health": "API可用"
            }

        health_status = await scheduler_integration.get_health_status()
        
        # 计算总体健康状态
        all_healthy = all(health_status.values())
        
        # 获取不健康的服务
        unhealthy_services = [
            scheduler_integration.scheduler.service_dependencies[key].name
            for key, healthy in health_status.items()
            if not healthy
        ]
        
        result = {
            "status": "healthy" if all_healthy else "unhealthy",
            "overall_healthy": all_healthy,
            "services": {}
        }
        
        # 详细的服务状态
        for service_key, is_healthy in health_status.items():
            dependency = scheduler_integration.scheduler.service_dependencies.get(service_key)
            result["services"][service_key] = {
                "name": dependency.name if dependency else service_key,
                "healthy": is_healthy,
                "required": dependency.required if dependency else True
            }
        
        if unhealthy_services:
            result["unhealthy_services"] = unhealthy_services
        
        return result
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}"
        }

@scheduler_router.get("/metrics")
async def get_metrics():
    """获取任务执行指标"""
    try:
        all_stats = task_metrics.get_all_stats()
        
        # 计算总体指标
        total_executions = sum(stats["execution_count"] for stats in all_stats.values())
        total_failures = sum(stats["failure_count"] for stats in all_stats.values())
        overall_success_rate = ((total_executions - total_failures) / total_executions * 100) if total_executions > 0 else 0
        
        return {
            "overall_metrics": {
                "total_executions": total_executions,
                "total_failures": total_failures,
                "overall_success_rate": round(overall_success_rate, 2)
            },
            "task_metrics": all_stats
        }
        
    except Exception as e:
        logger.error(f"获取指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")

@scheduler_router.post("/restart")
async def restart_scheduler(
    scheduler_integration = Depends(get_scheduler_integration)
):
    """重启调度器"""
    try:
        logger.info("开始重启调度器...")
        
        # 停止调度器
        await scheduler_integration.shutdown()
        
        # 重新初始化
        success = await scheduler_integration.initialize()
        
        if success:
            message = "调度器重启成功"
            logger.info(message)
            return {"message": message, "status": "success"}
        else:
            message = "调度器重启失败"
            logger.error(message)
            raise HTTPException(status_code=500, detail=message)
            
    except Exception as e:
        logger.error(f"重启调度器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重启调度器失败: {str(e)}")
