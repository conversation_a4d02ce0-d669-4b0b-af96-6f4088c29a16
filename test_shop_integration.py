"""
商店系统集成测试
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ShopIntegrationTester:
    """商店系统集成测试器"""
    
    def __init__(self, server_url: str = "ws://localhost:8000/ws"):
        self.server_url = server_url
        self.websocket = None
        self.username = "test_player"
        self.token = "test_token"
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            logger.info(f"已连接到服务器: {self.server_url}")
            
            # 发送认证消息
            auth_message = {
                "msgId": 1,  # LOGIN
                "data": {
                    "username": self.username,
                    "token": self.token
                }
            }
            
            await self.websocket.send(json.dumps(auth_message))
            response = await self.websocket.recv()
            logger.info(f"认证响应: {response}")
            
            return True
            
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("已断开连接")
    
    async def send_message(self, msg_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送消息并等待响应"""
        try:
            message = {
                "msgId": msg_id,
                "data": data
            }
            
            await self.websocket.send(json.dumps(message))
            logger.info(f"发送消息: {message}")
            
            response = await self.websocket.recv()
            response_data = json.loads(response)
            logger.info(f"收到响应: {response_data}")
            
            return response_data
            
        except Exception as e:
            logger.error(f"发送消息失败: {str(e)}")
            return {"error": str(e)}
    
    async def test_get_shop_list(self):
        """测试获取商店列表"""
        print("\n=== 测试获取商店列表 ===")
        
        response = await self.send_message(350, {})  # SHOP_GET_LIST
        
        if response.get("data", {}).get("success"):
            shops = response["data"]["shops"]
            print(f"✅ 获取商店列表成功，共 {len(shops)} 个商店")
            for shop in shops:
                print(f"  - {shop['shop_name']} ({shop['shop_type']})")
            return shops
        else:
            print(f"❌ 获取商店列表失败: {response}")
            return []
    
    async def test_get_shop_items(self, shop_id: str):
        """测试获取商店商品"""
        print(f"\n=== 测试获取商店商品: {shop_id} ===")
        
        response = await self.send_message(351, {"shop_id": shop_id})  # SHOP_GET_ITEMS
        
        if response.get("data", {}).get("success"):
            items = response["data"]["items"]
            print(f"✅ 获取商店商品成功，共 {len(items)} 个商品")
            for item in items:
                print(f"  - {item['item_template_id']} x{item['item_quantity']}")
                print(f"    价格: {item['final_price']} {item['currency_type']}")
                print(f"    限购剩余: {item['limit_remaining']}")
            return items
        else:
            print(f"❌ 获取商店商品失败: {response}")
            return []
    
    async def test_get_item_detail(self, config_id: str):
        """测试获取商品详情"""
        print(f"\n=== 测试获取商品详情: {config_id} ===")
        
        response = await self.send_message(352, {"config_id": config_id})  # SHOP_GET_ITEM_DETAIL
        
        if response.get("data", {}).get("success"):
            data = response["data"]
            print(f"✅ 获取商品详情成功")
            print(f"  商品: {data['item_template_id']} x{data['item_quantity']}")
            print(f"  原价: {data['price_info']['original_price']} {data['price_info']['currency_type']}")
            print(f"  现价: {data['price_info']['final_price']} {data['price_info']['currency_type']}")
            print(f"  可购买: {data['can_purchase']}")
            print(f"  限购: {data['limit_status']['remaining']}/{data['limit_status']['limit_count']}")
            return data
        else:
            print(f"❌ 获取商品详情失败: {response}")
            return None
    
    async def test_preview_purchase(self, config_id: str, quantity: int = 1):
        """测试预览购买"""
        print(f"\n=== 测试预览购买: {config_id} x{quantity} ===")
        
        response = await self.send_message(354, {  # SHOP_PREVIEW_PURCHASE
            "config_id": config_id,
            "quantity": quantity
        })
        
        if response.get("data", {}).get("success"):
            preview = response["data"]["preview"]
            print(f"✅ 预览购买成功")
            print(f"  可购买: {preview['can_purchase']}")
            if preview["can_purchase"]:
                print(f"  总价: {preview['final_price']} {preview['currency_type']}")
                print(f"  折扣: {preview['discount_amount']}")
            else:
                print(f"  错误: {preview.get('error', '未知错误')}")
            return preview
        else:
            print(f"❌ 预览购买失败: {response}")
            return None
    
    async def test_purchase_item(self, config_id: str, quantity: int = 1):
        """测试购买商品"""
        print(f"\n=== 测试购买商品: {config_id} x{quantity} ===")
        
        response = await self.send_message(353, {  # SHOP_PURCHASE
            "config_id": config_id,
            "quantity": quantity,
            "metadata": {"test": True}
        })
        
        # 等待购买成功通知
        try:
            notification = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            notification_data = json.loads(notification)
            
            if notification_data.get("msgId") == 357:  # SHOP_PURCHASE_SUCCESS
                print(f"✅ 购买成功通知: {notification_data}")
                return True
            else:
                print(f"❌ 购买失败: {response}")
                return False
                
        except asyncio.TimeoutError:
            print(f"❌ 购买超时，未收到成功通知")
            return False
    
    async def test_get_purchase_history(self):
        """测试获取购买历史"""
        print(f"\n=== 测试获取购买历史 ===")
        
        response = await self.send_message(355, {  # SHOP_GET_HISTORY
            "limit": 10,
            "offset": 0
        })
        
        if response.get("data", {}).get("success"):
            purchases = response["data"]["purchases"]
            print(f"✅ 获取购买历史成功，共 {len(purchases)} 条记录")
            for purchase in purchases:
                print(f"  - {purchase['purchase_time']}: {purchase['item_template_id']} x{purchase['quantity']}")
            return purchases
        else:
            print(f"❌ 获取购买历史失败: {response}")
            return []
    
    async def test_get_purchase_limits(self):
        """测试获取限购状态"""
        print(f"\n=== 测试获取限购状态 ===")
        
        response = await self.send_message(356, {})  # SHOP_GET_LIMITS
        
        if response.get("data", {}).get("success"):
            limits = response["data"]["limits"]
            print(f"✅ 获取限购状态成功")
            for limit_type, status in limits.items():
                if isinstance(status, dict) and status.get("counters"):
                    print(f"  {limit_type}: {len(status['counters'])} 个商品有限购")
            return limits
        else:
            print(f"❌ 获取限购状态失败: {response}")
            return {}
    
    async def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始商店系统集成测试")
        print("=" * 50)
        
        try:
            # 连接服务器
            if not await self.connect():
                print("❌ 连接服务器失败，测试终止")
                return
            
            # 1. 测试获取商店列表
            shops = await self.test_get_shop_list()
            if not shops:
                print("❌ 没有可用商店，测试终止")
                return
            
            # 2. 测试获取第一个商店的商品
            first_shop = shops[0]
            items = await self.test_get_shop_items(first_shop["shop_id"])
            if not items:
                print("❌ 商店没有商品，跳过后续测试")
            else:
                # 3. 测试获取第一个商品的详情
                first_item = items[0]
                detail = await self.test_get_item_detail(first_item["config_id"])
                
                if detail and detail["can_purchase"]:
                    # 4. 测试预览购买
                    preview = await self.test_preview_purchase(first_item["config_id"], 1)
                    
                    if preview and preview["can_purchase"]:
                        # 5. 测试实际购买（注意：这会消耗游戏资源）
                        print("\n⚠️  即将进行实际购买测试，这会消耗游戏资源")
                        print("如果不想进行实际购买，请在5秒内按Ctrl+C终止")
                        try:
                            await asyncio.sleep(5)
                            await self.test_purchase_item(first_item["config_id"], 1)
                        except KeyboardInterrupt:
                            print("\n⏹️  用户取消了购买测试")
            
            # 6. 测试获取购买历史
            await self.test_get_purchase_history()
            
            # 7. 测试获取限购状态
            await self.test_get_purchase_limits()
            
            print("\n✅ 商店系统集成测试完成")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {str(e)}")
            logger.error(f"测试失败: {str(e)}")
        
        finally:
            await self.disconnect()


async def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试器
    tester = ShopIntegrationTester()
    
    # 运行测试
    await tester.run_full_test()


if __name__ == "__main__":
    print("商店系统集成测试")
    print("确保游戏服务器正在运行在 localhost:8000")
    print("按Enter开始测试...")
    input()
    
    asyncio.run(main())
