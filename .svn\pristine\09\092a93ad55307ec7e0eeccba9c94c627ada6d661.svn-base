"""
消息幂等性管理器
防止消息重复处理，确保系统的一致性
"""

import asyncio
import hashlib
import json
import logging
import time
import traceback
from typing import Dict, Any, Optional, Set
from datetime import datetime
from redis_manager import RedisManager
from logger_config import setup_logger

logger = setup_logger(__name__)

# 自定义JSON编码器，处理特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理特殊类型"""
    def default(self, obj):
        import inspect
        try:
            from bson import ObjectId
        except ImportError:
            ObjectId = None

        if ObjectId and isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, bytes):
            return obj.decode('utf-8')
        elif inspect.iscoroutine(obj):
            # 协程对象不能序列化，返回描述信息
            return f"<coroutine: {obj.__name__ if hasattr(obj, '__name__') else str(type(obj))}>"
        elif callable(obj):
            # 可调用对象返回描述信息
            return f"<callable: {obj.__name__ if hasattr(obj, '__name__') else str(type(obj))}>"
        elif hasattr(obj, '__dict__'):
            # 对象有属性字典，尝试序列化其属性
            try:
                return {k: v for k, v in obj.__dict__.items() if not k.startswith('_')}
            except:
                return str(obj)
        return super().default(obj)


class MessageIdempotencyManager:
    """消息幂等性管理器"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.redis_manager = None
        self.processed_messages: Set[str] = set()  # 本地缓存
        self.max_local_cache_size = 10000  # 本地缓存最大大小
        self.message_ttl = 3600  # 消息记录TTL (1小时)
        
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    await cls._instance.initialize()
        return cls._instance
    
    async def initialize(self):
        """初始化管理器"""
        try:
            self.redis_manager = await RedisManager.get_instance()
            logger.info("消息幂等性管理器初始化完成")
        except Exception as e:
            logger.error(f"消息幂等性管理器初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def generate_message_id(self, msg_id: int, username: str, data: Dict[str, Any], 
                           timestamp: Optional[float] = None) -> str:
        """
        生成消息唯一ID
        
        Args:
            msg_id: 消息类型ID
            username: 用户名
            data: 消息数据
            timestamp: 时间戳 (可选，用于客户端提供的消息ID)
        
        Returns:
            消息唯一ID
        """
        try:
            # 如果客户端提供了消息ID，直接使用
            if 'message_id' in data:
                return str(data['message_id'])
            
            # 否则根据消息内容生成ID
            content = {
                'msg_id': msg_id,
                'username': username,
                'data': data,
                'timestamp': timestamp or time.time()
            }
            
            # 生成内容哈希
            content_str = json.dumps(content, sort_keys=True, ensure_ascii=False, cls=CustomJSONEncoder)
            message_hash = hashlib.sha256(content_str.encode('utf-8')).hexdigest()
            
            return f"{msg_id}:{username}:{message_hash[:16]}"
            
        except Exception as e:
            logger.error(f"生成消息ID失败: {str(e)}")
            # 返回一个基于时间的ID作为后备
            return f"{msg_id}:{username}:{int(time.time() * 1000)}"
    
    async def is_message_processed(self, message_id: str) -> bool:
        """
        检查消息是否已经处理过
        
        Args:
            message_id: 消息唯一ID
            
        Returns:
            True if已处理, False if未处理
        """
        try:
            # 先检查本地缓存
            if message_id in self.processed_messages:
                logger.debug(f"消息已在本地缓存中找到: {message_id}")
                return True
            
            # 检查Redis缓存
            redis_key = f"processed_message:{message_id}"
            redis_client = await self.redis_manager.get_redis()
            exists = await redis_client.exists(redis_key)
            
            if exists:
                # 添加到本地缓存
                self._add_to_local_cache(message_id)
                logger.debug(f"消息已在Redis中找到: {message_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查消息处理状态失败: {message_id}, 错误: {str(e)}")
            # 出错时假设未处理，避免阻塞正常流程
            return False
    
    async def mark_message_processed(self, message_id: str, result: Optional[Dict] = None):
        """
        标记消息为已处理
        
        Args:
            message_id: 消息唯一ID
            result: 处理结果 (可选)
        """
        try:
            # 添加到本地缓存
            self._add_to_local_cache(message_id)
            
            # 存储到Redis
            redis_key = f"processed_message:{message_id}"
            redis_client = await self.redis_manager.get_redis()
            
            message_data = {
                'processed_at': time.time(),
                'result': result
            }
            
            await redis_client.setex(
                redis_key,
                self.message_ttl,
                json.dumps(message_data, cls=CustomJSONEncoder)
            )
            
            logger.debug(f"标记消息已处理: {message_id}")
            
        except Exception as e:
            logger.error(f"标记消息处理状态失败: {message_id}, 错误: {str(e)}")
    
    def _add_to_local_cache(self, message_id: str):
        """添加到本地缓存"""
        self.processed_messages.add(message_id)
        
        # 如果本地缓存过大，清理一部分
        if len(self.processed_messages) > self.max_local_cache_size:
            # 移除一半的缓存项 (简单的LRU策略)
            items_to_remove = len(self.processed_messages) // 2
            for _ in range(items_to_remove):
                self.processed_messages.pop()
    
    async def get_message_result(self, message_id: str) -> Optional[Dict]:
        """
        获取已处理消息的结果
        
        Args:
            message_id: 消息唯一ID
            
        Returns:
            消息处理结果，如果未找到返回None
        """
        try:
            redis_key = f"processed_message:{message_id}"
            redis_client = await self.redis_manager.get_redis()
            
            data = await redis_client.get(redis_key)
            if data:
                message_data = json.loads(data)
                return message_data.get('result')
            
            return None
            
        except Exception as e:
            logger.error(f"获取消息结果失败: {message_id}, 错误: {str(e)}")
            return None
    
    async def cleanup_expired_messages(self):
        """清理过期的消息记录 (由Redis TTL自动处理，这里主要清理本地缓存)"""
        try:
            # 清理本地缓存 (简单策略：定期清空)
            if len(self.processed_messages) > self.max_local_cache_size * 0.8:
                self.processed_messages.clear()
                logger.debug("清理本地消息缓存")
                
        except Exception as e:
            logger.error(f"清理过期消息失败: {str(e)}")


# 全局实例
message_idempotency_manager = None


async def get_message_idempotency_manager():
    """获取全局消息幂等性管理器实例"""
    global message_idempotency_manager
    if message_idempotency_manager is None:
        message_idempotency_manager = await MessageIdempotencyManager.get_instance()
    return message_idempotency_manager
