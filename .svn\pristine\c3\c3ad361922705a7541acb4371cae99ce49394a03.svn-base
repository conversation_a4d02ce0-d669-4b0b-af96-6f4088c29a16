"""
商店系统数据库管理器
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from pymongo import UpdateOne
from mongodb_manager import MongoDBManager
from shop_models import (
    Shop, ShopItemConfig, ShopDiscount, ShopPurchase, PurchaseLimitCounter
)

logger = logging.getLogger(__name__)


class ShopDatabaseManager:
    """商店数据库管理器"""
    
    def __init__(self):
        self.shops_collection = "shops"
        self.item_configs_collection = "shop_item_configs"
        self.discounts_collection = "shop_discounts"
        self.purchases_collection = "shop_purchases"
        self.limit_counters_collection = "purchase_limit_counters"
        self._indexes_created = False
    
    async def _get_db(self):
        """获取数据库连接"""
        db_manager = await MongoDBManager.get_instance()
        db = await db_manager.get_db()
        
        # 首次使用时自动创建索引
        if not self._indexes_created:
            await self._ensure_indexes_created(db)
            self._indexes_created = True
            
        return db
    
    async def _ensure_indexes_created(self, db):
        """确保数据库索引已创建"""
        try:
            # 商店表索引
            shops_collection = db[self.shops_collection]
            await shops_collection.create_index("shop_id", unique=True, name="shop_id_unique")
            await shops_collection.create_index([("shop_type", 1), ("is_active", 1)], name="shop_type_active")
            await shops_collection.create_index("sort_order", name="shop_sort_order")
            
            # 商品配置表索引
            configs_collection = db[self.item_configs_collection]
            await configs_collection.create_index("config_id", unique=True, name="config_id_unique")
            await configs_collection.create_index([("shop_id", 1), ("is_active", 1), ("sort_order", 1)], name="shop_items")
            await configs_collection.create_index("item_template_id", name="item_template_id")
            
            # 折扣表索引
            discounts_collection = db[self.discounts_collection]
            await discounts_collection.create_index("discount_id", unique=True, name="discount_id_unique")
            await discounts_collection.create_index([("scope_type", 1), ("is_active", 1)], name="discount_scope")
            
            # 购买记录表索引
            purchases_collection = db[self.purchases_collection]
            await purchases_collection.create_index("purchase_id", unique=True, name="purchase_id_unique")
            await purchases_collection.create_index([("player_id", 1), ("purchase_time", -1)], name="player_purchases")
            await purchases_collection.create_index([("shop_id", 1), ("config_id", 1)], name="shop_item_purchases")
            
            # 限购计数器表索引
            counters_collection = db[self.limit_counters_collection]
            await counters_collection.create_index("counter_id", unique=True, name="counter_id_unique")
            await counters_collection.create_index([("player_id", 1), ("limit_type", 1)], name="player_limits")
            await counters_collection.create_index([("scope_value", 1), ("limit_type", 1)], name="scope_limits")
            await counters_collection.create_index("period_end", name="period_end")
            
            logger.info("商店系统数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"创建商店数据库索引时发生错误: {str(e)}")
    
    # ==================== 商店操作 ====================
    
    def generate_shop_id(self) -> str:
        """生成商店ID"""
        return f"shop_{uuid.uuid4().hex[:12]}"
    
    async def create_shop(self, shop: Shop) -> bool:
        """创建商店"""
        try:
            db = await self._get_db()
            collection = db[self.shops_collection]
            
            shop_data = shop.to_dict()
            result = await collection.insert_one(shop_data)
            
            if result.inserted_id:
                logger.info(f"商店创建成功: {shop.shop_id}")
                return True
            else:
                logger.error(f"商店创建失败: {shop.shop_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建商店时发生错误: {str(e)}")
            return False
    
    async def get_shop(self, shop_id: str) -> Optional[Shop]:
        """获取商店"""
        try:
            db = await self._get_db()
            collection = db[self.shops_collection]
            
            shop_doc = await collection.find_one({"shop_id": shop_id})
            if shop_doc:
                return Shop.from_dict(shop_doc)
            return None
            
        except Exception as e:
            logger.error(f"获取商店时发生错误: {str(e)}")
            return None
    
    async def get_shops_by_type(self, shop_type: str, is_active: bool = True) -> List[Shop]:
        """根据类型获取商店列表"""
        try:
            db = await self._get_db()
            collection = db[self.shops_collection]
            
            query = {"shop_type": shop_type}
            if is_active is not None:
                query["is_active"] = is_active
            
            cursor = collection.find(query).sort("sort_order", 1)
            shops = []
            
            async for shop_doc in cursor:
                shop = Shop.from_dict(shop_doc)
                shops.append(shop)
            
            return shops
            
        except Exception as e:
            logger.error(f"获取商店列表时发生错误: {str(e)}")
            return []
    
    async def update_shop(self, shop: Shop) -> bool:
        """更新商店"""
        try:
            db = await self._get_db()
            collection = db[self.shops_collection]
            
            shop.updated_at = datetime.now()
            shop_data = shop.to_dict()
            
            result = await collection.update_one(
                {"shop_id": shop.shop_id},
                {"$set": shop_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新商店时发生错误: {str(e)}")
            return False
    
    # ==================== 商品配置操作 ====================
    
    def generate_config_id(self, shop_id: str, item_template_id: str) -> str:
        """生成配置ID"""
        return f"{shop_id}:{item_template_id}:{uuid.uuid4().hex[:8]}"
    
    async def create_item_config(self, config: ShopItemConfig) -> bool:
        """创建商品配置"""
        try:
            db = await self._get_db()
            collection = db[self.item_configs_collection]
            
            config_data = config.to_dict()
            result = await collection.insert_one(config_data)
            
            if result.inserted_id:
                logger.info(f"商品配置创建成功: {config.config_id}")
                return True
            else:
                logger.error(f"商品配置创建失败: {config.config_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建商品配置时发生错误: {str(e)}")
            return False
    
    async def get_shop_item_configs(self, shop_id: str, is_active: bool = True) -> List[ShopItemConfig]:
        """获取商店商品配置列表"""
        try:
            db = await self._get_db()
            collection = db[self.item_configs_collection]
            
            query = {"shop_id": shop_id}
            if is_active is not None:
                query["is_active"] = is_active
            
            cursor = collection.find(query).sort("sort_order", 1)
            configs = []
            
            async for config_doc in cursor:
                config = ShopItemConfig.from_dict(config_doc)
                configs.append(config)
            
            return configs
            
        except Exception as e:
            logger.error(f"获取商店商品配置时发生错误: {str(e)}")
            return []
    
    async def get_item_config(self, config_id: str) -> Optional[ShopItemConfig]:
        """获取商品配置"""
        try:
            db = await self._get_db()
            collection = db[self.item_configs_collection]
            
            config_doc = await collection.find_one({"config_id": config_id})
            if config_doc:
                return ShopItemConfig.from_dict(config_doc)
            return None
            
        except Exception as e:
            logger.error(f"获取商品配置时发生错误: {str(e)}")
            return None
    
    async def update_item_config(self, config: ShopItemConfig) -> bool:
        """更新商品配置"""
        try:
            db = await self._get_db()
            collection = db[self.item_configs_collection]
            
            config.updated_at = datetime.now()
            config_data = config.to_dict()
            
            result = await collection.update_one(
                {"config_id": config.config_id},
                {"$set": config_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"更新商品配置时发生错误: {str(e)}")
            return False
    
    # ==================== 购买记录操作 ====================
    
    def generate_purchase_id(self) -> str:
        """生成购买ID"""
        return f"purchase_{uuid.uuid4().hex[:16]}"
    
    async def create_purchase(self, purchase: ShopPurchase) -> bool:
        """创建购买记录"""
        try:
            db = await self._get_db()
            collection = db[self.purchases_collection]
            
            purchase_data = purchase.to_dict()
            result = await collection.insert_one(purchase_data)
            
            if result.inserted_id:
                logger.info(f"购买记录创建成功: {purchase.purchase_id}")
                return True
            else:
                logger.error(f"购买记录创建失败: {purchase.purchase_id}")
                return False
                
        except Exception as e:
            logger.error(f"创建购买记录时发生错误: {str(e)}")
            return False
    
    async def get_player_purchases(self, player_id: str, limit: int = 50, offset: int = 0) -> List[ShopPurchase]:
        """获取玩家购买记录"""
        try:
            db = await self._get_db()
            collection = db[self.purchases_collection]
            
            cursor = collection.find({"player_id": player_id}).sort("purchase_time", -1).skip(offset).limit(limit)
            purchases = []
            
            async for purchase_doc in cursor:
                purchase = ShopPurchase.from_dict(purchase_doc)
                purchases.append(purchase)
            
            return purchases
            
        except Exception as e:
            logger.error(f"获取玩家购买记录时发生错误: {str(e)}")
            return []

    # ==================== 限购计数器操作 ====================

    def generate_counter_id(self, player_id: str, scope_value: str, limit_type: str) -> str:
        """生成计数器ID"""
        return f"{player_id}:{scope_value}:{limit_type}"

    async def get_limit_counter(self, player_id: str, scope_value: str, limit_type: str) -> Optional[PurchaseLimitCounter]:
        """获取限购计数器"""
        try:
            db = await self._get_db()
            collection = db[self.limit_counters_collection]

            counter_id = self.generate_counter_id(player_id, scope_value, limit_type)
            counter_doc = await collection.find_one({"counter_id": counter_id})

            if counter_doc:
                return PurchaseLimitCounter.from_dict(counter_doc)
            return None

        except Exception as e:
            logger.error(f"获取限购计数器时发生错误: {str(e)}")
            return None

    async def create_or_update_counter(self, counter: PurchaseLimitCounter) -> bool:
        """创建或更新限购计数器"""
        try:
            db = await self._get_db()
            collection = db[self.limit_counters_collection]

            counter.last_updated = datetime.now()
            counter_data = counter.to_dict()

            result = await collection.update_one(
                {"counter_id": counter.counter_id},
                {"$set": counter_data},
                upsert=True
            )

            return result.acknowledged

        except Exception as e:
            logger.error(f"创建或更新限购计数器时发生错误: {str(e)}")
            return False

    async def increment_counter(self, player_id: str, scope_value: str, limit_type: str,
                              increment: int, limit_count: int, period_start: datetime,
                              period_end: datetime) -> Optional[int]:
        """增加限购计数"""
        try:
            db = await self._get_db()
            collection = db[self.limit_counters_collection]

            counter_id = self.generate_counter_id(player_id, scope_value, limit_type)
            now = datetime.now()

            # 使用 findAndModify 原子性更新
            result = await collection.find_one_and_update(
                {"counter_id": counter_id},
                {
                    "$inc": {"current_count": increment},
                    "$set": {
                        "last_purchase_time": now,
                        "last_updated": now
                    },
                    "$setOnInsert": {
                        "player_id": player_id,
                        "limit_scope": "item",  # 默认为商品级别限购
                        "scope_value": scope_value,
                        "limit_type": limit_type,
                        "period_start": period_start,
                        "period_end": period_end,
                        "limit_count": limit_count,
                        "created_at": now
                    }
                },
                upsert=True,
                return_document=True
            )

            if result:
                return result["current_count"]
            return None

        except Exception as e:
            logger.error(f"增加限购计数时发生错误: {str(e)}")
            return None

    async def get_player_counters(self, player_id: str, limit_type: str) -> List[PurchaseLimitCounter]:
        """获取玩家的所有限购计数器"""
        try:
            db = await self._get_db()
            collection = db[self.limit_counters_collection]

            cursor = collection.find({
                "player_id": player_id,
                "limit_type": limit_type
            })

            counters = []
            async for counter_doc in cursor:
                counter = PurchaseLimitCounter.from_dict(counter_doc)
                counters.append(counter)

            return counters

        except Exception as e:
            logger.error(f"获取玩家限购计数器时发生错误: {str(e)}")
            return []

    async def cleanup_expired_counters(self, limit_type: str) -> int:
        """清理过期的限购计数器"""
        try:
            db = await self._get_db()
            collection = db[self.limit_counters_collection]

            now = datetime.now()

            result = await collection.delete_many({
                "limit_type": limit_type,
                "period_end": {"$lt": now}
            })

            logger.info(f"清理过期 {limit_type} 限购计数器: {result.deleted_count} 个")
            return result.deleted_count

        except Exception as e:
            logger.error(f"清理过期限购计数器时发生错误: {str(e)}")
            return 0

    # ==================== 折扣操作 ====================

    def generate_discount_id(self) -> str:
        """生成折扣ID"""
        return f"discount_{uuid.uuid4().hex[:12]}"

    async def create_discount(self, discount: ShopDiscount) -> bool:
        """创建折扣"""
        try:
            db = await self._get_db()
            collection = db[self.discounts_collection]

            discount_data = discount.to_dict()
            result = await collection.insert_one(discount_data)

            if result.inserted_id:
                logger.info(f"折扣创建成功: {discount.discount_id}")
                return True
            else:
                logger.error(f"折扣创建失败: {discount.discount_id}")
                return False

        except Exception as e:
            logger.error(f"创建折扣时发生错误: {str(e)}")
            return False

    async def get_applicable_discounts(self, scope_type: str, scope_value: str) -> List[ShopDiscount]:
        """获取适用的折扣"""
        try:
            db = await self._get_db()
            collection = db[self.discounts_collection]

            # 查询适用的折扣
            query = {
                "is_active": True,
                "$or": [
                    {"scope_type": "global"},
                    {
                        "scope_type": scope_type,
                        "scope_values": {"$in": [scope_value]}
                    }
                ]
            }

            cursor = collection.find(query).sort("priority", -1)
            discounts = []

            async for discount_doc in cursor:
                discount = ShopDiscount.from_dict(discount_doc)
                discounts.append(discount)

            return discounts

        except Exception as e:
            logger.error(f"获取适用折扣时发生错误: {str(e)}")
            return []

    # ==================== 统计操作 ====================

    async def get_shop_sales_stats(self, shop_id: str, days: int = 7) -> Dict[str, Any]:
        """获取商店销售统计"""
        try:
            db = await self._get_db()
            collection = db[self.purchases_collection]

            start_time = datetime.now() - timedelta(days=days)

            pipeline = [
                {
                    "$match": {
                        "shop_id": shop_id,
                        "purchase_time": {"$gte": start_time}
                    }
                },
                {
                    "$group": {
                        "_id": "$config_id",
                        "total_sales": {"$sum": "$item_quantity"},
                        "total_revenue": {"$sum": "$final_price"},
                        "purchase_count": {"$sum": 1}
                    }
                },
                {
                    "$sort": {"total_sales": -1}
                }
            ]

            cursor = collection.aggregate(pipeline)
            stats = []

            async for stat in cursor:
                stats.append(stat)

            return {
                "shop_id": shop_id,
                "period_days": days,
                "items": stats
            }

        except Exception as e:
            logger.error(f"获取商店销售统计时发生错误: {str(e)}")
            return {}
