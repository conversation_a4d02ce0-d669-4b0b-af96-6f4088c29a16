"""
公会成员管理服务
处理公会成员相关的业务逻辑
"""

import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from guild_models import (
    GuildMember, GuildApplication, GuildResponse, GuildPosition,
    ApplyGuildRequest, ProcessApplicationRequest, ChangeMemberPositionRequest,
    ApplicationStatus
)
from guild_manager import GuildDatabaseManager
from guild_cache_manager import GuildCacheManager
from guild_permissions import (
    GuildPermission, PermissionChecker, validate_guild_operation,
    validate_member_management, validate_position_change,
    GuildPermissionError, InsufficientPermissionError
)
from service_locator import ServiceLocator

logger = logging.getLogger(__name__)


class GuildMemberService:
    """公会成员管理服务"""
    
    def __init__(self):
        self.db_manager = GuildDatabaseManager()
        self.cache_manager = GuildCacheManager()

    # ==================== 内部通知方法 ====================

    async def _send_guild_notification(self, player_id: str, guild_info: dict = None, notification_type: str = "guild_info_update") -> bool:
        """发送公会通知给单个玩家"""
        try:
            connection_manager = ServiceLocator.get("conn_manager")
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False

            message = {
                "msgId": "GUILD_NOTIFICATION",
                "success": True,
                "data": {
                    "type": notification_type,
                    "guild_info": guild_info,
                    "timestamp": datetime.now().isoformat()
                }
            }

            return await connection_manager.send_personal_message_to_user(message, player_id)

        except Exception as e:
            logger.error(f"发送公会通知失败: {player_id}, 错误: {str(e)}")
            return False

    async def _send_guild_notifications_to_members(self, member_ids: list, guild_info: dict = None, notification_type: str = "guild_info_update") -> dict:
        """发送公会通知给多个成员"""
        results = {}
        for player_id in member_ids:
            results[player_id] = await self._send_guild_notification(player_id, guild_info, notification_type)
        return results

    # ==================== 公会申请相关 ====================
    
    async def apply_to_guild(self, player_id: str, player_name: str, request: ApplyGuildRequest) -> GuildResponse:
        """申请加入公会"""
        try:
            # 检查玩家是否已在公会中
            existing_guild = await self._get_player_guild_id(player_id)
            if existing_guild:
                return GuildResponse(
                    success=False,
                    error="您已经在其他公会中"
                )
            
            # 检查公会是否存在
            guild = await self.db_manager.get_guild_by_id(request.guild_id)
            if not guild:
                return GuildResponse(
                    success=False,
                    error="公会不存在"
                )
            
            # 检查公会是否已满员
            if guild.member_count >= guild.max_members:
                return GuildResponse(
                    success=False,
                    error="公会已满员"
                )
            
            # 检查是否已有待处理的申请
            existing_apps = await self.db_manager.get_player_applications(player_id)
            for app in existing_apps:
                if app.guild_id == request.guild_id and app.status == ApplicationStatus.PENDING:
                    return GuildResponse(
                        success=False,
                        error="您已经申请过该公会，请等待审核"
                    )
            
            # 使用分布式锁
            async with await self.cache_manager.acquire_member_lock(player_id, "apply"):
                # 创建申请记录
                application_id = self.db_manager.generate_application_id()
                application = GuildApplication(
                    application_id=application_id,
                    guild_id=request.guild_id,
                    player_id=player_id,
                    player_name=player_name,
                    message=request.message,
                    applied_at=datetime.now()
                )
                
                if await self.db_manager.create_application(application):
                    logger.info(f"公会申请创建成功: {player_id} -> {request.guild_id}")
                    
                    return GuildResponse(
                        success=True,
                        message="申请已提交，请等待审核",
                        data={"application": application.to_dict()}
                    )
                else:
                    return GuildResponse(
                        success=False,
                        error="提交申请失败，请稍后重试"
                    )
                    
        except Exception as e:
            logger.error(f"申请加入公会时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="申请加入公会时发生内部错误"
            )

    async def process_application(self, guild_id: str, processor_id: str, 
                                request: ProcessApplicationRequest) -> GuildResponse:
        """处理公会申请"""
        try:
            # 检查处理者权限
            processor = await self._get_member_info(guild_id, processor_id)
            if not processor:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            # 验证权限
            try:
                validate_guild_operation(processor.position, GuildPermission.APPROVE_APPLICATION)
            except GuildPermissionError as e:
                return GuildResponse(
                    success=False,
                    error=str(e)
                )
            
            # 获取申请信息
            application = await self.db_manager.get_application(request.application_id)
            if not application:
                return GuildResponse(
                    success=False,
                    error="申请不存在"
                )
            
            if application.guild_id != guild_id:
                return GuildResponse(
                    success=False,
                    error="申请不属于该公会"
                )
            
            if application.status != ApplicationStatus.PENDING:
                return GuildResponse(
                    success=False,
                    error="申请已被处理"
                )
            
            # 使用分布式锁
            async with await self.cache_manager.acquire_guild_lock(guild_id, "process_application"):
                # 处理申请
                if request.action == "approve":
                    # 检查公会是否还有空位
                    guild = await self.db_manager.get_guild_by_id(guild_id)
                    if guild.member_count >= guild.max_members:
                        return GuildResponse(
                            success=False,
                            error="公会已满员，无法通过申请"
                        )
                    
                    # 检查申请者是否已在其他公会
                    existing_guild = await self._get_player_guild_id(application.player_id)
                    if existing_guild:
                        # 更新申请状态为拒绝
                        await self.db_manager.process_application(
                            request.application_id,
                            ApplicationStatus.REJECTED,
                            processor_id,
                            "申请者已加入其他公会"
                        )
                        return GuildResponse(
                            success=False,
                            error="申请者已加入其他公会"
                        )
                    
                    # 添加成员
                    new_member = GuildMember(
                        guild_id=guild_id,
                        player_id=application.player_id,
                        player_name=application.player_name,
                        position=GuildPosition.MEMBER,
                        joined_at=datetime.now(),
                        last_active=datetime.now(),
                        player_level=application.player_level,
                        player_power=application.player_power
                    )
                    
                    if await self.db_manager.add_member(new_member):
                        # 更新申请状态
                        await self.db_manager.process_application(
                            request.application_id,
                            ApplicationStatus.APPROVED,
                            processor_id,
                            request.reason
                        )

                        # 清除相关缓存
                        await self.cache_manager.invalidate_member_cache(guild_id)
                        await self.cache_manager.cache_player_guild(application.player_id, guild_id)

                        # 推送公会信息给新成员
                        await self._send_guild_notification(
                            application.player_id, guild.to_dict()
                        )

                        # 通知其他成员有新成员加入
                        try:
                            existing_members = await self.db_manager.get_guild_members(guild_id)
                            member_ids = [m.player_id for m in existing_members if m.player_id != application.player_id]
                            if member_ids:
                                # 获取更新后的公会信息（包含新的成员数量）
                                updated_guild = await self.db_manager.get_guild_by_id(guild_id)
                                if updated_guild:
                                    await self._send_guild_notifications_to_members(
                                        member_ids, updated_guild.to_dict()
                                    )
                                    logger.debug(f"已通知 {len(member_ids)} 名成员有新成员加入")
                        except Exception as e:
                            logger.error(f"通知其他成员新成员加入失败: {str(e)}")

                        logger.info(f"公会申请通过: {application.player_id} -> {guild_id}")

                        return GuildResponse(
                            success=True,
                            message="申请已通过，成员已加入公会"
                        )
                    else:
                        return GuildResponse(
                            success=False,
                            error="添加成员失败"
                        )
                
                elif request.action == "reject":
                    # 拒绝申请
                    if await self.db_manager.process_application(
                        request.application_id,
                        ApplicationStatus.REJECTED,
                        processor_id,
                        request.reason
                    ):
                        logger.info(f"公会申请拒绝: {application.player_id} -> {guild_id}")
                        
                        return GuildResponse(
                            success=True,
                            message="申请已拒绝"
                        )
                    else:
                        return GuildResponse(
                            success=False,
                            error="处理申请失败"
                        )
                
                else:
                    return GuildResponse(
                        success=False,
                        error="无效的操作类型"
                    )
                    
        except Exception as e:
            logger.error(f"处理公会申请时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="处理公会申请时发生内部错误"
            )

    async def get_guild_applications(self, guild_id: str, player_id: str) -> GuildResponse:
        """获取公会申请列表"""
        try:
            # 检查权限
            member = await self._get_member_info(guild_id, player_id)
            if not member:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            try:
                validate_guild_operation(member.position, GuildPermission.APPROVE_APPLICATION)
            except GuildPermissionError as e:
                return GuildResponse(
                    success=False,
                    error=str(e)
                )
            
            # 获取待处理申请
            applications = await self.db_manager.get_guild_applications(guild_id, ApplicationStatus.PENDING)
            
            return GuildResponse(
                success=True,
                data={
                    "applications": [app.to_dict() for app in applications],
                    "count": len(applications)
                }
            )
            
        except Exception as e:
            logger.error(f"获取公会申请列表时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="获取公会申请列表时发生内部错误"
            )

    # ==================== 成员管理相关 ====================
    
    async def get_guild_members(self, guild_id: str, player_id: str = None) -> GuildResponse:
        """获取公会成员列表"""
        try:
            # 如果指定了玩家ID，检查权限
            if player_id:
                member = await self._get_member_info(guild_id, player_id)
                if not member:
                    return GuildResponse(
                        success=False,
                        error="您不是该公会成员"
                    )
            
            # 先从缓存获取
            members = await self.cache_manager.get_cached_member_list(guild_id)
            
            # 缓存未命中，从数据库获取
            if not members:
                members = await self.db_manager.get_guild_members(guild_id)
                if members:
                    # 缓存结果
                    await self.cache_manager.cache_member_list(guild_id, members)
            
            # 获取在线状态
            online_members = await self.cache_manager.get_online_members(guild_id)
            
            # 添加在线状态信息
            members_data = []
            for member in members:
                member_dict = member.to_dict()
                member_dict["is_online"] = member.player_id in online_members
                members_data.append(member_dict)
            
            return GuildResponse(
                success=True,
                data={
                    "members": members_data,
                    "count": len(members_data),
                    "online_count": len(online_members)
                }
            )
            
        except Exception as e:
            logger.error(f"获取公会成员列表时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="获取公会成员列表时发生内部错误"
            )

    async def remove_member(self, guild_id: str, remover_id: str, target_player_id: str) -> GuildResponse:
        """移除公会成员"""
        try:
            # 检查操作者权限
            remover = await self._get_member_info(guild_id, remover_id)
            if not remover:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            # 检查目标成员
            target_member = await self._get_member_info(guild_id, target_player_id)
            if not target_member:
                return GuildResponse(
                    success=False,
                    error="目标成员不存在"
                )
            
            # 不能移除自己
            if remover_id == target_player_id:
                return GuildResponse(
                    success=False,
                    error="不能移除自己"
                )
            
            # 验证权限
            try:
                validate_member_management(remover.position, target_member.position, "remove")
            except GuildPermissionError as e:
                return GuildResponse(
                    success=False,
                    error=str(e)
                )
            
            # 使用分布式锁
            async with await self.cache_manager.acquire_guild_lock(guild_id, "remove_member"):
                if await self.db_manager.remove_member(guild_id, target_player_id):
                    # 清除相关缓存
                    await self.cache_manager.invalidate_member_cache(guild_id, target_player_id)

                    # 获取公会信息用于通知
                    guild = await self.db_manager.get_guild_by_id(guild_id)

                    # 推送被移除通知
                    await self._send_guild_notification(target_player_id, None, "guild_removed")

                    # 通知其他成员成员数量变化
                    try:
                        remaining_members = await self.db_manager.get_guild_members(guild_id)
                        member_ids = [m.player_id for m in remaining_members if m.player_id != target_player_id]
                        if member_ids and guild:
                            await self._send_guild_notifications_to_members(
                                member_ids, guild.to_dict()
                            )
                            logger.debug(f"已通知 {len(member_ids)} 名成员公会信息变化")
                    except Exception as e:
                        logger.error(f"通知其他成员公会信息变化失败: {str(e)}")

                    logger.info(f"公会成员移除成功: {guild_id} - {target_player_id} by {remover_id}")

                    return GuildResponse(
                        success=True,
                        message="成员已被移除"
                    )
                else:
                    return GuildResponse(
                        success=False,
                        error="移除成员失败"
                    )
                    
        except Exception as e:
            logger.error(f"移除公会成员时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="移除公会成员时发生内部错误"
            )

    async def leave_guild(self, guild_id: str, player_id: str) -> GuildResponse:
        """离开公会"""
        try:
            # 检查成员信息
            member = await self._get_member_info(guild_id, player_id)
            if not member:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            # 会长不能直接离开公会，需要先转让会长职位或解散公会
            if member.position == GuildPosition.LEADER:
                return GuildResponse(
                    success=False,
                    error="会长不能直接离开公会，请先转让会长职位或解散公会"
                )
            
            # 使用分布式锁
            async with await self.cache_manager.acquire_member_lock(player_id, "leave"):
                if await self.db_manager.remove_member(guild_id, player_id):
                    # 清除相关缓存
                    await self.cache_manager.invalidate_member_cache(guild_id, player_id)

                    # 获取公会信息用于通知
                    guild = await self.db_manager.get_guild_by_id(guild_id)

                    # 推送离开公会通知
                    await self._send_guild_notification(player_id, None, "guild_left")

                    # 通知其他成员成员数量变化
                    try:
                        remaining_members = await self.db_manager.get_guild_members(guild_id)
                        member_ids = [m.player_id for m in remaining_members if m.player_id != player_id]
                        if member_ids and guild:
                            await self._send_guild_notifications_to_members(
                                member_ids, guild.to_dict()
                            )
                            logger.debug(f"已通知 {len(member_ids)} 名成员公会信息变化")
                    except Exception as e:
                        logger.error(f"通知其他成员公会信息变化失败: {str(e)}")

                    logger.info(f"成员离开公会: {guild_id} - {player_id}")

                    return GuildResponse(
                        success=True,
                        message="已离开公会"
                    )
                else:
                    return GuildResponse(
                        success=False,
                        error="离开公会失败"
                    )
                    
        except Exception as e:
            logger.error(f"离开公会时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="离开公会时发生内部错误"
            )

    # ==================== 辅助方法 ====================
    
    async def _get_player_guild_id(self, player_id: str) -> Optional[str]:
        """获取玩家所在的公会ID"""
        try:
            # 先从缓存获取
            guild_id = await self.cache_manager.get_cached_player_guild(player_id)
            
            # 缓存未命中，从数据库获取
            if not guild_id:
                guild_id = await self.db_manager.get_player_guild(player_id)
                if guild_id:
                    # 缓存结果
                    await self.cache_manager.cache_player_guild(player_id, guild_id)
            
            return guild_id
            
        except Exception as e:
            logger.error(f"获取玩家公会ID时发生错误: {str(e)}")
            return None

    async def _get_member_info(self, guild_id: str, player_id: str) -> Optional[GuildMember]:
        """获取成员信息"""
        try:
            # 先从缓存获取
            member = await self.cache_manager.get_cached_member_info(guild_id, player_id)
            
            # 缓存未命中，从数据库获取
            if not member:
                member = await self.db_manager.get_member(guild_id, player_id)
                if member:
                    # 缓存结果
                    await self.cache_manager.cache_member_info(member)
            
            return member
            
        except Exception as e:
            logger.error(f"获取成员信息时发生错误: {str(e)}")
            return None
