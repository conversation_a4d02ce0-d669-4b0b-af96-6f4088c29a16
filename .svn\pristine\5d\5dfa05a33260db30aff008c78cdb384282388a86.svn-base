from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.websockets import WebSocketState
from typing import Dict, List, Callable
import logging
import json
import asyncio
import traceback
import os
from logger_config import setup_logger
from auth import UserRegister, UserLogin, register_user, login_user, logout_user, get_current_user, router as auth_router
from models import ResponseModel, MessageModel
from msgManager import MessageManager
from ConnectionManager import ConnectionManager
from config import config
from utils import handle_error
from enums import MessageId
import websocket_handlers  # 导入 WebSocket 处理器
from datetime import datetime
from service_locator import ServiceLocator
from task_queue import TaskQueue
from distributed_task import distributed_task
from scheduler_manager import SchedulerManager
from game_server_scheduler_integration import (
    initialize_game_server_scheduler,
    shutdown_game_server_scheduler,
    register_tasksQueue
)
from UserCacheManager import UserCacheManager
# Import monster cooldown manager
from xxsg.monster_cooldown import Monster<PERSON><PERSON>downManager
from equipment_service_distributed import DistributedEquipmentService
# 初始化日志系统
logger = setup_logger(__name__)

# 创建应用
app = FastAPI(title="Game Server API", version="2.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 实例化连接管理器
connection_manager = ConnectionManager()

# 实例化消息管理器
msg_manager = MessageManager()


# 实例化装备服务
distributed_equipment_service = DistributedEquipmentService()


# 实例化任务队列
task_queue = TaskQueue()

# 注册服务
ServiceLocator.register("conn_manager", connection_manager)
ServiceLocator.register("task_queue", task_queue)
ServiceLocator.register("distributed_equipment_service", distributed_equipment_service)
# 路由器
app.include_router(auth_router)


@app.get("/userinfo")
async def get_user_info(request: Request):
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="缺少或无效的 Authorization 头")
        token = auth_header.split(" ")[1]
        username = await get_current_user(token)
        user_cache = await UserCacheManager.get_instance()
        user = await user_cache.get_user_by_username(username)
        if not user:
            raise HTTPException(status_code=404, detail="用户未找到")        
        user_data = user.serialize(exclude_fields=["password"])
        user_data.pop("_id", None)        
        logger.info(f"获取用户信息，用户 {username}，返回字段: {list(user_data.keys())}")
        return JSONResponse(
            status_code=200,
            content=ResponseModel(
                success=True,
                code=200,
                data=user_data
            ).model_dump(),
            headers={"Content-Type": "application/json"}
        )
    except HTTPException:
        raise
    except Exception as e:
        return await handle_error("获取用户信息失败", 500, exception=e)

# WebSocket 事件处理
@app.websocket("/ws/{token}")
async def websocket_endpoint(websocket: WebSocket, token: str):
    logger.info(f"WebSocket 连接尝试，token: {token[:10]}..., worker: {os.getpid()}")
    username = None
    try:
        try:
            username = await get_current_user(token)
        except HTTPException as e:
            logger.error(f"Token 验证失败，token: {token[:10]}...: {e.detail}")
            if websocket.application_state != WebSocketState.DISCONNECTED:
                try:
                    await websocket.close(code=1008, reason=f"认证失败: {e.detail}")
                except Exception as close_e:
                    logger.warning(f"关闭 WebSocket 失败，token: {token[:10]}..., 错误: {str(close_e)}")
            return
        
        # 连接到WebSocket
        await connection_manager.connect(websocket, token, username)

        try:
            while True:
                if websocket.application_state == WebSocketState.CONNECTED:
                    data = await websocket.receive_text()
                    logger.debug(f"收到消息，用户: {username}, token: {token[:10]}, 数据: {data}")
                    try:
                        message = json.loads(data)
                        msgId = message.get("msgId")
                        message_data = message.get("data", {})
                        if msgId is not None and isinstance(msgId, int):
                            start_time = asyncio.get_event_loop().time()      
                            await msg_manager.dispatch_message(msgId, message_data, websocket, username, token, connection_manager)
                            end_time = asyncio.get_event_loop().time()
                            logger.debug(f"处理 msgId {msgId} 耗时: {(end_time - start_time) * 1000:.2f} 毫秒，用户: {username}, worker: {os.getpid()}")
                        else:
                            await connection_manager.send_personal_message(MessageModel(
                                msgId=MessageId.ERROR,
                                data={"error": "无效的消息 ID"}
                            ).model_dump(), token)
                    except json.JSONDecodeError as e:
                        logger.error(f"消息格式无效: {str(e)}")
                        await connection_manager.send_personal_message(MessageModel(
                            msgId=MessageId.ERROR,
                            data={"error": "消息格式无效"}
                        ).model_dump(), token)
                else:
                    logger.warning(f"WebSocket 状态异常，用户: {username}, token: {token[:10]}...，状态: {websocket.application_state}")
                    await connection_manager.disconnect(token)
                    break
        except WebSocketDisconnect as e:
            logger.info(f"WebSocket 断开，用户: {username}, token: {token[:10]}..., 代码: {e.code}, 原因: {e.reason}, worker: {os.getpid()}")
            await connection_manager.disconnect(token)
        except RuntimeError as e:
            logger.error(f"WebSocket 运行时错误，用户: {username}, token: {token[:10]}...: {str(e)}")
            await connection_manager.disconnect(token)
    except Exception as e:
        logger.error(f"意外的 WebSocket 错误，用户: {username or '未知'}, token: {token[:10]}...: {str(e)}, 类型: {type(e).__name__}")
        logger.error(traceback.format_exc())
        if username and token in connection_manager.active_connections:
            await connection_manager.disconnect(token)
        if websocket.application_state != WebSocketState.DISCONNECTED:
            try:
                await websocket.close(code=1008, reason=f"内部服务器错误: {str(e)}")
            except Exception as close_e:
                logger.warning(f"关闭 WebSocket 失败，token: {token[:10]}..., 错误: {str(close_e)}")
    finally:
        if username and token in connection_manager.active_connections:
            try:
                await connection_manager.disconnect(token)
                logger.info(f"确保用户 {username} 断开连接，token: {token[:10]}..., worker: {os.getpid()}")
            except Exception as e:
                logger.error(f"最终断开连接失败，用户: {username}, token: {token[:10]}..., 错误: {str(e)}")
# 启动事件
@app.on_event("startup")
async def startup_event():
    try:
        logger.info(f"启动服务器，初始化数据库和连接管理器... (Worker: {os.getpid()})")
        await connection_manager.initialize()
        
        # 加载游戏配置
        config.get_item_config()
        # 确保加载怪物配置
        config.get_monster_config()
        logger.info(f"游戏配置加载完成 (Worker: {os.getpid()})")
        
        # 初始化分布式装备服务
        await distributed_equipment_service.initialize()

        # 启动配置监控
        asyncio.create_task(config.monitor_game_configs())
        # 注册调度器监控API
        try:
            from simple_scheduler_api import scheduler_router
            app.include_router(scheduler_router)
            logger.info("调度器监控API路由已注册")
        except Exception as e:
            logger.error(f"注册调度器监控API失败: {str(e)}")
            logger.error(traceback.format_exc())

        # 初始化统一调度器
        scheduler_success = await initialize_game_server_scheduler()
        if scheduler_success:
            logger.info(f"统一调度器初始化成功 (Worker: {os.getpid()})")
        else:
            logger.error(f"统一调度器初始化失败 (Worker: {os.getpid()})")

        # 注册任务队列（兼容性）
        await register_tasksQueue()

        # 启动日志清理管理器
        try:
            from LogCleanupManager import get_log_cleanup_manager
            log_cleanup_manager = await get_log_cleanup_manager()
            await log_cleanup_manager.start_cleanup_task()
            logger.info(f"日志清理管理器已启动 (Worker: {os.getpid()})")
        except Exception as e:
            logger.error(f"启动日志清理管理器失败: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 初始化怪物冷却管理器
        try:
            monster_cooldown_manager = MonsterCooldownManager()
            ServiceLocator.register("monster_cooldown_manager", monster_cooldown_manager)
            logger.info(f"Monster cooldown manager initialized (Worker: {os.getpid()})")
            
            # 恢复冷却数据
            await monster_cooldown_manager.restore_from_mongodb()
        except Exception as e:
            logger.error(f"Failed to initialize monster cooldown manager: {str(e)}")
            logger.error(traceback.format_exc())

        logger.info(f"初始化完成 (Worker: {os.getpid()})")
    except Exception as e:
        logger.error(f"启动时初始化失败: {str(e)}")
        logger.error(traceback.format_exc())
        logger.warning("初始化失败，服务器将继续运行，但可能影响功能")
async def register_tasksQueue():
    handlers = {
            "reset_daily": do_reset_daily,
            "push_online_count": do_push_online_count,
            "monster_cooldown_persist_cooldowns": monster_cooldown_persist_cooldowns,
            "monster_cooldown_notify_expired_cooldowns": monster_cooldown_notify_expired_cooldowns
        }
    asyncio.create_task(task_queue.consume_tasks(handlers))
    
# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info(f"关闭服务器... (Worker: {os.getpid()})")
    try:
        # 关闭怪物冷却管理器
        monster_cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
        if monster_cooldown_manager:
            await monster_cooldown_manager.shutdown()
            logger.info("怪物冷却管理器已关闭")
            
        # 停止日志清理管理器
        try:
            from LogCleanupManager import get_log_cleanup_manager
            log_cleanup_manager = await get_log_cleanup_manager()
            await log_cleanup_manager.stop_cleanup_task()
            logger.info("日志清理管理器已停止")
        except Exception as e:
            logger.error(f"停止日志清理管理器失败: {str(e)}")

        # 关闭统一调度器
        await shutdown_game_server_scheduler()
        logger.info("统一调度器已关闭")

        await connection_manager.cleanup()
        await task_queue.close()
        logger.info(f"服务器资源已清理 (Worker: {os.getpid()})")
    except Exception as e:
        logger.error(f"关闭时清理资源失败: {str(e)}")
        logger.error(traceback.format_exc())
#---------------------------------task---------------------------------
async def do_reset_daily(params):
    print(f"[worker] 执行每日重置")
    await connection_manager.broadcast(MessageModel(
        msgId=MessageId.CHAT,
        data={"content": "每日重置"}
    ).model_dump(), 0)
    print("[worker] 每日重置完成")    

async def do_push_online_count(params):
    print("[worker] 执行推送在线人数")
    await connection_manager.broadcast(MessageModel(
        msgId=MessageId.CHAT,
        data={"content": "每日重置"}
    ).model_dump(), 0)
    print("[worker] 在线人数推送完成")
async def monster_cooldown_persist_cooldowns(params):
    print("[worker] 执行怪物冷却持久化")
    monster_cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
    if monster_cooldown_manager:
        success = await monster_cooldown_manager.persist_to_mongodb()
        if success:
            print("[worker] 怪物冷却持久化完成")
        else:
            print("[worker] 怪物冷却持久化失败")
    else:
        print("[worker] 怪物冷却管理器不可用")
async def monster_cooldown_notify_expired_cooldowns(params):
    print("[worker] 执行怪物冷却通知")
    monster_cooldown_manager = ServiceLocator.get("monster_cooldown_manager")
    if monster_cooldown_manager:
        notified_count = await monster_cooldown_manager.notify_expired_cooldowns()
        print(f"[worker] 怪物冷却通知完成，已通知 {notified_count} 个怪物刷新")
    else:
        print("[worker] 怪物冷却管理器不可用")
#---------------------------------task---------------------------------
