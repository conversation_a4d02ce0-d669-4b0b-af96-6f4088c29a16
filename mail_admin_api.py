"""
邮件系统管理API
提供邮件管理的HTTP接口
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Form, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from mail_service_distributed import MailServiceDistributed
from mail_models import SystemMailRequest, BroadcastMailRequest, MailAttachmentData, AttachmentType
from UserCacheManager import UserCacheManager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/admin/mail", tags=["邮件管理"])

# 模板引擎
templates = Jinja2Templates(directory="templates")

# 邮件服务实例
mail_service = MailServiceDistributed()


class SendSystemMailRequest(BaseModel):
    """发送系统邮件请求"""
    receiver_type: str  # "username" 或 "nickname"
    receiver_value: str  # 用户名或昵称
    title: str
    content: str
    expire_days: int = 7
    attachments: Optional[List[dict]] = None


class SendBroadcastMailRequest(BaseModel):
    """发送广播邮件请求"""
    title: str
    content: str
    expire_days: int = 7
    target_type: str  # "all" 或 "specific"
    target_users: Optional[List[str]] = None
    attachments: Optional[List[dict]] = None


@router.get("/", response_class=HTMLResponse)
async def mail_admin_page(request: Request):
    """邮件管理页面"""
    return templates.TemplateResponse("mail_admin.html", {"request": request})


@router.post("/send_system_mail")
async def send_system_mail_api(request: SendSystemMailRequest):
    """发送系统邮件API"""
    try:
        # 根据类型查找用户ID
        user_cache = await UserCacheManager.get_instance()
        receiver_id = None
        
        if request.receiver_type == "username":
            # 直接使用用户名作为ID
            receiver_id = request.receiver_value
        elif request.receiver_type == "nickname":
            # 通过昵称查找用户ID
            # 这里需要实现通过昵称查找用户的逻辑
            # 暂时使用简单的方法，实际可能需要查询数据库
            try:
                user_data = await user_cache.get_user_by_username(request.receiver_value)
                if user_data and user_data.nickname == request.receiver_value:
                    receiver_id = user_data.username
                else:
                    # 如果通过用户名查找不到，可能需要查询数据库中所有用户
                    # 这里简化处理，直接返回错误
                    raise HTTPException(status_code=404, detail=f"未找到昵称为 '{request.receiver_value}' 的用户")
            except Exception:
                raise HTTPException(status_code=404, detail=f"未找到昵称为 '{request.receiver_value}' 的用户")
        else:
            raise HTTPException(status_code=400, detail="无效的接收者类型")
        
        if not receiver_id:
            raise HTTPException(status_code=404, detail="未找到指定用户")
        
        # 处理附件
        attachments = []
        if request.attachments:
            for attachment_data in request.attachments:
                attachment = MailAttachmentData(
                    attachment_type=AttachmentType.ITEM,  # 目前只支持道具
                    item_id=str(attachment_data.get("item_id", "")),
                    item_name=attachment_data.get("item_name", ""),
                    quantity=int(attachment_data.get("quantity", 1)),
                    quality=int(attachment_data.get("quality", 1))
                )
                attachments.append(attachment)
        
        # 创建系统邮件请求
        system_mail_request = SystemMailRequest(
            title=request.title,
            content=request.content,
            expire_days=request.expire_days,
            attachments=attachments if attachments else None
        )
        
        # 发送邮件
        result = await mail_service.send_system_mail(receiver_id, system_mail_request)
        
        if result.success:
            return {
                "success": True,
                "message": f"系统邮件发送成功给用户: {receiver_id}",
                "data": result.data
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送系统邮件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送邮件失败: {str(e)}")


@router.post("/send_broadcast_mail")
async def send_broadcast_mail_api(request: SendBroadcastMailRequest):
    """发送广播邮件API"""
    try:
        # 处理附件
        attachments = []
        if request.attachments:
            for attachment_data in request.attachments:
                attachment = MailAttachmentData(
                    attachment_type=AttachmentType.ITEM,  # 目前只支持道具
                    item_id=str(attachment_data.get("item_id", "")),
                    item_name=attachment_data.get("item_name", ""),
                    quantity=int(attachment_data.get("quantity", 1)),
                    quality=int(attachment_data.get("quality", 1))
                )
                attachments.append(attachment)
        
        # 处理目标用户
        target_players = None
        if request.target_type == "specific" and request.target_users:
            target_players = request.target_users
        
        # 创建广播邮件请求
        broadcast_request = BroadcastMailRequest(
            title=request.title,
            content=request.content,
            expire_days=request.expire_days,
            target_players=target_players,
            target_type=request.target_type,
            attachments=attachments if attachments else None
        )
        
        # 发送广播邮件
        result = await mail_service.send_broadcast_mail(broadcast_request)
        
        if result.success:
            return {
                "success": True,
                "message": "广播邮件发送成功",
                "data": result.data
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送广播邮件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送广播邮件失败: {str(e)}")


@router.get("/search_user/{query}")
async def search_user_api(query: str):
    """搜索用户API"""
    try:
        user_cache = await UserCacheManager.get_instance()
        
        # 尝试通过用户名查找
        try:
            user_data = await user_cache.get_user_by_username(query)
            if user_data:
                return {
                    "success": True,
                    "users": [{
                        "username": user_data.username,
                        "nickname": user_data.nickname or user_data.username,
                        "id": user_data.id
                    }]
                }
        except Exception:
            pass
        
        # 如果没找到，返回空结果
        return {
            "success": True,
            "users": []
        }
        
    except Exception as e:
        logger.error(f"搜索用户时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索用户失败: {str(e)}")


@router.get("/cleanup_expired")
async def cleanup_expired_mails_api():
    """清理过期邮件API"""
    try:
        result = await mail_service.cleanup_expired_mails()

        if result.success:
            return {
                "success": True,
                "message": f"清理完成，删除了 {result.data.get('deleted_count', 0)} 封过期邮件"
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理过期邮件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理过期邮件失败: {str(e)}")


@router.get("/cleanup_expired_templates")
async def cleanup_expired_templates_api():
    """清理过期邮件模板API"""
    try:
        result = await mail_service.cleanup_expired_templates()

        if result.success:
            return {
                "success": True,
                "message": f"清理完成，删除了 {result.data.get('deleted_count', 0)} 个过期模板"
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理过期邮件模板时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理过期邮件模板失败: {str(e)}")


@router.get("/cleanup_old_records")
async def cleanup_old_records_api():
    """清理旧的处理记录API"""
    try:
        result = await mail_service.cleanup_old_processed_records(30)

        if result.success:
            return {
                "success": True,
                "message": f"清理完成，删除了 {result.data.get('deleted_count', 0)} 条旧记录"
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理旧处理记录时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理旧处理记录失败: {str(e)}")


@router.get("/statistics")
async def get_mail_statistics_api():
    """获取邮件系统统计信息API"""
    try:
        result = await mail_service.get_mail_system_statistics()

        if result.success:
            return {
                "success": True,
                "data": result.data
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮件系统统计信息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取邮件系统统计信息失败: {str(e)}")


# 导出路由器
mail_admin_router = router
