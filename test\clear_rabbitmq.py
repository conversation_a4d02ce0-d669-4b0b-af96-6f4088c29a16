import requests
import json
from urllib.parse import quote
import sys

# 配置 RabbitMQ 管理 API 访问信息
RABBITMQ_HOST = "*************"
RABBITMQ_PORT = 15672
USERNAME = "admin"
PASSWORD = "P4e3Bcm2qTA0EawK"
VHOST = "bthost"  # 默认虚拟主机，需根据实际环境修改
API_BASE_URL = f"http://{RABBITMQ_HOST}:{RABBITMQ_PORT}/api"

# 默认交换器名称（不可删除）
DEFAULT_EXCHANGES = [
    "", "amq.direct", "amq.fanout", "amq.headers", "amq.match",
    "amq.rabbitmq.log", "amq.rabbitmq.trace", "amq.topic"
]

def get_auth_session():
    """创建带认证的 HTTP 会话"""
    session = requests.Session()
    session.auth = (USERNAME, PASSWORD)
    session.headers.update({"Content-Type": "application/json"})
    return session

def list_queues(session, vhost):
    """列出指定虚拟主机的所有队列"""
    url = f"{API_BASE_URL}/queues/{quote(vhost, safe='')}"
    response = session.get(url)
    if response.status_code != 200:
        print(f"Failed to list queues: {response.text}")
        return []
    return response.json()

def delete_queue(session, vhost, queue_name):
    """删除指定队列"""
    url = f"{API_BASE_URL}/queues/{quote(vhost, safe='')}/{quote(queue_name, safe='')}"
    response = session.delete(url)
    if response.status_code == 204:
        print(f"Deleted queue: {queue_name}")
    else:
        print(f"Failed to delete queue {queue_name}: {response.text}")

def list_exchanges(session, vhost):
    """列出指定虚拟主机的所有交换器"""
    url = f"{API_BASE_URL}/exchanges/{quote(vhost, safe='')}"
    response = session.get(url)
    if response.status_code != 200:
        print(f"Failed to list exchanges: {response.text}")
        return []
    return response.json()

def delete_exchange(session, vhost, exchange_name):
    """删除指定交换器"""
    if exchange_name in DEFAULT_EXCHANGES:
        print(f"Skipping default exchange: {exchange_name}")
        return
    url = f"{API_BASE_URL}/exchanges/{quote(vhost, safe='')}/{quote(exchange_name, safe='')}"
    response = session.delete(url)
    if response.status_code == 204:
        print(f"Deleted exchange: {exchange_name}")
    else:
        print(f"Failed to delete exchange {exchange_name}: {response.text}")

def list_connections(session):
    """列出所有连接"""
    url = f"{API_BASE_URL}/connections"
    response = session.get(url)
    if response.status_code != 200:
        print(f"Failed to list connections: {response.text}")
        return []
    return response.json()

def close_connection(session, connection_name):
    """关闭指定连接"""
    url = f"{API_BASE_URL}/connections/{quote(connection_name, safe='')}"
    response = session.delete(url)
    if response.status_code == 204:
        print(f"Closed connection: {connection_name}")
    else:
        print(f"Failed to close connection {connection_name}: {response.text}")

def clear_rabbitmq():
    """清除 RabbitMQ 中的所有数据"""
    session = get_auth_session()
    
    try:
        # 1. 删除所有队列
        print("Listing and deleting all queues...")
        queues = list_queues(session, VHOST)
        for queue in queues:
            delete_queue(session, VHOST, queue["name"])
        
        # 2. 删除所有非默认交换器
        print("\nListing and deleting all exchanges...")
        exchanges = list_exchanges(session, VHOST)
        for exchange in exchanges:
            delete_exchange(session, VHOST, exchange["name"])
        
        # 3. 关闭所有连接
        print("\nListing and closing all connections...")
        connections = list_connections(session)
        for connection in connections:
            # 跳过管理界面自身的连接
            if "http" not in connection["client_properties"].get("product", "").lower():
                close_connection(session, connection["name"])
        
        print("\nAll data cleared successfully!")
    
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        sys.exit(1)
    
    finally:
        session.close()

if __name__ == "__main__":
    print("WARNING: This script will DELETE ALL queues, exchanges, and connections in RabbitMQ!")
    confirm = input("Are you sure you want to proceed? (yes/no): ").strip().lower()
    if confirm == "yes":
        clear_rabbitmq()
    else:
        print("Operation cancelled.")