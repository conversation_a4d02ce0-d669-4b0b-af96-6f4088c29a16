import logging
import traceback
import asyncio
from typing import Callable, Tuple, Any, Dict, Optional
from logger_config import setup_logger

# 初始化日志系统
logger = setup_logger(__name__)

class TransactionManager:
    """
    MongoDB事务管理器
    提供事务支持检测和事务执行包装
    支持自动降级到非事务模式
    """
    
    def __init__(self, db_manager):
        """
        初始化事务管理器
        
        Args:
            db_manager: DatabaseManager实例，提供数据库连接
        """
        self.db = db_manager.db
        self.mongo_client = db_manager.mongo_client
        self.redis_client = db_manager.redis_client
        self.supports_transactions = False
        self.max_transaction_retries = 3
        self.transaction_retry_delay = 0.5  # 秒
        
    async def initialize(self):
        """初始化事务管理器并检查事务支持"""
        self.supports_transactions = await self._check_transaction_support()
        if self.supports_transactions:
            logger.info("MongoDB事务支持已启用")
        else:
            logger.warning("MongoDB不支持事务，将以非事务模式运行。如需事务支持，请配置MongoDB副本集。")
        
    async def _check_transaction_support(self) -> bool:
        """
        检查MongoDB是否支持事务(需要副本集)
        
        Returns:
            bool: 是否支持事务
        """
        try:
            # 获取MongoDB服务器状态
            server_status = await self.mongo_client.admin.command("serverStatus")
            
            # 检查是否为副本集
            is_replica_set = await self.mongo_client.admin.command("ismaster")
            
            # 检查MongoDB版本(4.0+支持事务)
            version = server_status.get("version", "")
            version_parts = version.split(".")
            major_version = int(version_parts[0]) if version_parts else 0
            
            # 副本集且版本>=4.0支持事务
            supports_tx = "setName" in is_replica_set and major_version >= 4
            
            logger.debug(f"MongoDB版本: {version}, 副本集: {'是' if 'setName' in is_replica_set else '否'}, 事务支持: {'是' if supports_tx else '否'}")
            
            return supports_tx
        except Exception as e:
            logger.error(f"检查MongoDB事务支持时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False
            
    async def run_transaction(self, operations_func: Callable) -> Tuple[bool, Dict[str, Any]]:
        """
        在事务中执行一系列操作
        
        Args:
            operations_func: 异步函数，接收session参数执行数据库操作
            
        Returns:
            Tuple[bool, Dict]: (成功标志, 结果字典)
        """
        if not self.supports_transactions:
            logger.debug("MongoDB不支持事务，使用非事务模式执行操作")
            return await self._run_without_transaction(operations_func)
        
        # 事务重试逻辑    
        for attempt in range(self.max_transaction_retries):
            try:
                async with await self.mongo_client.start_session() as session:
                    return await self._run_with_session(session, operations_func)
            except Exception as e:
                last_attempt = attempt == self.max_transaction_retries - 1
                error_type = type(e).__name__
                
                if "TransientTransactionError" in error_type and not last_attempt:
                    # 临时事务错误，可以重试
                    retry_delay = self.transaction_retry_delay * (2 ** attempt)  # 指数退避
                    logger.warning(f"事务执行遇到临时错误(将在{retry_delay:.1f}秒后重试): {str(e)}")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    # 其他错误或最后一次尝试失败
                    logger.error(f"事务执行失败(尝试 {attempt+1}/{self.max_transaction_retries}): {str(e)}")
                    logger.error(traceback.format_exc())
                    return False, {"error": f"事务执行失败: {error_type} - {str(e)}"}
                    
    async def _run_with_session(self, session, operations_func: Callable) -> Tuple[bool, Dict[str, Any]]:
        """
        使用提供的会话执行事务
        
        Args:
            session: MongoDB会话
            operations_func: 接收会话的操作函数
            
        Returns:
            Tuple[bool, Dict]: (成功标志, 结果字典)
        """
        try:
            async with session.start_transaction():
                # 在事务中执行操作
                start_time = asyncio.get_event_loop().time()
                result = await operations_func(session)
                end_time = asyncio.get_event_loop().time()
                
                logger.debug(f"事务操作耗时: {(end_time - start_time) * 1000:.2f} ms")
                return True, result
        except Exception as e:
            logger.error(f"事务内操作失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 事务会自动回滚
            return False, {"error": str(e)}
            
    async def _run_without_transaction(self, operations_func: Callable) -> Tuple[bool, Dict[str, Any]]:
        """
        无事务支持时的操作
        
        Args:
            operations_func: 接收None会话参数的操作函数
            
        Returns:
            Tuple[bool, Dict]: (成功标志, 结果字典)
        """
        try:
            # 调用操作函数，传入None表示无会话
            start_time = asyncio.get_event_loop().time()
            result = await operations_func(None)
            end_time = asyncio.get_event_loop().time()
            
            logger.debug(f"非事务操作耗时: {(end_time - start_time) * 1000:.2f} ms")
            return True, result
        except Exception as e:
            logger.error(f"非事务操作失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False, {"error": str(e)} 