"""
公会权限管理系统
定义公会职位权限和权限检查机制
"""

from enum import Enum
from typing import List, Dict, Set
from functools import wraps
import logging
from guild_models import GuildPosition

logger = logging.getLogger(__name__)


class GuildPermission(str, Enum):
    """公会权限枚举"""
    # 基础权限
    VIEW_GUILD = "view_guild"                   # 查看公会信息
    CHAT = "chat"                               # 公会聊天
    CONTRIBUTE = "contribute"                   # 贡献资源
    
    # 成员管理权限
    INVITE_MEMBER = "invite_member"             # 邀请成员
    REMOVE_MEMBER = "remove_member"             # 移除成员
    APPROVE_APPLICATION = "approve_application" # 审批申请
    CHANGE_MEMBER_POSITION = "change_member_position" # 变更成员职位
    
    # 公会管理权限
    UPDATE_GUILD_INFO = "update_guild_info"     # 修改公会信息
    MANAGE_ANNOUNCEMENTS = "manage_announcements" # 管理公告
    MANAGE_SETTINGS = "manage_settings"         # 管理公会设置
    
    # 高级权限
    DISBAND_GUILD = "disband_guild"             # 解散公会
    TRANSFER_LEADERSHIP = "transfer_leadership" # 转让会长
    MANAGE_POSITIONS = "manage_positions"       # 管理职位


class GuildPermissionManager:
    """公会权限管理器"""
    
    # 职位权限映射
    POSITION_PERMISSIONS: Dict[GuildPosition, Set[GuildPermission]] = {
        GuildPosition.LEADER: {
            # 会长拥有所有权限
            GuildPermission.VIEW_GUILD,
            GuildPermission.CHAT,
            GuildPermission.CONTRIBUTE,
            GuildPermission.INVITE_MEMBER,
            GuildPermission.REMOVE_MEMBER,
            GuildPermission.APPROVE_APPLICATION,
            GuildPermission.CHANGE_MEMBER_POSITION,
            GuildPermission.UPDATE_GUILD_INFO,
            GuildPermission.MANAGE_ANNOUNCEMENTS,
            GuildPermission.MANAGE_SETTINGS,
            GuildPermission.DISBAND_GUILD,
            GuildPermission.TRANSFER_LEADERSHIP,
            GuildPermission.MANAGE_POSITIONS,
        },
        
        GuildPosition.VICE_LEADER: {
            # 副会长权限
            GuildPermission.VIEW_GUILD,
            GuildPermission.CHAT,
            GuildPermission.CONTRIBUTE,
            GuildPermission.INVITE_MEMBER,
            GuildPermission.REMOVE_MEMBER,
            GuildPermission.APPROVE_APPLICATION,
            GuildPermission.CHANGE_MEMBER_POSITION,
            GuildPermission.UPDATE_GUILD_INFO,
            GuildPermission.MANAGE_ANNOUNCEMENTS,
            GuildPermission.MANAGE_SETTINGS,
        },
        
        GuildPosition.ELDER: {
            # 长老权限
            GuildPermission.VIEW_GUILD,
            GuildPermission.CHAT,
            GuildPermission.CONTRIBUTE,
            GuildPermission.INVITE_MEMBER,
            GuildPermission.APPROVE_APPLICATION,
            GuildPermission.MANAGE_ANNOUNCEMENTS,
        },
        
        GuildPosition.MEMBER: {
            # 普通成员权限
            GuildPermission.VIEW_GUILD,
            GuildPermission.CHAT,
            GuildPermission.CONTRIBUTE,
        }
    }
    
    # 职位等级（数字越大权限越高）
    POSITION_LEVELS: Dict[GuildPosition, int] = {
        GuildPosition.MEMBER: 1,
        GuildPosition.ELDER: 2,
        GuildPosition.VICE_LEADER: 3,
        GuildPosition.LEADER: 4,
    }

    @classmethod
    def has_permission(cls, position: GuildPosition, permission: GuildPermission) -> bool:
        """检查职位是否拥有指定权限"""
        return permission in cls.POSITION_PERMISSIONS.get(position, set())

    @classmethod
    def get_permissions(cls, position: GuildPosition) -> Set[GuildPermission]:
        """获取职位的所有权限"""
        return cls.POSITION_PERMISSIONS.get(position, set())

    @classmethod
    def can_manage_position(cls, manager_position: GuildPosition, target_position: GuildPosition) -> bool:
        """检查是否可以管理目标职位"""
        manager_level = cls.POSITION_LEVELS.get(manager_position, 0)
        target_level = cls.POSITION_LEVELS.get(target_position, 0)
        
        # 只能管理比自己等级低的职位
        return manager_level > target_level

    @classmethod
    def can_remove_member(cls, remover_position: GuildPosition, target_position: GuildPosition) -> bool:
        """检查是否可以移除目标成员"""
        # 会长可以移除任何人（除了自己）
        if remover_position == GuildPosition.LEADER:
            return target_position != GuildPosition.LEADER
        
        # 副会长可以移除长老和成员
        if remover_position == GuildPosition.VICE_LEADER:
            return target_position in [GuildPosition.ELDER, GuildPosition.MEMBER]
        
        # 长老不能移除任何人
        return False

    @classmethod
    def get_manageable_positions(cls, manager_position: GuildPosition) -> List[GuildPosition]:
        """获取可以管理的职位列表"""
        manager_level = cls.POSITION_LEVELS.get(manager_position, 0)
        manageable = []
        
        for position, level in cls.POSITION_LEVELS.items():
            if level < manager_level:
                manageable.append(position)
        
        return manageable

    @classmethod
    def validate_position_change(cls, changer_position: GuildPosition, 
                                target_current_position: GuildPosition, 
                                target_new_position: GuildPosition) -> bool:
        """验证职位变更是否合法"""
        # 检查是否有权限管理目标的当前职位
        if not cls.can_manage_position(changer_position, target_current_position):
            return False
        
        # 检查是否有权限设置目标的新职位
        if not cls.can_manage_position(changer_position, target_new_position):
            return False
        
        # 不能将成员提升到与自己相同或更高的职位
        changer_level = cls.POSITION_LEVELS.get(changer_position, 0)
        new_level = cls.POSITION_LEVELS.get(target_new_position, 0)
        
        return new_level < changer_level


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def check_guild_permission(player_position: GuildPosition, required_permission: GuildPermission) -> bool:
        """检查玩家是否拥有指定的公会权限"""
        return GuildPermissionManager.has_permission(player_position, required_permission)

    @staticmethod
    def check_management_permission(manager_position: GuildPosition, target_position: GuildPosition) -> bool:
        """检查是否有管理目标职位的权限"""
        return GuildPermissionManager.can_manage_position(manager_position, target_position)

    @staticmethod
    def check_removal_permission(remover_position: GuildPosition, target_position: GuildPosition) -> bool:
        """检查是否有移除目标成员的权限"""
        return GuildPermissionManager.can_remove_member(remover_position, target_position)


def require_guild_permission(permission: GuildPermission):
    """公会权限装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里需要从参数中获取玩家的公会职位
            # 具体实现会在服务层中处理
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_position_level(min_level: int):
    """职位等级装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里需要从参数中获取玩家的职位等级
            # 具体实现会在服务层中处理
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class GuildPermissionError(Exception):
    """公会权限错误"""
    def __init__(self, message: str, required_permission: GuildPermission = None):
        self.message = message
        self.required_permission = required_permission
        super().__init__(self.message)


class InsufficientPermissionError(GuildPermissionError):
    """权限不足错误"""
    def __init__(self, required_permission: GuildPermission):
        message = f"权限不足，需要权限: {required_permission.value}"
        super().__init__(message, required_permission)


class InvalidPositionError(GuildPermissionError):
    """无效职位错误"""
    def __init__(self, message: str):
        super().__init__(message)


# 权限检查工具函数
def validate_guild_operation(player_position: GuildPosition, 
                           required_permission: GuildPermission) -> None:
    """验证公会操作权限，如果没有权限则抛出异常"""
    if not PermissionChecker.check_guild_permission(player_position, required_permission):
        raise InsufficientPermissionError(required_permission)


def validate_member_management(manager_position: GuildPosition, 
                             target_position: GuildPosition, 
                             operation: str) -> None:
    """验证成员管理权限"""
    if operation == "remove":
        if not PermissionChecker.check_removal_permission(manager_position, target_position):
            raise GuildPermissionError(f"没有权限移除 {target_position.value} 职位的成员")
    elif operation == "manage":
        if not PermissionChecker.check_management_permission(manager_position, target_position):
            raise GuildPermissionError(f"没有权限管理 {target_position.value} 职位的成员")


def validate_position_change(changer_position: GuildPosition,
                           current_position: GuildPosition,
                           new_position: GuildPosition) -> None:
    """验证职位变更权限"""
    if not GuildPermissionManager.validate_position_change(changer_position, current_position, new_position):
        raise GuildPermissionError(f"没有权限将 {current_position.value} 变更为 {new_position.value}")
