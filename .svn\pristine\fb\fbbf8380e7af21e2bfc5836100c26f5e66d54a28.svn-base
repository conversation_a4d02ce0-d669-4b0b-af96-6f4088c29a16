"""
商店缓存服务
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from shop_redis_manager import get_shop_redis
from shop_models import Shop, ShopItemConfig, ShopDiscount

logger = logging.getLogger(__name__)


class ShopCacheService:
    """商店缓存服务"""
    
    def __init__(self):
        self.redis = None
        
        # 缓存TTL配置
        self.CACHE_TTL = {
            "shop_config": 3600,        # 1小时
            "shop_items": 1800,         # 30分钟
            "shop_access": 300,         # 5分钟
            "discount_config": 3600,    # 1小时
            "limit_counters": "dynamic" # 到周期结束
        }
    
    async def _get_redis(self):
        """获取Redis连接"""
        if self.redis is None:
            self.redis = await get_shop_redis()
        return self.redis
    
    # ==================== 商店配置缓存 ====================
    
    def _get_shop_config_key(self, shop_id: str) -> str:
        """获取商店配置缓存键"""
        return f"shop:config:{shop_id}"
    
    async def cache_shop_config(self, shop: Shop) -> bool:
        """缓存商店配置"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_config_key(shop.shop_id)
            
            shop_data = json.dumps(shop.to_dict(), ensure_ascii=False)
            await redis.setex(key, self.CACHE_TTL["shop_config"], shop_data)
            
            logger.debug(f"缓存商店配置: {shop.shop_id}")
            return True
            
        except Exception as e:
            logger.error(f"缓存商店配置失败: {str(e)}")
            return False
    
    async def get_cached_shop_config(self, shop_id: str) -> Optional[Shop]:
        """获取缓存的商店配置"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_config_key(shop_id)
            
            shop_data = await redis.get(key)
            if shop_data:
                shop_dict = json.loads(shop_data)
                return Shop.from_dict(shop_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"获取缓存商店配置失败: {str(e)}")
            return None
    
    async def invalidate_shop_config(self, shop_id: str) -> bool:
        """清除商店配置缓存"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_config_key(shop_id)
            
            await redis.delete(key)
            logger.debug(f"清除商店配置缓存: {shop_id}")
            return True
            
        except Exception as e:
            logger.error(f"清除商店配置缓存失败: {str(e)}")
            return False
    
    # ==================== 商店商品缓存 ====================
    
    def _get_shop_items_key(self, shop_id: str) -> str:
        """获取商店商品缓存键"""
        return f"shop:items:{shop_id}"
    
    async def cache_shop_items(self, shop_id: str, items: List[ShopItemConfig]) -> bool:
        """缓存商店商品列表"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_items_key(shop_id)
            
            items_data = json.dumps([item.to_dict() for item in items], ensure_ascii=False)
            await redis.setex(key, self.CACHE_TTL["shop_items"], items_data)
            
            logger.debug(f"缓存商店商品: {shop_id}, {len(items)} 个商品")
            return True
            
        except Exception as e:
            logger.error(f"缓存商店商品失败: {str(e)}")
            return False
    
    async def get_cached_shop_items(self, shop_id: str) -> Optional[List[ShopItemConfig]]:
        """获取缓存的商店商品列表"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_items_key(shop_id)
            
            items_data = await redis.get(key)
            if items_data:
                items_list = json.loads(items_data)
                return [ShopItemConfig.from_dict(item_dict) for item_dict in items_list]
            
            return None
            
        except Exception as e:
            logger.error(f"获取缓存商店商品失败: {str(e)}")
            return None
    
    async def invalidate_shop_items(self, shop_id: str) -> bool:
        """清除商店商品缓存"""
        try:
            redis = await self._get_redis()
            key = self._get_shop_items_key(shop_id)
            
            await redis.delete(key)
            logger.debug(f"清除商店商品缓存: {shop_id}")
            return True
            
        except Exception as e:
            logger.error(f"清除商店商品缓存失败: {str(e)}")
            return False
    
    # ==================== 限购计数缓存 (Hash结构优化) ====================
    
    def _get_limit_counters_key(self, player_id: str, limit_type: str) -> str:
        """获取限购计数器缓存键"""
        return f"limit:counters:{player_id}:{limit_type}"
    
    async def get_counter(self, player_id: str, scope_value: str, limit_type: str) -> int:
        """获取限购计数"""
        try:
            redis = await self._get_redis()
            key = self._get_limit_counters_key(player_id, limit_type)
            
            count = await redis.hget(key, scope_value)
            return int(count) if count else 0
            
        except Exception as e:
            logger.error(f"获取限购计数失败: {str(e)}")
            return 0
    
    async def increment_counter(self, player_id: str, scope_value: str, limit_type: str, 
                              increment: int, ttl: int) -> int:
        """增加限购计数"""
        try:
            redis = await self._get_redis()
            key = self._get_limit_counters_key(player_id, limit_type)
            
            # 使用管道操作保证原子性
            pipe = redis.pipeline()
            pipe.hincrby(key, scope_value, increment)
            pipe.expire(key, ttl)
            results = await pipe.execute()
            
            new_count = results[0]
            logger.debug(f"增加限购计数: {player_id}:{scope_value}:{limit_type} +{increment} = {new_count}")
            return new_count
            
        except Exception as e:
            logger.error(f"增加限购计数失败: {str(e)}")
            return 0
    
    async def get_all_counters(self, player_id: str, limit_type: str) -> Dict[str, int]:
        """获取玩家所有限购计数"""
        try:
            redis = await self._get_redis()
            key = self._get_limit_counters_key(player_id, limit_type)
            
            counters = await redis.hgetall(key)
            return {k: int(v) for k, v in counters.items()}
            
        except Exception as e:
            logger.error(f"获取所有限购计数失败: {str(e)}")
            return {}
    
    async def reset_player_counters(self, player_id: str, limit_type: str) -> bool:
        """重置玩家限购计数"""
        try:
            redis = await self._get_redis()
            key = self._get_limit_counters_key(player_id, limit_type)
            
            await redis.delete(key)
            logger.debug(f"重置玩家限购计数: {player_id}:{limit_type}")
            return True
            
        except Exception as e:
            logger.error(f"重置玩家限购计数失败: {str(e)}")
            return False
    
    # ==================== 重置时间戳缓存 ====================
    
    def _get_reset_timestamp_key(self, limit_type: str) -> str:
        """获取重置时间戳缓存键"""
        return f"reset:timestamp:{limit_type}"
    
    def _get_player_check_key(self, player_id: str, limit_type: str) -> str:
        """获取玩家检查时间缓存键"""
        return f"reset:check:{player_id}:{limit_type}"
    
    async def set_global_reset_timestamp(self, limit_type: str, timestamp: str) -> bool:
        """设置全局重置时间戳"""
        try:
            redis = await self._get_redis()
            key = self._get_reset_timestamp_key(limit_type)
            
            await redis.set(key, timestamp)
            logger.info(f"设置全局重置时间戳: {limit_type} = {timestamp}")
            return True
            
        except Exception as e:
            logger.error(f"设置全局重置时间戳失败: {str(e)}")
            return False
    
    async def get_global_reset_timestamp(self, limit_type: str) -> Optional[str]:
        """获取全局重置时间戳"""
        try:
            redis = await self._get_redis()
            key = self._get_reset_timestamp_key(limit_type)
            
            timestamp = await redis.get(key)
            return timestamp
            
        except Exception as e:
            logger.error(f"获取全局重置时间戳失败: {str(e)}")
            return None
    
    async def set_player_check_timestamp(self, player_id: str, limit_type: str, timestamp: str, ttl: int) -> bool:
        """设置玩家检查时间戳"""
        try:
            redis = await self._get_redis()
            key = self._get_player_check_key(player_id, limit_type)
            
            await redis.setex(key, ttl, timestamp)
            return True
            
        except Exception as e:
            logger.error(f"设置玩家检查时间戳失败: {str(e)}")
            return False
    
    async def get_player_check_timestamp(self, player_id: str, limit_type: str) -> Optional[str]:
        """获取玩家检查时间戳"""
        try:
            redis = await self._get_redis()
            key = self._get_player_check_key(player_id, limit_type)
            
            timestamp = await redis.get(key)
            return timestamp
            
        except Exception as e:
            logger.error(f"获取玩家检查时间戳失败: {str(e)}")
            return None
    
    # ==================== 折扣缓存 ====================
    
    def _get_discount_config_key(self, discount_id: str) -> str:
        """获取折扣配置缓存键"""
        return f"discount:config:{discount_id}"
    
    async def cache_discount_config(self, discount: ShopDiscount) -> bool:
        """缓存折扣配置"""
        try:
            redis = await self._get_redis()
            key = self._get_discount_config_key(discount.discount_id)
            
            discount_data = json.dumps(discount.to_dict(), ensure_ascii=False)
            await redis.setex(key, self.CACHE_TTL["discount_config"], discount_data)
            
            logger.debug(f"缓存折扣配置: {discount.discount_id}")
            return True
            
        except Exception as e:
            logger.error(f"缓存折扣配置失败: {str(e)}")
            return False
    
    async def get_cached_discount_config(self, discount_id: str) -> Optional[ShopDiscount]:
        """获取缓存的折扣配置"""
        try:
            redis = await self._get_redis()
            key = self._get_discount_config_key(discount_id)
            
            discount_data = await redis.get(key)
            if discount_data:
                discount_dict = json.loads(discount_data)
                return ShopDiscount.from_dict(discount_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"获取缓存折扣配置失败: {str(e)}")
            return None
    
    # ==================== 批量操作 ====================
    
    async def invalidate_shop_all_cache(self, shop_id: str) -> bool:
        """清除商店所有相关缓存"""
        try:
            await self.invalidate_shop_config(shop_id)
            await self.invalidate_shop_items(shop_id)
            
            logger.info(f"清除商店所有缓存: {shop_id}")
            return True
            
        except Exception as e:
            logger.error(f"清除商店所有缓存失败: {str(e)}")
            return False
    
    async def cleanup_expired_cache(self) -> int:
        """清理过期缓存 (Redis会自动处理，这里主要用于统计)"""
        try:
            redis = await self._get_redis()
            
            # 获取所有相关的键
            patterns = [
                "shop:*",
                "limit:counters:*",
                "reset:*",
                "discount:*"
            ]
            
            total_keys = 0
            for pattern in patterns:
                keys = await redis.keys(pattern)
                total_keys += len(keys)
            
            logger.info(f"当前缓存键总数: {total_keys}")
            return total_keys
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {str(e)}")
            return 0
