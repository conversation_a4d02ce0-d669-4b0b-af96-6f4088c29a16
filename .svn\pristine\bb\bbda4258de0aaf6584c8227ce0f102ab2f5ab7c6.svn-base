import aiohttp
import asyncio
import json
import logging
import random
import time
import argparse
import uuid
import string
from datetime import datetime
from websockets.exceptions import ConnectionClosed
import websockets

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stress_test.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_URL = "http://localhost:8000/"
WS_URL = "ws://localhost:8000/ws/{}"
# SERVER_URL = "http://*************:8080/game_server/"
# WS_URL = "ws://*************:8080/game_server/ws/{}"
TEST_DURATION = 300
DISCONNECT_PROB = 0.05
RECONNECT_DELAY = 5.0
MAX_RECONNECT_ATTEMPTS = 3
RECEIVE_TIMEOUT = 120.0
HEARTBEAT_INTERVAL = 10.0
MESSAGE_INTERVAL = 2.0
PAYLOAD_SIZE = 2048
BURST_MODE = False
BURST_INTERVAL = 25.0  # 缩短间隔以增加突发强度
BURST_MESSAGE_COUNT = 100  # 增加突发消息数

# 生成随机 payload
def generate_payload(size: int) -> str:
    return ''.join(random.choices(string.ascii_letters + string.digits, k=size))

async def register_user(session: aiohttp.ClientSession, username: str, password: str = "password123") -> bool:
    """注册用户"""
    try:
        async with session.post(
            f"{SERVER_URL}register",
            json={"username": username, "password": password}
        ) as response:
            #text/html格式
            data = await response.text()
            # 尝试将响应解析为 JSON
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                logger.error(f"用户 {username} 注册失败: 响应不是有效的 JSON: {data}")
                return False
            if data.get("success"):
                logger.info(f"用户 {username} 注册成功")
                return True
            else:
                logger.warning(f"用户 {username} 注册失败: {data.get('message')}")
                return False
    except Exception as e:
        logger.error(f"用户 {username} 注册失败: {str(e)}")
        return False

async def login_user(session: aiohttp.ClientSession, username: str, password: str = "password123") -> str:
    """用户登录并获取 token"""
    try:
        async with session.post(
            f"{SERVER_URL}login",
            json={"username": username, "password": password}
        ) as response:
            data = await response.json()
            logger.debug(f"用户 {username} 登录响应: {data}")
            if data.get("success"):
                token = data["data"]["access_token"]
                logger.info(f"用户 {username} 登录成功，token: {token[:20]}...")
                return token
            else:
                logger.error(f"用户 {username} 登录失败: {data.get('message')}")
                return ""
    except Exception as e:
        logger.error(f"用户 {username} 登录失败: {str(e)}")
        return ""

async def client_task(client_id: int, session: aiohttp.ClientSession):
    """客户端任务，模拟高数据和高计算负载"""
    username = f"test_user_{client_id}"
    stats = {
        "messages_sent": {"heartbeat": 0, "chat": 0, "item_ops": 0},
        "messages_received": {"heartbeat": 0, "chat": 0, "item_ops": 0},
        "errors": {"connection": 0, "message": 0},
        "average_latency": {"heartbeat": [], "chat": [], "item_ops": []},
        "connection_errors": {},
        "successful_connections": 0
    }
    task_start_time = time.time()
    websocket = None
    reconnect_attempts = 0
    item_ids = []  # 存储可用的道具 ID

    async def connect():
        nonlocal websocket, reconnect_attempts
        try:
            if not await register_user(session, username):
                logger.info(f"用户 {username} 已存在，尝试登录")
            token = await login_user(session, username)
            if not token:
                logger.error(f"客户端 {client_id} 登录失败")
                stats["connection_errors"][f"login_failed_{client_id}"] = stats["connection_errors"].get(f"login_failed_{client_id}", 0) + 1
                stats["errors"]["connection"] += 1
                return False
            websocket = await websockets.connect(WS_URL.format(token), open_timeout=20, close_timeout=2)
            logger.info(f"客户端 {client_id} 连接成功，用户名: {username}")
            stats["successful_connections"] += 1
            reconnect_attempts = 0
            return True
        except Exception as e:
            logger.error(f"客户端 {client_id} 连接失败: {str(e)}")
            stats["connection_errors"][str(e)] = stats["connection_errors"].get(str(e), 0) + 1
            stats["errors"]["connection"] += 1
            return False

    async def send_message(msg_type: str, message: dict) -> bool:
        nonlocal websocket
        if not websocket or websocket.state != websockets.protocol.State.OPEN:
            logger.debug(f"客户端 {client_id} WebSocket 未初始化或已关闭")
            return False
        try:
            start_time = time.time()
            await asyncio.wait_for(websocket.send(json.dumps(message)), timeout=5.0)
            stats["messages_sent"][msg_type] += 1
            logger.debug(f"客户端 {client_id} 发送 {msg_type} 消息，时间: {datetime.now()}")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=RECEIVE_TIMEOUT)
                latency = (time.time() - start_time) * 1000  # 毫秒
                logger.debug(f"客户端 {client_id} 接收 {msg_type} 响应: {response}, 延迟: {latency:.2f} 毫秒")
                response_data = json.loads(response)
                stats["messages_received"][msg_type] += 1
                stats["average_latency"][msg_type].append(latency / 1000)  # 转换为秒
                # 更新道具 ID 列表
                if msg_type == "item_ops" and message.get("msgId") == 5 and response_data.get("data", {}).get("item"):
                    item_id = response_data["data"]["item"].get("id")
                    if item_id:
                        item_ids.append(item_id)
                return True
            except asyncio.TimeoutError:
                logger.warning(f"客户端 {client_id} {msg_type} 响应超时")
                stats["errors"]["message"] += 1
                return False
            except json.JSONDecodeError as e:
                logger.error(f"客户端 {client_id} {msg_type} 响应解析失败: {str(e)}, 原始响应: {response}")
                stats["errors"]["message"] += 1
                return False
            except Exception as e:
                logger.error(f"客户端 {client_id} 接收 {msg_type} 响应失败: {str(e)}")
                stats["errors"]["message"] += 1
                return False
        except (asyncio.TimeoutError, ConnectionClosed, Exception) as e:
            logger.error(f"客户端 {client_id} 发送 {msg_type} 失败: {str(e)}")
            stats["errors"]["message"] += 1
            return False

    async def send_heartbeat():
        message = {"msgId": 0, "data": {"heartbeat": True}}
        return await send_message("heartbeat", message)

    async def send_chat():
        message = {
            "msgId": 1,
            "data": {"content": f"Chat from {username}: {generate_payload(PAYLOAD_SIZE)}"}
        }
        return await send_message("chat", message)

    async def send_item_operation():
        try:
            op_type = random.choice(["add", "increase", "delete"])
            if op_type == "add":
                message = {
                    "msgId": 5,
                    "data": {
                        "defid": random.randint(1, 1000),
                        "attributes": {"type": "test", "value": generate_payload(PAYLOAD_SIZE // 4)},
                        "quantity": random.randint(1, 10)
                    }
                }
            elif op_type == "increase":
                message = {
                    "msgId": 8,
                    "data": {
                        "defid": random.randint(1, 1000),
                        "amount": random.randint(1, 5)
                    }
                }
            else:  # delete
                # 使用已知的道具 ID 或随机生成
                item_id = random.choice(item_ids) if item_ids else str(uuid.uuid4())
                message = {
                    "msgId": 7,
                    "data": {"item_id": item_id}
                }
            return await send_message("item_ops", message)
        except Exception as e:
            logger.error(f"客户端 {client_id} 道具操作失败: {str(e)}")
            stats["errors"]["message"] += 1
            return False

    async def get_items():
        message = {"msgId": 6, "data": {}}
        return await send_message("item_ops", message)

    async def burst_messages():
        for _ in range(BURST_MESSAGE_COUNT):
            if random.random() < 0.4:  # 增加道具操作频率
                await send_item_operation()
            else:
                await send_chat()
            await asyncio.sleep(0.005)  # 更短的间隔以提高突发强度

    try:
        async with asyncio.timeout(TEST_DURATION + 5):
            if not await connect():
                return stats
            # 初始获取道具列表
            await get_items()
            last_burst_time = time.time()
            last_heart_time = time.time()
            while True:
                try:
                    if random.random() < DISCONNECT_PROB:
                        logger.info(f"客户端 {client_id} 主动断开连接")
                        await websocket.close()
                        websocket = None
                        if reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
                            await asyncio.sleep(RECONNECT_DELAY * (2 ** reconnect_attempts))
                            reconnect_attempts += 1
                            if await connect():
                                await get_items()  # 重连后重新获取道具
                                continue
                        break

                    # 发送心跳
                    if (time.time() - last_heart_time) > HEARTBEAT_INTERVAL:
                        await send_heartbeat()
                        last_heart_time = time.time()
                    # 发送普通消息（增加道具操作概率）
                    x = random.random()
                    if x < 0.2:   
                        await send_item_operation()    
                        pass
                    if x  < 0.3:
                        await send_chat()

                    # 突发模式
                    if  (time.time() - last_burst_time) > BURST_INTERVAL:
                        logger.info(f"客户端 {client_id} 进入突发模式")
                        await burst_messages()
                        last_burst_time = time.time()

                    # 定期更新道具列表
                    if random.random() < 0.1:
                        await get_items()

                    await asyncio.sleep(MESSAGE_INTERVAL)
                except asyncio.TimeoutError:
                    logger.info(f"客户端 {client_id} 测试超时，退出")
                    break
                except ConnectionClosed:
                    if reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
                        logger.info(f"客户端 {client_id} 连接断开，尝试重连 {reconnect_attempts + 1}/{MAX_RECONNECT_ATTEMPTS}")
                        await asyncio.sleep(RECONNECT_DELAY * (2 ** reconnect_attempts))
                        reconnect_attempts += 1
                        if await connect():
                            await get_items()
                            continue
                    break
    except asyncio.TimeoutError:
        logger.info(f"客户端 {client_id} 达到测试时长 {TEST_DURATION} 秒，退出")
    except Exception as e:
        logger.error(f"客户端 {client_id} 发生错误: {str(e)}")
        stats["errors"]["message"] += 1
    finally:
        if websocket:
            try:
                await asyncio.wait_for(websocket.close(), timeout=1.0)
                logger.info(f"客户端 {client_id} 关闭连接")
            except Exception as e:
                logger.error(f"客户端 {client_id} 关闭连接失败: {str(e)}")
        logger.info(f"客户端 {client_id} 退出，运行时间: {time.time() - task_start_time:.2f} 秒")
    return stats
async def run_tasks_with_interval(num_clients: int, interval: float, session: aiohttp.ClientSession):
    tasks = []
    for i in range(num_clients):
        # 创建任务（不立即执行，而是注册到事件循环）
        task = asyncio.create_task(client_task(i, session))
        tasks.append(task)
        # 等待interval秒后再创建下一个任务（关键）
        await asyncio.sleep(interval)
    
    # 等待所有任务完成
    return await asyncio.gather(*tasks, return_exceptions=True)
async def main(num_clients: int, duration: int, payload_size: int, burst_mode: bool):
    global TEST_DURATION, PAYLOAD_SIZE, BURST_MODE
    TEST_DURATION = duration
    PAYLOAD_SIZE = payload_size
    BURST_MODE = burst_mode
    stats = {
        "messages_sent": {"heartbeat": 0, "chat": 0, "item_ops": 0},
        "messages_received": {"heartbeat": 0, "chat": 0, "item_ops": 0},
        "errors": {"connection": 0, "message": 0},
        "average_latency": {"heartbeat": [], "chat": [], "item_ops": []},
        "connection_errors": {},
        "successful_connections": 0
    }
    start_time = time.time()
    async with aiohttp.ClientSession() as session:
        results = await run_tasks_with_interval(num_clients, 2, session)
        for result in results:
            if isinstance(result, dict):
                for key in stats["messages_sent"]:
                    stats["messages_sent"][key] += result["messages_sent"][key]
                    stats["messages_received"][key] += result["messages_received"][key]
                    stats["average_latency"][key].extend(result["average_latency"][key])
                for key in stats["errors"]:
                    stats["errors"][key] += result["errors"][key]
                stats["successful_connections"] += result["successful_connections"]
                for k, v in result["connection_errors"].items():
                    stats["connection_errors"][k] = stats["connection_errors"].get(k, 0) + v
            else:
                logger.error(f"客户端任务返回异常: {str(result)}")
                stats["errors"]["message"] += 1

    duration = time.time() - start_time
    logger.info(f"测试持续时间: {duration:.2f} 秒")
    logger.info(f"成功连接: {stats['successful_connections']}")
    logger.info(f"失败连接: {max(0, num_clients - stats['successful_connections'])}")  # 修复负数问题
    for msg_type in stats["messages_sent"]:
        logger.info(f"{msg_type.capitalize()} 发送消息: {stats['messages_sent'][msg_type]}")
        logger.info(f"{msg_type.capitalize()} 接收消息: {stats['messages_received'][msg_type]}")
        avg_latency = (
            sum(stats["average_latency"][msg_type]) / len(stats["average_latency"][msg_type]) * 1000
            if stats["average_latency"][msg_type]
            else 0
        )
        logger.info(f"{msg_type.capitalize()} 平均延迟: {avg_latency:.2f} 毫秒")
    logger.info(f"连接错误: {stats['errors']['connection']}")
    logger.info(f"消息错误: {stats['errors']['message']}")
    logger.info(f"连接错误详情: {stats['connection_errors']}")
    total_messages = sum(stats["messages_sent"].values()) + sum(stats["messages_received"].values())
    throughput = total_messages / duration if duration > 0 else 0
    logger.info(f"每秒消息吞吐量: {throughput:.2f} 条/秒")
    logger.info(f"Payload 大小: {PAYLOAD_SIZE} 字节")
    logger.info(f"突发模式: {'启用' if BURST_MODE else '禁用'}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Game Server Stress Test")
    parser.add_argument("--num-clients", type=int, default=100, help="Number of clients")
    parser.add_argument("--duration", type=int, default=300, help="Test duration in seconds")
    parser.add_argument("--payload-size", type=int, default=2048, help="Payload size in bytes")
    parser.add_argument("--burst-mode", action="store_true", help="Enable burst mode")
    args = parser.parse_args()

    start_time = time.time()
    logger.info(f"启动压力测试: 客户端数={args.num_clients}, 持续时间={args.duration}秒, "
                f"心跳间隔={HEARTBEAT_INTERVAL}秒, 消息间隔={MESSAGE_INTERVAL}秒, "
                f"Payload大小={args.payload_size}字节, 突发模式={'启用' if args.burst_mode else '禁用'}")
    asyncio.run(main(args.num_clients, args.duration, args.payload_size, args.burst_mode))