<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强商品面板演示</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            background-color: #f8fafc;
            padding: 20px;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 30px;
        }
        .demo-description {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }
        .feature-icon {
            color: #10b981;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎨 增强商品面板演示</h1>
        
        <div class="demo-description">
            <h2>✨ 新增功能特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <span>详细的限购信息显示</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔄</span>
                    <span>刷新权重和概率统计</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⏰</span>
                    <span>创建和更新时间信息</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎯</span>
                    <span>可用性时间配置</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💰</span>
                    <span>增强的价格信息展示</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎮</span>
                    <span>更多操作按钮（复制、启用/禁用）</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span>分区式信息组织</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span>响应式设计优化</span>
                </div>
            </div>
        </div>

        <div class="item-list" id="demoItemList">
            <!-- 演示商品卡片将在这里生成 -->
        </div>
    </div>

    <script>
        // 模拟商品数据
        const mockItems = [
            {
                config_id: "shop_demo:gold_sword:001",
                item_template_id: "gold_sword",
                item_quantity: 1,
                item_quality: 3,
                slot_id: 1,
                price_config: {
                    currency_type: "gold",
                    base_price: 1500
                },
                original_price: 1500,
                final_price: 1200,
                discount_amount: 300,
                purchase_limit: {
                    daily_limit: 3,
                    weekly_limit: 10
                },
                availability: {
                    start_time: "2024-01-01T00:00:00Z",
                    end_time: "2024-12-31T23:59:59Z"
                },
                refresh_weight: 150,
                refresh_probability: 0.8,
                sort_order: 1,
                is_active: true,
                display_config: {
                    name: "黄金之剑",
                    description: "锋利的黄金制作的剑，攻击力+50"
                },
                created_at: "2024-01-15T10:30:00Z",
                updated_at: "2024-01-20T14:45:00Z"
            },
            {
                config_id: "shop_demo:magic_potion:002",
                item_template_id: "magic_potion",
                item_quantity: 5,
                item_quality: 2,
                slot_id: null,
                price_config: {
                    currency_type: "diamond",
                    base_price: 50
                },
                original_price: 50,
                final_price: 50,
                discount_amount: 0,
                purchase_limit: {
                    daily_limit: 10,
                    total_limit: 100
                },
                availability: {},
                refresh_weight: 200,
                refresh_probability: 1.0,
                sort_order: 2,
                is_active: false,
                display_config: {
                    name: "魔法药水",
                    description: "恢复100点魔法值的神奇药水"
                },
                created_at: "2024-01-10T08:15:00Z",
                updated_at: "2024-01-18T16:20:00Z"
            },
            {
                config_id: "shop_demo:rare_gem:003",
                item_template_id: "rare_gem",
                item_quantity: 1,
                item_quality: 5,
                slot_id: 3,
                price_config: {
                    currency_type: "arena_coin",
                    base_price: 2000
                },
                original_price: 2000,
                final_price: 2000,
                discount_amount: 0,
                purchase_limit: {
                    monthly_limit: 1
                },
                availability: {
                    start_time: "2024-02-01T00:00:00Z",
                    conditions: {
                        level: 50,
                        vip: 3
                    }
                },
                refresh_weight: 50,
                refresh_probability: 0.1,
                sort_order: 3,
                is_active: true,
                display_config: {
                    name: "稀有宝石",
                    description: "极其珍贵的宝石，可用于装备强化"
                },
                created_at: "2024-01-05T12:00:00Z",
                updated_at: "2024-01-25T09:30:00Z"
            }
        ];

        // 创建模拟的ItemManager
        class DemoItemManager {
            constructor() {
                this.items = mockItems;
            }

            // 复制原有的方法
            formatPrice(priceConfig) {
                if (!priceConfig) return '未设置';
                
                const currencyMap = {
                    'gold': '金币',
                    'diamond': '钻石',
                    'arena_coin': '竞技场币',
                    'guild_coin': '公会币',
                    'honor_point': '荣誉点'
                };
                
                const currency = priceConfig.currency_type || priceConfig.currency;
                const amount = priceConfig.base_price || priceConfig.amount;
                const currencyText = currencyMap[currency] || currency;
                
                return `${amount} ${currencyText}`;
            }

            getQualityText(quality) {
                const qualityMap = {
                    1: '普通',
                    2: '优秀',
                    3: '稀有',
                    4: '史诗',
                    5: '传说',
                    6: '神话'
                };
                return qualityMap[quality] || '';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            formatPurchaseLimit(purchaseLimit) {
                if (!purchaseLimit || Object.keys(purchaseLimit).length === 0) {
                    return { hasLimit: false, html: '' };
                }

                let limitParts = [];
                
                if (purchaseLimit.daily_limit) {
                    limitParts.push(`<div class="limit-item">
                        <i class="icon-daily">📅</i>
                        每日限购: <span class="limit-value">${purchaseLimit.daily_limit}</span>
                    </div>`);
                }
                
                if (purchaseLimit.weekly_limit) {
                    limitParts.push(`<div class="limit-item">
                        <i class="icon-weekly">📊</i>
                        每周限购: <span class="limit-value">${purchaseLimit.weekly_limit}</span>
                    </div>`);
                }
                
                if (purchaseLimit.monthly_limit) {
                    limitParts.push(`<div class="limit-item">
                        <i class="icon-monthly">🗓️</i>
                        每月限购: <span class="limit-value">${purchaseLimit.monthly_limit}</span>
                    </div>`);
                }
                
                if (purchaseLimit.total_limit) {
                    limitParts.push(`<div class="limit-item">
                        <i class="icon-total">🎯</i>
                        总限购: <span class="limit-value">${purchaseLimit.total_limit}</span>
                    </div>`);
                }

                return {
                    hasLimit: limitParts.length > 0,
                    html: limitParts.join('')
                };
            }

            formatAvailability(availability) {
                if (!availability || Object.keys(availability).length === 0) {
                    return { hasAvailability: false, html: '' };
                }

                let availabilityParts = [];
                
                if (availability.start_time) {
                    const startTime = new Date(availability.start_time);
                    availabilityParts.push(`<div class="availability-item">
                        <i class="icon-start">🟢</i>
                        开始时间: <span class="time-value">${this.formatDateTime(startTime)}</span>
                    </div>`);
                }
                
                if (availability.end_time) {
                    const endTime = new Date(availability.end_time);
                    availabilityParts.push(`<div class="availability-item">
                        <i class="icon-end">🔴</i>
                        结束时间: <span class="time-value">${this.formatDateTime(endTime)}</span>
                    </div>`);
                }
                
                if (availability.conditions && Object.keys(availability.conditions).length > 0) {
                    availabilityParts.push(`<div class="availability-item">
                        <i class="icon-conditions">⚙️</i>
                        条件: <span class="conditions-value">${JSON.stringify(availability.conditions)}</span>
                    </div>`);
                }

                return {
                    hasAvailability: availabilityParts.length > 0,
                    html: availabilityParts.join('')
                };
            }

            formatTimeInfo(item) {
                let timeParts = [];
                
                if (item.created_at) {
                    const createdTime = new Date(item.created_at);
                    timeParts.push(`<div class="time-item">
                        <i class="icon-created">➕</i>
                        创建时间: <span class="time-value">${this.formatDateTime(createdTime)}</span>
                    </div>`);
                }
                
                if (item.updated_at) {
                    const updatedTime = new Date(item.updated_at);
                    timeParts.push(`<div class="time-item">
                        <i class="icon-updated">✏️</i>
                        更新时间: <span class="time-value">${this.formatDateTime(updatedTime)}</span>
                    </div>`);
                }

                return {
                    html: timeParts.join('')
                };
            }

            formatDisplayConfig(displayConfig) {
                if (!displayConfig || Object.keys(displayConfig).length === 0) {
                    return { name: '', description: '' };
                }

                return {
                    name: displayConfig.name || '',
                    description: displayConfig.description || ''
                };
            }

            calculateRelativeWeight(weight) {
                const totalWeight = this.items.reduce((sum, item) => sum + (item.refresh_weight || 0), 0);
                if (totalWeight === 0) return '0.0';
                const percentage = (weight / totalWeight) * 100;
                return percentage.toFixed(1);
            }

            formatDateTime(date) {
                if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
                    return '无效时间';
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }

            // 简化的createItemCard方法（移除了实际的操作功能）
            createItemCard(item) {
                const statusClass = item.is_active ? 'active' : 'inactive';
                const statusText = item.is_active ? '激活' : '禁用';
                const qualityText = this.getQualityText(item.item_quality);
                const priceText = this.formatPrice(item.price_config);
                
                const purchaseLimitInfo = this.formatPurchaseLimit(item.purchase_limit);
                const availabilityInfo = this.formatAvailability(item.availability);
                const timeInfo = this.formatTimeInfo(item);
                const displayInfo = this.formatDisplayConfig(item.display_config);
                
                return `
                    <div class="item-card ${statusClass}" data-config-id="${item.config_id}">
                        <div class="item-card-header">
                            <div class="item-info">
                                <h3 class="item-template-id">
                                    📦 ${this.escapeHtml(item.item_template_id)}
                                </h3>
                                <div class="item-config-id">
                                    🆔 ${this.escapeHtml(item.config_id)}
                                </div>
                                ${displayInfo.name ? `<div class="item-display-name">${displayInfo.name}</div>` : ''}
                            </div>
                            <div class="item-status">
                                <span class="status-badge ${statusClass}">
                                    ${item.is_active ? '✅' : '❌'} ${statusText}
                                </span>
                                ${qualityText ? `<span class="quality-badge quality-${item.item_quality}">${qualityText}</span>` : ''}
                                <span class="sort-order-badge">排序: ${item.sort_order || 0}</span>
                            </div>
                        </div>
                        
                        <div class="item-section">
                            <div class="section-title">
                                ℹ️ 基础信息
                            </div>
                            <div class="item-details">
                                <div class="item-detail-row">
                                    <span class="item-detail-label">🔢 数量:</span>
                                    <span class="item-detail-value">${item.item_quantity}</span>
                                </div>
                                ${item.slot_id ? `
                                <div class="item-detail-row">
                                    <span class="item-detail-label">🎰 槽位:</span>
                                    <span class="item-detail-value">${item.slot_id}</span>
                                </div>
                                ` : ''}
                                ${displayInfo.description ? `
                                <div class="item-detail-row">
                                    <span class="item-detail-label">📝 描述:</span>
                                    <span class="item-detail-value">${displayInfo.description}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        
                        <div class="item-section">
                            <div class="section-title">💰 价格信息</div>
                            <div class="item-price-info">
                                <div class="item-price-main">${priceText}</div>
                                ${item.original_price && item.final_price !== item.original_price ? `
                                <div class="item-price-details">
                                    <span class="original-price">原价: ${item.original_price}</span>
                                    <span class="final-price">现价: ${item.final_price}</span>
                                    ${item.discount_amount > 0 ? `<span class="discount">优惠: -${item.discount_amount}</span>` : ''}
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        
                        ${purchaseLimitInfo.hasLimit ? `
                        <div class="item-section">
                            <div class="section-title">🚫 限购信息</div>
                            <div class="item-limit-info">
                                ${purchaseLimitInfo.html}
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="item-section">
                            <div class="section-title">🔄 刷新配置</div>
                            <div class="item-refresh-info">
                                <div class="refresh-row">
                                    <span class="refresh-label">权重:</span>
                                    <span class="refresh-value weight-value">${item.refresh_weight}</span>
                                </div>
                                <div class="refresh-row">
                                    <span class="refresh-label">概率:</span>
                                    <span class="refresh-value probability-value">${(item.refresh_probability * 100).toFixed(1)}%</span>
                                </div>
                                <div class="refresh-row">
                                    <span class="refresh-label">相对权重:</span>
                                    <span class="refresh-value relative-weight">${this.calculateRelativeWeight(item.refresh_weight)}%</span>
                                </div>
                            </div>
                        </div>
                        
                        ${availabilityInfo.hasAvailability ? `
                        <div class="item-section">
                            <div class="section-title">⏰ 可用性配置</div>
                            <div class="item-availability-info">
                                ${availabilityInfo.html}
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="item-section">
                            <div class="section-title">🕒 时间信息</div>
                            <div class="item-time-info">
                                ${timeInfo.html}
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-sm btn-primary" title="编辑商品配置">✏️ 编辑</button>
                            <button class="btn btn-sm btn-secondary" title="复制商品配置">📋 复制</button>
                            <button class="btn btn-sm ${item.is_active ? 'btn-warning' : 'btn-success'}" title="${item.is_active ? '禁用' : '启用'}商品">
                                ${item.is_active ? '⏸️ 禁用' : '▶️ 启用'}
                            </button>
                            <button class="btn btn-sm btn-danger" title="删除商品配置">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            }

            renderDemo() {
                const container = document.getElementById('demoItemList');
                if (container) {
                    container.innerHTML = this.items.map(item => this.createItemCard(item)).join('');
                }
            }
        }

        // 页面加载完成后渲染演示
        document.addEventListener('DOMContentLoaded', () => {
            const demoManager = new DemoItemManager();
            demoManager.renderDemo();
        });
    </script>
</body>
</html>
