"""
公会核心服务
处理公会相关的业务逻辑，支持分布式环境
"""

import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from guild_models import (
    Guild, GuildMember, GuildApplication, GuildResponse, GuildListResponse,
    CreateGuildRequest, UpdateGuildRequest, GuildStatus, GuildPosition
)
from guild_manager import GuildDatabaseManager
from guild_cache_manager import GuildCacheManager
from guild_permissions import (
    GuildPermission, PermissionChecker, validate_guild_operation,
    GuildPermissionError, InsufficientPermissionError
)
from service_locator import ServiceLocator
from UserCacheManager import SimpleUserInfo,UserCacheManager
logger = logging.getLogger(__name__)


class GuildServiceDistributed:
    """公会核心服务 - 分布式版本"""
    
    def __init__(self):
        self.db_manager = GuildDatabaseManager()
        
        # 公会配置
        self.config = {
            "max_guild_name_length": 20,
            "min_guild_name_length": 2,
            "max_description_length": 200,
            "default_max_members": 30,
            "create_cooldown": 86400,  # 24小时创建冷却
            "max_guilds_per_server": 1000
        }

    # ==================== 内部通知方法 ====================

    async def _send_guild_notification(self, player_id: str, guild_info: dict = None, notification_type: str = "guild_info_update") -> bool:
        """发送公会通知给单个玩家"""
        try:
            connection_manager = ServiceLocator.get("conn_manager")
            if not connection_manager:
                logger.error("连接管理器不可用")
                return False

            message = {
                "msgId": "GUILD_NOTIFICATION",
                "success": True,
                "data": {
                    "type": notification_type,
                    "guild_info": guild_info,
                    "timestamp": datetime.now().isoformat()
                }
            }

            return await connection_manager.send_personal_message_to_user(message, player_id)

        except Exception as e:
            logger.error(f"发送公会通知失败: {player_id}, 错误: {str(e)}")
            return False

    async def _send_guild_notifications_to_members(self, member_ids: list, guild_info: dict = None, notification_type: str = "guild_info_update") -> dict:
        """发送公会通知给多个成员"""
        results = {}
        for player_id in member_ids:
            results[player_id] = await self._send_guild_notification(player_id, guild_info, notification_type)
        return results

    # ==================== 公会基础操作 ====================
    
    async def create_guild(self, player_id: str, request: CreateGuildRequest) -> GuildResponse:
        """创建公会"""
        try:
            # 检查玩家是否已在公会中
            existing_guild = await self.get_player_guild_id(player_id)
            if existing_guild:
                return GuildResponse(
                    success=False,
                    error="您已经在其他公会中，无法创建新公会"
                )
            
            # 检查公会名称是否可用
            if not await self.db_manager.is_guild_name_available(request.name):
                return GuildResponse(
                    success=False,
                    error="公会名称已被使用"
                )
            
            # 检查服务器公会数量限制
            guild_count = await self.db_manager.get_guild_count()
            if guild_count >= self.config["max_guilds_per_server"]:
                return GuildResponse(
                    success=False,
                    error="服务器公会数量已达上限"
                )
            cache_manager = await GuildCacheManager.get_instance()
            # 使用分布式锁防止并发创建
            async with await cache_manager.acquire_guild_lock("create", "global"):
                # 再次检查名称可用性（双重检查）
                if not await self.db_manager.is_guild_name_available(request.name):
                    return GuildResponse(
                        success=False,
                        error="公会名称已被使用"
                    )
                
                # 创建公会对象
                guild_id = self.db_manager.generate_guild_id()
                guild = Guild(
                    guild_id=guild_id,
                    name=request.name,
                    description=request.description,
                    logo=request.logo,
                    leader_id=player_id,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                # 保存到数据库
                if not await self.db_manager.create_guild(guild):
                    return GuildResponse(
                        success=False,
                        error="创建公会失败，请稍后重试"
                    )
                user_cace  = await UserCacheManager.get_instance()
                simpleinfo = await user_cace.get_simple_user_info(player_id)
                logger.info(f"创建公会: {guild_id} - {request.name} by {player_id} - {simpleinfo}")
                # 添加会长为成员
                leader_member = GuildMember(
                    guild_id=guild_id,
                    playerInfo = simpleinfo,
                    position=GuildPosition.LEADER,
                    contribution=0,
                    weekly_contribution=0,
                    total_contribution=0,
                    # 使用当前时间作为加入时间和最后活跃时间
                    joined_at=datetime.now(),
                    last_active=datetime.now()            
                )
                
                if not await self.db_manager.add_member(leader_member):
                    # 如果添加成员失败，需要回滚公会创建
                    await self.db_manager.disband_guild(guild_id)
                    return GuildResponse(
                        success=False,
                        error="创建公会失败，请稍后重试"
                    )
                cache_manager = await GuildCacheManager.get_instance()
                # 缓存公会信息
                await cache_manager.cache_guild_info(guild)
                await cache_manager.cache_player_guild(player_id, guild_id)

                # 推送公会信息给会长
                await self._send_guild_notification(player_id, guild.to_dict())

                logger.info(f"公会创建成功: {guild_id} - {request.name} by {player_id}")

                return GuildResponse(
                    success=True,
                    message="公会创建成功",
                    data={"guild": guild.to_dict()}
                )
                
        except Exception as e:
            logger.error(f"创建公会时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="创建公会时发生内部错误"
            )
    
    async def get_guild_info(self, guild_id: str, player_id: str = None) -> GuildResponse:
        """获取公会信息"""
        try:
            cache_manager = await GuildCacheManager.get_instance()
            # 先从缓存获取
            guild = await cache_manager.get_cached_guild_info(guild_id)
            
            # 缓存未命中，从数据库获取
            if not guild:
                guild = await self.db_manager.get_guild_by_id(guild_id)
                if not guild:
                    return GuildResponse(
                        success=False,
                        error="公会不存在"
                    )
                
                # 缓存公会信息
                await cache_manager.cache_guild_info(guild)
            
            # 如果指定了玩家ID，检查权限
            if player_id:
                member = await self.get_member_info(guild_id, player_id)
                if not member:
                    return GuildResponse(
                        success=False,
                        error="您不是该公会成员"
                    )
            
            return GuildResponse(
                success=True,
                data={"guild": guild.to_dict()}
            )
            
        except Exception as e:
            logger.error(f"获取公会信息时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="获取公会信息时发生内部错误"
            )

    async def update_guild_info(self, guild_id: str, player_id: str, request: UpdateGuildRequest) -> GuildResponse:
        """更新公会信息"""
        try:
            # 检查玩家权限
            member = await self.get_member_info(guild_id, player_id)
            if not member:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            # 验证权限
            try:
                validate_guild_operation(member.position, GuildPermission.UPDATE_GUILD_INFO)
            except GuildPermissionError as e:
                return GuildResponse(
                    success=False,
                    error=str(e)
                )
            cache_manager = await GuildCacheManager.get_instance()
            # 使用分布式锁
            async with await cache_manager.acquire_guild_lock(guild_id, "update"):
                # 构建更新数据
                update_data = {}
                
                if request.name is not None:
                    # 检查新名称是否可用
                    if request.name != (await self.db_manager.get_guild_by_id(guild_id)).name:
                        if not await self.db_manager.is_guild_name_available(request.name):
                            return GuildResponse(
                                success=False,
                                error="公会名称已被使用"
                            )
                    update_data["name"] = request.name
                
                if request.description is not None:
                    update_data["description"] = request.description
                
                if request.logo is not None:
                    update_data["logo"] = request.logo
                
                if request.join_condition is not None:
                    update_data["join_condition"] = request.join_condition
                
                if not update_data:
                    return GuildResponse(
                        success=False,
                        error="没有需要更新的内容"
                    )
                
                # 更新数据库
                if await self.db_manager.update_guild(guild_id, update_data):
                    # 使缓存失效
                    await cache_manager.invalidate_guild_info(guild_id)

                    # 获取更新后的公会信息
                    updated_guild = await self.db_manager.get_guild_by_id(guild_id)
                    if updated_guild:
                        # 获取所有成员ID
                        members = await self.db_manager.get_guild_members(guild_id)
                        member_ids = [member.player_id for member in members]

                        # 通知所有成员公会信息变更
                        await self._send_guild_notifications_to_members(member_ids, updated_guild.to_dict())

                    logger.info(f"公会信息更新成功: {guild_id} by {player_id}")

                    return GuildResponse(
                        success=True,
                        message="公会信息更新成功"
                    )
                else:
                    return GuildResponse(
                        success=False,
                        error="更新公会信息失败"
                    )
                    
        except Exception as e:
            logger.error(f"更新公会信息时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="更新公会信息时发生内部错误"
            )

    async def disband_guild(self, guild_id: str, player_id: str) -> GuildResponse:
        """解散公会"""
        try:
            # 检查玩家权限
            member = await self.get_member_info(guild_id, player_id)
            if not member:
                return GuildResponse(
                    success=False,
                    error="您不是该公会成员"
                )
            
            # 验证权限（只有会长可以解散公会）
            try:
                validate_guild_operation(member.position, GuildPermission.DISBAND_GUILD)
            except GuildPermissionError as e:
                return GuildResponse(
                    success=False,
                    error=str(e)
                )
            cache_manager = await GuildCacheManager.get_instance()
            # 使用分布式锁
            async with await cache_manager.acquire_guild_lock(guild_id, "disband"):
                # 获取所有成员信息（在解散前）
                members = await self.db_manager.get_guild_members(guild_id)

                # 解散公会
                if await self.db_manager.disband_guild(guild_id):
                    # 清除所有相关缓存
                    await cache_manager.invalidate_all_guild_cache(guild_id)

                    # 通知所有成员公会已解散
                    for member in members:
                        try:
                            await self._send_guild_notification(
                                member.player_id, None, "guild_disbanded"
                            )
                        except Exception as e:
                            logger.error(f"通知成员公会解散失败: {member.player_id}, 错误: {str(e)}")

                    logger.info(f"公会解散成功: {guild_id} by {player_id}, 已通知 {len(members)} 名成员")

                    return GuildResponse(
                        success=True,
                        message="公会已解散"
                    )
                else:
                    return GuildResponse(
                        success=False,
                        error="解散公会失败"
                    )
                    
        except Exception as e:
            logger.error(f"解散公会时发生错误: {str(e)}")
            return GuildResponse(
                success=False,
                error="解散公会时发生内部错误"
            )

    async def search_guilds(self, keyword: str = "", page: int = 1, page_size: int = 20) -> GuildListResponse:
        """搜索公会"""
        try:
            # 参数验证
            page = max(1, page)
            page_size = min(max(1, page_size), 50)  # 限制每页最多50条
            
            # 从数据库搜索
            result = await self.db_manager.search_guilds(keyword, page, page_size)
            
            return GuildListResponse(
                success=True,
                guilds=result["guilds"],
                total=result["total"],
                page=result["page"],
                page_size=result["page_size"]
            )
            
        except Exception as e:
            logger.error(f"搜索公会时发生错误: {str(e)}")
            return GuildListResponse(
                success=False,
                guilds=[],
                total=0,
                page=page,
                page_size=page_size
            )

    # ==================== 辅助方法 ====================
    
    async def get_player_guild_id(self, player_id: str) -> Optional[str]:
        """获取玩家所在的公会ID"""
        try:
            cache_manager = await GuildCacheManager.get_instance()
            # 先从缓存获取
            guild_id = await cache_manager.get_cached_player_guild(player_id)
            
            # 缓存未命中，从数据库获取
            if not guild_id:
                guild_id = await self.db_manager.get_player_guild(player_id)
                if guild_id:
                    # 缓存结果
                    await cache_manager.cache_player_guild(player_id, guild_id)
            
            return guild_id
            
        except Exception as e:
            logger.error(f"获取玩家公会ID时发生错误: {str(e)}")
            return None

    async def get_member_info(self, guild_id: str, player_id: str) -> Optional[GuildMember]:
        """获取成员信息"""
        try:
            # 先从缓存获取
            member = await self.cache_manager.get_cached_member_info(guild_id, player_id)
            
            # 缓存未命中，从数据库获取
            if not member:
                member = await self.db_manager.get_member(guild_id, player_id)
                if member:
                    # 缓存结果
                    await self.cache_manager.cache_member_info(member)
            
            return member
            
        except Exception as e:
            logger.error(f"获取成员信息时发生错误: {str(e)}")
            return None
